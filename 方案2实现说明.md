# 方案2：事件监听判断CommonSearchInput请求来源

## 实现思路

采用事件监听的方式来判断CommonSearchInput组件的请求来源，通过监听RemarkForm组件中的按钮点击事件，动态设置请求来源标记，并根据标记调整CommonSearchInput的请求参数。

## 核心实现

### 1. 在general.vue中添加事件监听逻辑

```javascript
// 方案2：使用事件监听判断请求来源
const requestSource = ref('normal'); // 'normal', 'remarkForm'

// 监听RemarkForm按钮点击事件
const handleRemarkFormClick = (event: Event) => {
    const target = event.target as HTMLElement;
    if (target && target.textContent && 
        (target.textContent.includes('View Original') || target.textContent.includes('View Latest'))) {
        console.log('检测到RemarkForm按钮点击，设置请求来源为remarkForm');
        requestSource.value = 'remarkForm';
        
        // 设置一个定时器，在短时间后重置来源标记
        setTimeout(() => {
            requestSource.value = 'normal';
        }, 5000); // 5秒后重置
    }
}

// 获取动态参数的方法
const getDynamicParams = () => {
    const baseParams = { "searchType": "custodyMarket", "status": null };
    
    // 根据来源添加不同的参数
    if (requestSource.value === 'remarkForm') {
        return JSON.stringify({
            ...baseParams,
            "source": "remarkForm",
            "requestId": Date.now() // 添加时间戳确保参数不同
        });
    }
    
    return JSON.stringify(baseParams);
}

// 监听CommonSearchInput的搜索事件
const handleSearchInputClick = () => {
    // 在CommonSearchInput触发搜索时，检查当前的请求来源
    if (requestSource.value === 'remarkForm') {
        console.log('CommonSearchInput请求来源：RemarkForm');
        // 这里可以添加特殊处理逻辑
    } else {
        console.log('CommonSearchInput请求来源：正常操作');
    }
}

// 组件挂载时添加事件监听
onMounted(() => {
    // 监听整个文档的点击事件
    document.addEventListener('click', handleRemarkFormClick);
})

// 组件卸载时移除事件监听
onUnmounted(() => {
    document.removeEventListener('click', handleRemarkFormClick);
})
```

### 2. 修改CommonSearchInput使用动态参数

```vue
<CommonSearchInput v-model="ruleForm.form.custodyMarket"
    commType="CUSTODY_MARKET"
    url="/datamgmt/api/v1/searchinput"
    :params="getDynamicParams()"
    codeTitle="csscl.acctCode.custodyMarket"
    style="width:750px" 
    :disabled="ruleForm.form.custodyLevel === 'GC'"
    @click="handleSearchInputClick" />
```

## 工作原理

1. **事件监听**：通过监听整个文档的点击事件，检测RemarkForm中"View Original"和"View Latest"按钮的点击

2. **来源标记**：当检测到RemarkForm按钮点击时，设置`requestSource`为'remarkForm'

3. **动态参数**：`getDynamicParams()`方法根据`requestSource`的值返回不同的参数：
   - 正常情况：`{ "searchType": "custodyMarket", "status": null }`
   - RemarkForm触发：`{ "searchType": "custodyMarket", "status": null, "source": "remarkForm", "requestId": 时间戳 }`

4. **自动重置**：使用定时器在5秒后自动重置来源标记，避免状态污染

5. **日志记录**：在控制台输出请求来源信息，便于调试

## 后端处理

后端可以通过检查请求参数中的`source`字段来判断请求来源：

```javascript
// 后端伪代码
if (params.source === 'remarkForm') {
    // 来自RemarkForm的请求处理逻辑
    console.log('处理来自RemarkForm的请求');
    return handleRemarkFormRequest(params);
} else {
    // 正常请求处理逻辑
    console.log('处理正常请求');
    return handleNormalRequest(params);
}
```

## 优点

1. **无侵入性**：不需要修改CommonSearchInput和RemarkForm组件
2. **灵活性**：可以轻松扩展支持更多来源判断
3. **自动化**：通过事件监听自动检测，无需手动调用
4. **可调试**：提供详细的日志输出
5. **安全性**：使用定时器自动重置，避免状态残留

## 注意事项

1. 事件监听器会在组件卸载时自动移除，避免内存泄漏
2. 定时器设置为5秒，可根据实际需要调整
3. 通过按钮文本内容判断，需要确保按钮文本稳定
4. 添加了时间戳确保每次请求参数都不同
