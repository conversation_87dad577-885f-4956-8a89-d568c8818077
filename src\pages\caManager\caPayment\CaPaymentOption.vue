<template>
    <Grid url="/bff/ca/api/v1/ca-event-option/get-ca-event-option-page-list"
        isShowSearch="false"
        ref="gridRef"
        :params="params"
        :selectable="false"
        :header-cell-style="headerCellStyle"
        :on-db-click="handleClick"
    >
        <template v-slot:tableColumnFront="slotProps">
            <el-table-column sortable="custom" prop="caEventOptionSequenceNumber"
                             :label="$t('csscl.ca.common.optionNo')" align="center" />
            <el-table-column sortable="custom" prop="caEventOptionType"
                             :label="$t('csscl.ca.common.cashScripBothNoac')" align="center" />

            <el-table-column :label="$t('csscl.ca.common.cash')" align="center">
                <el-table-column sortable="custom" prop="optionPayDateForCash"
                                 :label="$t('csscl.ca.common.optionPayDateCash')" align="center" />
                <el-table-column sortable="custom" prop="cashUnitCcyCode"
                                 :label="$t('csscl.ca.common.currency')" align="center" />
                <el-table-column sortable="custom" prop="cashUnitAmount"
                                 :label="$t('csscl.ca.common.distributionRate')"  align="center" />
                <el-table-column sortable="custom" prop="cashUnit"
                                 :label="$t('csscl.ca.common.per')" align="center" />
            </el-table-column>

            <el-table-column :label="$t('csscl.ca.common.scrip')" align="center">
                <el-table-column sortable="custom" prop="optionPayDateForScrip" :label="$t('csscl.ca.common.optionPayDateScrip')" align="center" />
                <el-table-column sortable="custom" prop="newInstrumentCode" :label="$t('csscl.ca.common.newSecurityId')" align="center" />
                <el-table-column sortable="custom" prop="scripRateNewUnit" :label="$t('csscl.ca.common.share')" align="center" />
                <el-table-column sortable="custom" prop="scripRateOldUnit" :label="$t('csscl.ca.common.per')" align="center" />
            </el-table-column>

            <el-table-column sortable="custom" prop="optionPayStatus"
                             :label="$t('csscl.ca.common.paymentStatus')" align="center" />
            <el-table-column sortable="custom" prop="optionPayDateForCash"
                             :label="$t('csscl.ca.common.recordStatus')" align="center">
              <template #default="scope">
                {{ getRecordStatusDesc(scope.row) }}
              </template>
            </el-table-column>
        </template>
    </Grid>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, defineExpose} from 'vue';
import Grid from '~/pages/caManager/caClientResponse/CaClientResponseGrid.vue';
import {getRecordStatusDesc} from "~/util/Function";

const gridRef = ref();
const params = ref({
    caEventReferenceNumber: ''
});

const showDetails = async (eventRefNo: any) => {
    params.value = { caEventReferenceNumber: eventRefNo };
    // 触发 Grid 组件重新加载数据
    await gridRef.value.load();
};

const headerCellStyle  = (row:any, column:any, rowIndex:any, columnIndex:any) => {
    if (rowIndex === 0) {
        if (columnIndex === 2) {
            return { background: '#FFFFCC' }
        }
        if (columnIndex === 3) {
          return { background: '#CCFFFF' }
        }
    }
};

const emit = defineEmits(['row-click']);
const handleClick = (row: any) => {
  emit('row-click', row, params.value.caEventReferenceNumber);
};

const clearData = () => {
  // 清空表格数据
  gridRef.value.tableData = [];
  // 清空总数
  gridRef.value.total = 0;
}

defineExpose({ 
    showDetails,
    handleClick,
    clearData
});
</script>

<style>

</style>