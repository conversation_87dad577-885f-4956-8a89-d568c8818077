<template #default>
  <SearchInput
        :title="$t(props.title ? props.title : props.codeTitle ? props.codeTitle : 'csscl.commonCode.code')"
        :url="thisUrl()"
        :searchType="props.commType"
        ref="searchInput"
        :maxlength="maxlength()"
        :params="thisParams()"
        :disabled="props.disabled"
        :columns="[
          {
            title: $t(props.codeTitle ? props.codeTitle : 'csscl.commonCode.code'),
            colName: 'code',
          },
          {
            title: $t(props.codeDescTitle ? props.codeDescTitle : 'csscl.commonCode.description'),
            colName: 'codeDesc',
          }
        ]"
        /> 
</template>

<script lang="ts" setup>
import { ref, watch, computed, defineEmits } from 'vue';

const props = defineProps(['commType', 'disabled', 'title', 'codeTitle', 'codeDescTitle', 'maxlength', 'url', 'params']);
const searchInput = ref();

const maxlength = () => {
  return props.maxlength || 20;
}

//  Start SET-41 chengchengfu 2025/07/02
const thisUrl = () => {
  return props.url || 'COMMONLIST';
}

const thisParams = () => {
  if (!props.params) return {};
  let parsed = {};
  try {
    parsed = JSON.parse(props.params);
  } catch (e) {
    console.error('Invalid JSON string:', e);
  }
  return parsed;
}
//  End SET-41 chengchengfu 2025/07/02

defineExpose({
  searchInput
})
</script>

<style>

</style>