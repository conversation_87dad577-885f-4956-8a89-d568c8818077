<template>
  <BasePanel :searchParams="searchParams" :paramListData='paramListData' url="/bff/ca/api/v1/ca-event/get-ca-event-on-payment-page-list"
             :params="{ modeEdit: 'Y' }"  ref="tableRef" :sortProp="{}" :isHideAdd="true" :hide-operation=true :show-details="showDetails"
             @search="handleCaSearch">
    <template v-slot:searchPanel="slotProps">
      <FormRow>
        <ElFormItemProxy label-width="150" :label="$t('csscl.ca.common.eventRefNo')" prop="caEventReferenceNumber" >
          <el-input v-model="slotProps.form.caEventReferenceNumber" input-style="text-transform:none" />
        </ElFormItemProxy>
        <ElFormItemProxy label-width="150" :label="$t('csscl.ca.common.eventCategory')" prop="eventCategory">
          <SearchInput style="width: 200px" v-model="slotProps.form.caEventCategory"
                       url="/datamgmt/api/v1/searchinput" showDesc="false"
                       :title="$t('csscl.ca.common.eventCategory')"
                       :params="{searchType: 'caEventCategoryCode'}"
                       :columns="[
                          {
                              title: $t('csscl.ca.common.eventCategoryCode'),
                              colName: 'code',
                          },
                          {
                              title: $t('csscl.ca.common.eventTypeDescription'),
                              colName: 'codeDesc',
                          }
                       ]"
                       :pageSizes="[10, 20, 30]">
          </SearchInput>
        </ElFormItemProxy>
        <ElFormItemProxy label-width="150" :label="$t('csscl.ca.common.swiftEventType')" prop="swiftEventType">
          <SearchInput style="width: 200px" v-model="slotProps.form.swiftEventCode"
                       url="/datamgmt/api/v1/searchinput" showDesc="false"
                       :title="$t('csscl.ca.common.swiftEventType')"
                       :params="{searchType: 'swiftEventCode'}"
                       :columns="[
                          {
                              title: $t('csscl.ca.common.eventTypeCode'),
                              colName: 'code',
                          },
                          {
                              title: $t('csscl.ca.common.eventTypeDescription'),
                              colName: 'codeDesc',
                          }
                       ]"
                       :pageSizes="[10, 20, 30]">
          </SearchInput>
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy label-width="150" :label="$t('csscl.ca.common.recordDate')" prop="recordDate" >
          <DateItem :validate-event="false" v-model="slotProps.form.recordDate" type="date" style="width: 190px;" />
        </ElFormItemProxy>
        <ElFormItemProxy label-width="150" :label="$t('csscl.ca.common.securityId')" prop="instrumentCode">
          <SearchInput style="width: 200px" v-model="slotProps.form.instrumentCode"
                       url="/datamgmt/api/v1/searchinput" showDesc="false"
                       :title="$t('csscl.ca.common.instrument')"
                       :params="{searchType: 'instrumentCode'}"
                       :columns="[
                          {
                              title: $t('csscl.ca.common.instrumentCode'),
                              colName: 'code',
                          },
                          {
                              title: $t('csscl.ca.common.instrumentDescription'),
                              colName: 'codeDesc',
                          }
                       ]"
                       :pageSizes="[10, 20, 30]">
          </SearchInput>
        </ElFormItemProxy>
        <ElFormItemProxy label-width="150" :label="$t('csscl.ca.common.paymentStatus')" prop="paymentStatus">
          <Select v-model="slotProps.form.paymentStatus" type="STATUS" style="width: 170px;"/>
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy label-width="150" :label="$t('csscl.ca.common.clearingAgent')" prop="clearingAgent">
          <SearchInput style="width: 200px" v-model="slotProps.form.clearingAgentCode"
                       url="/datamgmt/api/v1/searchinput" showDesc="false"
                       :title="$t('csscl.ca.common.clearingAgent')"
                       :params="{searchType: 'clearingAgentCode'}"
                       :columns="[
                          {
                              title: $t('csscl.ca.common.clearingAgentCode'),
                              colName: 'code',
                          },
                          {
                              title: $t('csscl.ca.common.clearingAgentName'),
                              colName: 'codeDesc',
                          }
                       ]"
                       :pageSizes="[10, 20, 30]">
          </SearchInput>
        </ElFormItemProxy>
        <ElFormItemProxy label-width="150" :label="$t('csscl.ca.common.custodyAccountNo')" prop="custodyAccountNo">
          <SearchInput style="width: 200px" v-model="slotProps.form.tradingAccountCode"
                       url="/datamgmt/api/v1/searchinput" showDesc="false"
                       :title="$t('csscl.acctCode.custodyAcctNumber')"
                       :params="{searchType: 'custodyAcct'}"
                       :columns="[
                          {
                              title: $t('csscl.acctCode.custodyAcctNumber'),
                              colName: 'code',
                          },
                          {
                              title: $t('csscl.acctCode.accountShortName'),
                              colName: 'codeDesc',
                          }
                       ]"
                       :pageSizes="[10, 20, 30]">
          </SearchInput>
        </ElFormItemProxy>
        <ElFormItemProxy label-width="150" :label="$t('csscl.ca.common.custodianAccount')" prop="custodianAccountNumber">
            <el-input v-model="slotProps.form.custodianAccountNumber" input-style="text-transform:none" style="width: 170px;" />
        </ElFormItemProxy>
      </FormRow>
    </template>
    <template v-slot:tableHeaderTitle>
      <div style="margin: 0px; padding: 3px; border-bottom: 2px solid #FFAB2D;">
        <el-text style="color: #FFAB2D; font: 14px bold;">
          CA Event
        </el-text>
      </div>
    </template>
    <template v-slot:tableColumn>
      <el-table-column align="left" header-align="center" sortable="custom" prop="caEventReferenceNumber"
                       :label="$t('csscl.ca.common.eventRefNo')" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="caEventCategory"
                       :label="$t('csscl.ca.common.eventCategory')" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="swiftInstructionType"
                       :label="$t('csscl.ca.common.camv')" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="swiftEventCode"
                       :label="$t('csscl.ca.common.swiftEventType')" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="clearingAgentCode"
                       :label="$t('csscl.ca.common.clearingAgent')" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="caCashPaymentMethod"
                       :label="$t('csscl.ca.common.paymentMethod')" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="announceDescription"
                       :label="$t('csscl.ca.common.description')" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="instrumentCode"
                       :label="$t('csscl.ca.common.securityId')" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="instrumentShortName"
                       :label="$t('csscl.ca.common.securityName')" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="caEventCustodianPaymentReconMatchInd"
                       :label="$t('csscl.ca.common.paymentReconMatched')" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="recordDate"
                       :label="$t('csscl.ca.common.recordDate')" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="eventValueDate"
                       :label="$t('csscl.ca.common.valueDate')" />
    </template>

    <template v-slot:contentBottom>
      <br />
      <br />
      <!-- Ca Option ------------------------------>
      <div style="margin: 0px; padding: 3px; border-bottom: 2px solid #FFAB2D;">
        <el-text style="color: #FFAB2D; font: 14px bold;">
          CA Option
        </el-text>
      </div>
      <CaOption ref="caOptionRef" @row-click="handleOptionRowClick"/>
      <br />
      <br />
      <!-- CA Client ------------------------------>
      <div style="margin: 0px; padding: 3px; border-bottom: 2px solid #FFAB2D;">
        <el-text style="color: #FFAB2D; font: 14px bold;">
          CA Client
        </el-text>
      </div>
      <CaClient ref="caClientRef" />
      <br />
      <br />
      <el-space>
        <el-button @click="exportClick">
          {{ $t('csscl.ca.common.exportCSV') }}
        </el-button>
      </el-space>
      <el-space>
        <el-button @click="" style="margin-left: 15px;" color="#5679da" :dark="isDark">
          {{ $t('csscl.ca.common.distribute') }}
        </el-button>
      </el-space>
      <el-space>
        <el-button @click="" style="margin-left: 15px;" color="#5679da" :dark="isDark">
          {{ $t('csscl.ca.common.backdateDistribute') }}
        </el-button>
      </el-space>
      <el-space>
        <el-button @click="" style="margin-left: 15px;" color="#5679da" :dark="isDark">
          {{ $t('csscl.ca.common.contractual') }}
        </el-button>
      </el-space>
      <el-space>
        <el-button @click="" style="margin-left: 15px;" color="#5679da" :dark="isDark">
          {{ $t('csscl.ca.common.reverseContractual') }}
        </el-button>
      </el-space>
      <el-space>
        <el-button type="success" @click="" style="margin-left: 15px;">
          {{ $t('csscl.ca.common.reverse') }}
        </el-button>
      </el-space>
    </template>
  </BasePanel>

</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance, watch } from 'vue';
import { Search } from '@element-plus/icons-vue'
import { downloadFilePost, showWarningMsg } from '~/util/Function.js';
import BasePanel from '~/pages/base/CaIndex.vue'
import FormItemSign from "~/pages/base/FormItemSign.vue";
import CaOption from "~/pages/caManager/caPayment/CaPaymentOption.vue";
import CaClient from "~/pages/caManager/caPayment/CaPaymentClient.vue";
import {getTimeZone} from "~/util/DateUtils";
import {isDark} from "~/composables";


const { proxy } = getCurrentInstance()
const paramListData = {};
const searchParams = ref({
  caEventReferenceNumber: '',
  caEventCategory: '',
  swiftEventCode: '',
  recordDate: '',
  instrumentCode: '',
  paymentStatus: '',
  clearingAgentCode: '',
  tradingAccountCode: '',
  custodianAccountNumber: ''

});

const tableRef = ref();
const caOptionRef = ref();
const caClientRef = ref();
const selectedEventRow = ref(null);
const selectedOptionRow = ref(null);
const reload = () => {
  tableRef.value.load();
}
const showDetails = async (row) => {
  selectedEventRow.value = row;

  // 清空 CA Option 和 CA Client 数据
  selectedOptionRow.value = null;
  caClientRef.value.clearData?.();

  // 渲染 CA Option组件
  await caOptionRef.value.showDetails(row.caEventReferenceNumber);
}

const handleOptionRowClick = async (row: any, eventRefNo: any) => {
  selectedOptionRow.value = row;
  // 渲染 CA Client组件
  await caClientRef.value.showDetails(row, eventRefNo);
};

const exportClick = async() => {
  if (!selectedEventRow.value) {
    showWarningMsg("Please select a row in CA Event table.");
    return;
  }
  if (!selectedOptionRow.value) {
    showWarningMsg("Please select a row in CA Option table.");
    return;
  }
  let params = {
    data: {
      caEventReferenceNumber: selectedEventRow.value.caEventReferenceNumber ,
      caEventOptionSequenceNo: selectedOptionRow.value.caEventOptionSequenceNumber
    },
    header: {timezone: getTimeZone(), lang:"en_US"}
  };
  downloadFilePost("/bff/ca/api/v1/ca-event-client-payment-info/export-ca-event-client-payment-info", params);
}

const handleCaSearch = async() => {
  caOptionRef.value?.clearData();
  caClientRef.value?.clearData();
}

defineExpose({
  showDetails
});
</script>

<style></style>