<template>
  <div style="display:flex;justify-content: center; align-items:center; width: 100%;height: 100%;position: absolute;">
    <el-space v-if="!isAutoLogin||isAutoLogin=='N'" style="text-align: center;margin:auto;">
      <el-button @click="loginByDeveloper" type="primary" link>
        Login By Developer
      </el-button>
      <el-button @click="loginHttpFn" type="warning" link>
        Login by UMS
      </el-button>
    </el-space>
  </div>
</template>

<script setup lang="ts">
import oidcClient from '~/util/oidcClient'
import { getCurrentInstance, ref } from 'vue';
import { useCookies } from "vue3-cookies";
import {ElMessage, ElMessageBox} from 'element-plus';
import jwt_decode from 'jwt-decode';
// Start SK-COMMON-0083, Tom.Li, 2024/08/19
// import { initialize, getBaseUrl } from '~/util/Function';
import { afterLoginOperat, getBaseUrl,clearCookies } from '~/util/Function';
// End SK-COMMON-0083, Tom.Li, 2024/08/19
const {
  getTokenHttp,
  getQueryString
} = oidcClient
const { cookies } = useCookies();
const { proxy } = getCurrentInstance();
const {
  loginHttp
} = oidcClient
const isAutoLogin = ref(import.meta.env.VITE_AUTO_LOGIN);

const loginByDeveloper = () => {
  
  location.href = getBaseUrl() + "?isDev=Y";
}

const loginHttpFn = () => {
  loginHttp({ loginOkUrl: import.meta.env.VITE_FRONTEND_HOME })
    .then((res) => {
      console.log('res', res);
    }).catch((err) => {
      console.log("err", err);
    });
}

const getTokenHttpFn = async () => {
  let userData = await getTokenHttp({ httOnly: true })
    .catch((err) => {
      ElMessage({
        message: 'Session has been idle over UMS time limit. Please exit the browser and re-enter system.',
        type: 'error'
      });
    })
  if (userData) {
    const userInfo = jwt_decode(cookies.get("id_token"));
    cookies.set("access_token",userData.access_token);
    cookies.set("refresh_token",userData.refresh_token);
    loginBeforeCheck(userInfo).then((data) => {
      if(data){
        console.log("init-data",data)
        const alertMsg = data.alertMessage[0];
        ElMessageBox.confirm(alertMsg,'Warning', {
          confirmButtonText: 'OK',
          cancelButtonText: 'Cancel',
          type: 'warning',
        }).then(async() => {
          await init(userInfo)
        }).catch(() => {
          clearCookies(true)
        });

      }
    }).catch(async() => {
      await init(userInfo)
    });
  }
}
const init = async(userInfo)=>{
  let body = await proxy.$axios.post('/auth/api/v1/ums/init', "userId=" + userInfo.nameId + "&roles=" + userInfo.authorisation);
  if (body.success) {
    // Start SK-COMMON-0083, Tom.Li, 2024/08/19
    afterLoginOperat(body);
    // End SK-COMMON-0083, Tom.Li, 2024/08/19
    // Start SK-COMMON-0059, Tom.Li, 2024/08/14
    localStorage.setItem('apiDecode',body.data);
    // End SK-COMMON-0059, Tom.Li, 2024/08/14
    await proxy.$axios.patch('/auth/api/v1/user/cache');
    cookies.set('loginTime', proxy.$moment().format('YYYY/MM/DD HH:mm:ss'));
    cookies.set('username', userInfo.nameId);
    location.href = getBaseUrl() ;
  }
}
const checkLogin = async () => {
  const keyStr = window.location.href.split("?")[1]
  const code = getQueryString('code', keyStr);
  if (!cookies.get("id_token") && code) {
    await getTokenHttpFn();
  } else if(isAutoLogin.value=='Y') {
    loginHttpFn();
  }
}
const loginBeforeCheck = (userInfo) => new Promise((resolve, reject) => {
  proxy.$axios.post('/auth/api/v1/user/login/b4check', {
    "userId": userInfo.nameId,
  }).then((body) => {
    body.success && body.data ? resolve(body) : reject(false);
  }).catch(() => reject(false));
});

checkLogin();

</script>