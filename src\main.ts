import { createApp } from "vue";
import App from "./App.vue";
import LoginApp from "./LoginApp.vue";
import generateRouter from './router';
import ElementPlus from 'element-plus';
import axios from './axios';
import 'element-plus/dist/index.css';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';
import { createI18n } from 'vue-i18n';
import pinia from './store';
import VueCookies, { useCookies } from 'vue3-cookies';
import "~/styles/index.scss";
import "uno.css";
import moment from 'moment';
import directives from '~/util/directives';

import "element-plus/theme-chalk/src/message-box.scss";
import "element-plus/theme-chalk/src/message.scss";

import { currentInfo } from '~/store/modules/currentInfo';
import { commonCode } from '~/store/modules/commonCode';
// Start SK-COMMON-0083, Tom.Li, 2024/08/19
import { haveLoggedIn } from '~/util/Function.js';
// End SK-COMMON-0083, Tom.Li, 2024/08/19
function isDev() {
  // Start SK-COMMON-0098, <PERSON>.Li, 2024/08/23
  if(localStorage.getItem('isDev')){
    return localStorage.getItem('isDev') === 'Y';
  }
  // End SK-COMMON-0098, Tom.Li, 2024/08/23
  let url = window.location.href;
  if (url.indexOf("?") > 0) {
    let urlStr = url.split('?')[1]
    const urlSearchParams = new URLSearchParams(urlStr)
    const result = Object.fromEntries(urlSearchParams.entries())
    return result.isDev == 'Y';
  }
  return false;
}
const { cookies } = useCookies();
if (isDev()) {
  axios.defaults.baseURL = import.meta.env.VITE_API_DEV_URL;
} else {
  localStorage.removeItem('isDev');
  axios.defaults.baseURL = import.meta.env.VITE_API_BASE_URL;
}
// Start SK-COMMON-0083, Tom.Li, 2024/08/19
// if (!cookies.get("x-esession") && !isDev()) {
if (!haveLoggedIn() && !isDev()) {
// End SK-COMMON-0083, Tom.Li, 2024/08/19
  // axios.defaults.baseURL = import.meta.env.VITE_API_BASE_URL;
  const app = createApp(LoginApp);
  app.use(VueCookies);
  app.use(pinia);
  const currentInfoStore = currentInfo();
  const commonCodeStore = commonCode();
  app.config.globalProperties.$moment = moment;
  app.config.globalProperties.$axios = axios;
  app.config.globalProperties.$currentInfoStore = currentInfoStore;
  app.config.globalProperties.$commonCodeStore = commonCodeStore;
  app.mount("#app");
} else {
  // axios.defaults.baseURL = import.meta.env.VITE_API_DEV_URL;
  const app = createApp(App);
  app.use(pinia);
  const currentInfoStore = currentInfo();
  const commonCodeStore = commonCode();


  app.config.globalProperties.$moment = moment;
  app.config.globalProperties.$axios = axios;
  app.config.globalProperties.$currentInfoStore = currentInfoStore;
  app.config.globalProperties.$commonCodeStore = commonCodeStore;
  app.use(VueCookies);

  const router = generateRouter();
  app.use(router);
  app.use(ElementPlus);
  app.use(directives);

  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
  }
  if (!cookies.get("x-esession") && isDev()) {
    app.mount("#app");
  } else {
    axios.get("/datamgmt/api/v1/messages?packages=menu,web,message&name=en_US").then((msg) => {
      //导入语言文件 
      const messages = {
        enUS: msg.data
      }
      const localeData = {
        globalInjection: true, //如果设置true, $t() 函数将注册到全局
        legacy: false,
        locale: "enUS",
        messages  // set locale messages
      }
      const i18n = createI18n(localeData);
      app.use(i18n);
      app.mount("#app");
    });
  }
}
