<template>
  <BasePanel :searchParams="searchParams" :paramListData="paramListData" :params="{ modeEdit: 'Y' }"
    url="/bff/ca/api/v1/ca-event/get-entitle-summary-page-list" :selectable="selectable" :editRow="editRow"
    :clickRow="clickRow" ref="tableRef" :showDetails="showDetails" :isHideAdd="true">
    <template v-slot:searchPanel="slotProps">
      <FormRow>
        <ElFormItemProxy :label="$t('csscl.ca.common.eventRefNo')" label-width="150" prop="caEventReferenceNumber">
          <GeneralSearchInput v-model="slotProps.form.caEventReferenceNumber" style="width:290px" :title="$t('csscl.ca.common.eventRefNo')" 
          searchType="caEventReferenceNumber" showDesc="false"/>
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.ca.common.eventCategoryCode')" label-width="150" prop="eventCategory">
          <SearchInput style="width: 270px" v-model="slotProps.form.caEventCategory"
            url="/datamgmt/api/v1/searchinput" showDesc="true"
            :title="$t('csscl.ca.common.eventCategory')"
            :params="{searchType: 'caEventCategoryCode'}"
            :columns="[
              {
                  title: $t('csscl.ca.common.eventCategoryCode'),
                  colName: 'code',
              },
              {
                  title: $t('csscl.ca.common.eventCategoryDescription'),
                  colName: 'codeDesc',
              }
            ]"
            :pageSizes="[10, 20, 30]">
          </SearchInput>  
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.ca.common.swiftEventType')" label-width="150" prop="swiftEventType">
          <SearchInput style="width: 200px" v-model="slotProps.form.swiftEventCode" :disabled="false"
            url="/datamgmt/api/v1/searchinput" showDesc="true" :title="$t('csscl.ca.common.eventType')"
            :params="{ searchType: 'caEventType' }" :columns="[
              {
                title: $t('csscl.ca.common.eventTypeCode'),
                colName: 'code',
              },
              {
                title: $t('csscl.ca.common.eventTypeDescription'),
                colName: 'codeDesc',
              }
            ]" :pageSizes="[10, 20, 30]">
          </SearchInput>
        </ElFormItemProxy>
        <ElFormItemProxy />
      </FormRow>
      <FormRow>
        <ElFormItemProxy :label="$t('csscl.ca.common.recordDate')" label-width="150" prop="recordDate">
          <DateItem :validate-event="false" v-model="slotProps.form.recordDate" type="date" style="width: 285px;" />
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.ca.common.securityId')" label-width="150" prop="securityId">
          <GeneralSearchInput v-model="slotProps.form.instrumentCode" style="width:270px" searchType="instrumentCode" showDesc="true"/>
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.ca.common.caEventStatus')" label-width="150" prop="caEventStatus">
          <Select v-model="slotProps.form.caEventStatus" type='CA_EVENT_STATUS'
            v-model:desc="paramListData.caEventStatus" />
        </ElFormItemProxy>
        <ElFormItemProxy />
      </FormRow>
      <FormRow>
        <ElFormItemProxy :label="$t('csscl.ca.common.clearingAgent')" label-width="150" prop="clearingAgent">
          <GeneralSearchInput v-model="slotProps.form.clearingAgentCode" style="width:280px" searchType="clearingAgentCode" showDesc="true"/>
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.ca.common.custodyAccountNo')" label-width="150" prop="custodyAccountNo">
          <GeneralSearchInput v-model="slotProps.form.custodyAccountNumber" style="width:280px" searchType="custodyAcct" showDesc="false"/>
        </ElFormItemProxy>
        <ElFormItemProxy />
        <ElFormItemProxy />
      </FormRow>
    </template>
    <template v-slot:tableHeaderTitle>
      <SectionTitle :title="$t('csscl.ca.common.caEvent')" />
    </template>
    <template v-slot:tableColumn>
      <el-table-column sortable="custom" prop="caEventReferenceNumber" :label="$t('csscl.ca.common.eventRefNo')" />
      <el-table-column sortable="custom" prop="caEventCategory" :label="$t('csscl.ca.common.eventCategory')" />
      <el-table-column sortable="custom" prop="clearingAgentCode" :label="$t('csscl.ca.common.clearingAgent')" />
      <el-table-column sortable="custom" prop="announceDescription" :label="$t('csscl.ca.common.description')" />
      <el-table-column sortable="custom" prop="instrumentCode" :label="$t('csscl.ca.common.securityId')" />
      <el-table-column sortable="custom" prop="instrumentShortName" :label="$t('csscl.ca.common.securityName')" />
      <el-table-column sortable="custom" prop="recordDate" :label="$t('csscl.ca.common.recordDate')" />
      <el-table-column sortable="custom" prop="totalSnapshotQuantity"
        :label="$t('csscl.ca.common.totalSnapshotQuantity')" />
      <el-table-column sortable="custom" prop="totalEntitleSelectedQuantity"
        :label="$t('csscl.ca.common.totalEntitleSelectedQuantity')" />
      <el-table-column sortable="custom" prop="custodianEntitleQuantity"
        :label="$t('csscl.ca.common.totalEntitledQuantity')" />
      <el-table-column sortable="custom" prop="custodianEntitleQuantityReconMatchIndicator"
        :label="$t('csscl.ca.common.match')" />
    </template>
  </BasePanel>
  <Details ref="detailsRef" :reload="reload" />
</template>

<script lang="ts" setup>
import { ref, getCurrentInstance } from 'vue';
import BasePanel from '~/pages/base/CaIndex.vue';
import SectionTitle from '~/components/Ca/CaSectionTitle.vue';
import Details from './CaEntitlementDetails.vue';
const keyName = "caEventOid";
const clientEntitlementDetailsUrl = "/bff/ca/api/v1/ca-event-client-entitlement/get-ca-event-client-entitlement-page-list";

const searchParams = ref({
  caEventReferenceNumber: "",
  caEventCategory: "",
  swiftEventCode: "",
  recordDate: "",
  instrumentCode: "",
  caEventStatus: "",
  clearingAgentCode: "",
  custodyAccountNumber: ""
});
const paramListData = ref({});

const tableSelectRowsRef = ref([]);

const clientEntitlementDetailsList = ref(new Map());

const { proxy } = getCurrentInstance()
const detailsRef = ref();
const tableRef = ref();
const clickRow = async (row) => {
  console.log("clickRow, enter");
  tableSelectRowsRef.value = row;
  console.log("clickRow, exit");
}
function selectable(row, index) {
  // if(row?.processStatus !== 'F'){
  //   return false;
  // }
  return true;
}

const showDetails = (row, disabled) => {
  console.log("showDetails, enter");
  console.log(row, disabled);
  detailsRef.value.showDetails(row, disabled);
  console.log("showDetails, exit");
}

const editRow = (row) => {
  console.log("editRow, enter");
  detailsRef.value.editRow(row);
  console.log("editRow, exit");
}

const reload = () => {
  tableRef.value.load();
}

</script>

<style></style>
