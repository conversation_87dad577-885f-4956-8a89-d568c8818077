<template>
  <div :class="getClass()" >
    <ElFormItemProxy v-bind="$attrs" :show-message="false" >
      <el-space :code="$attrs.prop" >
        <el-tooltip popper-class="form-item-tip" :append-to-body="true"  :teleported="false" placement="bottom-start" :visible="isShowTip">
          <template #content>
            <span @click="()=>{isShowTip = false}">&nbsp;{{ showOriginalVal() }}&nbsp;</span>
          </template>
          <slot></slot>
        </el-tooltip>
        <el-icon v-if="isShowSign()"><InfoFilled  @click="isShowTip = !isShowTip" style="color:var(--ep-color-danger);" /></el-icon>
        <!--<el-form><el-button v-if="isShowSign()" link :icon="InfoFilled" @click="isShowTip = !isShowTip" type="danger" /></el-form>-->
      </el-space>
    </ElFormItemProxy>
  </div>
</template>

<script lang="ts" setup>
import { ref, getCurrentInstance, watch } from 'vue';
import moment from "moment"
import {
  InfoFilled
} from '@element-plus/icons-vue'
import { parseBool } from '~/util/Function.js';
const props = defineProps(['detailsRef', 'showBef']);
const { proxy } = getCurrentInstance();
const isShowTip = ref(false);
props.detailsRef?.addField && props.detailsRef?.addField(proxy.$attrs.prop,proxy.$attrs.hideLabel?proxy.$attrs.hideLabel:proxy.$attrs.label);
const unEqVals = (val1: any, val2: any) => {
  if(!isNaN(Number(val1))) {
    return Number(val1) != Number(val2);
  } else {
    return val1 != val2;
  }
}
const isShowSign = () => {
  if (parseBool(props.showBef || "") == false) {
    return false;
  }
  if (!props.detailsRef) {
    return false;
  }
  if (props.detailsRef?.hasOwnProperty("addValidField") && proxy.$attrs.prop && !proxy.$attrs.unvalid) {
    props.detailsRef.addValidField(proxy.$attrs.prop);
  }
  let showSts = props.detailsRef?.currentRow && props.detailsRef?.currentRow.beforeImage
      && unEqVals(trim(props.detailsRef?.currentRow.beforeImage[proxy.$attrs.prop] || ""), trim(props.detailsRef?.currentRow[proxy.$attrs.prop] || ""));
  if (!showSts) {
    isShowTip.value = false;
  }
  return showSts;
}
const msg = ref("");
const trim = (val) => {
  return val ? String(val).trim() : val;
} 
const showOriginalVal = () => {
  let val = props.detailsRef?.currentRow.beforeImage[proxy.$attrs.prop];
  // if(0) false 
  if (val || val === 0) {
      proxy.$axios.get("/datamgmt/api/v1/comcode/before?label="+( proxy.$attrs.selectType ? ('type:'+proxy.$attrs.selectType) :  proxy.$attrs.prop)+"&code=" + val).then((body) => {
        if(body.success) {
          if(body.data!=""){
            msg.value = body.data;
          }else{
            if(moment.isDate(val)){
              val = moment(val).format("YYYY/MM/DD")
            }
            msg.value = val;
          }
        }else{
          msg.value = val;
        }
      });
      return msg.value;
  }
  return "";
}
const getClass = () => {
  if (isShowSign()) {
    return 'form-item-sign';
  }
  return 'form-item-unsign';
}
</script>
<style>
.form-item-sign .ep-form-item__content .ep-input__wrapper,
.form-item-sign .ep-form-item__content .ep-select__wrapper,
.form-item-sign .ep-textarea .ep-textarea__inner,
.form-item-sign .ep-textarea.is-disabled .ep-textarea__inner,
.form-item-sign .ep-form-item__content .select-class {
  background-color: lightyellow;
}

.form-item-tip {
  padding: 1px 8px;
  min-width: 20px;
  min-height: 10px;
}
</style>
