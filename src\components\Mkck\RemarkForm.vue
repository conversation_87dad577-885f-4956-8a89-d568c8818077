<template>
    <el-form :validateOnRuleChange="false" label-width="auto" label-position='left'>
        <br>
        <el-row class="mkck-row" :gutter="62" >
            <el-col :span="9">
                <el-form-item label="Record Status Desc" >
                    <el-input :value=mkckRemark?.recordStatusDesc disabled input-style="text-transform: none;">
                    </el-input>

                </el-form-item>

            </el-col>

            <el-col :span="2">
            </el-col>
            <el-col :span="9">
                <el-row >
                    <el-col :span="12">
                        <el-form-item v-if="mkckRemark.rejectCode" label="Reject Code/Desc"  >
                            <el-input v-if="mkckRemark.rejectCode" v-model="mkckRemark.rejectCode" disabled ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label=" " label-width="10px" >
                            <el-input v-if="mkckRemark.rejectCode" :model-value="getCommonDesc('REJECT_REASON', mkckRemark.rejectCode)"  disabled input-style="text-transform: none;">
                               
                            </el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-col>
        </el-row>
        <el-row class="mkck-row"  :gutter="62" >
            <el-col :span="9">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="Maker User ID/Name">
                            <el-input v-model="mkckRemark.makerUserId" disabled></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label=" " label-width="10px">
                            <el-input v-model="mkckRemark.makerUserName" disabled input-style="text-transform: none;"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-col>
            <el-col :span="2">
            </el-col>
            <el-col :span="9">
                <el-row>
                    <el-col :span="12">
                        <el-form-item v-if="mkckRemark.approveUserId" label="Approver User ID/Name">
                            <el-input v-if="mkckRemark.approveUserId" v-model="mkckRemark.approveUserId" disabled ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label=" " label-width="10px">
                            <el-input v-if="mkckRemark.approveUserId" v-model="mkckRemark.approveUserName" disabled input-style="text-transform: none;"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-col>

        </el-row>
        <el-row class="mkck-row" :gutter="62" style="margin-top: 4px!important;">
            <el-col :span="9">
                <el-form-item label="Maker Remark">
                    <el-input type="textarea" v-model="mkckRemark.makerRemark" disabled ></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="2">
            </el-col>
            <el-col :span="9">
                <el-form-item v-if="mkckRemark.approveUserId" label="Approver Remark">
                    <el-input type="textarea" v-if="mkckRemark.approveUserId" v-model="mkckRemark.approveRemark" disabled ></el-input>
                </el-form-item>
            </el-col>
        </el-row>
    </el-form>
    <div style="float: right;margin: 0px 57px 10px;">
        <el-button type="primary" v-if="mkckRemark.mkckAction === 'U' && viewLatest"
            @click="viewOriginalForm">View Original </el-button> 
        <el-button type="primary" v-if="mkckRemark.mkckAction === 'U' && !viewLatest"
            @click="viewLatestForm">View Latest</el-button>
    </div>
    <br>
  <div style="paddingBlock:5px;width: 100%;display: flex;flex-flow: wrap;border-top: 2px solid #b31a25;"></div>


</template>
<script lang="ts" setup>
import { ref, getCurrentInstance } from 'vue';
import axios from 'axios';
import { getCommonDesc} from '~/util/Function.js';
const props = defineProps(['mkckRemark', 'viewOriginalForm']);

const { proxy } = getCurrentInstance()
const viewLatest = ref(true);
function viewOriginalForm() {
    let buttons = document.querySelectorAll("div.detail-buttons button:not([close])");
    for (let i = 0; i < buttons.length; i++) {
        buttons[i].style.display="none";
    }
    viewLatest.value = false;
    props.viewOriginalForm(props.mkckRemark.pendingOid, true);
}

function viewLatestForm() {
    let buttons = document.querySelectorAll("div.detail-buttons button:not([close])");
    for (let i = 0; i < buttons.length; i++) {
        buttons[i].style.display="";
    }
    viewLatest.value = true;
    props.viewOriginalForm(props.mkckRemark.eventOid, props.mkckRemark.rejectCode||props.mkckRemark.recordStatus=="Rejected for edit"?false:true);
}
</script>
  
<style>
.mkck-row {
    margin-left: -10px !important;
}
</style>

<style scoped>
::v-deep .ep-textarea__inner {
  min-height: 100px !important;
}
</style>