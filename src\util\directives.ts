import type { App } from 'vue'

/**
 *  in order to prevent duplicate submission form
 */
const preventDuplicateClick = {
    mounted(el: any, binding: any) {
      el.addEventListener('click', () => {
        if (!el.disabled) {
          el.disabled = true;
          setTimeout(() => {
            el.disabled = false;
          }, binding.value || 1000);
        }
      });
    }
  };
   

export default {
    install(app: App) {
        app.directive('pre-dup-click', preventDuplicateClick);
    }
    
};