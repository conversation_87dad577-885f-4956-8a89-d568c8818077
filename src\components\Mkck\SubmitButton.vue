<template>
  <div >
    <el-button type="primary" @click="clickSubmitBtn" v-if="$currentInfoStore.currentPermission['Edit']&&(mkckRow.recordStatus === 'R' || mkckRow.recordStatus === 'PD' 
    ||(!mkckRow.isDoubleCheck&& mkckRow.recordStatus !== 'PA'&& mkckRow.recordStatus !== 'PA1'&& mkckRow.recordStatus !== 'PA2'))">Submit</el-button >
    <el-dialog v-model="showSubmitDialog" title="Submit" :close-on-click-modal="false" width="600" class="mkck-dialog" append-to-body :show-close="false">
      <template #header>
        <div class="mkckTitle">
          <span class="title-name">Submit</span>
        </div>
      </template>
      <br>
      <el-col style="padding-right: 12px; padding-left: 12px;">
        <el-form-item label="Remark">
          <el-input type="textarea" v-model="remark"  ></el-input>
        </el-form-item>
      </el-col>  
      <br>
      <div class="button-group">
        <el-button @click="handleCancel" class="ep-button-custom">Cancel</el-button>
        <el-button type="primary" @click="handleSubmit" class="ep-button-custom" v-pre-dup-click>OK</el-button>
      </div>
      <br>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { ref ,getCurrentInstance} from 'vue';
import axios from 'axios';

import ElementPlus, { ElMessage, ElMessageBox ,ElLoading} from 'element-plus';
import type { Action } from 'element-plus'
const { proxy } = getCurrentInstance();
const props = defineProps(['mkckRow', 'eventOid', 'mkckAction','funcId', 'handleCancel','handleSaveAndSubmit','isSave','beforeSubmit']);
const showSubmitDialog = ref(false);
const remark = ref('');
const clickSubmitBtn = async () => {
  if(props.beforeSubmit){
    let ret = await props.beforeSubmit();
    if(ret === false){
      return;
    }
  }
  showDialog();
}
const showDialog = () => {
  ElMessageBox.confirm(
    'Confirm to submit?',
    'Warning',
    {
      confirmButtonText: 'OK',
      cancelButtonText: 'Cancel',
      type: 'warning',
    }
  )
 .then(() => {
      showSubmitDialog.value = true;
      remark.value = '';
    })
    .catch(() => {
      
        })
};
const handleCancel = () => {
  showSubmitDialog.value = false;
}


const handleSubmit = async () => {

 if(props?.mkckAction==='D'||(await props.handleSaveAndSubmit() && props.eventOid)){
  const loading = ElLoading.service({
    lock: true,
    text: 'Loading',
    background: 'rgba(0, 0, 0, 0)',
  })
    const response = await axios.post('/datamgmt/api/v1/makerchecker/initial', {
        flowMakerCheckerOid: props.mkckRow.mkckOid,
        remark: remark.value,
        status: "A",
        eventOid: props.eventOid,
        eventType: 'SD',
        funcId: props.funcId
      }).then((body) => {
        loading.close()
          if(body.success){
              ElMessageBox.alert("Submitted successfully.", 'Success', {
              confirmButtonText: 'OK',
              type: 'success',
              callback: (action: Action) => {
                props.handleCancel && props.handleCancel();
              }
           });
          }
        });
      proxy.$axios.post('/datamgmt/api/v1/handler/unlock', {
            flag: true,
        });
     
  }
};


</script>


<style scoped>
.dialog-divider {
  border-top: 2px solid lightgrey;
  /* 添加横线样式 */
  margin-top: 10px;
  /* 设置横线上边距 */
  margin-bottom: 10px;
  /* 设置横线下边距 */
}

.button-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}



.button-group {
  display: flex;
  justify-content: center;
  align-items: center;
}

::v-deep .ep-textarea__inner {
  min-height: 150px !important;
}
</style>