<template>
  <BasePanel :searchParams="searchParams" url="/datamgmt/api/v1/exboard/list" :params="{ modeEdit: 'Y' }"
    :showDetails="showDetails" :deleteRow="deleteRow" :editRow="editRow" ref="tableRef" hideOperation="true">
    <template v-slot:searchPanel="slotProps">
      <FormRow>
        <ElFormItemProxy :label="$t('common.title.opCtryRegionCode')" prop="opCtryRegionCode">
          
          <CtryRegionSearchInput v-model="slotProps.form.opCtryRegionCode" 
            showDesc="false" 
            style="width: 120px" 
            opCtryRegion />

        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy :label="$t('csscl.exchang.exchangeCode')" prop="exchangeCode">
            <GeneralSearchInput v-model="slotProps.form.exchangeCode"
                    showDesc="false" 
                    searchType="exchangeCode" />
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy :label="$t('csscl.exchang.exchangeShortName')" prop="exchangeShortName">
          <el-input style="width: 400px" maxlength="50" v-model="slotProps.form.exchangeShortName" class="text-none" />
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy :label="$t('csscl.exchang.exchangeBoardCode')" prop="exBoardCode">
             <GeneralSearchInput v-model="slotProps.form.exBoardCode"
                    showDesc="false" 
                    searchType="exboardCode" />
        </ElFormItemProxy>
       
      </FormRow>
      <FormRow>
        <ElFormItemProxy :label="$t('csscl.exchang.exchangeBoardShortName')" prop="exBoardShortName">
          <el-input style="width: 400px"  maxlength="50" v-model="slotProps.form.exBoardShortName" class="text-none" />
        </ElFormItemProxy>
        
      </FormRow>
    </template>
    <template v-slot:tableColumn>
      <el-table-column header-align="center" sortable="custom" prop="opCtryRegionCode" :label="$t('common.title.opCtryRegionCode')" width="260" />
      <el-table-column header-align="center" sortable="custom" prop="exchangeCode" :label="$t('csscl.exchang.exchangeCode')" width="180" />
      <el-table-column header-align="center" sortable="custom" prop="exchangeShortName" :label="$t('csscl.exchang.exchangeShortName')" width="280" />
      <el-table-column header-align="center" sortable="custom" prop="exBoardShortName" :label="$t('csscl.exchang.exchangeBoardShortName')"  width="320"/>
      <el-table-column header-align="center" sortable="custom" prop="exBoardCode" :label="$t('csscl.exchang.exchangeBoardCode')" width="300" />
      <el-table-column header-align="center" sortable="custom" prop="placeOfSettlement" :label="$t('csscl.exchang.cashStockSettleMethod')" width="240" />
      <el-table-column header-align="center" sortable="custom" prop="status" :label="$t('common.title.status')" width="150" >
        <template #default="scope">
          {{ getCommonDesc('STATUS', scope.row.status) }}
        </template>
      </el-table-column>
      <el-table-column header-align="center" sortable="custom" prop="recordStatus" :label="$t('common.title.recordStatus')">
        <template #default="scope">
          {{ getCommonDesc('RECORD_STATUS', scope.row.recordStatus) }}
            <span v-if="scope.row.recordStatus&&scope.row.recordStatus!=='A' ">
              for  {{ getCommonDesc('MKCK_ACTION', scope.row.mkckAction) }}
            </span>
        </template>
      </el-table-column>
    </template>
  </BasePanel>
  <Details ref="detailsRef" :reload="reload"/>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance, watch } from 'vue';
import {
  Search
} from '@element-plus/icons-vue'
import BasePanel from '~/pages/base/index.vue'
import Details from './Details.vue'
import  { getCommonDesc } from '~/util/Function.js';
import CtryRegionSearchInput from '~/pages/base/CtryRegionSearchInput.vue';

const tableRef = ref();
const { proxy } = getCurrentInstance()
const searchParams = ref({
  //顺序和上面绑定参数一致
  opCtryRegionCode:"",
  exchangeCode:"",
  exchangeShortName:"",
  exBoardCode:"",
  exBoardShortName:""
});

const detailsRef = ref();
const showDetails = (row, disabled) => {
  detailsRef.value.showDetails(row, disabled);
}
const deleteRow = (row) => {
  console.log("Delete ...");
}
const editRow = (row) => {
  detailsRef.value.editRow(row);
}
const reload = () => {
  tableRef.value.load();
}
//-------------------------------

</script>

<style></style>