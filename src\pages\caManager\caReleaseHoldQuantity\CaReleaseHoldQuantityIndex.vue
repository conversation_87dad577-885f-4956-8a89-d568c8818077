<template>
  <BasePanel :searchParams="searchParams" :paramListData="paramListData" url="/bff/ca/api/v1/ca-event/get-ca-event-page-list"
    selectFirstRecord="true" :clickRow="handleClick"
    ref="tableRef" :hideOperation="true" :isHideAdd="true" :params="{ modeEdit: 'Y' }" :beforeSearch= "beforeSearch">
    <template v-slot:searchPanel="slotProps">
      <FormRow>
        <ElFormItemProxy label-width="250" :label="$t('csscl.ca.common.eventRefNo')" prop="caEventReferenceNumber">
          <GeneralSearchInput v-model="slotProps.form.caEventReferenceNumber" style="width:280px" :title="$t('csscl.ca.common.eventRefNo')" searchType="caEventReferenceNumber" showDesc="false"/>
        </ElFormItemProxy>
        <ElFormItemProxy label-width="150" :label="$t('csscl.ca.common.eventCategoryCode')" prop="caEventCategory">
          <SearchInput style="width: 150px" v-model="slotProps.form.caEventCategory"
            url="/datamgmt/api/v1/searchinput" showDesc="false"
            :title="$t('csscl.ca.common.eventCategory')"
            :params="{searchType: 'caEventCategoryCode'}"
            :columns="[
              {
                  title: $t('csscl.ca.common.eventCategoryCode'),
                  colName: 'code',
              },
              {
                  title: $t('csscl.ca.common.eventCategoryDescription'),
                  colName: 'codeDesc',
              }
            ]"
            :pageSizes="[10, 20, 30]">
          </SearchInput>
        </ElFormItemProxy>
        <ElFormItemProxy label-width="130" :label="$t('csscl.ca.common.swiftEventCode')" prop="swiftEventCode">
          <GeneralSearchInput v-model="slotProps.form.swiftEventCode" inputStyle="width:100px" style="width:350px" searchType="swiftEventCode" showDesc="true"/>
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy label-width="250" :label="$t('csscl.ca.common.recordDate')" prop="recordDate" >
          <DateItem v-model="slotProps.form.recordDate"/>
        </ElFormItemProxy>
        <ElFormItemProxy label-width="150" :label="$t('csscl.ca.common.securityId')" prop="instrumentCode">
          <GeneralSearchInput v-model="slotProps.form.instrumentCode" inputStyle="width:150px" style="width:350px" searchType="instrumentCode" showDesc="true"/>
        </ElFormItemProxy>
        <ElFormItemProxy></ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy label-width="250" :label="$t('csscl.ca.common.subCustodianClearingAgentCode')" prop="clearingAgentCode">
          <GeneralSearchInput v-model="slotProps.form.clearingAgentCode" style="width:270px" searchType="clearingAgentCode" showDesc="true"/>
        </ElFormItemProxy>
        <ElFormItemProxy label-width="150" :label="$t('csscl.ca.common.custodyAccountNo')" prop="tradingAccountCode">
          <GeneralSearchInput v-model="slotProps.form.tradingAccountCode" style="width:280px" searchType="custodyAcct" showDesc="false"/>
        </ElFormItemProxy>
        <ElFormItemProxy></ElFormItemProxy>
      </FormRow>
    </template>  
    <template v-slot:tableHeaderTitle>
      <div style="margin: 0px; padding: 3px; border-bottom: 2px solid #FFAB2D;">
        <el-text style="color: #FFAB2D; font: 14px bold;">
          {{ $t('csscl.ca.common.caEvent') }}
        </el-text>
      </div>
    </template>
    <template v-slot:tableColumn>
      <el-table-column sortable="custom" prop="caEventReferenceNumber" :label="$t('csscl.ca.common.eventRefNo')" align="center"/>
      <el-table-column sortable="custom" prop="caEventCategory" :label="$t('csscl.ca.common.eventCategoryCode')" align="center"/>
      <el-table-column sortable="custom" prop="caEventCategoryDesc" :label="$t('csscl.ca.common.eventCategory')" align="center">
         <template #default="scope">
          {{ getCommonDesc('CA_EVENT_CATEGORY_CODE', scope.row.caEventCategory) }}
        </template>
      </el-table-column>
      <el-table-column sortable="custom" prop="swiftInstructionType" :label="$t('csscl.ca.common.camv')" align="center"/>
      <el-table-column sortable="custom" prop="swiftEventCode" :label="$t('csscl.ca.common.swiftEventType')" align="center"/>
      <el-table-column sortable="custom" prop="announceDescription" :label="$t('csscl.ca.common.description')" align="center"/>
      <el-table-column sortable="custom" prop="clearingAgentCode" :label="$t('csscl.ca.common.subCustodianClearingAgentCode')" align="center"/>
      <el-table-column sortable="custom" prop="instrumentCode" :label="$t('csscl.ca.common.securityId')" align="center"/>
      <el-table-column sortable="custom" prop="instrumentShortName" :label="$t('csscl.ca.common.securityName')" align="center"/>
      <el-table-column sortable="custom" prop="recordDate" :label="$t('csscl.ca.common.recordDate')" align="center"/>
      <el-table-column sortable="custom" prop="eventValueDate" :label="$t('csscl.ca.common.valueDate')" align="center"/>
    </template>

    <template v-slot:contentBottom>
      <br />
      <br />
      <!-- CA Hold Quantity ------------------------------>
      <div style="margin: 0px; padding: 3px; border-bottom: 2px solid #FFAB2D;">
        <el-text style="color: #FFAB2D; font: 14px bold;">
          {{ $t('csscl.ca.common.caHoldQuantity') }}
        </el-text>
      </div>
      <CaHoldQuantityTable ref="holdQuantityTableRef" />
    </template>
  </BasePanel>

</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance, watch } from 'vue';
import { getCommonDesc } from '~/util/Function.js';
import BasePanel from '~/pages/base/CaIndex.vue'
import CaHoldQuantityTable from "~/pages/caManager/caReleaseHoldQuantity/CaReleaseHoldQuantityTable.vue";

const paramListData = {};
const searchParams = {
  caEventReferenceNumber: "",
  recordDate:"",
  clearingAgentCode: "",
  caEventCategory: "",
  instrumentCode: "",
  custodianAccountNumber: "",
  swiftEventCode:"",
  caEventStatus:""
};
const tableRef = ref();
const holdQuantityTableRef = ref();
const handleClick = async (row) => { 
// 清空 subPage 数据
  clearSubPage();
  // 渲染 subPage
  await holdQuantityTableRef.value.showDetails(row);
}
const beforeSearch= () => {
  // 清空 subPage 数据
  clearSubPage();
}
const clearSubPage = async() => {
  holdQuantityTableRef.value.clearData?.();
}
</script>

<style></style>