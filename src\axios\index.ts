import axios from 'axios';
import axiosRetry from 'axios-retry';
import { useCookies } from "vue3-cookies";
import { ElMessage , ElMessageBox} from 'element-plus';
import moment from 'moment';
import { currentInfo } from '~/store/modules/currentInfo';
import { getExplorer, clearCookies } from "~/util/Function";
import oidcClient from '~/util/oidcClient'
const {
    refreshTokenHttp,
} = oidcClient

const { cookies } = useCookies();
let isPopNum = 0;

//axios.defaults.baseURL = window.endpoints.BASE_URL;
//axios.defaults.baseURL = window.endpoints.BASE_URL + window.endpoints.API_URL;
axios.defaults.baseURL = import.meta.env.VITE_API_BASE_URL;

// 添加请求拦截器
axios.interceptors.request.use(function (config) {
	cookies.remove("session");
    const currentInfoStore = currentInfo();
    let funcId = currentInfoStore.getCurrentFuncId();
    config.headers["channel"] = getExplorer();
    config.headers["x-request-id"] = currentInfoStore.getCurrentUUID;
    config.headers["function-id"] = funcId;
    config.headers["request-time"] = moment().format('YYYYMMDD HH:mm:ss');
    config.headers["Accept-Language"] = "en_US";
    // Start SK-COMMON-0059, Tom.Li, 2024/08/14
    config.headers["X-api-decode"] = localStorage.getItem('apiDecode');
    // End SK-COMMON-0059, Tom.Li, 2024/08/14
    if (cookies.get("x-esession")||cookies.get("id_token")) {
        config.headers["X-Esession"] = cookies.get("x-esession");
        config.headers["id-token"] = cookies.get("id_token");
        if(cookies.get("access_token")) {
            config.headers.Authorization = "Bearer " + cookies.get("access_token");
        }
        return config;
    } else {
        if (config.url == "/auth/api/v1/user/login" || config.url == "/auth/api/v1/user/login/b4check") {
            return config;
        }
        return Promise.reject(new Error(""))
    }
}, function (error) {

    return Promise.reject(error);
});



// 添加响应拦截器
// Start SK-COMMON-0059, Tom.Li, 2024/08/14
// axios.interceptors.response.use(function (response) {
axios.interceptors.response.use(async function (response) {
// End SK-COMMON-0059, Tom.Li, 2024/08/14
    if (!response.data.success) {
        if (response.data.alertMessage) {
        // Start SK-COMMON-0059, Tom.Li, 2024/08/14
        // ElMessageBox.alert(response.data.alertMessage[0], 'Error', {
        //     confirmButtonText: 'OK',
        //     type: 'error',
        // })
            await popWarning(async ()=>{
                // Start SK-COMMON-0067, Tom.Li, 2024/08/15
                // return await ElMessageBox.alert(response.data.alertMessage[0], 'Error', {
                let msg = response.data.alertMessage[0];
                if(msg =='message.system.error') {
                    msg = 'Sorry that your request was not successfully processed. Please try again.';
                }
                // Start R2411A-56930, Tom.Li, 2024/09/09
                if(msg =='message.system.reload.again') {
                    msg = 'Network Transmission is not good at this moment. Please reload the page again. If you still encounter same problem after reload the page, please contact system administrator for assistance.';
                }
                // End R2411A-56930, Tom.Li, 2024/09/09
                return await ElMessageBox.alert(msg, 'Error', {
                // End SK-COMMON-0067, Tom.Li, 2024/08/15
                    confirmButtonText: 'OK',
                    type: 'error',
                });
            // Start SK-COMMON-0105, Tom.Li, 2024/08/24
            }, async ()=>{
                if (response.data.messageId == 'message.system.maintenance' 
                    // Start R2411A-56930, Tom.Li, 2024/09/09
                    || response.data.messageId == 'message.system.reload.again'
                    // End R2411A-56930, Tom.Li, 2024/09/09
                ) {
                    clearCookies(true);
                }
            });
            // End SK-COMMON-0105, Tom.Li, 2024/08/24
        // End SK-COMMON-0059, Tom.Li, 2024/08/14
        }
    }
    if (response.headers["x-esession"]) {
        cookies.set("x-esession", response.headers["x-esession"].split(";")[0]);
    }
    if (response.config.responseType === 'blob') {
        return response;
    }
    return response.data;
}, async function (error) {
    if (error.response) {
        // Start R2411A-35745, Tom.Li, 2024/08/20
        if(error.response.request.responseType == 'blob'){
            try {
                error.response.data = JSON.parse(await error.response.data.text());
            } catch (error) {
                
            }
        }
        // End R2411A-35745, Tom.Li, 2024/08/20
        if(error.response.data.messageId == 'message.system.kick.out'|| error.response.data.messageId == "message.auth.session.timeout"){
            //相同用户重新登录提示
            let errorMsg = error.response.data.alertMessage[0];
            if(error.response.data.messageId == "message.auth.session.timeout" && (errorMsg=='' || errorMsg==null || errorMsg=='message.auth.session.timeout')){
                errorMsg = 'The session has timed out.'
            }
            await popWarning(async ()=>{
            // Start SK-COMMON-0059, Tom.Li, 2024/08/14
            //  return ElMessageBox.alert(errorMsg, 'Error', {
                return await ElMessageBox.alert(errorMsg, 'Error', {
            // End SK-COMMON-0059, Tom.Li, 2024/08/14
                    confirmButtonText: 'OK',
                    type: 'warning',
                    });
            }, async ()=>{
                clearCookies(true);
            });
        } else if(error?.response?.status == 401) {
            return Promise.reject(error);
        // Start SK-COMMON-0059, Tom.Li, 2024/09/03
        } else if(error?.response?.status == 403) {
            let errorMsg = error.response.data.alertMessage[0];
            await popWarning(async ()=>{
                return await ElMessageBox.alert(errorMsg, 'Error', {
                        confirmButtonText: 'OK',
                        type: 'warning',
                        });
                }, async ()=>{
                    clearCookies(true);
                });
        } else if(error?.response?.data?.respHeader?.respStatus == '01') {
        // End SK-COMMON-0059, Tom.Li, 2024/09/03
            return error.response.data;
        } else if(error.response.data.messageId != 'message.system.kick.out'&&error.response.data.messageId != "message.auth.session.timeout"){
            if(error.response.data.messageId){
                let errorMsg = error.response.data.alertMessage[0];
                if(error.response.data.messageId == "message.auth.session.timeout" && (error.response.data.alertMessage=='' || error.response.data.alertMessage==null)){
                    errorMsg = 'The session has timed out.'
                }
                ElMessage({
                    message: errorMsg,
                    type: 'error',
                    plain: true,
                });
            }
  
        }
    } else {
        clearCookies(true);
    }
});

const popWarning = async (operator: Function, thenFunc: Function) => {
    if(isPopNum==0) {
        ++isPopNum;
        await operator().then(async ()=>{
            await thenFunc();
            isPopNum = 0;
        }).catch(async ()=>{
            await thenFunc();
            isPopNum = 0;
        });
    }
}
axiosRetry(axios, {
    retries: 1,
    retryDelay: () => {
        return 50;
    },
    shouldResetTimeout: true,
    retryCondition: async (error) => {
        if (error?.response?.status === 401) {
            await refreshToken();
            return true;
        } else {
            return false;
        }
    }
});
const refreshToken = async () => {
    //cookies.get("access_token")
    try {
        const userData = await refreshTokenHttp({
            failCb: (err: any) => {
                ElMessage({
                    message: err?.message || 'Bad Request',
                    type: 'error'
                })
            }
        });
        if (userData) {
            cookies.set("access_token",userData.access_token);
            //cookies.set("refresh_token",userData.refresh_token);
        }
    } catch (error) {
        await popWarning(async ()=>{
            return ElMessageBox.alert("Token Time Out/Refresh Token Fail, please login again.", 'Error', {
                confirmButtonText: 'OK',
                type: 'warning',
                });
        }, async ()=>{
            clearCookies(true);
        });
    }
}
export default axios