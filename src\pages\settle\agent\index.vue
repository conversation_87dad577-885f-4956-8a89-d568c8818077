<template>
  <BasePanel :searchParams="searchParams" :paramListData="paramListData" url="/datamgmt/api/v1/clearingagent/list" :params="{ modeEdit: 'Y' }"
    :showDetails="showDetails" :deleteRow="deleteRow" :editRow="editRow" ref="tableRef">
    <template v-slot:searchPanel="slotProps">
      <FormRow>
        <ElFormItemProxy label-width="380" :label="$t('csscl.agent.clearingAgentCode')" prop="clearingAgentCode">
            <GeneralSearchInput  v-model="slotProps.form.clearingAgentCode"
                    showDesc="false"
                    searchType="clearingAgentCode" />
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.agent.opCtryRegionCode')" prop="opCtryRegionCode">
          <CtryRegionSearchInput v-model="slotProps.form.opCtryRegionCode" 
            showDesc="false" 
            style="width: 120px"
            opCtryRegion />
        </ElFormItemProxy>
       
      </FormRow>
     <FormRow>
      <ElFormItemProxy label-width="380" :label="$t('csscl.agent.shortName')" prop="agentShortName">
          <el-input maxlength="50" v-model="slotProps.form.agentShortName"  style="width:550px" input-style="text-transform:none" />
        </ElFormItemProxy>
     </FormRow>

    </template>
    <template v-slot:tableColumn>
      <el-table-column header-align="center"  sortable="custom" prop="opCtryRegionCode" :label="$t('csscl.agent.opCtryRegionCode')" width="280"/>
      <el-table-column header-align="center" sortable="custom" prop="clearingAgentCode" :label="$t('csscl.agent.clearingAgentCode')" width="380"/>
      <el-table-column header-align="center" sortable="custom" prop="agentShortName" :label="$t('csscl.agent.shortName')"  width="650"/>
      <el-table-column header-align="center" sortable="custom" prop="status" :label="$t('common.title.status')"  >
        <template #default="scope">
          {{ getCommonDesc('STATUS', scope.row.status) }}
        </template>
      </el-table-column>
      <el-table-column header-align="center" sortable="custom" prop="recordStsMkckAction" :label="$t('common.title.recordStatus')" >
        <template #default="scope">
          <!-- scope.row -->
          {{ getRecordStatusDesc(scope.row) }}
        </template>
      </el-table-column>
    </template>
  </BasePanel>
  <Details ref="detailsRef" :reload="reload"/>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance, watch } from 'vue';
import {
  Search
} from '@element-plus/icons-vue'
import BasePanel from '~/pages/base/index.vue'
import Details from './Details.vue'
import  { getCommonDesc, getRecordStatusDesc } from '~/util/Function.js';
import CtryRegionSearchInput from '~/pages/base/CtryRegionSearchInput.vue';
import { commonRules, showValidateMsg } from '~/util/Validators.js';
import type { FormInstance, FormRules } from 'element-plus';

const tableRef = ref();
const paramListData = {};
const reload = () => {
  tableRef.value.load();
}
const { proxy } = getCurrentInstance()
const searchParams = ref({
  //顺序和上面绑定参数一致
  clearingAgentCode:"",
  opCtryRegionCode:"",
  agentShortName:"",
});

const detailsRef = ref();
const showDetails = (row,disabled) => {
  detailsRef.value.showDetails(row,disabled);
}
const deleteRow = (row) => {
  console.log("Delete ...");
}
const editRow = (row) => {
  detailsRef.value.editRow(row);
}

interface AgentForm {
    opCtryRegionCode: string
    clearingAgentCode:string
    agentShortName: string
}
const rules = reactive<FormRules<AgentForm>>({
    opCtryRegionCode: [
        commonRules.required,
        commonRules.length(1, 3),
    ],
    clearingAgentCode: [
        commonRules.required,
        commonRules.length(1, 10),
        commonRules.name,
    ],
    agentShortName: [
        commonRules.required,
        commonRules.length(1, 50),
    ]
})
//-------------------------------

</script>

<style></style>