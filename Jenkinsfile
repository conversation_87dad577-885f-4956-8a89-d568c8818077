def remote = [:]
remote.name = '************'
remote.host = '************'
remote.user = 'root'
remote.password = 'ebankP@ssw0rd'
remote.allowAnyHosts = true

def kubernetes = [:]
kubernetes.name = '*************'
kubernetes.host = '*************'
kubernetes.user = 'root'
kubernetes.password = 'ebankP@ssw0rd'
kubernetes.allowAnyHosts = true


def latestTag = null

pipeline {
    agent any

    environment {
        APP_NAME = 'csscl-web'
    }  
    stages {
        
        stage('release version') {
			steps {
				script {
                    print "Push event\n"
       			    latestTag = sh(returnStdout:  true, script: "cd /ccpboc/build/ccppo && git tag --sort version:refname | tail -1").trim()
       			    echo "latestTag ${latestTag}"
                    env.TAG = sh(returnStdout: true, script: "cd  ${WORKSPACE} && git rev-parse HEAD").trim()
                    echo "commitHash ${TAG}"

				} 
			}
        }
        stage('npm install') {
            steps {                
                sshCommand remote: remote, command: "export JAVA_HOME=/usr/lib/jvm/jdk-17-oracle-x64 && cd  ${WORKSPACE} && npm install"
                
                
            }
        }
        stage('npm run build') {
            steps {                
                sshCommand remote: remote, command: "export JAVA_HOME=/usr/lib/jvm/jdk-17-oracle-x64 && cd  ${WORKSPACE} && npm run build"
                
                
            }
        }
        stage('Docker Build') {
            steps {
                sshCommand remote: remote, command: "export JAVA_HOME=/usr/lib/jvm/jdk-17-oracle-x64 && cd  ${WORKSPACE} && docker build -t **************:5001/csscl-web:${TAG} --rm=true . "
                
            }
        }       
        stage('Docker Push') {
            steps {
                sshCommand remote: remote, command: "export JAVA_HOME=/usr/lib/jvm/jdk-17-oracle-x64 && cd  ${WORKSPACE} && docker push **************:5001/csscl-web:${TAG}"
                
            }
        }

        stage('K8 Apply ConfigMap') {
            steps {
                sshCommand remote : kubernetes, command: "cd /root/bochk-ccp/openshift/${APP_NAME}/dev/iac && kubectl apply -f configmap.yaml"
            }
        }
        stage('K8 Apply Service') {
            steps {
                script {
 
                    try {
                       sshCommand remote : kubernetes, command: "cd /root/bochk-ccp/openshift/${APP_NAME}/dev/iac  && kubectl apply -f service.yaml"
                    } catch (err) {
                        echo err.getMessage()
                        sh 'exit 1'   // failure
                    }  
                }                 
            }
        }
        stage('K8 Apply Deployment') {
            steps {
                sshCommand remote : kubernetes, command: "cd /root/bochk-ccp/openshift/${APP_NAME}/dev/deployment && sed 's/TAG/${TAG}/' deployment-v1.yaml | kubectl apply -f -"
            }
        }

    }
}
