<template>
  <div>
      <div class="multipleSearchInput" style="display: table;width:200px;" :style="style">
          <div style="display: table-cell; width:100%;" >
            <el-select v-model="multiVal"
                       remote
                       multiple
                       readonly
                       collapse-tags
                       collapse-tags-tooltip
                       placeholder=" "
                       popper-class="hideClass"
                       @change="change" >
              <el-option v-for="item in selectOpts" :key="item[firstColName]"
                         :label="item[secondColName||firstColName]"
                         :value="item[firstColName]" />
            </el-select>
          </div>
          <div style="display: table-cell; width: 20px; padding: 0 5px; vertical-align: middle">
            <el-icon-search style="width: 20px;" @click="handleSearch" class="search-icon" :searchType="searchType" />
          </div>
      </div>

    <el-dialog v-model="searcDialogVisible" :modal="false" :close-on-click-modal="false"
      class="search-input" :close-on-press-escape="false" :destroy-on-close="true" draggable :show-close="false"
      modal-class="searchInput-dialog" @close="hideSearchDialog" append-to-body>

      <template #header>
        <div style="margin:0;padding: 0;background-color: var(--ep-color-primary);height: 32px; width:100%; ">
          <el-icon-close  @click="hideSearchDialog()" style="height: 26px; width:26px; background-color: var(--ep-color-primary); border:none; color:#ffffff;float:right;margin: 3px 3px 0 0; cursor: pointer;" />
          <div style="margin: 0;padding: 0;" >
            <div style="height:6px; width:100%;border: none;"></div>
            <span style="color:#fff; font-size: 18px; height: 32px; padding: 10px; width:100% "> {{ props.title  }} </span>
          </div>
        </div>
      </template>

      <!-- <template #append> -->
      <div class="searchInput-dialog-content">
          <div class="searchInput-dialog-form-inline" >
          <FormRow>
            <ElFormItemProxy label-width="350" style="text-align: left;" 
            v-for="(item,idx) in columns" 
            :label="item.title" >
            <el-input clearable v-model="formInline[item.colName]" 
              :disabled="disabled"
              :maxlength="idx == 0 ? maxlength: 999"
              @input="dialogInput()"
              style="width:280px; margin-top: 5px;" 
              :input-id="item.colName" aria-autocomplete="none"/>
          </ElFormItemProxy>
        </FormRow>
        <slot name="searchPanel" :form="formInline"></slot>
      </div>
        <div class="searchInput-dialog-result">
          <el-form>
          <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="pageSizes"
            :layout="pagePaginationLayout" v-model:total="total" @current-change="handleChange" @size-change="handleChange"
            style="background-color: lightgrey;padding-inline: 10px;" >
            Total {{ total }} records
          </el-pagination>
          </el-form>
          <el-table border :data="tableData" table-layout="auto" :highlight-current-row="true"
            ref="resultTableRef" @selection-change="handleSelectionChange"
            @row-dblclick="dbClick" >
            <el-table-column type="selection" width="30" :selectable="(row,idx)=>{ return !parseBool(props.disabled) }"/>
            <el-table-column v-for="(item,idx) in columns"
              :prop="item.colName" 
              :label="item.title" 
              :width="item.width?item.width: idx == 0 ? '300' : '*'" />
            <slot name="tableColumn"></slot>
          </el-table>
          <el-form>
          <el-pagination v-if="!parseBool(props.hideBottomPageination)" v-model:current-page="currentPage"
            v-model:page-size="pageSize" :page-sizes="pageSizes" :layout="pagePaginationLayout" v-model:total="total"
            @size-change="handleChange" @selection-change="handleSelectionChange"
            @current-change="handleChange" style="background-color: lightgrey;padding-inline: 10px;" small>
            Total {{ total }} records
          </el-pagination>
          </el-form>
        </div>
      </div>
      <!-- </template> -->
      <template #footer>
        <div class="dialog-footer" style="text-align: center">
          <span @click="hideSearchDialog" class="ep-button ep-button-custom">Cancel</span>
          <el-button type="primary" @click="handleOK" class="ep-button-custom">OK</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, reactive, getCurrentInstance, computed,  } from 'vue';
import {getCommonDesc, parseBool} from '~/util/Function.js';

const emit = defineEmits(['update:modelValue', 'update:desc']);
const { proxy } = getCurrentInstance()

const props = defineProps({
  url: '',
  title: '',
  readonly: {
    default: false,
  },
  alt: {
    default: undefined,
  },
  desc: {
    type: String,
    default : null,
  },
  value2Array: {
    default: false,
  },
  value: {
    default: undefined,
  },
  setTitle: {
    default: undefined,
  },
  style: {
    type: Object,
    default: () => ({})
  },
  disabled: {
    default: false,
  },
  params: {
    type: Object,
    default: () => ({ status: 'A', recordStatus: 'A' })
  },
  searchParams: {
    type: Array,
    default: () => [],
  },
  hideBottomPageination: {
    default: true,
  },
  maxlength: {
    default: 9999,
  },
  searchType: {
    default: '',
  },
  valJoin: {
    default: ",",
  },
  columns: {
    type: Array,
    default: () => [
    ],
  },
  modelValue: {
    default: [],
  },
  pageSizes: {
    type: Array,
    default() {
      return [10, 20, 30, 40]
    }
  },
  pagePaginationLayout: {
    type: String,
    default: 'sizes, , jumper, prev, pager, next, ->, slot'
  },
});

const searcDialogVisible = ref(false);
const multiVal = ref([]);
const desc = ref("");
const _multiVal = ref([]);
const curPageSelected = ref([]);
const autoSelected = ref(false);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);
const param = ref({});
const currentRow = ref();
const firstColName = props.columns[0].colName;
const secondColName = props.columns[1]?.colName || firstColName;
const formInline = reactive({});
const resultTableRef = ref();
const selectOpts = ref([]);

watch(multiVal, (newValue) => {
  if (parseBool(props.value2Array)) {
    emit('update:modelValue', newValue);
  } else {
    emit('update:modelValue', newValue.join(props.valJoin));
  }
});

watch(() => props.modelValue, () => {
  if (parseBool(props.value2Array)) {
    multiVal.value = props.modelValue || [];
  } else {
    multiVal.value = props.modelValue && props.modelValue != '' ? props.modelValue.split(props.valJoin) : [];
    // if (props.modelValue) {
    //   multiVal.value = (props.modelValue?.split(props.valJoin)) || [];
    // }
  }
});

watch(desc, (newValue) => {
  emit('update:desc', newValue);
});

watch(() => props.desc, () => {
  desc.value = props.desc;
});

const handleSelectionChange = (rows) => {
  if (autoSelected.value) {
    return;
  }
  for (let i = 0; i < rows.length; i++) {
    let row = rows[i];
    if (!_multiVal.value.includes(row[firstColName])) {
      _multiVal.value.push(row[firstColName]);
    }
    if (curPageSelected.value.includes(row[firstColName])) {
      curPageSelected.value = curPageSelected.value.filter(a => a !== row[firstColName])
    }
    for (let j = 0; j < selectOpts.value?.length; j++) {
      let val = selectOpts.value[j];
      if (val[firstColName] == row[firstColName]) {
        row = null;
        break;
      }
    }
    if (row) {
      selectOpts.value.push(row);
    }
  }
  curPageSelected.value.forEach(val => {
    _multiVal.value = _multiVal.value.filter(v => v !== val);
  });
}

const change = (val) => {
  if (val) {
    let d = "";
    let opts = selectOpts.value;
    for (let i = 0; i < val.length; i++) {
      if(!val[i]) {
        continue;
      }
      for (let j = 0; j < opts.length; j++) {
        let opt = opts[j];
        if (val[i] == opt[firstColName]) {
          d += ((d&&",") + opt[secondColName]);
        }
      }
    }
    desc.value = d;
  } else {
    desc.value = null;
  }
}

// close Search Dialog
const hideSearchDialog = () => {
  delete formInline[secondColName];
  delete formInline[firstColName];
  searcDialogVisible.value = false;
  _multiVal.value = [];
  curPageSelected.value = [];
}

// show Search Dialog
const openSearchDialog = () => {
  searcDialogVisible.value = true;
  currentPage.value = 1;
  _multiVal.value = [].concat(multiVal.value)
}
// bind table data

const loadData = async (afterSearch = undefined) => {
  if (props.url == "COMMONLIST") {
    console.log(props.searchType, props.params, param.value)
    let allCommCode = proxy.$commonCodeStore.getAll[props.searchType];
    let firstVal = upper(param.value[firstColName]);
    let sendVal = upper(param.value[secondColName]);
    let commCodes = [];
    if (firstVal != "" || sendVal != "") {
      allCommCode.forEach((e, idx) => {
        let firstFlag = firstVal == "" ? true: false;
        let sendFlag = sendVal == "" ? true: false;
        if (firstVal.indexOf("%") > -1 && RegExp("^"+(firstVal.replaceAll('%', '.*')) + "$").test(upper(e[firstColName]))) {
          firstFlag = true;
        } else if (firstVal == upper(e[firstColName])) {
          firstFlag = true;
        }
        if (sendVal.indexOf("%") > -1 &&  RegExp("^"+(sendVal.replaceAll('%', '.*')) + "$").test(upper(e[secondColName]))) {
          sendFlag = true;
        } else if (sendVal == upper(e[secondColName])) {
          sendFlag = true;
        }
        if (firstFlag && sendFlag) {
          commCodes.push(e);
        }
      });
    } else {
      commCodes = allCommCode;
    }
    total.value = commCodes.length;
    currentPage.value = currentPage.value;
    let start = (Number(currentPage.value) - 1) * pageSize.value;
    let end = Number(currentPage.value) * pageSize.value;
    let data = commCodes.slice(start, end);
    tableData.value = data;
    if(afterSearch) {
      afterSearch(props.params, data);
    }
  } else {
    console.log(props.searchType, props.params, param.value)
    const msg = await proxy.$axios.post(props.url, {
      param: { ...props.params, ...param.value, },
      current: currentPage.value,
      pageSize: pageSize.value,
    });
    if (msg?.success) {
      total.value = msg.data.total;
      currentPage.value = msg.data.page;
      let data = msg.data.data;
      tableData.value = data;
      if(afterSearch) {
        afterSearch(props.params, data);
      }
    }
  }
}

const load = () => {
  loadData(afterSearch);
}

const alt = () => {
  return props.alt || props.title; 
}

watch(formInline, (newValue) => {
  if (newValue[firstColName] || newValue[secondColName] ) {
    currentPage.value = 1;
    onSearch();
  }
});

const dbClick = (row) => {
  resultTableRef.value.toggleRowSelection(row, undefined);
}

const upper = (str) => {
  if (!str) {
    return "";
  }
  return String(str).toUpperCase();
}

const afterSearch = (param, data) => {
  curPageSelected.value = [];
  setTimeout(()=>{
    autoSelected.value = true;
    data.forEach(row => {
      if (multiVal.value.includes(row[firstColName])) {
        resultTableRef.value.toggleRowSelection(row, undefined);
        curPageSelected.value.push(row[firstColName])
      }
    });
    autoSelected.value = false;
  }, 200)
  if (props.afterSearch) {
    props.afterSearch(param, data);
  }
}
// click search icon
const handleSearch = async () => {
  openSearchDialog();
  onSearch();
};

// change page size
const handleChange = () => {
  onSearch();
}

const onSearch = () => {
  currentRow.value = null;
  param.value = {};
  //formInline? param.value = formInline : ref();
  for (let key in formInline) {
    if (formInline.hasOwnProperty(key) && formInline[key]) {
      param.value[key] = formInline[key];
    }
  }
  load();
}

const handleOK = (rows) => {
  multiVal.value = [].concat(_multiVal.value);
  _multiVal.value = [];
  change(multiVal.value);
  hideSearchDialog();
}

const dialogInput = () => {
  if (!formInline[firstColName] && !formInline[secondColName]) {
    onSearch();
  }
}

const validSearch = () => {
  return true;
}

defineExpose({
  validSearch,
})
</script>

<style>

.hideClass {
  display: none;
}

.multipleSearchInput .ep-select__selection {
  flex-wrap: unset;
}

.multipleSearchInput .ep-select__suffix {
  display: none;
}

.searchInput-dialog {
  width: 750px;
  height: 500px;
}

.searchInput-dialog-content {
  max-width: 750px;
  max-height: 500px;
  overflow-y: auto;
}

.searchInput-dialog-content .form-row {
  width: 100% !important;
}

.searchInput-dialog-result {
  margin: 10px;
  max-height: 400px;
  overflow-y: auto;
}

.searchInput-dialog .ep-dialog {
  overflow-y: auto;
  --ep-dialog-width: 750px !important;
}

/* .searchInput-dialog .ep-overlay-dialog .ep-dialog>header { */
/* display: none; */
/*  no show original header*/
/* } */

.searchInput-dialog .ep-pagination .btn-prev {
  margin-left: 16px !important;
}


.searchInput-dialog .ep-pagination .ep-select {
  width: 90px !important;
}

.searchInput-dialog-form-inline {
  padding-inline: 10px;
  margin: 10px;
}

.searchInput-dialog-form-inline .ep-input {
  --ep-input-width: 150px;
}

.search-input {
  padding: 0px !important;
}

.search-input .ep-dialog__header {
  /* background-color: #e66; 
  padding: 5px 0px 5px 10px;
  */
  padding:0;
}

.ep-dialog__title {
  color:#fff;
}

.search-input .ep-dialog__header button {
  height: 32px;
  width: 32px;
}

.search-input .ep-dialog__body .ep-form {
  background-color: #f4f4f5;
  margin: 0px;
  padding: 0px;
}

.search-input .ep-dialog__footer {
  display: grid;
  padding: 10px 20px;
}

.search-icon {
  width: 20px;
  height: 16px;
}

.search-error>div {
  -webkit-box-shadow: 0 0 0 1px var(--ep-color-danger) inset !important; 
}
</style>