#!/bin/bash
###
 # @Auther:
 # @Date: 2024-11-05
 # @Description: 
###
ROOT_DIR=/opt/app-root/src
# ROOT_DIR=/opt/app-root/src/${VITE_BASEPATH}

# Replace env vars in files served by NGINX
# for file in $ROOT_DIR/index.html $ROOT_DIR/static/js/env-config-*.js;
for file in $ROOT_DIR/index.html $ROOT_DIR/assets/*-*.js;
do
	echo "processing $file ...";
	sed -i 's|VITE_APP_VERSION_PLACEHOLDER|'"${VITE_APP_VERSION}"'|g' $file
	sed -i 's|VITE_SYSTEM_PLACEHOLDER|'"${VITE_SYSTEM}"'|g' $file
	# sed -i 's|VITE_BASEPATH_PLACEHOLDER|'"${VITE_BASEPATH}"'|g' $file
	sed -i 's|VITE_FRONTEND_HOME_PLACEHOLDER|'"${VITE_FRONTEND_HOME}"'|g' $file
	sed -i 's|VITE_FRONTEND_PLACEHOLDER|'"${VITE_FRONTEND}"'|g' $file
	sed -i 's|VITE_OIDCURL_PLACEHOLDER|'"${VITE_OIDCURL}"'|g' $file
	sed -i 's|VITE_REDIRECTURL_PLACEHOLDER|'"${VITE_REDIRECTURL}"'|g' $file
	sed -i 's|VITE_SERVICE_PLACEHOLDER|'"${VITE_SERVICE}"'|g' $file
	sed -i 's|VITE_AUTHORIZATION_URL_PLACEHOLDER|'"${VITE_AUTHORIZATION_URL}"'|g' $file
	sed -i 's|VITE_TOKEN_URL_PLACEHOLDER|'"${VITE_TOKEN_URL}"'|g' $file
	sed -i 's|VITE_LOGOUT_URL_PLACEHOLDER|'"${VITE_LOGOUT_URL}"'|g' $file
	sed -i 's|VITE_REFRESH_URL_PLACEHOLDER|'"${VITE_REFRESH_URL}"'|g' $file
	sed -i 's|VITE_API_BASE_URL_PLACEHOLDER|'"${VITE_API_BASE_URL}"'|g' $file
	sed -i 's|VITE_API_DEV_URL_PLACEHOLDER|'"${VITE_API_DEV_URL}"'|g' $file
	sed -i 's|VITE_AUTO_LOGIN_PLACEHOLDER|'"${VITE_AUTO_LOGIN}"'|g' $file
	# Your other varables hear...
	echo "sh command successful"
done
exec "$@"
