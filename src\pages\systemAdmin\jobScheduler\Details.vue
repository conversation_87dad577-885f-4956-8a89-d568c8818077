<template>
    <BaseDetails ref="details" :handleSave="handleSave"   :viewOriginalForm="viewOriginalForm"  :reload="props.reload" :form="jobForm">
        <el-form :validateOnRuleChange="false" :disabled="formDisabled" ref="jobFormRef" label-width="auto"  
        label-position='left'
        style="width: 100%" :model="jobForm.form" :rules="rules" status-icon>
            <el-row>
                <el-col :span="6">
                    <FormItemSign :detailsRef="details" :label="$t('common.title.opCtryRegionCode')" prop="opCtryRegionCode">
                        <CtryRegionSearchInput  v-model="jobForm.form.opCtryRegionCode"
                            style="width: 260px"
                            showDesc="false"
                            opCtryRegion />
                    </FormItemSign>
                </el-col>
                
                <el-col :span="8" :offset="1"  >
                    <FormItemSign :detailsRef="details" :label="$t('csscl.jobScheduler.jobId')" prop="jobId"  label-width="80px" >
                        
                        <GeneralSearchInput v-model="jobForm.form.jobId" 
                            style="width: 630px"
                            inputStyle="width: 145px"
                            :dbClick="jobIdClick"
                            searchType="jobId"
                            :disabled="!isAddFlag"
                            />
                        
                    </FormItemSign>
                </el-col>
                <el-col :span="6" :offset="2">
                    <FormItemSign :detailsRef="details" :label="$t('common.title.status')" prop="status" label-width="80px">
                        <Select v-model="jobForm.form.status" clearable style="width: 150px" type='STATUS' />
                    </FormItemSign>
                </el-col>
            </el-row>

            <el-row >
                <el-col :span="6">
                    <!-- <FormItemSign :detailsRef="details" :label="$t('csscl.jobScheduler.jobName')" prop="jobName" >
                        <Select v-model="jobForm.form.jobName" style="width: 240px" valueKey="jobName" vkEnqual :source="jobNameList" />
                    </FormItemSign> -->
                    <FormItemSign :detailsRef="details" :label="$t('csscl.jobScheduler.type')" prop="jobType">
                        <Select v-model="jobForm.form.jobType" disabled style="width: 227px" type='JOB_TYPE' />
                    </FormItemSign>
                </el-col>
                <el-col :span="8" :offset="1">
                    <!-- <FormItemSign :detailsRef="details" label-width="80px" :label="$t('csscl.jobScheduler.type')" prop="jobType">
                        <Select v-model="jobForm.form.jobType" clearable style="width: 260px" type='JOB_TYPE' />
                    </FormItemSign> -->
                </el-col>
                <el-col :span="6"></el-col>
            </el-row>
            <br>
            <span style="font-size: 16px; font-weight: bold;">Selection Criteria</span>
            <div style="paddingBlock:5px;width: 100%;display: flex;flex-flow: wrap;border-top: 1px solid;"></div>
          
            <el-row >
                <el-col :span="8">
                    <el-space>
                        <FormItemSign :detailsRef="details" :label="$t('csscl.jobScheduler.effectiveDateFrom')" label-width="140px" prop="effectiveDateFrom" >
                            <div style="position:relative">
                                <DateItem v-model="jobForm.form.effectiveDateFrom"
                                          :title="$t('message.later.equal.curdate', [$t('csscl.jobScheduler.effectiveDateFrom')]) + '\r' +
                                          $t('message.later.dateto', [$t('csscl.jobScheduler.effectiveDateFrom'), $t('csscl.jobScheduler.effectiveDateTo')] ) "/>
                            </div>
                        </FormItemSign>
                        <FormItemSign :detailsRef="details" prop="effectiveDateTo" :label="$t('common.title.date.to')" :hideLabel="$t('csscl.jobScheduler.effectiveDateTo')" label-width="45" >
                            <div style="position:relative">
                            <DateItem v-model="jobForm.form.effectiveDateTo"
                                      :title="$t('message.later.curdate', [$t('csscl.jobScheduler.effectiveDateTo')])" />
                            </div>
                        </FormItemSign>
                    </el-space>
                </el-col>
                <el-col :span="6" >
                    <FormItemSign :detailsRef="details" :label="$t('csscl.jobScheduler.frequency')" prop="frequency" label-width="90px" >
                        <Select v-model="jobForm.form.frequency" style="width: 260px" type='FREQUENCY' :change="changeFrequency" />
                    </FormItemSign>
                </el-col>
            </el-row>

            <el-row>
                <el-col :span="20">
                    <el-checkbox-group v-model="jobForm.form.tmpFrequencyDays" >
                        <el-checkbox label="Monday" value="Monday" class="days-checkbox" :disabled="!isWeek" @change="handleCheckChange('Monday')"></el-checkbox>
                        <el-checkbox label="Tuesday" value="Tuesday" class="days-checkbox" :disabled="!isWeek" @change="handleCheckChange('Tuesday')"></el-checkbox>
                        <el-checkbox label="Wednesday" value="Wednesday" class="days-checkbox" :disabled="!isWeek" @change="handleCheckChange('Wednesday')"></el-checkbox>
                        <el-checkbox label="Thursday" value="Thursday" class="days-checkbox" :disabled="!isWeek" @change="handleCheckChange('Thursday')"></el-checkbox>
                        <el-checkbox label="Friday" value="Friday" class="days-checkbox" :disabled="!isWeek" @change="handleCheckChange('Friday')"></el-checkbox>
                        <el-checkbox label="Saturday" value="Saturday" class="days-checkbox" :disabled="!isWeek" @change="handleCheckChange('Saturday')"></el-checkbox>
                        <el-checkbox label="Sunday" value="Sunday" class="days-checkbox" :disabled="!isWeek" @change="handleCheckChange('Sunday')"></el-checkbox>
                    </el-checkbox-group>
                </el-col>
            </el-row>


            <el-row >
                <el-col :span="8">
                    <FormItemSign :detailsRef="details" :label="$t('csscl.jobScheduler.channel')" prop="channel" label-width="140px" >
                        <Select v-model="jobForm.form.channel" style="width: 120px" type='SCHEDULER_CHANNEL'  :change="changeChannel" disabled/>
                    </FormItemSign>
                </el-col>
                <el-col :span="6" >
                    <FormItemSign :detailsRef="details" :label="$t('csscl.jobScheduler.ftgidCode')" prop="ftgidCode"  label-width="90px" >
                        <GeneralSearchInput v-model="jobForm.form.ftgidCode" setTitle="true"  inputStyle="width: 180px" style="width: 500px" searchType="ftgidCode" :disabled="!isFTGChannel" />
                    </FormItemSign>
                </el-col>
            </el-row>
            <br>
            <EditGrid v-model="jobForm.form.flowJobScheduleTimeVPOS" 
                tableWidth="50%"
                :form="newTimeRow" oid="flowJobScheduleTimeOid"
                :rules="newTimeRowRules"
                :details="details"
                :disabled="formDisabled"
                uniqueKey="scheduleTime"
                ref="scheduleTimeGridRef"
                >
                <template #columns>
                    <el-table-column :detailsRef="sondetails" prop="scheduleTime" :label="$t('csscl.jobScheduler.sheduleTime')" /> 
                        <el-table-column  prop="status" :label="$t('common.title.status')">
                            <template #default="scope">
                            {{ getCommonDesc('STATUS', scope.row.status) }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="recordStatus" width="220" :label="$t('common.title.recordStatus')">
                            <template #default="scope">
                                {{ getCommonDesc('RECORD_STATUS', scope.row.recordStatus) }}
                                    <span v-if="scope.row.recordStatus&&scope.row.recordStatus!=='A' ">
                                    for  {{ getCommonDesc('MKCK_ACTION', scope.row.mkckAction) }}
                                    </span>
                            </template>
                        </el-table-column>
                </template>
                <template #form>
                    <FormItemSign prop="scheduleTime" :detailsRef="details" label-width="120px" :label="$t('csscl.jobScheduler.sheduleTime')" required>
                        <el-time-picker v-model="newTimeRow.scheduleTime" format="HH:mm" value-format="HH:mm" style="width: 300px;"></el-time-picker>
                    </FormItemSign>
                    <FormItemSign prop="status" :detailsRef="details" label-width="120px" :label="$t('common.title.status')" required>
                        <Select v-model="newTimeRow.status" style="width: 300px;" type="STATUS" />
                    </FormItemSign>
                </template>
            </EditGrid>
        </el-form>
    </BaseDetails>
</template>

<script lang="ts" setup>
import { ref, reactive, getCurrentInstance, watchEffect } from 'vue';
import type { FormInstance, FormRules } from 'element-plus'
import BaseDetails from '~/pages/base/Details.vue';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import CtryRegionSearchInput from '~/pages/base/CtryRegionSearchInput.vue';
import { getOid,getCommonDesc,getSysCtrlDate, saveMsgBox, showErrorMsg} from '~/util/Function.js';
import { commonRules, showValidateMsg } from '~/util/Validators.js';
import { addCustValid, cloneObj, compListInCustValid } from "~/util/ModifiedValidate";

const { proxy } = getCurrentInstance();
const props = defineProps(['reload']);
const details = ref();
const sondetails = ref();
const formDisabled = ref(false);

const jobNameList = ref({});
const newTimeRow = ref({});
const editDis = ref(false);
const originalFormData = ref();
const isWeek = ref(false);
const isFTGChannel= ref(false);
const initJobScheduleTime = ref();

const newTimeRowRules = reactive<FormRules>({
    scheduleTime: [commonRules.required],
    status: [commonRules.required],
});

const changeFrequency = (val) => {
    if('W'===val||'BW'===val||'ETW'===val||'EFW'===val){
        isWeek.value=true;
       if(!jobForm.form.tmpFrequencyDays){
         jobForm.form.tmpFrequencyDays =[]
       }
    }else{
        isWeek.value=false;
        jobForm.form.tmpFrequencyDays =[]
    }
};

const changeChannel = (val) => {
    if('FTG'===val){
        isFTGChannel.value=true;
    }else{
        isFTGChannel.value=false;
        jobForm.form.ftgidCode =null;
    }
};

const  handleCheckChange=(day) =>{
    //   if (jobForm.form.tmpFrequencyDays.length > 1) {
    //     jobForm.form.tmpFrequencyDays=[day];
    //   } else if (jobForm.form.tmpFrequencyDays.length === 1 && jobForm.form.tmpFrequencyDays[0] !== day) {
    //     jobForm.form.tmpFrequencyDays = [day];
    //   }
    }

const validWeek =()=>{
    if(isWeek.value){
        if(jobForm.form.tmpFrequencyDays.length < 1){
            ElMessage({
                message: 'Please select a day of the week',
                type: 'error',
                duration: 10000,
                offset: 100,
                showClose: true,
            });
            return true;
        }
    }
    return false;
}

const validScheduleTime = () => {
    let rows = scheduleTimeGridRef.value.showData.filter(function(item){
        return item?.status === 'A' && item?.mkckAction != 'D';
    });
    if (!rows||rows.length == 0) {
        showErrorMsg('At least one active schedule time');
        return true;
    }
    return false;
};
////////////////////////////////////////////////////////////////Time  start

const scheduleTimeGridRef = ref();


////////////////////////////////////////////////////////////////Time  end
const viewOriginalForm = (pendingOid, isDisabled) => {
    formDisabled.value = isDisabled;
  proxy.$axios.get("/rptsched/api/v1/inter/scheduler/job?flowJobControlOid="+pendingOid).then((body) => {
        if(body.success) {
            jobForm.form = body.data;
        }
    });
}

const editRow = (row, disabled,newId) => {
    if(row?.isApproveDetail && disabled){
        let eventPkey = row?.eventPkey;
        let approveNumber = row?.approveNumber;
        jobForm.form = row?.afterImage;
        details.value.currentRow = jobForm.form;
        proxy.$axios.get("/rptsched/api/v1/report/scheduler/approve?eventPkey=" + eventPkey+
        "&approveNumber="+approveNumber).then((body) => {
            if (body.success) {
                if(body.data){
                    jobForm.form.flowJobScheduleTimeVPOS=body.data.flowJobScheduleTimeVPOS;
                }
            }
        });
    } else {
        const oid = newId || (row?.currentOid && getOid(row, disabled, proxy));
        if (oid) {
            isAddFlag.value=false;
            proxy.$axios.get("/rptsched/api/v1/inter/scheduler/job?flowJobControlOid=" + oid).then((body) => {
                if (body.success) {
                    jobForm.form = body.data;
                    originalFormData.value={...jobForm.form}
                    details.value.currentRow = body.data;
                    changeFrequency(body.data.frequency)
                    changeChannel(body.data.channel)
                    //Start SIR-Cristin-R057 HYC 20240813
                    //Submit a reject for create data , no validation for Effective Date 
                    if(body.data.recordStatus==='R'&&body.data.mkckAction==='C'){
                        isAddFlag.value=true;  
                    }
                    //Start SIR-Cristin-R057 HYC 20240813
                    jobForm.form.validFields=['tmpFrequencyDays']
                    initJobScheduleTime.value = cloneObj(jobForm.form.flowJobScheduleTimeVPOS);
                    addCustValid(jobForm.form, ()=>{
                      return compListInCustValid(scheduleTimeGridRef.value.showData,
                          initJobScheduleTime.value,
                          'flowJobScheduleTimeOid',
                          ['scheduleTime', 'recordStatus', 'mkckAction', 'status',]);
                    });
                }
                details.value.initWatch({w1:jobForm,w2:newTimeRow});
            });
            editDis.value = true;
        }
    }
}

let curPrcsDate=null;
const handleSave = async (searchValid, isOnlyValidate, unPopping) => {
    if (scheduleTimeGridRef.value.isEditing()) {
        showErrorMsg("The sub table data has not been saved. Please confirm if you want to leave.");
        return false;
    }
    curPrcsDate= await getSysCtrlDate();
    let valid= await validWeek();
    if(valid){
        return false;
    }
    let validSc= await validScheduleTime();
    if(validSc){
        return false;
    }
    let result = await jobFormRef.value.validate((valid, fields) => {
        if (valid) {

        } else {
            showValidateMsg(details, fields);
        }
    });
    if (isOnlyValidate) {
        return result;
    }
    if (result && searchValid && await saveMsgBox(unPopping)) {
        let formData = {...jobForm.form};
        formData.flowJobScheduleTimeVPOS = scheduleTimeGridRef.value.mergeShowData();
        if (jobForm.form.flowJobControlOid) {
            const msg = await proxy.$axios.patch("/rptsched/api/v1/inter/scheduler/job", formData);
            details.value.writebackId(msg.data);
            editRow(null,null,msg.data);
            return msg.success;
        } else {
            const msg = await proxy.$axios.post("/rptsched/api/v1/inter/scheduler/job", formData);
            details.value.writebackId(msg.data);
            editRow(null,null,msg.data);
            return msg.success;
        }
    }
    return false;
}

const jobIdClick = (row) => {
    jobForm.form.jobType = row.var1;
    jobForm.form.channel =row.var2;
    changeChannel( jobForm.form.channel)
}
const isAddFlag=ref(false);
const showDetails = (row, isdoubleCheck, jobNames) => {
    jobNameList.value = jobNames;
    isWeek.value=false;
    isFTGChannel.value=false;
    if(isdoubleCheck||row.recordStatus==='PA'){
        formDisabled.value = true;
        details.value.showDetails(row, true)
    }else{
        formDisabled.value = false;
        details.value.showDetails(row, false)
    }
    jobForm.form = {};

    if (row.currentOid) {
        editRow(row,isdoubleCheck);

    }else{
        isAddFlag.value=true;
        details.value.initWatch({w1:jobForm,w2:newTimeRow});
    }
    jobForm.form.validFields=['flowJobScheduleTimeVPOS','tmpFrequencyDays'];
}

defineExpose({
    details,
    sondetails,
    editRow,
    showDetails,
});
// --------------------------------------------

interface JobForm {
    flowJobControlOid: string;
    opCtryRegionCode: string;
    jobName: string;
    jobType: string;
    jobTarget: string;
    frequency: string;
    frequencyDays: string;
    tmpFrequencyDays: string[];
    processTimes: string;
    effectiveDateFrom: string;
    effectiveDateTo: string;
    flowJobScheduleTimeVPOS: TimeItem[];
    jobDesc: string;
    action: string;
    status: string;
    mkckOid: string;
    pendingOid: string;
    channel: string;
    itfPathId: string;
    itfFileName: string;
    ftgidCode: string;
}
export type TimeItem = {
    index: number;
    flowJobControlOid: string;
    scheduleTime: string;
    status: string;
    recordStatus: string;
    mkckOid: string;
    pendingOid: string;
};

const jobFormRef = ref<FormInstance>()
const jobForm = reactive({
    form: {
        flowJobControlOid: "",
        opCtryRegionCode: "",
        jobName: "",
        jobType: "",
        jobTarget: "",
        frequency: "",
        frequencyDays: "",
        tmpFrequencyDays: "",
        processTimes: "",
        effectiveDateFrom: "",
        effectiveDateTo: "",
        flowJobScheduleTimeVPOS: [],
        jobDesc: "",
        action: "",
        status: "",
        mkckOid: "",
        pendingOid: "",
        channel: "",
        itfPathId: "",
        itfFileName: "",
        ftgidCode: "",
    }
})
const validateFTGIdCode =  async (rule: any, value: any, callback: any) => {
    if(jobForm.form?.channel ==='FTG'){
        if (value) {
            callback()
            return;
        }else {
            callback(new Error('Please input '))
            return;
        }
    }
    callback()
}

const validateDateFrom =  (rule: any, value: any, callback: any) => {
    let currentDate = new Date(curPrcsDate);
    currentDate.setHours(0, 0, 0, 0);
    const effectiveDateTo = jobForm.form?.effectiveDateTo;
    if(isAddFlag.value){
        if (value) {
            const valueDate = new Date(value);
            if (valueDate < currentDate) {
                callback(new Error('Date must be later than or equal to the current date.'));
                return;
            }
            if (effectiveDateTo) {
                const effectiveDateToDate = new Date(effectiveDateTo);
                if (valueDate > effectiveDateToDate) {
                    callback(new Error(proxy.$t('message.earlier.equal.dateto', [proxy.$t('csscl.jobScheduler.effectiveDateFrom'), proxy.$t('csscl.common.dateTo')])));
                    return;
                }
            }
        }
    }else{
        //Start SIR-HLH-R96 HYC 20240820
        if (value) {
            const valueDate = new Date(value);
            if (effectiveDateTo) {
                    const effectiveDateToDate = new Date(effectiveDateTo);
                    if (valueDate > effectiveDateToDate) {
                        callback(new Error(proxy.$t('message.earlier.equal.dateto', [proxy.$t('csscl.jobScheduler.effectiveDateFrom'), proxy.$t('csscl.common.dateTo')])));
                        return;
                    }
                }
            }
        //End SIR-HLH-R96 HYC 20240820
    }
    callback();
};

const validateDateTo =  (rule: any, value: any, callback: any) => {
    let currentDate =new Date(curPrcsDate);
    currentDate.setHours(0, 0, 0, 0);
    const effectiveDateTo = jobForm.form?.effectiveDateTo;
    if (effectiveDateTo) {
                const effectiveDateToDate = new Date(effectiveDateTo);
                if (effectiveDateToDate < currentDate) {
                ElMessage({
                    message: 'Effective Date To must be later than or equal to the current date.',
                    type: 'error',
                    duration: 10000,
                    offset: 100,
                    showClose: true,
                    });
                return;
             }
        }
    callback();
};
const validateFrequency = (rule: any, value: any, callback: any) => {
    if (value) {
        if(value!=='D'){
            if(jobForm.form?.tmpFrequencyDays){
                callback();
            return;
            }else{
                callback(new Error('Days of week must be entered if Frequency=Weekly,Biweekly,Every Third Week,Every FourthWeek'));
                return;
            }
        }
    }
    callback();
};
const rules = reactive<FormRules<JobForm>>({
    opCtryRegionCode: [
        commonRules.required,
    ],
    status: [
        commonRules.selectRequired,
    ],
    jobType: [
        commonRules.selectRequired,
    ],
    jobId: [
        commonRules.required,
    ],
    frequency: [
        commonRules.required,
        { validator: validateFrequency, trigger: 'blur' },
    ],
    effectiveDateFrom: [
        commonRules.required,
        { validator: validateDateFrom, trigger: 'blur' },
        // commonRules.laterEquCurProcDate,
        // commonRules.laterDt(()=>{ return jobForm.form.effectiveDateTo }, proxy.$t("csscl.jobScheduler.effectiveDateTo")),
    ],
    effectiveDateTo: [
        commonRules.laterCurProcDate,
    ],
    ftgidCode: [
        { validator: validateFTGIdCode, trigger: 'blur' },
    ]

    
})

const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate((valid, fields) => {
        if (valid) {
            console.log('submit!')
        } else {
            console.log('error submit!', fields)
        }
    })
}

const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.resetFields()
}

const options = Array.from({ length: 10000 }).map((_, idx) => ({
    value: `${idx + 1}`,
    label: `${idx + 1}`,
}))
</script>

<style>
.days-checkbox{
    margin-right: 70px;
}

</style>