<template> 
  <BaseDetails ref="details" :handleSave="handleSave" :reload="props.reload" :form="ruleForm">
    <el-form :validateOnRuleChange="false" :disabled="formDisabled" ref="ruleFormRef" style="width: 100%" :model="ruleForm.form" status-icon>
      <FormRow>
        <FormItemSign :detailsRef="details" :label="$t('common.title.opCtryRegionCode')" label-width="200" prop="opCtryRegionCode">
          <CtryRegionSearchInput v-model="ruleForm.form.opCtryRegionCode" showDesc="false" style="width: 120px" opCtryRegion />
        </FormItemSign>
        <FormItemSign :detailsRef="details" :label="$t('csscl.nomineeManagement.clientAccountCode')" label-width="170" prop="clientCode">
          <el-input v-model="ruleForm.form.clientCode" style="width: 200px" />
        </FormItemSign>
        <FormItemSign :detailsRef="details"></FormItemSign>
      </FormRow>
      <FormRow>
        <FormItemSign :detailsRef="details" :label="$t('csscl.nomineeManagement.currencyCode')" label-width="200" prop="currencyCode">
           
          <CurrencySearchInput v-model="ruleForm.form.currencyCode" 
            style="width: 100px"
            showDesc="false" />

        </FormItemSign>
        <FormItemSign :detailsRef="details" :label="$t('csscl.nomineeManagement.bankAccountNo')" label-width="170" prop="bankAccountNo">
          <el-input v-model="ruleForm.form.bankAccountNo" style="width: 350px; " />
        </FormItemSign>
        <FormItemSign :detailsRef="details" :label="$t('csscl.nomineeManagement.parentBankAccountNo')" label-width="190" prop="parentBankAccountNo">
          <el-input v-model="ruleForm.form.parentBankAccountNo" style="width: 300px; " />
        </FormItemSign>
      </FormRow>  
      <FormRow>  
        <FormItemSign :detailsRef="details" :label="$t('common.title.status')" label-width="200" prop="status">
          <Select v-model="ruleForm.form.status" style="width: 150px" type='STATUS' />
        </FormItemSign>      
      </FormRow>
    </el-form>
  </BaseDetails>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue';
import type { FormInstance, FormRules } from 'element-plus'
import BaseDetails from '~/pages/base/Details.vue';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import CtryRegionSearchInput from '~/pages/base/CtryRegionSearchInput.vue'
import CurrencySearchInput from '~/pages/base/CurrencySearchInput.vue';
import { formatCashAccountNumber} from '~/util/AccountUtils.js'; 
import { saveMsgBox } from '~/util/Function.js';

const { proxy } = getCurrentInstance();
const props = defineProps(['reload']);
const details = ref();
const formDisabled = ref(false);
const editDis = ref(false);
const editRow = (row,newId) => {
  const oid = newId ||row?.nomineeOid;
  if (oid) {
    proxy.$axios.get("/datamgmt/api/v1/nominee?nomineeId="+oid).then((body) => {
        if(body.success) {
            ruleForm.form = body.data;
        }
    });
    editDis.value = true;
  } else {
    ruleForm.form = {};
    editDis.value = false;
  }
}  
const handleSave = async (searchValid, isOnlyValidate, unPopping) => {
  let result = await ruleFormRef.value.validate((valid, fields) => {
      if (valid) {

      } else {
          console.log('error submit!', fields)
      }
  });
    if (isOnlyValidate) {
        return result;
    }
  if (result && searchValid && await saveMsgBox(unPopping)) {
      if (ruleForm.form.nomineeOid) {
          const msg = await proxy.$axios.patch("/datamgmt/api/v1/nominee", {
              ...ruleForm.form,
          });
          details.value.writebackId(msg.data);
          editRow(null,msg.data)
          return msg.success;
      } else {
          const msg = await proxy.$axios.post("/datamgmt/api/v1/nominee", {
              ...ruleForm.form,
          });
          details.value.writebackId(msg.data);
          editRow(null,msg.data)
          return msg.success;
      }
  }
  return false;
}
const showDetails = (row, disabled) => {
  formDisabled.value = disabled;
  details.value.showDetails(row, disabled)
  editRow(row);
}
defineExpose({
  details,
  editRow,
  showDetails,
});
// --------------------------------------------

/*interface RuleForm {
  ftgidName: String
  status: String
}*/

const ruleFormRef = ref()
const ruleForm = reactive({
  form: {
    /*ftgidName: "",
    status: "",*/
  }
})

/*const rules = reactive<FormRules<RuleForm>>({
  ftgidName: [
      { required: true, message: 'Please input ', trigger: 'blur' },
      { min: 1, max: 50, message: 'Length should be 1 to 50', trigger: 'blur' },
  ],
  status: [
      { required: true, message: 'Please select one ', trigger: 'blur' },
  ],
})*/

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
      if (valid) {
          console.log('submit!')
      } else {
          console.log('error submit!', fields)
      }
  })
}

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
}

const options = Array.from({ length: 10000 }).map((_, idx) => ({
  value: `${idx + 1}`,
  label: `${idx + 1}`,
}))

</script>

<style></style>