VITE_APP_VERSION = 1.0.2
VITE_SYSTEM = Custody and Clearing Platform(DEV)
VITE_BASEPATH=/
VITE_FRONTEND_HOME=http://localhost/
VITE_FRONTEND=http://localhost/
VITE_OIDCURL= http://localhost:3000
VITE_REDIRECTURL= http://localhost:3000/logout
VITE_SERVICE= https://dev-apigw.ftn.bochkuatclout.com
# 接口请求地址，会设置到 axios 的 baseURL 参数上
VITE_API_BASE_URL = http://localhost:8002
# message 请求的网关地址
VITE_API_DEV_URL = http://localhost:8002
VITE_WS_PROTOCOL = ws