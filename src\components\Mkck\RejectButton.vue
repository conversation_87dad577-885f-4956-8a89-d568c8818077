<template>
  <div >
    <!-- <CButtons  :action="'Approve'" type="info" @click="showDialog" v-if="mkckRow.recordStatus==='PA' ">Reject</CButtons> -->
    <el-button type="info" @click="showDialog" v-if="(mkckRow.recordStatus==='PA'|| mkckRow.recordStatus==='PA1' || mkckRow.recordStatus==='PA2')&& $currentInfoStore.currentPermission['Approve'] ">Reject</el-button>
    <el-dialog v-model="showRejectDialog" title="Reject" :close-on-click-modal="false" width="600" class="mkck-dialog" append-to-body :show-close="false">
      <template #header>
        <div class="mkckTitle">
          <span class="title-name">Reject</span>
        </div>
      </template>
      <br>
      <el-form :validateOnRuleChange="false" ref="rejectFormRef" :model="rejectForm" :rules="rules">
        <el-col style="padding-right: 12px; padding-left: 12px;">
          <el-form-item label="Reject Code" prop="rejectCode" label-width="100px">
            <CommonSearchInput v-model="rejectForm.rejectCode" 
              maxlength="40"
              style="width:447px"
              inputStyle="width:150px"
              commType="REJECT_REASON" 
              codeTitle="csscl.mkck.rejectCode" 
              codeDescTitle="csscl.mkck.rejectDesc" />                
          </el-form-item>
        </el-col>
        <br>
        <el-col style="padding-right: 12px; padding-left: 12px;">
        <el-form-item label="Remark&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" prop="remark" label-width="100px">
            <el-input type="textarea" v-model="rejectForm.remark" input-style="text-transform: none;" ></el-input>
        </el-form-item>
        </el-col>
      </el-form>
      <br>
      
      <div class="button-group" >
        <el-button @click="handleCancel" class="ep-button-custom">Cancel</el-button>
        <el-button type="primary" @click="handleReject" class="ep-button-custom">OK</el-button>
      </div>
      <br>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
    import { ref, reactive, getCurrentInstance } from 'vue';
    import axios from 'axios';
    import ElementPlus, { ElMessage, ElMessageBox } from 'element-plus';
    import type { Action, FormRules, FormInstance } from 'element-plus'  
    import CommonSearchInput from '~/pages/base/CommonSearchInput.vue';
    const props =  defineProps(['mkckRow','handleCancel']);
    const showRejectDialog = ref(false);
    //const remark = ref('');
    //const rejectCode = ref('');
    const { proxy } = getCurrentInstance();

    const showDialog = () => {
      showRejectDialog.value = true;
      rejectForm.rejectCode = "";
      rejectForm.remark = "";
    };
    const handleCancel = () => {
        showRejectDialog.value = false;
    };

    const handleReject = async () => {
      rejectFormRef.value.validate(valid =>{
        if(valid) {
            const response =  axios.post('/datamgmt/api/v1/makerchecker/reject', {
                    flowMakerCheckerOid: props.mkckRow.mkckOid,
                    status: "R",
                    ...rejectForm
            }).then((body) => {
              if(body.success){
                ElMessageBox.alert("Rejected successfully.", 'Success', {
                  confirmButtonText: 'OK',
                  type: 'success',
                  callback: (action: Action) => {
                    props.handleCancel && props.handleCancel();
                  }
              });


              proxy.$axios.post('/datamgmt/api/v1/handler/unlock', {
                  flag: true,
              });
            }
          });

        } else {

        }

      })
    };


//validate
  interface RejectRuleForm {
    rejectCode: String
    remark: String
  }

  const rejectFormRef = ref<FormInstance>();
  const rejectForm = reactive({
    rejectCode: "",
    remark: ""
  })



  const rules = reactive<FormRules<RejectRuleForm>>({
    rejectCode: [
      { required: true, message: proxy.$t('system.validate.not.select'), trigger: 'change' },
    ]
  })
</script>


<style scoped>
.dialog-divider {
  border-top: 2px solid lightgrey; /* 添加横线样式 */
  margin-top: 10px; /* 设置横线上边距 */
  margin-bottom: 10px; /* 设置横线下边距 */
}
.button-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.button-group {
  display: flex;
  justify-content: center;
  align-items: center;
}
::v-deep .ep-textarea__inner {
  min-height: 150px !important;
}
</style>