<template>
  <BasePanel :searchParams="searchParams" :paramListData='paramListData' url="/auth/api/v1/user/role/list" :showDetails="showDetails"
    :beforeEdit="beforeEdit" :deleteRow="deleteRow" :editRow="editRow" ref="tableRef">
    <template v-slot:searchPanel="slotProps">
      <FormRow>
        <ElFormItemProxy label-width="220" :label="$t('common.title.opCtryRegionCode')" prop="opCtryRegionCode">
          <CtryRegionSearchInput v-model="slotProps.form.opCtryRegionCode" showDesc="false" style="width: 110px" opCtryRegion />
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy label-width="220" :label="$t('csscl.useradmin.role.roleId')" prop="roleId">
          <SearchInput style="width: 300px" v-model="slotProps.form.roleId" 
            maxlength="7"
            url="/auth/api/v1/user/role/list"
            :title="$t('csscl.useradmin.role.roleId')" 
            :params="{ }" 
            :columns="[
              {
                title: $t('csscl.useradmin.role.roleId'),
                colName: 'roleId',
              },
              {
                title: $t('csscl.useradmin.role.roleName'),
                colName: 'roleName',
              }
            ]" :pageSizes="[10, 20, 30]">
          </SearchInput>
        </ElFormItemProxy>
        <ElFormItemProxy label-width="100" :label="$t('csscl.useradmin.role.roleName')" prop="roleName">
          <el-input v-model="slotProps.form.roleName" maxlength="100" style="width: 250px" input-style="text-transform:none" />
        </ElFormItemProxy>
        <ElFormItemProxy label-width="80" :label="$t('csscl.useradmin.usr.status')" prop="status">
          <Select v-model="slotProps.form.status" style="width: 120px" type='STATUS' :change="statusType(slotProps.form.status)"/>
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy label-width="220" :label="$t('common.title.recordStatus')" prop="multipleRecordStatus">
          <MultipleSelect v-model="slotProps.form.multipleRecordStatus" v-model:desc="paramListData.multipleRecordStatus"  />
        </ElFormItemProxy>
        <ElFormItemProxy></ElFormItemProxy>
        <ElFormItemProxy></ElFormItemProxy>
      </FormRow>
    </template>
    <template v-slot:tableColumn>
      <el-table-column sortable="custom" prop="opCtryRegionCode" :label="$t('common.title.opCtryRegionCode')" />
      <el-table-column sortable="custom" prop="roleId" :label="$t('csscl.useradmin.role.roleId')" />
      <el-table-column sortable="custom" prop="roleName" :label="$t('csscl.useradmin.role.roleName')" />
      <el-table-column sortable="custom" prop="status" :label="$t('csscl.useradmin.usr.status')">
        <template #default="scope">
          {{ getCommonDesc('STATUS', scope.row.status) }}
        </template>
      </el-table-column>
      <el-table-column sortable="custom" prop="recordStatus" :label="$t('csscl.useradmin.usr.recordStatus')">
        <template #default="scope">
          <!-- scope.row -->
          {{ getRecordStatusDesc(scope.row) }}
        </template>
      </el-table-column>
    </template>
  </BasePanel>
  <Details ref="detailsRef" :reload="reload" />
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance, watch } from 'vue';
import {
  Search
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus';
import BasePanel from '~/pages/base/index.vue'
import Details from './Details.vue'
import { getOid } from '~/util/Function.js';
import  { getCommonDesc, getRecordStatusDesc } from '~/util/Function.js';

import CtryRegionSearchInput from '~/pages/base/CtryRegionSearchInput.vue'
const { proxy } = getCurrentInstance()
const paramListData = ref({});
const searchParams = ref({
  opCtryRegionCode: '',
  roleId: '',
  roleName: '',
  status: '',
  multipleRecordStatus:[],
});

const tableRef = ref();
const detailsRef = ref();
const showDetails = (row, disabled) => {
  detailsRef.value.showDetails(row, disabled);
}
const deleteRow = (row) => {
  proxy.$axios.delete("/auth/api/v1/user/role?sysRoleOid=" +getOid(row, false,null,true)).then((body) => {
    if (body.success) {
      tableRef.value.load();
    }
  });
}
const editRow = (row) => {
  detailsRef.value.editRow(row);
}
const reload = () => {
  tableRef.value.load();
}
const beforeEdit = async (row) => {
  let msg = await proxy.$axios.get("/auth/api/v1/user/role/pending?" +
    "roleId=" + row.roleId +
    "&sysRoleOid=" + getOid(row)
  );
  if (msg.success && msg.data.length == 0) {
    return true;
  } else {
    ElMessage({
      message: "There are pending records present.",
      type: 'error',
    });
    return false;
  }
}
//paramList 参数显示用的
function statusType(value){
  paramListData._value.status =  getCommonDesc('STATUS', value);
}
function recordStatusType(value){
  paramListData._value.recordStatus =  getCommonDesc('RECORD_STATUS', value);
}
</script>

<style></style>