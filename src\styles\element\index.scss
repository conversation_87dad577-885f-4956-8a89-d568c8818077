$--colors: (
  "primary": (
    "base": #b31a25,
  ),
  "success": (
    "base": #21ba45,
  ),
  "warning": (
    "base": #f2711c,
  ),
  "danger": (
    "base": #db2828,
  ),
  "error": (
    "base": #db2828,
  ),
  "info": (
    "base": #909399,
  ),
);
//--ep-menu-horizontal-height

// we can add this to custom namespace, default is 'el'
@forward "element-plus/theme-chalk/src/mixins/config.scss" with (
  $namespace: "ep"
);

// You should use them in scss, because we calculate it by sass.
// comment next lines to use default color
@forward "element-plus/theme-chalk/src/common/var.scss" with (
  // do not use same name, it will override.
  $colors: $--colors,
  $button-padding-horizontal: ("default": 50px),
  $border-radius: ("base": 0px, "small": 0px),
  $font-size: ("base": 13px),
  $text-color: ("regular": #000000, "secondary": #000000),
  $tag: ("font-size": 13px),
  $checkbox: (
    "checked-bg-color": #ffffff, 
    "checked-text-color": #000000, 
    "checked-input-border-color": #e6e6e6, 
    "checked-icon-color": #000000,
    "input-border-color-hover": unset, 
    ),
  $disabled: (
    "text-color":#000000, //original color is #a8abb2
  ),
  $messagebox: (
    "padding-primary": 0px,
    "border-radius": 0px,
    "font-line-height": 28px,
    "title-color": #ffffff,
  ),
  $overlay-color: (
    "lighter": rgba(0, 0, 0, .2)
  )
);

// if you want to import all
// @use "element-plus/theme-chalk/src/index.scss" as *;

// You can comment it to hide debug info.
// @debug $--colors;

// custom dark variables
@use "./dark.scss";
