<template>
  <BasePanel :searchParams="searchParams" url="/rptsched/api/v1/data/extractor/list" :params="{ modeEdit: 'Y' }"
    :showDetails="showDetails" :deleteRow="deleteRow" :editRow="editRow" :rules="rules" :hideEditBtn="hideEditBtn" ref="tableRef"
    >
    <template v-slot:searchPanel="slotProps" >
      <FormRow>
        <ElFormItemProxy>
          <ElFormItemProxy label-width="150px" :label="fieldsDtl.fields.uploadedDateFrom"  prop="uploadedDateFrom">
            <DateItem :validate-event="false" v-model="slotProps.form.uploadedDateFrom"
                      :title="proxy.$t('message.earlier.equal.curdate', [fieldsDtl.fields.uploadedDateFrom] ) + '\r' +
                        proxy.$t('message.earlier.equal.dateto', [fieldsDtl.fields.uploadedDateFrom, fieldsDtl.fields.uploadedDateTo] ) + '\r' +
                        proxy.$t('message.date.range.error', [7] ) "
                      type="date" style="width: 130px;"/>
          </ElFormItemProxy>
          <ElFormItemProxy label-width="45px" :label="$t('common.title.date.to')" :hideLabel="fieldsDtl.fields.uploadedDateTo" prop="uploadedDateTo">
            <DateItem :validate-event="false" v-model="slotProps.form.uploadedDateTo"
                      :title="proxy.$t('message.earlier.equal.curdate', [fieldsDtl.fields.uploadedDateTo] ) + '\r' +
                        proxy.$t('message.date.range.error', [7] ) "
                      type="date" style="width: 130px;"/>
          </ElFormItemProxy>
        </ElFormItemProxy>
        <ElFormItemProxy>
          <ElFormItemProxy label-width="150px" :label="fieldsDtl.fields.executionDateFrom"  prop="executionDateFrom">
            <DateItem :validate-event="false" v-model="slotProps.form.executionDateFrom"
                      :title="proxy.$t('message.earlier.equal.curdate', [fieldsDtl.fields.executionDateFrom] ) + '\r' +
                        proxy.$t('message.earlier.equal.dateto', [fieldsDtl.fields.executionDateFrom, fieldsDtl.fields.executionDateTo] ) + '\r' +
                        proxy.$t('message.date.range.error', [7] ) "
                      type="date" style="width: 130px;"/>
          </ElFormItemProxy>
          <ElFormItemProxy label-width="45px" :label="$t('common.title.date.to')" :hideLabel="fieldsDtl.fields.executionDateTo"  prop="executionDateTo">
            <DateItem :validate-event="false" v-model="slotProps.form.executionDateTo"
                      :title="proxy.$t('message.earlier.equal.curdate', [fieldsDtl.fields.executionDateTo] ) + '\r' +
                        proxy.$t('message.date.range.error', [7] ) "
                      type="date" style="width: 130px;"/>
          </ElFormItemProxy>
        </ElFormItemProxy>
      
    </FormRow>
    </template>
    <template v-slot:tableColumn >
        <el-table-column sortable="custom" prop="uploadedDate" :label="$t('csscl.admin.dataExtractor.uploadedDate')" width="200" header-align="center"/>
        <el-table-column sortable="custom" prop="fileName" :label="$t('csscl.admin.dataExtractor.fileName')" width="*" header-align="center"/>
        <el-table-column sortable="custom" prop="executionDate" :label="$t('csscl.admin.dataExtractor.executionDateTime')" width="220" header-align="center"/>
        <el-table-column sortable="custom" prop="recordStatus" :label="$t('common.title.recordStatus')" width="250" header-align="center">
          <template #default="scope">
            <!-- scope.row -->
            {{ getCommonDesc('RECORD_STATUS', scope.row.recordStatus) }}
            <span v-if="scope.row.recordStatus&&scope.row.recordStatus!=='A' ">
              for  {{ getCommonDesc('MKCK_ACTION', scope.row.mkckAction) }}
            </span>   
          </template>
        </el-table-column>
        <el-table-column :label="$t('csscl.admin.dataExtractor.dlResult')" width="120" align="center" header-align="center">
          <template #default="scope">
            <el-icon-download v-if="scope.row.recordStatus == 'A' && scope.row?.fileName"
                style="width:20px;height:20px;color:darkorange" @click="handleDownload(scope.row)" />
          </template>
        </el-table-column>
    </template>
  </BasePanel>
  <Details ref="detailsRef" :reload="reload" />
</template>

<script lang="ts" setup>
import { ref, getCurrentInstance, watch, reactive } from 'vue';
import {Download} from '@element-plus/icons-vue'
import BasePanel from '~/pages/base/index.vue'
import Details from './Details.vue'
import  { getCommonDesc, downloadFile } from '~/util/Function.js';
import  { commonRules } from '~/util/Validators.js';
import { ElMessageBox } from 'element-plus';

const { proxy } = getCurrentInstance()
const searchParams = {
  uploadedDateFrom:"",
  uploadedDateTo:"",
  executionDateFrom:"",
  executionDateTo:"",
};

const tableRef = ref();
const detailsRef = ref();
const showDetails = (row, disabled) => {
  detailsRef.value.showDetails(row, disabled);
}
const deleteRow = (row) => {
  proxy.$axios.delete("/rptsched/api/v1/data/extractor?rptDataExtractorOid="+row.rptDataExtractorOid).then((body)=>{
    if(body.success) {
      reload();
    }
  });
}
const editRow = (row) => {
  detailsRef.value.editRow(row);
}
const reload = () => {
  tableRef.value.load();
}
const hideEditBtn = (row) => {
  if(row?.recordStatus=='A'){
    return true;
  }
}

const fieldsDtl = {
  fields:{
    uploadedDateFrom: proxy.$t('csscl.admin.dataExtractor.uploadedDateFrom'),
    uploadedDateTo: proxy.$t('csscl.admin.dataExtractor.uploadedDateTo'),
    executionDateFrom: proxy.$t('csscl.admin.dataExtractor.executionDateFrom'),
    executionDateTo: proxy.$t('csscl.admin.dataExtractor.executionDateTo'),
  }
}

const rules = reactive({
  uploadedDateFrom:[
    commonRules.earlierEquCurDate,
    commonRules.earlierEquDt(()=>{ return searchParams.uploadedDateTo }, fieldsDtl.fields.uploadedDateTo),
    commonRules.diffDate(7,()=>{ return searchParams.uploadedDateTo }, fieldsDtl.fields.uploadedDateTo),
  ],
  uploadedDateTo:[
    commonRules.earlierEquCurDate,
  ],
  executionDateFrom:[
    commonRules.earlierEquCurDate,
    commonRules.earlierEquDt(()=>{ return searchParams.executionDateTo }, fieldsDtl.fields.executionDateTo),
    commonRules.diffDate(7,()=>{ return searchParams.executionDateTo }, fieldsDtl.fields.executionDateTo),
  ],
  executionDateTo:[
    commonRules.earlierEquCurDate,
  ]

});

//-------------------------------

function handleDownload(row){
  if(row?.currentOid){
    //Start R2411A-59803 AMOR 20240918
    let funcId = proxy.$currentInfoStore.getCurrentFuncId(); 
    if(row.filePath==null || row.rptFileName==null){
      ElMessageBox.alert('Please download the file from report center.', 'Warning');
    }else{
        let params = {
        funcId: funcId,
        rptFilePath: row.filePath,
        rptFileName: row.rptFileName
      };
      downloadFile("/rptsched/api/v1/report/center/download", params);
    }
    // downloadFile("/rptsched/api/v1/data/extractor/download?rptDataExtractorOid="+row.currentOid, {});
    //End R2411A-59803 AMOR 20240918
  } else {
    ElMessageBox.alert('Data exception.', 'Warning');
  }
}

</script>

<style>

</style>