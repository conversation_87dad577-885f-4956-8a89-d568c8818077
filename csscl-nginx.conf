server {
  #listen 8081 ssl;
  listen 8080;
  server_name csscl-web;
  #ssl_certificate /etc/ssl/certs/csscl-web.crt;
  #ssl_certificate_key /etc/ssl/private/csscl-web.key;
  #ssl_protocols TLSv1.2 TLSv1.3;

  proxy_set_header X-Real-IP $remote_addr;
  proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

  #Enable gzip
  gzip on;
  gzip_vary on;
  gzip_min_length 1024;
  gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml;
  gzip_disable "MSIE [1-6]\.";

  root /usr/share/nginx/html;

  location / {

    #Always no cache stored for index.html
    location ~* \.(html)$ {
        add_header Cache-Control no-store always;
    }

    add_header Cache-Control no-cache always;

    #root /usr/share/nginx/html;
    try_files $uri $uri/index.html /index.html;    
    #proxy_set_header X-NginX-Proxy true;
  }

}

server {
  listen 9080;
  server_name csscl-health-check;

  location / {
        return 200 'OK';
  }

}
