<template #default>

  <el-input v-model="data" 
    class="input-text"
    ref="inputObj"
    :style="style"
    :disabled="disabled"
    :searchField="searchField"
    :onlyLetters="onlyLetters"
    :alt="alt()"
    @blur="onBlur"
    @change="change" >
    <template #prepend v-if="$slots.prepend">
      <slot name="prepend"></slot>
    </template>
    <template #append v-if="$slots.append">
      <slot name="append"></slot>
    </template>
    </el-input>
  
</template>

<script lang="ts" setup>
import { ref, computed, defineEmits, watch, nextTick } from 'vue';
import { parseBool } from '~/util/Function.js';

const props = defineProps(["modelValue", "style", 'disabled', 'change', 'uppercase', 'onlyLetters', 'searchField', "rules", 'required', 'alt', 'title', 'trim', 'blur']);
const emit = defineEmits(['update:modelValue', "update:rules"]);
const isUpper = parseBool(props.uppercase);
const isTrim = parseBool(props.trim);
const inputObj = ref();
const input = ref();
const data = computed({
  get(){
    return props.modelValue;
  },
  set(val){
    if (isUpper) {
      val = val.toUpperCase();
    }
    if (parseBool(props.onlyLetters)) {
        if (parseBool(props.onlyLetters) === true && parseBool(props.searchField)) {
            val = val.replace(/[^a-zA-Z%]/g,'');
        } else if (parseBool(props.onlyLetters) === true) {
            val = val.replace(/[^a-zA-Z]/g,'');
        } else {
            val = val.replace(new RegExp("[^" + props.onlyLetters + "]", "g"),'');
        }
    }
    emit("update:modelValue", val);
  }
});
const rules = computed({
  get(){
    return props.rules || [];
  },
  set(val){
    emit("update:rules", val);
  }
});

const alt = () => {
  return props.alt || props.title;
}

const onBlur = () => {
  if(props.blur){
    props.blur();
  }
  if (isTrim) {
    let val = props.modelValue;
    val = val.trim();
    emit("update:modelValue", val);
  }
}

const change = (val) => {
  if(props.change){
    props.change(val);
  }
}

nextTick(()=>{ input.value = inputObj.value.input });
defineExpose({
	input,
});

</script>

<style>
</style>