<template> 
    <div>
        <!-- CA Grid -->
        <el-form :validateOnRuleChange="false" disabled>
            <div style="position: absolute;right: 60px; top: 210px;">
                <el-icon-download style="width:20px;height:20px;color:darkorange;padding-left: 192px;" @click="downloadClick" />
                <div></div>
                <el-text>Refreshed as at {{date}}</el-text>
            </div>
            <!-- enquiry detail -->
            <FormRow diabled>
                <FormItemSign :detailsRef="details" :label="$t('csscl.cashopt.enquiry.bal.currentBal')" prop="balForm.r2CurrentBal"><el-input class="right-aligned-input" v-model="ruleForm.form.balForm.r2CurrentBal" :formatter="thousFormatK"/></FormItemSign>
                <FormItemSign :detailsRef="details" :label="$t('csscl.cashopt.enquiry.bal.odInterest')" prop="balForm.r2OdInt"><el-input class="right-aligned-input" v-model="ruleForm.form.balForm.r2OdInt" :formatter="thousFormatK"/></FormItemSign>
            </FormRow>

            <FormRow diabled>
                <FormItemSign :detailsRef="details" :label="$t('csscl.cashopt.enquiry.bal.bochkAvaBal')" prop="balForm.r2AvailBal1"><el-input class="right-aligned-input" v-model="ruleForm.form.balForm.r2AvailBal1" :formatter="thousFormatK"/></FormItemSign>
                <FormItemSign :detailsRef="details" :label="$t('csscl.cashopt.enquiry.bal.interestPayable')" prop="balForm.r2IntPay"><el-input class="right-aligned-input" v-model="ruleForm.form.balForm.r2IntPay" /></FormItemSign>
            </FormRow>

            <FormRow diabled>
                <FormItemSign :detailsRef="details" :label="$t('csscl.cashopt.enquiry.bal.bochkAvaBalExcluedeODLimit')" prop="balForm.r2AvailBal2"><el-input class="right-aligned-input" v-model="ruleForm.form.balForm.r2AvailBal2" :formatter="thousFormatK"/></FormItemSign>
                <FormItemSign :detailsRef="details" :label="$t('csscl.cashopt.enquiry.bal.crLimit')" prop="balForm.r2CrLmt"><el-input class="right-aligned-input" v-model="ruleForm.form.balForm.r2CrLmt" :formatter="thousFormatK"/></FormItemSign>
            </FormRow>

            <FormRow diabled>
                <FormItemSign :detailsRef="details" :label="$t('csscl.cashopt.enquiry.bal.mtdDepoBal')" prop="balForm.r2MtdDepBal"><el-input class="right-aligned-input" v-model="ruleForm.form.balForm.r2MtdDepBal" :formatter="thousFormatK"/></FormItemSign>
                <FormItemSign :detailsRef="details" :label="$t('csscl.cashopt.enquiry.bal.holdBal')" prop="balForm.r2HoldBal"><el-input class="right-aligned-input" v-model="ruleForm.form.balForm.r2HoldBal" :formatter="thousFormatK"/></FormItemSign>
            </FormRow>

            <FormRow diabled>
                <FormItemSign :detailsRef="details" :label="$t('csscl.cashopt.enquiry.bal.totFloatBal')" prop="balForm.r2TotFloBal"><el-input class="right-aligned-input" v-model="ruleForm.form.balForm.r2TotFloBal" :formatter="thousFormatK"/></FormItemSign>
                <FormItemSign :detailsRef="details" :label="$t('csscl.cashopt.enquiry.bal.crFloatBal')" prop="balForm.r2CrFloBal"><el-input class="right-aligned-input" v-model="ruleForm.form.balForm.r2CrFloBal" :formatter="thousFormatK"/></FormItemSign>
            </FormRow>

            <FormRow diabled>
                <FormItemSign :detailsRef="details" :label="$t('csscl.cashopt.enquiry.bal.floatCrPercentage')" prop="balForm.r2FloCrPct"><el-input class="right-aligned-input" v-model="ruleForm.form.balForm.r2FloCrPct" /></FormItemSign>
                <FormItemSign :detailsRef="details" :label="$t('csscl.cashopt.enquiry.bal.floatCrLimit')" prop="balForm.r2FloCrLmt"><el-input class="right-aligned-input" v-model="ruleForm.form.balForm.r2FloCrLmt" :formatter="thousFormatK"/></FormItemSign>
            </FormRow>

            <FormRow diabled>
                <FormItemSign :detailsRef="details" :label="$t('csscl.cashopt.enquiry.bal.osShareOdLimit')" prop="balForm.r2ShaLmtOd"><el-input class="right-aligned-input" v-model="ruleForm.form.balForm.r2ShaLmtOd" :formatter="thousFormatK"/></FormItemSign>
                <FormItemSign :detailsRef="details" :label="$t('csscl.cashopt.enquiry.bal.totAvaShareLimitGrpLevel')" prop="balForm.r2TotAvaShaLmt"><el-input class="right-aligned-input" v-model="ruleForm.form.balForm.r2TotAvaShaLmt" :formatter="thousFormatK"/></FormItemSign>
            </FormRow>

            <FormRow diabled>
                <FormItemSign :detailsRef="details" :label="$t('csscl.cashopt.enquiry.bal.avaShareLimitGrpLimitOd')" prop="balForm.r2AvaShaLmtOd"><el-input class="right-aligned-input" v-model="ruleForm.form.balForm.r2AvaShaLmtOd" :formatter="thousFormatK"/></FormItemSign>
                <FormItemSign :detailsRef="details" :label="$t('csscl.cashopt.enquiry.bal.avaShareLimitGrpLevelIodIodc')" prop="balForm.r2AvaShaLmtIod"><el-input class="right-aligned-input" v-model="ruleForm.form.balForm.r2AvaShaLmtIod" :formatter="thousFormatK"/></FormItemSign>
            </FormRow>

            <FormRow diabled>
                <FormItemSign :detailsRef="details" :label="$t('csscl.cashopt.enquiry.bal.avaShareLimitGrpLevelFc')" prop="balForm.r2AvaShaLmtFc"><el-input class="right-aligned-input" v-model="ruleForm.form.balForm.r2AvaShaLmtFc" :formatter="thousFormatK"/></FormItemSign>
                <FormItemSign :detailsRef="details" :label="$t('csscl.cashopt.enquiry.bal.avaFcLimitNonShare')" prop="balForm.r2AvaFcLmt"><el-input class="right-aligned-input" v-model="ruleForm.form.balForm.r2AvaFcLmt" :formatter="thousFormatK"/></FormItemSign>
            </FormRow>

            <FormRow diabled>
                <FormItemSign :detailsRef="details" :label="$t('csscl.cashopt.enquiry.bal.currentOdTimes')" prop="balForm.r2CurOdTimes"><el-input v-model="ruleForm.form.balForm.r2CurOdTimes" /></FormItemSign>
                <FormItemSign :detailsRef="details" :label="$t('csscl.cashopt.enquiry.bal.minPayDueDt')" prop="balForm.r2MinPayDueDt"><el-input v-model="ruleForm.form.balForm.r2MinPayDueDt" /></FormItemSign>
            </FormRow>

            <FormRow diabled>
                <FormItemSign :detailsRef="details" :label="$t('csscl.cashopt.enquiry.bal.TotOdTimes')" prop="balForm.r2TotOdTimes"><el-input v-model="ruleForm.form.balForm.r2TotOdTimes" /></FormItemSign>
                <FormItemSign :detailsRef="details" :label="$t('csscl.cashopt.enquiry.bal.noInterestIndicator')" prop="balForm.r2NoIntAcInd"><el-input v-model="ruleForm.form.balForm.r2NoIntAcInd" /></FormItemSign>
            </FormRow>

            <FormRow diabled>
                <FormItemSign :detailsRef="details" :label="$t('csscl.cashopt.enquiry.bal.productType')" prop="balForm.r2ProductType"><el-input v-model="ruleForm.form.balForm.r2ProductType" /></FormItemSign>
                <FormItemSign :detailsRef="details" :label="$t('csscl.cashopt.enquiry.bal.subProductType')" prop="balForm.r2SubProType"><el-input v-model="ruleForm.form.balForm.r2SubProType" /></FormItemSign>
            </FormRow>

        </el-form>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import Grid from '~/pages/base/Grid.vue';
import { useRouter } from 'vue-router';
import { getDateAndTime, downloadBatchFile, thousFormatK } from '~/util/Function.js';
import { ElMessageBox } from 'element-plus'

const router = useRouter()
const props = defineProps([ "ruleForm", "details"]);
const { proxy } = getCurrentInstance();
const details = props.details;
const ruleForm = props.ruleForm;
let formInline = reactive({});
let date = getDateAndTime();
const gridClick = (row) => {
    Object.assign(ruleForm.form.caPayMethodVPO, row);
}

const downloadClick = async()=>{
    ElMessageBox.confirm(
    'Are you sure you want to download it?',
    'Warning',
    {
      confirmButtonText: 'OK',
      cancelButtonText: 'Cancel',
      type: 'warning',
    })
    .then(() => {
      downloadBatchFile("/eapmgmt/api/v1/boc/balDownload", ruleForm.form);
    })
    .catch(() => {})
}

</script>

<style>
.right-aligned-input .ep-input__inner {
  text-align: right;
}
</style>