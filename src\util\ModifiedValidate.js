
const getValidFields = (type, key) => {
    key = key == null ? "" : key;
    let fields = {
        CSSCL_CLIENT001:['custodyLevel','custodyMarket','omnibusSegregate'],
        CSSCL_CLIENT002:['deleteDocumentVPOs', 'identifierPrefVPOs', 'documentVPOs', 'clientGroupCode','fundMgrCode','proxyCtryCode','clientFundId','cashMgmtServiceInd','startDate','endDate','custodyLevel','custodyMarket','processingSystem','omnibusSegregate'],
        CSSCL_CLIENT002_documentVPOs:['fileTypeCode','fileSubTypeCode','filePathId','expiryDate'],
        CSSCL_CLIENT002_identifierPrefVPOs:['seqNo'],
        CSSCL_CLIENT002_deleteDocumentVPOs:['recordStatus', 'mkckAction'],
        CSSCL_SETTL001:['exbDataList'],
        CSSCL_SETTL001_exbDataList:['caPayMethod','settlePayMethod'],
        CSSCL_SDATA003:['opCtryRegionCode', 'ctryRegionCode', 'isoCode', 'mailRegion', 'ctryRegionName', 'currencyCode', 'status'],
        CSSCL_CASHM005:['adjProjDate'],
        CSSCL_CASHM001:['deleteCustBookList','custbookList'],
        CSSCL_CASHM001_custbookList:['reconFlag','funcType','fxType','fxPair','fxRate','instrDt','valueDate','classCode','depositDate','maturityDate','timeDepositIntRate','timeDepositIntAmt','txnRef','remark','currencyCode','drCrInd',],
        CSSCL_SYSAD008:NOT_VALID,
        CSSCL_CASHM002:NOT_VALID,
        CSSCL_USRAD001_userAccessLvls:['level2Code', 'level3Code', 'level4Code', 'level5Code'],
    }
    let fs = fields[type + (key&&"_") + key];
    if (fs && key) {
        fs = fs.concat(ModifiedFlag);
    }
    return fs;
}

export const addModifiedFlag = (obj, key) => {
    addModifiedFlag.value = '$$$$$$';
    // var o = {}, eo = {};
    // eo[ModifiedFlag] = addModifiedFlag.value;
    // eo[EnterObj] = {};
    // eo[EnterObj][ModifiedFlag] = addModifiedFlag.value;
    // o[key] = [ eo ];
    // Object.assign(obj, o);
    let eo = addModifiedFlag.value, o = {};
    o[ModifiedFlag] = eo;
    o[EnterObj] = eo;
    obj[key] = [o];
    enterObjects[eo] = o;
}

export const removeModifiedFlag = (obj) => {
    let type = typeofObj(obj);
    if (type == typeofObj.OBJECT) {
        //if (obj[EnterObj]?.[ModifiedFlag] == addModifiedFlag.value) {
        //    delete obj[EnterObj][ModifiedFlag];
        //}
        if (obj[ModifiedFlag] == addModifiedFlag.value) {
            delete obj[ModifiedFlag];
        }
    } else if (type == typeofObj.ARRAY) {
        for (let i = 0; i < obj.length; i++) {
            let e = obj[i];
            if (e[ModifiedFlag] && e[ModifiedFlag] == addModifiedFlag.value) {
                obj.splice(i, 1);
                break;
            }
        }
    }
}

const NOT_VALID = 'NOT_VALID';
const CUST_LIST = 'CUST_LIST';
const CUST_VALID = 'CUST_VALID';
const END_HANDLE = 'END_HANDLE';
const EnterObj = "enterObj";
const BeforeImage = "beforeImage";
const ModifiedFlag = "ModifiedFlag";

/**
 * 给 current_obj 添加自定义的验证方法
 * @param obj current_obj
 * @param fn fn(current_obj, validObject)
 *
 */
export const addCustValid = (obj, fn) => {
    obj[CUST_VALID] = fn;
}
/**
 * 对象完成执行后
 * @param obj
 * @param fn
 */
export const addEndHandle = (obj, fn) => {
    obj[END_HANDLE] = fn;
}

let enterObjects = {};

export const clearEnterObjects = (key) => {
    if (key) {
        delete enterObjects[key];
    } else {
        enterObjects = {};
    }
}

/**
 * 把 obj 对象或 etObj 对象转化成 enterObjects [obj['enterObj']] = etObj || obj;
 * @param obj 当 etObj 值为 null 时，obj 当成 enterObject 存放 enterObjects 对象里
 * @param etObj
 * @param field 这个参数不为空，可以标识在 enterObjects 里的对象不随 obj 的改变而改变
 */
export const addEnterObj = (obj, etObj, field) => {
    let pk = obj['oid'];
    if (field && obj.hasOwnProperty(field)) {
        pk = field + "-" + obj[field];
    } else if (field && obj.hasOwnProperty("currentOid")) {
        pk = field + "-" + obj["currentOid"];
    }
    if (!obj[EnterObj] && enterObjects[pk]){
        obj[EnterObj] = pk;
    }
    if (!obj[EnterObj] && pk) {
        enterObjects[pk] = cloneObj(etObj || obj);
        delete enterObjects[pk][BeforeImage];
        obj[EnterObj] = pk;
    }
}

/**
 * 克隆对象
 * @param obj
 * @param fns 对象内需要复制的 function 名
 * @returns {any}
 */
export const cloneObj = (obj, fns) => {
    let o = JSON.parse(JSON.stringify(obj));
    if (fns) {
        fns = [].concat(fns);
        fns.forEach( e => { o[e] = obj[e] } );
    }
    return o;
}
/**
 * 给集合添加 EnterObj 对象
 * 当集合的信息会改变时(如子Grid刷新后，子Grid对象的改变)，需要配合 filed-pk 来标识数据 enterObject 还是保持不变
 * @param list 数据集合
 * @param pkField 当 pkField 使用主键名时，存放在 enterObjects = {  pkFiled-obj[pkFiled]: { Object } }
 * @returns {*}
 */
export const addEnterObj4List = (list, pkField) => {
    for (let o in list) {
        addEnterObj(list[o], null, pkField);
    }
    return list;
}
export const compListInCustValid = (currObj, enterObj, idKeys, validFields, vfKey) => {
    if (currObj.length !== enterObj.length) {
        return true;
    }
    idKeys = idKeys || 'currentOid'
    let result = false;
    for(let curr in currObj) {
        curr = currObj[curr];
        if (!curr[EnterObj]) {
            for (let e in enterObj) {
                e = enterObj[e];
                if (curr[idKeys] == e[idKeys]) {
                    addEnterObj(curr, e);
                    break;
                }
            }
        }
    }
    return validObject({ CUST_LIST:currObj }, vfKey, [CUST_LIST].concat(validFields || []));
}

/**
 * BASE 基础类型 String Number Boolean Date
 * OBJECT 对象类型
 * ARRAY 数据类型
 * @param obj 传入的Object
 */
export const typeofObj = (obj) => {
    typeofObj.BASE = "BASE";
    typeofObj.OBJECT = "OBJECT";
    typeofObj.ARRAY = "ARRAY";
    typeofObj.FUNCTION = "FUNCTION"
    if (obj != null) {
        let con = obj.constructor;
        if (con == String || con == Number || con == Boolean || con == Date) {
            return typeofObj.BASE;
        } else if (con == Object) {
            return typeofObj.OBJECT;
        } else if (con == Array) {
            return typeofObj.ARRAY;
        } else if (con == Function) {
            return typeofObj.FUNCTION;
        }
    }
}
/**
 * 统一排序方法
 * @param a
 * @returns {number}
 */
const letter2Num = (a) => {
    let num = "";
    a = a?.toUpperCase();
    if (a) {
        for (let i =0; i < a.length; i++) {
            num += String(a[i]).charCodeAt();
        }
    }
    return Number(num);
}

export const setPageFields = (vfs, funtId) => {
    setPageFields.validFields = vfs;
    setPageFields.funtId = funtId;
    focusType.type = focusType.BeforeImage;
    focusType.modified = false;
}

export const focusType = {
    BeforeImage: BeforeImage,
    EnterObj: EnterObj,
    type:null,
    modified:false,
}

/**
 * 当前数据的验证是不是修改
 * @param currObj 当前对象
 * @param sub 子列表字段名
 * @param validFields 指定验证字段集
 * @returns {boolean} false 代表无修改 true 代表有修改
 */
export const validObject = (currObj, sub, validFields) => {
    let fields = (currObj.validFields || []).concat(validFields || getValidFields(setPageFields.funtId, sub) || setPageFields.validFields);
    if (NOT_VALID == fields) { // 不校验
        return true;
    }
    if (currObj.hasOwnProperty(CUST_VALID) && !fields.includes(CUST_VALID)) {
        fields.unshift(CUST_VALID);
    }
    let result = false;
    let enterObj = enterObjects[currObj[EnterObj]] || {};
    let beforeObj = currObj[BeforeImage];
    let field = null, type = null;
    let obj = null, enter = null, before = null;
    for (let i = 0; i < fields.length; i++) {
        field = fields[i];
        if (!currObj.hasOwnProperty(field) && !currObj.length) { // currObj 为对象
            continue;
        }
        if (CUST_VALID == field) {
            result = currObj[field](currObj, validObject);
            if (result) { // 已经在 Custom Validate 检验到有变化，直接跳出循环结束
                break;
            }
            continue;
        }
        obj = currObj[field] == null ? "" : currObj[field];
        enter = enterObj[field] == null ? "" : enterObj[field];
        before = beforeObj?.[field] == null ? "" : beforeObj[field];
        if (focusType.type == focusType.BeforeImage
                && beforeObj != null
                && obj == before) { // 当处于 BeforeImage 的状态下，才做 BeforeImage 的验证
            if (!focusType.modified && obj != enter) { // 当把数据还原成原数据，需要标识做过修改
                focusType.modified = true;
            }
            continue;
        }
        if (obj == enter) { // 二次进入未修改的
            focusType.type = focusType.EnterObj;
            continue;
        }
        type = typeofObj(obj);
        if (type == typeofObj.BASE ) {
            result = obj.toString() != enter.toString(); // 已修改的基本类型
        } else if (type == typeofObj.ARRAY && obj.length > 0) { // 一般是字Grid的数据
            type = typeofObj(obj[0]);
            if (type == typeofObj.BASE) { // 多选的数据
                // 重新排序
                obj = obj.sort((a, b) => {
                    // 使用相同的排序规则
                    return letter2Num(a) - letter2Num(b);
                });
                if (focusType.type == focusType.BeforeImage && beforeObj != null) {
                    before = (before || []).sort((a, b) => {
                        // 使用相同的排序规则
                        return letter2Num(a) - letter2Num(b);
                    });
                    result = obj.join() != before?.join();
                }
                if (!result) {
                    enter = (enter || []).concat().sort((a, b) => {
                        // 使用相同的排序规则
                        return letter2Num(a) - letter2Num(b);
                    });
                    result = obj.join() != enter.join();
                    if (!result) { // 相同的时候
                        focusType.type = focusType.EnterObj;
                    }
                }
            } else if (type == typeofObj.OBJECT) { // 主要场景是子Grid数据
                for (let row in obj) {
                    row = obj[row];
                    if (row.rowIgnore) { // 列表里的数据要忽略参与校验，新增的数据未保存前又删除
                        continue;
                    }
                    if (row.isCreate || row.isDelete) { // 列表里的数据是新增的或已删除
                        result = true;
                        break;
                    }
                    let vfs = null;
                    if (CUST_LIST == field) {
                        vfs = cloneObj(validFields);
                        vfs.splice(i, 1);
                    }
                    type = focusType.type;
                    result = validObject(row, field, vfs);
                    if (!result
                        && row.recordStatus == 'A'
                        && row[BeforeImage] == null
                        && type == focusType.BeforeImage
                        && focusType.type == focusType.EnterObj) { // 当 Grid 记录状态为 'A' 且没有 BeforeImage 信息，数据无变化，则代表未修改
                        focusType.type = type;
                    }
                    if (result) {
                        break;
                    }
                }
            }
        } else if (type == typeofObj.OBJECT) { // 应对多个对象独立对象 预留应对
            if (Object.keys(obj).length > 3 || obj.hasOwnProperty("validFields")) { // 应对多对象循环使用
                result = validObject(obj, field);
            }
        }
        if (result) { // 当比较结束有 true 的结果时，证明已被修改了，直接结束。
            break;
        }
    }
    if (currObj.hasOwnProperty(END_HANDLE)) { // 应用于在做 CUST_VALID 验证后，需要做后继事情
        currObj[END_HANDLE](currObj);
    }
    return result;
}
