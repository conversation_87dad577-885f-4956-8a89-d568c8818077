<template>
    <div id="detail-dialog" :class="{ 'bold-labels': isBold }"
        style="position: initial; width: 100%; height: 100%;">
        <el-dialog v-model="dialogFormVisible" title="" width="100%" :modal="false" :show-close="false"
            :close-on-click-modal="false" :close-on-press-escape="false" :destroy-on-close="true" class
            modal-class="details-dialog">
            <el-row :gutter="24" style="margin: 0px; padding: 3px 10px; border-bottom: 2px solid lightgrey;">
                <el-col :span="10" style="padding:0px; ">
                    <el-space>
                        <el-button :icon="ArrowLeftBold" type="primary" @click="hideDetails" />
                        <el-text style="color: #b31a25;font-weight: bold;" class="mx-1" size="large">{{
                            $t($currentInfoStore.getHeaderTitle)
                        }}</el-text>
                    </el-space>
                </el-col>
                <el-col :span="10" :offset="4" style="padding:0px;">
                    <el-space class="detail-buttons" style="float:right;">
                        <ApproveButton :mkckRow="mkckRow" :handleCancel="handleCancel" :beforeApprove="beforeApprove" :limitCheck="limitCheck"></ApproveButton>
                        <RejectButton :mkckRow="mkckRow" :handleCancel="handleCancel"></RejectButton>
                        <SubmitButton :mkckRow="mkckRow" :eventOid="eventOid" :mkckAction="mkckAction"  :funcId="funcId" :handleCancel="handleCancel"
                            :isSave="isSave" :handleSaveAndSubmit="handleSaveAndSubmit" :beforeSubmit="beforeSubmit"
                            v-if="$currentInfoStore.currentPermission['Edit'] && !mkckRow.isDoubleCheck && !mkckRow.recordStatus?.startsWith('PA')"></SubmitButton>
                        <CButtons :action="'Edit'" type="info" @click="handleWithdraw"
                            v-if="mkckRow?.recordStatus === 'R' || mkckRow?.recordStatus === 'PD'">
                            Withdraw
                        </CButtons>
                        <!-- Start SK-COMMON-0053 Lisy 2024/08/12 -->
                        <CButtons :action="'Edit'" v-if="$currentInfoStore.currentPermission['Edit'] && props.handleSave && !mkckRow.isDoubleCheck && !mkckRow?.recordStatus?.startsWith('PA')" type="primary" @click="handleSave">
                            Save
                        </CButtons>
                        <!-- End SK-COMMON-0053 Lisy 2024/08/12 -->
                        <el-button close :icon="CloseBold" type="primary" @click="goHome" />
                    </el-space>
                </el-col>
            </el-row>
            <el-container style="height: 100%;">
                <el-header>
                    <RemarkForm :mkckRemark="mkckRemark" :viewOriginalForm="viewOriginalForm"
                        v-if="mkckRow?.recordStatus?.startsWith('PA') || mkckRow?.recordStatus === 'R'">
                    </RemarkForm>
                </el-header>
                <el-main>
                    <div style="padding: 10px;">
                        <slot></slot>
                    </div>
                </el-main>
                <el-footer>
                    <el-space class="detail-buttons" style="float:right;margin-bottom: 10px;margin-right: 10px;">
                        <ApproveButton :mkckRow="mkckRow" :handleCancel="handleCancel" :beforeApprove="beforeApprove" :limitCheck="limitCheck"></ApproveButton>
                        <RejectButton :mkckRow="mkckRow" :handleCancel="handleCancel"></RejectButton>
                        <SubmitButton :mkckRow="mkckRow" :eventOid="eventOid" :funcId="funcId" :mkckAction="mkckAction" :handleCancel="handleCancel"
                            :isSave="isSave" :handleSaveAndSubmit="handleSaveAndSubmit" :beforeSubmit="beforeSubmit"
                            v-if="$currentInfoStore.currentPermission['Edit'] && !mkckRow.isDoubleCheck && !mkckRow.recordStatus?.startsWith('PA')"></SubmitButton>
                        <CButtons :action="'Edit'" type="info" @click="handleWithdraw"
                            v-if="mkckRow?.recordStatus === 'R' || mkckRow?.recordStatus === 'PD'">
                            Withdraw
                        </CButtons>
                        <!-- Start HuZhongBin 2025/04/01, TBC代码，之后需要删除 -->
                        <CButtons :action="'Edit'" type="info" @click="handleTemplateApproe"
                            v-if="currentRow?.recordStatus === 'PD'">
                            Appove(Test)
                        </CButtons>
                        <!-- End HuZhongBin 2025/04/01, TBC代码，之后需要删除 -->
                        <!-- Start SK-COMMON-0053 Lisy 2024/08/12 -->
                        <CButtons :action="'Edit'" v-if="$currentInfoStore.currentPermission['Edit'] && props.handleSave && !mkckRow.isDoubleCheck && !mkckRow.recordStatus?.startsWith('PA')" type="primary" @click="handleSave">
                            Save
                        </CButtons>
                        <!-- End SK-COMMON-0053 Lisy 2024/08/12 -->
                        <el-button close style="visibility: hidden;" :icon="CloseBold" type="primary" />
                    </el-space>
                    <div style="width: 100%;display: flex;flex-flow: wrap;border-top: 2px solid #b31a25;" v-if="showFooter"></div>
                    <el-row style="margin: 0px;" :gutter="40" v-if="showFooter">
                        &nbsp;&nbsp;
                        <el-col :span="4">
                            <ElFormItemProxy label="Last Modify Date">
                                <el-input v-if="currentRow.updateDate != null" :value=currentRow?.updateDate disabled>
                                </el-input>
                                <el-input v-else :value=currentRow?.sysUpdateDate disabled>
                                </el-input>
                            </ElFormItemProxy>
                        </el-col>
                        <el-col :span="4">
                            <ElFormItemProxy label="Last Modified By">
                                <el-input v-if="currentRow.updateBy != null " :value=currentRow?.updateBy disabled>
                                </el-input>
                                <el-input v-else :value=currentRow?.sysUpdater disabled>
                                </el-input>
                            </ElFormItemProxy>
                        </el-col>
                        <el-col :span="4">
                            <ElFormItemProxy label="Last Approval Date">
                                <el-input :value=mkckRemark?.sysCreateDt disabled>
                                </el-input>
                            </ElFormItemProxy>
                        </el-col>

                        <el-col :span="4">
                            <ElFormItemProxy label="Last Approved By (1)">
                                <el-input :value=mkckRemark?.lastApproveUserId disabled>
                                </el-input>
                            </ElFormItemProxy>
                        </el-col>

                        <el-col :span="4">
                            <ElFormItemProxy label="Last Approved By (2)">
                                <el-input :value=mkckRemark?.approveUserId1 disabled>
                                </el-input>
                            </ElFormItemProxy>

                        </el-col>
                    </el-row>
                </el-footer>
            </el-container>
            <template #footer>
            </template>
        </el-dialog>

    </div>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted, computed, watch, getCurrentInstance,nextTick } from 'vue'
import {
    CloseBold,
    ArrowLeftBold
} from '@element-plus/icons-vue'
import axios from 'axios';
import { useRouter } from 'vue-router';
import ElementPlus, { ElMessage, ElMessageBox } from 'element-plus';
import type { Action } from 'element-plus'
import { getCommonDesc, getRecordStatusDesc, randomString, setInputLabel, validSearchInputValue, validDateItemValue, currentPageInfo } from '~/util/Function.js';
import  { commonRules, addCommonTxtRule } from '~/util/Validators.js';
import { typeofObj, validObject, setPageFields, addEnterObj, focusType, clearEnterObjects } from '~/util/ModifiedValidate.js';
import { useRoute } from 'vue-router';
const props = defineProps(['handleSave', 'handleSubmit', 'reload', 'viewOriginalForm', 'form', 'beforeSubmit','beforeSave' ,'showFooter', 'handleSaveAndSubmit', 'handleTempleateApprove', 'beforeSaveValid']);
const { proxy } = getCurrentInstance()
const dialogFormVisible = ref(false);
const _currRow = ref({});
const currentRow = computed({
  get() {
    return _currRow.value;
  },
  set(v) {
    _currRow.value = v;
    addEnterObj (v)
  }
});
const formData = props.form;
const detailForm = ref();
const mkckRow = ref({});
const eventOid = ref('');
const mkckAction = ref(null);
const funcId = ref('');
const mkckRemark = ref('');
const isDisabled = ref(false);
const router = useRouter();
const isBold = ref(true);
const isSave = ref(false);
// Start SK-COMMON-0053 Lisy 2024/08/12 
//const showSaveBtn = ref(false);
// End SK-COMMON-0053 Lisy 2024/08/12 
const showFooter = ref(true);
const isNewRecord = ref(false);
let input = {};
watch(() => currentRow.value, (newVal) => {
    if (dialogFormVisible.value) {
        let funcId = proxy.$currentInfoStore.getCurrentFuncId();
        if (funcId&&funcId!='ALL') {
            initMkck(currentRow.value, currentRow.value?.currentOid, funcId);
            showFooter.value = props.showFooter==false?false:true;
        }
    }
    // Start SK-COMMON-0053 Lisy 2024/08/12 
    if (currentRow.value?.currentOid) {
        isNewRecord.value = false;
    }
    // End SK-COMMON-0053 Lisy 2024/08/12 
});
watch(() => dialogFormVisible.value, (newVal) => {
    proxy.$currentInfoStore.setIsEditMain(newVal);
    let main = document.getElementById("detail-dialog");
    if (main) {
        // 查找有效的上一个兄弟元素节点
        let previousSibling = main.previousSibling;
        while (previousSibling && previousSibling.nodeType !== 1) {
            previousSibling = previousSibling.previousSibling;
        }

        if (newVal) {
            if (previousSibling) {
                previousSibling.style.display = "none";
            }
            main.style.display = "";
        } else {
            if (previousSibling) {
                previousSibling.style.display = "";
            }
            main.style.display = "none";
        }
    }
});

const showDetails = async (row, disabled) => {
    ElMessage.closeAll();
    mkckRow.value = {};
    currentRow.value = {};
    // Start SK-COMMON-0053 Lisy 2024/08/12 
    isNewRecord.value = true;
    // End SK-COMMON-0053 Lisy 2024/08/12 
    clearEnterObjects();
    isSave.value = false;
    dialogFormVisible.value = true;
    eventOid.value = null;
    mkckAction.value = null;
    isDisabled.value = disabled;
    proxy.$currentInfoStore.setCurrentUUID( randomString());
    // Start SK-COMMON-0053 Lisy 2024/08/12
//    showSaveBtn.value = false;    
    // End SK-COMMON-0053 Lisy 2024/08/12 
    userHasChangeInfo.value = true;
    finishLoadWatchData.value = false;
  //  watchObj.value = {};
    currentPageInfo.setDetailPageStatus(row);
    // Start SK-COMMON-0053 Lisy 2024/08/12
//    setTimeout(()=>{
//        if (!disabled) {
//            handleShowSaveBtn();
//        }
//    },500);
    // End SK-COMMON-0053 Lisy 2024/08/12 

    setTimeout(() => {
        init();
    }, 1000);
}

const viewOriginalForm = (pendingOid, isDisabled, isRejectEdit) => {
    // Start SK-COMMON-0053 Lisy 2024/08/12
//    if(isRejectEdit){
//        showSaveBtn.value = !isDisabled;
//    }
    // End SK-COMMON-0053 Lisy 2024/08/12 
    props.viewOriginalForm&&props.viewOriginalForm(pendingOid, isDisabled);
    // View Origi 时把高亮颜色去掉
    /*
    let yellows = document.querySelectorAll(".lightyellow");
    if (yellows.length) {
        for(let i = 0; i < yellows.length; i++) {
            yellows[i].className = yellows[i].className.replaceAll("lightyellow", " unlightyellow");
        }
    } else {
        yellows = document.querySelectorAll(".unlightyellow");
        if (yellows.length) {
            for(let i = 0; i < yellows.length; i++) {
                yellows[i].className = yellows[i].className.replaceAll("unlightyellow", " lightyellow");
            }
        } 
    }
    */
}

const initMkck = (row, eventOidValue, funcIdValue) => {
    //debugger;
    //row.mkckOid="36";
    mkckRow.value = row;
    mkckRow.value.isDoubleCheck = isDisabled.value
    //projection adjustment 一个视图对应六个表需要做审批
    if (row?.eventName == "txn_billing") {
        funcIdValue = "CSSCL_CASHM005_1";
    } else if (row?.eventName == "txn_cash_instr") {
        funcIdValue = "CSSCL_CASHM005_2";
    } else if (row?.eventName == "txn_csdr") {
        funcIdValue = "CSSCL_CASHM005_3";
    } else if (row?.eventName == "txn_ca_tax") {
        funcIdValue = "CSSCL_CASHM005_4";
    }else if (row?.eventName == "txn_settle_instr") {
        funcIdValue = "CSSCL_CASHM005_5";
    }else if (row?.eventName == "txn_ca_entitle_dtl") {
        funcIdValue = "CSSCL_CASHM005_6";
    }
    eventOid.value = eventOidValue;
    funcId.value = funcIdValue;
    mkckAction.value = row.mkckAction;

    getRemark(row);
    if("CSSCL_CASHM005"==funcIdValue){
        funcIdValue=row.eventName;
    }
    // let oid = row.pendingOid ? row.pendingOid : row.currentOid;
    // // 进来之后就要锁起来
    // if(!isDisabled.value && oid!=null &&(row.recordStatus == 'R' || row.recordStatus == 'PD' || row.recordStatus == 'A')){
    //     proxy.$axios.post('/datamgmt/api/v1/handler/lock' ,{
    //         eventName: funcIdValue,
    //         eventOid:oid,
    //         recordStatus:row.recordStatus,
    //         version:row.version,
    //         flag:true,
    //         recordOid: eventOidValue,
    //     });   
    // }
    
}

function getRemark(row) {
    if (row?.mkckOid) {
        axios.get("/datamgmt/api/v1/makerchecker/remark?mkckOid=" + row.mkckOid).then((body) => {
            if (body.success) {
                mkckRemark.value = body.data;
                if (body.data?.recordStatus) {
                    if(body.data?.recordStatus === 'R'){
                        if(body.data?.approveUserId1){
                        } else {
                            mkckRemark.value.sysCreateDt = "";
                            mkckRemark.value.lastApproveUserId = "";
                        }
                        mkckRemark.value.approveUserId1 = "";
                    }
                    mkckRemark.value.recordStatusDesc = getRecordStatusDesc(body.data);
                }
            }
        });
    } else {
        //no mkckOid, need do clean cache
        mkckRemark.value = {};
    }
}
const route = useRoute();
const hideDetails = async () => {
    let oid = currentRow.value?.pendingOid != null ? currentRow.value?.pendingOid : currentRow.value?.currentOid;


    if (isDisabled.value) {
        proxy.$axios.post('/datamgmt/api/v1/makerchecker/unlock', {
             flowMakerCheckerOid: currentRow.value?.mkckOid,
        });

        proxy.$axios.post('/datamgmt/api/v1/handler/unlock', {
                        flag: true,
          });
        if (route.path === '/') {
            dialogFormVisible.value = false;
            proxy.$currentInfoStore.setHeaderTitle('menu.MENU_DASHBOARD')
            props.reload && props.reload();
        } else {
            dialogFormVisible.value = false;
            props.reload && props.reload();
        }
    } else {
        ElMessageBox.confirm(
            'Confirm to exit maintenance?',
            'Warning',
            {
                confirmButtonText: 'OK',
                cancelButtonText: 'Cancel',
                type: 'warning',
            })
            .then(() => {
                proxy.$axios.post('/datamgmt/api/v1/makerchecker/unlock', {
                    flowMakerCheckerOid: currentRow.value?.mkckOid,
                });
                
                proxy.$axios.post('/datamgmt/api/v1/handler/unlock', {
                                flag: true,
                });
                dialogFormVisible.value = false;
                currentPageInfo.setSearchPage();
                props.reload && props.reload();
            })
            .catch(() => {

            })
    }
}
const beforeSaveValid = (str) => {
  let sts = (currentRow.value.recordStatus || 'A');
  if(!isNewRecord.value && !sts.startsWith("PA") && currentRow.value.mkckAction != 'D'){
    setPageFields(validFields, funcId.value);
    let result = validObject(currentRow.value)
    if (!result) {
      if (focusType.type == focusType.BeforeImage) {
        ElMessage({
          message: "Record CANNOT be " + str + " without data modified.",
          type: 'error',
          duration: 10000,
          offset: 100,
          showClose: true,
        });
        return false;
      }
      if (focusType.type == focusType.EnterObj && !focusType.modified && (str == 'saved' || (str == 'submitted' && sts == 'A'))) {
        ElMessage({
          message: proxy.$t('message.data.not.modify'),
          type: 'error',
          duration: 10000,
          offset: 100,
          showClose: true,
        });
        return false;
      }
    }
  }
  return true;
}
const handleSave = async () => {
    ElMessage.closeAll();

    // 优先使用子组件提供的 beforeSaveValid 方法
    if (props.beforeSaveValid) {
        if (!props.beforeSaveValid('saved')) {
            return;
        }
    } else if (!beforeSaveValid('saved')) {
        return;
    }

    if(props.beforeSave){
        let ret = await props.beforeSave();
        if (typeof ret === 'boolean') {
            if (ret === false) {
                return false;
            }
        }
    }
    await handleSave1();
}

// This method is out of use
const handleDataToUpper = (formData) => {
    // Start SK-COMMON-0191 20240924 LiShaoyi
    // let exp = formData?.["expField"];
    // let form = formData?.["form"];
    // for (let key in form) {
    //     if (exp?.includes(key) || !isNaN(form[key]) || !isNaN(Date.parse(form[key])) || typeof (form[key]) == 'object') {
    //         continue;
    //     }
    //     form[key] = form[key]?.toUpperCase();
    // }
    // End SK-COMMON-0191 20240924 LiShaoyi
}

const validInputedValue = async () => {
    let seaRet = await validSearchInputValue();
    seaRet = validDateItemValue() == false ? false : seaRet;
    return seaRet;
}

const handleSave1 = async () => {
    let seaRet = await validInputedValue();
    let inpRet = true;
    if (inpRet && props.handleSave) {
    	// Start SK-COMMON-0191 20240924 LiShaoyi
	// Remove uppercase of show content, remove auto-uppercase of save
       // handleDataToUpper(formData);
    	// End SK-COMMON-0191 20240924 LiShaoyi
        inpRet = await props.handleSave(seaRet, false);
    }
    if (seaRet && inpRet) {
        //Start SIR-Cristin-R047,SIR-Cristin-R043 AMOR,20240816
       let oid = eventOid.value!=null?eventOid.value:currentRow.value.currentOid;
       await proxy.$axios.get("/datamgmt/api/v1/handler?eventName=" + funcId.value + "&eventOid=" +oid + "&userName=" + "");
       //End SIR-Cristin-R047,SIR-Cristin-R043 AMOR,20240816
        proxy.$currentInfoStore.setCurrentUUID( randomString());
        isSave.value = true;
        ElMessageBox.alert("Save successfully.", 'Success', {
            confirmButtonText: 'OK',
            type: 'success',
            callback: (action: Action) => {
                // dialogFormVisible.value = false;
                // props.reload && props.reload();
            }
        });
    }
}
const userHasChangeInfo = ref(true);
//const watchObj = ref({});
const finishLoadWatchData = ref(false);
// watch( ()=>watchObj, (nv, lv) => {
//     if(finishLoadWatchData.value){
//         userHasChangeInfo.value = true;
//     }
// } ,{ deep: true});

const excludeFileds = ['oid', 'pid'];
// watch( ()=>detailForm, (nv, lv) => {
//     if (detailForm.value) {
//         let rulesArr = typeof(detailForm.value.rules) == 'function' ? detailForm.value.rules() : detailForm.value.rules;
//         if (rulesArr) {
//             for (let idx in rulesArr) {
//                 let ruleObj = rulesArr[idx];
//                 let form =  ruleObj.form ? ruleObj.form : detailForm.value.form;
//                 let rules = ruleObj.rules;
//                 for (let key in form) {
//                     if (excludeFileds.includes(key)) {
//                         continue;
//                     }
//                     let tf = typeof(form[key]);
//                     if (tf == 'string' || tf == 'number') {
//                         if ( !rules[key] ){
//                             rules[key] = [];
//                         }   
//                         // console.log(rules[key]);
//                         // if (!rules[key].includes(commonRules.name)) {
//                         //     rules[key].push(commonRules.name);
//                         // }
//                     }
//                 }
//             }
//         }
//     }
//     // console.log(detailForm.value)
// } ,{ deep: true});
const validFields = [];
const addValidField = (field) => {
  if (typeofObj(field) == typeofObj.ARRAY) {
    for (let i = 0; i < field.length; i++) {
      addValidField(field[i]);
    }
  } else {
    if (!validFields.includes(field)) {
      validFields.push(field);
    }
  }
}
const initWatch = (obj, ruleForm) => {
    // userHasChangeInfo.value = false;
    // watchObj.value = obj;
    // if (ruleForm) {
    //     detailForm.value = ruleForm;
    // } else {
    //     detailForm.value = obj;
    // }
    // nextTick(() => {
    //     finishLoadWatchData.value = true;
    // });
}
const beforeSubmit = async () => {
    ElMessage.closeAll();

    if (!(currentRow.value?.recordStatus?.startsWith('PA'))) {
      if (props.beforeSaveValid) {
        if (!props.beforeSaveValid('submitted')) {
          return false;
        }
      } else if (!beforeSaveValid('submitted')) {
        return false;
      }
    }

    let seaRet = await validInputedValue();
    if (!seaRet) {
        return false;
    }
    if (props.handleSave){
        let ret = await props.handleSave(true, true);
        if (typeof ret === 'boolean') {
            if (ret === false) {
                return false;
            }
        }
    }
    if(props.beforeSubmit){
        let ret = await props.beforeSubmit();
        if (typeof ret === 'boolean') {
            if (ret === false) {
                return false;
            }
        } else if (ret) {
            ElMessageBox.alert(ret, 'Warning', {
                confirmButtonText: 'OK',
                type: 'warning',
            });
            return false;
        }
    }
    return true;
}

const handleSaveAndSubmit = async () => {
    {
        let inpRet = true;
        if (props.handleSave){
            // Start SK-COMMON-0191 20240924 LiShaoyi
	    // handleDataToUpper(formData);
	    // End SK-COMMON-0191 20240924 LiShaoyi
            inpRet = await props.handleSave(true, false, true);
        } else if (props.handleSubmit) {
            inpRet = await props.handleSubmit(true, false, true);
        }
        if (inpRet) {
            //不弹框
            isSave.value = true;
            //return inpRet.data;
            // return eventOid.value;
            return  true;
        }
        return false;
    }
}
const handleTemplateApproe = async () => {
    const result = await props.handleTempleateApprove();
    if(typeof result === 'boolean' && result === true){
        handleCancel();
    }
}
const handleWithdraw = () => {
    ElMessageBox.confirm(
        'Confirm to withdraw?',
        'Warning',
        {
            confirmButtonText: 'OK',
            cancelButtonText: 'Cancel',
            type: 'warning',
        }
    )
        .then(() => {
            proxy.$axios.post('/datamgmt/api/v1/handler/unlock',{
                flag:false
            });  
            handleWithdraw1();
        })
        .catch(() => {

        })
}
const handleWithdraw1 = async () => {
  // TODO: CA不适用这块逻辑
    // const response = await axios.post('/datamgmt/api/v1/makerchecker/withdraw', {
    //     flowMakerCheckerOid: mkckRow.value.mkckOid,
    //     eventOid: eventOid.value,
    //     funcId: funcId.value,
    // });

    dialogFormVisible.value = false;
    props.reload && props.reload();
}

const handleCancel = () => {
    dialogFormVisible.value = false;
    props.reload && props.reload();
}
// Start SK-COMMON-0053 Lisy 2024/08/12 
//const showBtn = () => {
//    //PA状态不显示
//if (mkckRow.value === 'PA' || mkckRow.value === 'PA1' ||mkckRow.value === 'PA2') {
//        return false;
//    }
//
//    if (isDisabled.value && (mkckRow.value.recordStatus === 'PD' || mkckRow.value.recordStatus === 'A' || mkckRow.value.recordStatus === 'R')) {
//        return false;
//    }
//
//    if (mkckRow.value.recordStatus !== 'PA' && mkckRow.value.recordStatus !== 'PA1' && mkckRow.value.recordStatus !== 'PA2') {
//        return true;
//    }
//    return false;
//}
//const handleShowSaveBtn = () => {
//    showSaveBtn.value = false;
//    if(props.handleSave){
//        setTimeout(()=>{
//            isNewRecord.value = mkckRow.value.recordStatus ? false : true;
//            showSaveBtn.value = showBtn();
//        }, (mkckRow.value.recordStatus ? 100 : 1000));
//    }
//    //return false;
//}
// End SK-COMMON-0053 Lisy 2024/08/12 

const goHome = () => {
    let recordStatus = currentRow.value?.recordStatus;
    let oid = currentRow.value?.pendingOid != null ? currentRow.value?.pendingOid : currentRow.value?.currentOid;
    if (isDisabled.value) {
        proxy.$axios.post('/datamgmt/api/v1/makerchecker/unlock', {
            flowMakerCheckerOid: mkckRow.value?.mkckOid,
        });
        proxy.$axios.post('/datamgmt/api/v1/handler/unlock', {
                  flag: true,
              });
 
        dialogFormVisible.value = false;
        router.push({ path: "/" });
        proxy.$currentInfoStore.setHeaderTitle( 'menu.MENU_DASHBOARD')
        props.reload && props.reload();
    } else {
        ElMessageBox.confirm(
            'Confirm to exit maintenance?',
            'Warning',
            {
                confirmButtonText: 'OK',
                cancelButtonText: 'Cancel',
                type: 'warning',
            }
        )
            .then(() => {
                // if(recordStatus == 'R' ||recordStatus == 'PD' ||recordStatus == 'A'){
                //     proxy.$axios.post('/datamgmt/api/v1/handler/unlock',{
                //         eventName: funcId.value,
                //         eventOid:oid,
                //         flag:false
                //     });  
                // }
                proxy.$axios.post('/datamgmt/api/v1/handler/unlock',{
                        eventName: funcId.value,
                        eventOid:oid,
                        flag:false
                    });  
                proxy.$axios.post('/datamgmt/api/v1/makerchecker/unlock', {
                    flowMakerCheckerOid: mkckRow.value?.mkckOid,
                });
               
                dialogFormVisible.value = false;
                router.push({ path: "/" });
                proxy.$currentInfoStore.setHeaderTitle( 'menu.MENU_DASHBOARD')
                props.reload && props.reload();
            })
            .catch(() => {

            })
    }

}

const fields = ref({});
const addField = (code, desc) => {
    fields.value[code] = desc;
}
//Start SIR-Cristin-R047,SIR-Cristin-R043 AMOR,20240816
// const writebackId = (id) => {
const writebackId =async (id) => {
//Start SIR-Cristin-R047,SIR-Cristin-R043 AMOR,20240816
    eventOid.value = id;
}

// Start R2411A-52106, Tom.Li, 2024/09/02
const beforeApprove = async ()=>{
    let funcId = proxy.$currentInfoStore.getCurrentFuncId();
    if (funcId=='CSSCL_CASHM001'||funcId=='CSSCL_CASHM002') {
        if (currentRow.value?.processStatus == 'M') {
            return true;
        }
    }
    return await beforeSubmit();
}

const limitCheck = async ()=>{
    let funcId = proxy.$currentInfoStore.getCurrentFuncId();
    if(funcId=="CSSCL_CASHM001" ){
       let result =  await proxy.$axios.post('/cashmgmt/api/v1/bankstmt/reconciliation/cust/book/list/check', {
        reconBankStmtOid: currentRow.value?.currentOid,
        reconBankStmtPendingOid: currentRow.value?.pendingOid
            });
        if(result.data) {
            return true;
        }
    }
    if(funcId=="CSSCL_CASHM002"){
        let result = await proxy.$axios.get("/datamgmt/api/v1/data/cashinstr/appr/check?objectId=" + currentRow.value?.currentOid);
        if(result.data) {
            console.log(result);
            return true;
        }
    }
    return false;
}
// Start R2411A-52106, Tom.Li, 2024/09/02

defineExpose({
    hideDetails,
    showDetails,
    currentRow,
    initMkck,
    mkckRow,
    viewOriginalForm,
    fields,
    addValidField,
    addField,
    writebackId,
    handleSaveAndSubmit,
    initWatch,
    handleCancel,
});

const init = () => {
    setInputLabel(detailForm.value, null, null,".details-dialog ");
}

</script>
<style scoped>

.button-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}

.bold-labels>>>.ep-form-item__label {
    font-weight: normal !important;
}
</style>

<style>
.details-dialog {
    top: 60px;
    inset: unset !important;
    position: initial !important;
    width: 100%;
    height: max-content;
    min-height: -webkit-fill-available;
}

.details-dialog .ep-overlay-dialog .ep-dialog {
    margin: 0px;
    height: max-content;
    padding: 0px;
    min-width: 1700px;
    min-height: -webkit-fill-available;
}

.details-dialog .ep-overlay-dialog .ep-dialog .ep-dialog__body {
    min-width: 1700px;
}

.details-dialog .ep-overlay-dialog {
    position: initial;
    overflow: hidden;
    top: unset;
    right: unset;
    bottom: unset;
    height: max-content;
    min-height: -webkit-fill-available;
}

.details-dialog .ep-overlay-dialog .ep-dialog>header {
    display: none;
}

.details-dialog form {
    max-width: 1880px;
}

.details-dialog .dialog-footer {
    float: right;
}

.grid-continer form {
    max-width: none;
}
</style>
