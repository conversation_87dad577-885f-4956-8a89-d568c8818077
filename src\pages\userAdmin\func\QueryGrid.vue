<template>
    <el-row :gutter="24" class="demo-form-inline" style="margin: 0px;padding-block: 5px;">
      <el-col :span="18" style="padding:0px;">
        <div style="width: 100%;display: table;border-bottom: 2px solid #e6e6e6;min-height: 26px;">
          <div style="color: lightgray; font-weight: bold;display: table-cell;width: 60px;align-content: center;"> Order By: </div>
          <el-space
            style="color: lightgray; width: calc(100% - 60px); padding-bottom: 2px;">
            <el-tag v-for="(tag, index) in orderByDesc" :key="tag.code" closable type="info" @close="deleteOrder(tag)">
              {{ tag.name + " " + tag.order }}
            </el-tag> </el-space>
        </div>
      </el-col>
      <el-col :span="6">
      </el-col>
    </el-row>
    <el-form>
    <el-pagination class="query-pagination" :pager-count="5" v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[15, 25, 50, 100]"
      layout="sizes, , jumper, prev, pager, next, ->, slot" v-model:total="total" @current-change="handleChange" @size-change="handleChange"
      style="background-color: lightgrey;padding-inline: 10px;" >
      Total {{ total }} records
    </el-pagination>
    </el-form>
    <el-table border :data="tableData" table-layout="fixed" @row-dblclick="handleDbClick" @sort-change="handleSort" ref="tableRef"
      :header-cell-class-name="(params: any) => { setHeaderClass(params) }">
      <slot name="tableColumn"></slot>
    </el-table>
    <el-form>
    <el-pagination class="query-pagination" :pager-count="5" v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[15, 25, 50, 100]"
      layout="sizes, |, jumper, prev, pager, next, ->, slot" v-model:total="total" @current-change="handleChange" @size-change="handleChange"
      style="background-color: lightgrey;padding-inline: 10px;" >
      Total {{ total }} records
    </el-pagination>
    </el-form>
  </template>
  
  <script lang="ts" setup>
  import { ref, reactive, getCurrentInstance } from 'vue'
  import type { FormInstance } from 'element-plus'
  import { ElMessageBox, ElMessage } from 'element-plus';
  import { useRouter } from 'vue-router';
  import { useCookies } from "vue3-cookies";
  
  const router = useRouter()
  const { cookies } = useCookies()
  const { proxy } = getCurrentInstance()
  const formRef = ref<FormInstance>()
  const props = defineProps(['url', 'params', 'searchParams', 'showDetails', 'sortProp', 'beforeSearch', 'afterSearch']);
  const currentPage = ref(1);
  const pageSize = ref(15);
  const total = ref(0);
  
  const tableRef = ref();
  const tableData = ref([]);
  const param = ref({});
  const orderBy = ref("");
  const orderByDesc = ref([]);
  const sortField = ref({});
  const sortFieldName = ref({});
  
  const formInline = reactive(props.searchParams);
  
  const baseLoad = async () => {
    const msg = await proxy.$axios.post(props.url, {
      param: {
        ...param.value,
        ...props.params,
      },
      current: currentPage.value,
      pageSize: pageSize.value,
      orderBy: orderBy.value,
    });
    if (msg.success) {
      total.value = msg.data.total;
      currentPage.value = msg.data.page;
      tableData.value = msg.data.data;
      props.afterSearch && props.afterSearch(param.value, tableData.value);
    }
  }
  const load = async () => {
    baseLoad();
  }
  
  const onSearch = (p) => {
    param.value = p;
    props.beforeSearch && props.beforeSearch(param.value);
    load();
  }
  const onReset = (ref) => {
    orderBy.value = "";
    orderByDesc.value = [];
    sortField.value = {};
    sortFieldName.value = {};
    if (ref) {
      ref.resetFields();
      tableRef.value.clearSort();
    }
  }
  const handleChange = () => {
    load();
  }
  const handleSort = (obj) => {
    let sts = {
      ...sortField.value
    };
    let stsName = {
      ...sortFieldName.value
    };
    if (sts[obj.prop] && !obj.order) {
      delete sts[obj.prop];
      delete stsName[obj.prop];
    } else {
      sts[obj.prop] = obj.order;
      stsName[obj.prop] = obj.column.label;
    }
    sortField.value = sts;
    sortFieldName.value = stsName;
    changeSort(sts, stsName);
    load();
  }

  const handleDbClick = (row) => {
    props.showDetails(row, true);
  }
  
  const setHeaderClass = (params: any) => {
    params.column.order = sortField.value[params.column.property];
  }
  
  const changeSort = (sts, stsName) => {
    if (Object.keys(sts).length == 0) {
      orderBy.value = "";
      orderByDesc.value = [];
    } else {
      let obv = "";
      let obvNames = [];
      for (let key in sts) {
        obvNames.push({
          name: stsName[key],
          order: sts[key],
          code: key
        });
        if (props.sortProp && props.sortProp[key]) {
          let o = sts[key].charAt(0).toUpperCase();
          let d = "";
          for (let i = 0; i < props.sortProp[key].length; i++) {
            let ele = props.sortProp[key][i];
            d += ";" + ele + "-" + o;
          }
          obv += ";" + d.substring(1);
        } else {
          obv += ";" + key + "-" + sts[key].charAt(0).toUpperCase();
        }
      }
      orderBy.value = obv.substring(1);
      orderByDesc.value = obvNames;
    }
  }
  
  const deleteOrder = (tag) => {
    let sts = {
      ...sortField.value
    };
    let stsName = {
      ...sortFieldName.value
    };
    delete sts[tag.code];
    delete stsName[tag.code];
    sortField.value = sts;
    sortFieldName.value = stsName;
    changeSort(sts, stsName);
    console.log(sortField.value);
    load();
  }
  
  defineExpose({
    formInline,
    load,
    onSearch,
  })
  </script>
  
  <style>
  .demo-form-inline .el-input {
    --el-input-width: 220px;
  }
  
  .demo-form-inline {
    position: relative;
  }
  
  .demo-form-inline .el-select {
    --el-select-width: 220px;
  }

  .query-pagination>.btn-prev {
    margin-left: 15px !important;
  }
  </style>