<template>
    <div>
        <el-row style="width: 100%; margin: 0; padding: 0;">
            <el-table :data="showData" table-layout="auto" @row-click="handleClick"
                ref="gridRef" :cell-style="cellStyle" :border="true" scrollbar-always-on
                :style="'width: ' + ($attrs.tableWidth?$attrs.tableWidth:'calc(100% - 80px)') + '; margin: 0; padding: 0; float: left;' + $attrs.tableStyle"
                class-name="multiple-table" :row-class-name="tableRowClassName">
                <slot name="columns"></slot>
            </el-table>
            <el-space style="margin-left: 10px; float: left;" direction="vertical">
                <el-button :disabled="disabled" :icon="Plus" @click="addRecord" />
                <el-button :disabled="disabled" :icon="Minus" @click="deleteRecord" />
            </el-space>
        </el-row>
        <el-dialog v-model="searcDialogVisible" :modal="false" :close-on-click-modal="false"
            class="search-input" :close-on-press-escape="false" :destroy-on-close="true" draggable :show-close="false"
            modal-class="searchInput-dialog" @close="hideSearchDialog" append-to-body>

            <!-- 模拟框头部 -->
            <template #header>
                <div style="margin:0;padding: 0;background-color: var(--ep-color-primary);height: 32px; width:100%; ">
                <el-icon-close  @click="hideSearchDialog" style="height: 26px; width:26px; background-color: var(--ep-color-primary); border:none; color:#ffffff;float:right;margin: 3px 3px 0 0; cursor: pointer;" />
                <div style="margin: 0;padding: 0;" >
                    <div style="height:6px; width:100%;border: none;"></div>
                    <!-- 标题由外部传递 -->
                    <span style="color:#fff; font-size: 18px; height: 32px; padding: 10px; width:100% "> {{ props.title  }} </span>
                </div>
                </div>
            </template>

            <div class="searchInput-dialog-content">
                    <div class="searchInput-dialog-form-inline" >
                        <FormRow>
                            <!-- props: columns, disabled, onlyLetters; formInline -->
                            <ElFormItemProxy label-width="350" style="text-align: left;" v-for="(item) in columns" :label="item.title" >
                                <InputText clearable v-model="formInline[item.colName]"
                                    searchField="searchField"
                                    onlyLetters="false"
                                    maxlength="999"
                                    @input="clearInput()"
                                    style="width:280px; margin-top: 5px;" 
                                    :input-id="item.colName" aria-autocomplete="none">
                                </InputText>
                            </ElFormItemProxy>
                        </FormRow>
                    </div>

                    <div class="searchInput-dialog-result">
                        <el-form>
                            <!-- el-pagination: currentPage, pageSize, pageSizes, pagePaginationLayout, total, handleChange -->
                            <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="pageSizes"
                                :layout="pagePaginationLayout" v-model:total="total" @current-change="onSearch" @size-change="onSearch"
                                style="background-color: lightgrey;padding-inline: 10px;">
                                Total {{ total }} records
                            </el-pagination>
                        </el-form>
                        <!-- tableData -->
                        <el-table ref="tableRef" border :data="tableData" table-layout="auto" :highlight-current-row="true"
                            @row-click="handleClickInDialog"
                            @row-dblclick="handleDbClickInDialog"
                            >
                            <el-table-column v-for="(item,idx) in columns" 
                                :prop="item.colName" 
                                :label="item.title" 
                                :width="item.width?item.width: idx == 0 ? '300' : '*'" />
                            <slot name="tableColumn"></slot>
                        </el-table>
                    </div>
            </div>
            <!-- hideSearchDialog, handleSave 方法 -->
            <template #footer>
                <div class="dialog-footer" style="text-align: center">
                <span @click="hideSearchDialog" class="ep-button ep-button-custom">Cancel</span>
                <el-button type="primary" @click="handleOK" class="ep-button-custom">OK</el-button>
                </div>
            </template>
        </el-dialog>
    </div>    
</template>

<script lang="ts" setup>
import { onUnmounted, ref, watch, reactive, computed, getCurrentInstance } from 'vue'
import {
    Plus,
    Minus,
} from '@element-plus/icons-vue'
import { randomHashCode, highlight } from '~/util/Function';
import { ElMessage, ElMessageBox } from 'element-plus';
import { showErrorMsg } from '~/util/Function.js';


const searcDialogVisible = ref(false);
const props = defineProps(['data', 'details', 'oid', 'funcMod', 'editRow', 'deleteRow', 'lazy', 'isSelectFirst', 'clickOk',
    'onClick', 'modelValue', 'isShowSearch', 'beforeSearch', 'afterSearch', 'onReset', 'cellStyle',
    'selectable', 'isMultiple', 'isHideCheckBox', 'fieldsDtl', 'isManual', 'beforeClick', 'beforeSave', 'uniqueKey',
    'disabled', 'columns', 'title', 'params', 'url', 'uniqueKeyDesc', 'foreignKey']);
const { proxy } = getCurrentInstance();

const gridRef = ref();
const showData = ref([]);
const currentRow = ref({});
const currentInDialogRow = ref({});
const midifyData = ref({});
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const pagePaginationLayout = 'sizes, , jumper, prev, pager, next, ->, slot';
const pageSizes = [10, 20, 30, 40];
const tableData = ref([]);
const param = ref({});
const firstColName = props.columns[0].colName;
const secondColName = props.columns[1].colName;
const formInline = reactive({});
const tableRef = ref();

// 计算属性
const showDataUniqueKeyArray = computed(() => {
  return showData.value.map(obj => obj[props.uniqueKey]);
})

const emit = defineEmits(['showData']);

// 绑定 vpoList 到 showData
watch(() => props.modelValue, (newVal) => {
    if (newVal && showData.value != []) {
        showData.value = [...newVal];
    }
});

// 检测 showData 数据变化, 及时通知父组件
watch(() => showData.value, (newVal) => {
    if (newVal) {
        ElMessage.closeAll();
        emit('showData', newVal);
    }
}, { deep: true });

// 组件卸载前执行
onUnmounted(()=>{
    showData.value = [];
});

// 清空 midifyData, 供父组件调用
const clearModifyData = () => {
    midifyData.value = {};
}

// 点击新增按钮
const addRecord = () => {
    // searcDialogVisible 即展开选择框, isGridModified 是确保选择框没有数据, 如果 props.form 和 lastRow.value 有一个属性值对不上都提示
    if (searcDialogVisible.value) {
        return false;
    }
    // 清空当前行
    currentRow.value = {};
    // el-table 取消选中
    gridRef.value?.setCurrentRow(null);
    // 展开选择框
    searcDialogVisible.value = true;
    onSearch();
}

const deleteRecord = async () => {
    let rec = currentRow.value;
    if (rec==null|| Object.keys(rec).length==0) {
        return;
    }
    if (rec.mkckAction == 'D') {
      return;
    }
    ElMessageBox.confirm(
        'Delete the record?',
        'Warning',
        {
            confirmButtonText: 'OK',
            cancelButtonText: 'Cancel',
            type: 'warning',
        }
    ).then(async () => {
        // 新增数据, 直接清除
        if (rec?.mkckAction == 'C' && rec?.recordStatus == 'PD') {
            showData.value = showData.value.filter(function(item){
                return item?.oid !== rec?.oid;
            });
            if (!rec.sysCreateDate) {
                delete midifyData.value[rec.oid];
            } else {
                midifyData.value[rec.oid] = {
                    ...rec,
                    mkckAction: 'D',
                };
            }
        } else {
            // 需要审核才能删除
            rec.mkckAction= 'D';
            rec.recordStatus= 'PD';
            midifyData.value[rec.oid] = {
                ...rec,
                mkckAction: 'D',
                recordStatus: 'PD',
            };
        }
        rec.isDelete = true;
        hideSearchDialog();
    }).catch(() => {
    });
}

const hideSearchDialog = () => {
    delete formInline[secondColName];
    delete formInline[firstColName];
    searcDialogVisible.value = false;
}

const updateRow = (currentObj: Object, newObj: Object) => {
    for (let key in newObj) {
        currentObj[key] = newObj[key];
    }
}

const handleClick = async (row: any, column: any, event: Event) => {
    if (searcDialogVisible.value) {
        return false;
    }
    currentRow.value = row;
    gridRef.value?.setCurrentRow(row);
    searcDialogVisible.value = false;
}

const cellStyle = (row, column, rowIndex, columnIndex) => {
    if (props.cellStyle) {
        let style = props.cellStyle(row, column, rowIndex, columnIndex);
        if (style) {
            return style;
        }
    }
    return highlight(row);
}

const tableRowClassName = ({ row }) => {
    let selectedClass = "";
    if (currentRow.value.oid == row.oid) {
        selectedClass = ' selected-row';
    }
    return selectedClass;
}

const getModifyRecords = () => {
    return Object.values(midifyData.value);
}

const isEditing = () => {
    return searcDialogVisible.value;
}

const mergeShowData = () => {
    return showData.value.filter(function(item){
        return !(midifyData.value[item?.oid]&&item?.mkckAction == 'D');
    });
}

const addBatch = (data) => {
    let obj = {};
    let ele;
    let key = props.uniqueKey;
    if (!key) {
        key = "oid";
    }
    for (let i = 0; i < showData.value.length; i++) {
        ele = showData.value[i];
        obj[ele[key]] = ele;
    }
    let rec;
    for (let i = 0; i < data.length; i++) {
        ele = data[i]; // {a: xxx1; b: xxx2}
        if (obj[ele[key]]) {  // if (obj[xxx1])
            rec = obj[ele[key]]; // rec = obj[xxx1]
            if (rec.mkckAction == 'C') {

            } else {
                rec.mkckAction = 'U';
                rec.recordStatus = 'PD';
            }
            updateRow(rec, ele);
        } else {
            ele.mkckAction = 'C';
            ele.recordStatus = 'PD';
            rec = { ...ele };
            rec.oid = Math.trunc(randomHashCode());
            showData.value.push(rec);
        }
        midifyData.value[rec.oid] = rec;
    }
}

const clearInput = () => {
    if (!formInline[firstColName] && !formInline[secondColName]) {
        onSearch();
    }
}

const onSearch = async () => {
    param.value = {};
    for (var key in formInline) {
        if (formInline.hasOwnProperty(key) && formInline[key]) {
            param.value[key] = formInline[key];
        }
    }
    const msg = await proxy.$axios.post(props.url, {
        param: { ...props.params, ...param.value, },
        current: currentPage.value,
        pageSize: pageSize.value,
    });
    if (msg?.success) {
        total.value = msg.data.total;
        currentPage.value = msg.data.page;
        let data = msg.data.data;
        tableData.value = data;
    }
}

const handleClickInDialog = (row) => {
    currentInDialogRow.value = row;
}

const handleDbClickInDialog = (row) => {
    if (showDataUniqueKeyArray.value.includes(row[firstColName])) {
        showErrorMsg("The same data already exists.");
        return;
    }
    const rows: {}[] = [];
    const uniqueKey = props.uniqueKey;
    let obj = {};
    obj[uniqueKey] = row[firstColName];
    if (props.uniqueKeyDesc) {
        obj[props.uniqueKeyDesc] = row[secondColName];
    }
    if (props.foreignKey) {
        obj[props.foreignKey] = row['var1'];
    }
    rows.push(obj);
    addBatch(rows);
    hideSearchDialog();
}

const handleOK = () => {
    if (currentInDialogRow.value) {
        handleDbClickInDialog(currentInDialogRow.value)
    }
}

// 弹出框表单搜索
watch(formInline, (newValue) => {
    if (newValue[firstColName] || newValue[secondColName]) {
        currentPage.value = 1;
        onSearch();
    }
});

defineExpose({
    getModifyRecords,
    isEditing,
    showData,
    clearModifyData,
    mergeShowData,
    addBatch,
})
</script>