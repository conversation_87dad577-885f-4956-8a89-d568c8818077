<template> 
  <BasePanel :searchParams="searchParams" :paramListData='paramListData' url="/datamgmt/api/v1/ftg/list" :params="{ modeEdit: 'Y' }"
    :showDetails="showDetails" :deleteRow="deleteRow" :editRow="editRow" ref="tableRef" :sortProp="{}">
    <template v-slot:searchPanel="slotProps">
      <FormRow>
        <ElFormItemProxy label-width="115px" :label="$t('csscl.ftgManagement.clientFtgCode')" prop="clientFtgCode">
          <el-input v-model="slotProps.form.clientFtgCode" style="width: 200px" maxlength="20"/>
        </ElFormItemProxy>
        <ElFormItemProxy label-width="90px" :label="$t('csscl.ftgManagement.ftgidCode')" prop="ftgidCode">
          <el-input v-model="slotProps.form.ftgidCode" style="width: 200px" maxlength="20"/>
        </ElFormItemProxy>
        <ElFormItemProxy label-width="130px" :label="$t('csscl.ftgManagement.ftgidName')" prop="ftgidName">
          <el-input v-model="slotProps.form.ftgidName" style="width: 300px" class="text-none" maxlength="50"/> 
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy label-width="115px" :label="$t('csscl.ftgManagement.status')" prop="status">
          <Select v-model="slotProps.form.status" type='STATUS' :change="statusType(slotProps.form.status)" style="width: 200px"/>
        </ElFormItemProxy>
        <ElFormItemProxy></ElFormItemProxy>
        <ElFormItemProxy label-width="130px" :label="$t('common.title.recordStatus')" prop="multipleRecordStatus" >
          <MultipleSelect v-model="slotProps.form.multipleRecordStatus" v-model:desc="paramListData.multipleRecordStatus" style="width: 200px" />
        </ElFormItemProxy>
      </FormRow>
    </template>

    <template v-slot:tableColumn>
        <el-table-column align="left" header-align="center" sortable="custom" prop="clientFtgCode" :label="$t('csscl.ftgManagement.clientFtgCode')" width="300" />
        <el-table-column align="left" header-align="center" sortable="custom" prop="ftgidCode" :label="$t('csscl.ftgManagement.ftgidCode')" width="300" />
        <el-table-column align="left" header-align="center" sortable="custom" prop="ftgidName" :label="$t('csscl.ftgManagement.ftgidName')" />
        <el-table-column align="left" header-align="center" sortable="custom" prop="status" :label="$t('common.title.status')" width="180" >
          <template #default="scope">
            {{ getCommonDesc('STATUS', scope.row.status) }}
          </template>
        </el-table-column>
        <el-table-column align="left" header-align="center" sortable="custom" prop="recordStsMkckAction" :label="$t('common.title.recordStatus')" width="220" >
          <template #default="scope">
            {{ getRecordStatusDesc(scope.row) }}
          </template>
        </el-table-column>    
    </template>
  </BasePanel>
  <Details ref="detailsRef" :reload="reload" />
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance, watch } from 'vue';
import {
  Search
} from '@element-plus/icons-vue'
import BasePanel from '~/pages/base/index.vue'
import Details from './Details.vue'
import  { getCommonDesc, getRecordStatusDesc, getOid } from '~/util/Function.js';

const { proxy } = getCurrentInstance()
const paramListData = ref({});
const searchParams = ref({
  //顺序和上面绑定参数一致
  clientFtgCode:"",
  ftgidCode:"",
  ftgidName:"",
  status:"",
  multipleRecordStatus:[],
});

const tableRef = ref();
const detailsRef = ref();
const showDetails = (row, disabled) => {
  detailsRef.value.showDetails(row, disabled);
}
const deleteRow = (row) => {
  proxy.$axios.delete("/datamgmt/api/v1/ftg?ftgidId="+getOid(row, false,null,true)).then((body) => {
    if (body.success) {
      tableRef.value.load();
    }
  });
}
const editRow = (row) => {
  detailsRef.value.editRow(row);
}
const reload = () => {
  tableRef.value.load();
}
//paramList 参数显示用的
function statusType(value){
  paramListData._value.status =  getCommonDesc('STATUS', value);
}
function recordStatusType(value){
  paramListData._value.recordStatus =  getCommonDesc('RECORD_STATUS', value);
}
</script>

<style></style>