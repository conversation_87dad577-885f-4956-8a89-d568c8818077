<template #default>
  <el-input v-model="data" 
    class="select-input" 
    ref="inpObj"
    :disabled="disabled"
    selectinput
    input-style="display:none" >
    <template #prepend >
      <!-- Start SIR-Cristin-R033, TomLi, 2024/08/12 -->
       <!-- <div :style=" (selectObject[type()]?.style || 'width:200px') + ';position: absolute;height:0px;'" class="form-select-disabled-mask"></div> -->
      <div :style="style()" class="form-select-disabled-mask"></div>
      <!-- End SIR-Cristin-R033, TomLi, 2024/08/12 -->
      <select v-model="data" 
       :style="style()"
       :onchange="change"
       :onfocus="focus"
       :disabled="disabled"
       ref="selObj"
       class="select-class hoverHighline" >
       <option value="" v-if="!parseBool(hideEmpty)" />
       <option v-for="item in getSource()" :value="value(item)" :disabled="item.disabled" :init="selected(item)" >{{ label(item) }}</option>
       <option v-show="selObj?.value?.disabled">{{ data }}</option>
     </select>
    </template>
  </el-input>
</template>

<script lang="ts" setup>
import { ref, watch, computed, defineEmits, getCurrentInstance } from 'vue';
import  { parseBool, getCommonDesc } from '~/util/Function.js';

const { proxy } = getCurrentInstance()
const props = defineProps(["modelValue", "hideEmpty", "style", 'disabled', 'type', 'source', 'valueKey', 'labelKey', 'vkEnqual', 'change', 'focus', 'desc']);
const emit = defineEmits(['update:modelValue', 'update:desc']);
const inpObj = ref();
const selObj = ref();
const hideEmpty = ref(false)
const desc = computed({
  get(){
    return props.desc;
  },
  set(val){
    emit("update:desc", val);
  }
});
const data = computed({
  get(){
    return props.modelValue;
  },
  set(val){
    emit("update:modelValue", val);
  }
});

watch( ()=> (inpObj.value?.input?.disabled), (nv) => {
  if (!selObj?.value?.disabled) {
    selObj.value.disabled=nv;
  }
} );

const type = () => {
  return props.type?.toUpperCase() || "RECORD_STATUS";
}

const style = () => {
  return props.style || selectObject[type()]?.style || "width:200px";
}

const selected = (item) => {
  if (item.selected == true || item.selected == "selected") {
    data.value = data.value || value(item);
  }
}

const change = (e) => {
  if (e.target.value) {
    desc.value = getCommonDesc(type() , e.target.value);
  } else {
    desc.value = null;
  }
  
  if(props.change){
    props.change(e.target.value, e.target);
  }
}

const label = (item) => {
  return item[ props.labelKey || parseBool(props.vkEnqual) ? (props.valueKey || 'code') : 'codeDesc'];
}

const value = (item) => {
  return item[ props.valueKey || 'code' ];
}

const getSource = () => {
  return props.source || proxy.$commonCodeStore.getAll[ type()];
}

const focus = (e) => {
  if (!parseBool(props.hideEmpty)) {
    let isReq = inpObj.value.input.closest("div.ep-form-item.is-required");
    if (isReq) {
      hideEmpty.value = true;
    } else {
      hideEmpty.value = false;
    }
  }
  if (props.focus) {
    props.focus(e.target);
  }
}

const selectObject = {

  BANK_CODE: { style: 'width:100px' },
  CLIENT_TYPE_CODE: { style: 'width:180px' },
  AE_CODE: { style: 'width:100px' },
  SERVICE_PLAN_CODE: { style: 'width:180px' },
  ACCOUNT_NATURE_CODE: { style: 'width:180px' },
  ENTRY_TYPE_CODE: { style: 'width:180px' },
  UNIQUE_REF_TYPE_CODE: { style: 'width:180px' },
  OUTGOING_CHANNEL_CODE: { style: 'width:180px' },
  OUTGOING_CHANNEL_PURPOSE_CODE: { style: 'width:180px' },
  LANGUAGE: { style: 'width:180px' },
  SETTLE_METHOD_CODE: { style: 'width:180px' },
  CASH_SETTLE_METHOD_CODE: { style: 'width:180px' },
  BANK_ACC_TYPE_CODE: { style: 'width:180px' },
  BANK_ACC_PURPOSE_CODE: { style: 'width:180px' },
  SHADOW_CASH_ACC_PURPOSE_CODE: { style: 'width:180px' },
  CUST_ACC_INCOMING_CHANNEL_CODE: { style: 'width:180px' },
  CLR_AGENT_INCOMING_CHANNEL_CODE: { style: 'width:180px' },
  CLR_AGENT_INCOMING_CHANNEL_PURPOSE_CODE: { style: 'width:180px' },
  CASH_SETTLE_METHOD_SI_CODE: { style: 'width:180px' },
  CA_PAY_METHOD_CODE: { style: 'width:180px' },
  CASH_STOCK_SETTLE_METHOD_CODE: { style: 'width:180px' },
  EX_BOARD_HOLIDAY_TYPE_CODE: { style: 'width:180px' },
  CTRY_HOLIDAY_TYPE_CODE: { style: 'width:180px' },
  SETTLE_INSTR_INCOMING_CHANNEL_CODE: { style: 'width:180px' },
  STOCK_RECON_TYPE_CODE: { style: 'width:180px' },
  IDENTIFIER_TYPE_CODE: { style: 'width:180px' },
  SWIFT_TYPE_CODE: { style: 'width:180px' },
  PRODUCT_CODE: { style: 'width:180px' },
  PENALTY_TYPE: { style: 'width:180px' },
  CCY_CAL_METHOD: { style: 'width:180px' },
  ITF_FILE_TYPE: { style: 'width:180px' },
  CUST_ACC_INCOMING_CHANNEL_PURPOSE_CODE: { style: 'width:180px' },
  RECON_PROCESS_STATUS: { style: 'width:180px' },
  RECON_CHANNEL: { style: 'width:180px' },
  RECON_STATUS: { style: 'width:180px' },
  STATUS: { style: 'width:180px' },
  RECORD_STATUS: { style: 'width:180px' },
  FREQUENCY: { style: 'width:180px' },
  JOB_TYPE: { style: 'width:180px' },
  FUNCTION_TYPE: { style: 'width:180px' },
  WEEKLY_DAY: { style: 'width:180px' },
  CLIENT_GROUP: { style: 'width:180px' },
  FUND_MANAGER: { style: 'width:180px' },
  COUNTRY_AREA: { style: 'width:180px' },
  TXN_DESC: { style: 'width:180px' },
  TXN_TYPE: { style: 'width:180px' },
  BOC_MN_CODE: { style: 'width:180px' },
  MKCK_ACTION: { style: 'width:180px' },
  FX_TYPE: { style: 'width:180px' },
  DR_CR: { style: 'width:180px' },
  JOB_STATUS: { style: 'width:180px' },
  ACCOUNT_STATUS: { style: 'width:180px' },
  DOC_TYPE: { style: 'width:180px' },
  SCHEDULER_CHANNEL: { style: 'width:180px' },
  COM_YN: { style: 'width:100px' },
  ACCOUNT_GROUP: { style: 'width:180px' },
  ACCOUNTS: { style: 'width:180px' },
  REJECT_REASON: { style: 'width:180px' },
  PROCESSING_STATUS: { style: 'width:180px' },
  DASH_RECORD_STATUS: { style: 'width:180px' },
  MAIL_REGION: { style: 'width:180px' },
  PEND_QUE_STS: { style: 'width:180px' },
  TXN_TYPE_DESC: { style: 'width:180px' },
  TXN_FUNC_TYPE: { style: 'width:180px' },
  FILE_PROCESS_STATUS: { style: 'width:180px' },
  FX_DATA_SOURCE: { style: 'width:180px' },
  CODE_TYPE: { style:"width:600px" },
  CA_CREATE_CHANNEL: { style: "width:200px" },
  CA_HOLD_STATUS: { style: "width:200px" },
  ACCOUNT_TYPE: { style: "width:200px" },
  PAYMENT_TYPE: { style: "width:200px" },
  TAX_RATE_TYPE: { style: "width:200px" }
}

</script>

<style>
.select-input div {
  padding: 0;
  margin: 0;
  border:none;
  outline: none;
  box-shadow: none !important;
  background: none !important;
}

.select-input div:hover {
  box-shadow: none !important;
  background: none !important;
}

.is-disabled .select-class {
  background-color: var(--ep-disabled-bg-color);
  cursor: not-allowed;
  border: 0px;
  outline: 0px;
  box-shadow: none;
  -webkit-box-shadow:none;
}

.select-class {
  width:220px;
  height: 24px;
  padding-left: 6px;
  opacity: 1;
  color: var(--ep-text-color-regular);
  border:none;
  outline: none;
  border-radius: var(--ep-border-radius-base); 
  box-shadow: var(--ep-border-color) 0px 0px 0px 1px inset;
}

.select-class:focus {
  box-shadow:  0 0 0 1px var(--ep-input-focus-border-color) inset;
}

.is-error .select-class {  
  box-shadow: 0px 0px 0px 1px var(--ep-color-danger) inset;
}

.is-disabled .select-class {
  box-shadow: unset !important;
}

/* Start SIR-Cristin-R033, TomLi, 2024/08/12 */
.form-select-disabled-mask {
  position: absolute;
  height:0px;
}
/* End SIR-Cristin-R033, TomLi, 2024/08/12 */
.is-disabled .form-select-disabled-mask {
  height:24px !important;
  /* Start SIR-Cristin-R033, TomLi, 2024/08/12 */
  position: absolute;
  /* End SIR-Cristin-R033, TomLi, 2024/08/12 */
}
</style>