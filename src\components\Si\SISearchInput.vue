<template>
  <div>
      <div style="display: table;width:200px;" :style="style">
        <div style="display: table;" :style="inputStyle()">
          <div style="display: table-cell; width:100%;" >
            <InputText v-model="inpVal"
            :maxlength="maxlength"
            :disabled="disabled" 
            :class="sty"
            ref="codeObj"
            :onlyLetters="onlyLetters"
            :searchField="parseBool(props.searchField)"
            :setTitle="setTitle"
            :searchType="searchType"
            :readonly="readonly"
            :alt="alt()"
            :specialTxt="specialTxt"
            :onsearch="()=>{ return validSearch(); }"
            @blur="handleBlur" 
            @change="eventChange"
            @input="handleInput" aria-autocomplete="none"/>
            
          </div>
          <div style="display: table-cell; width: 20px; padding: 0 5px; vertical-align: middle">
            <el-icon-search style="width: 20px;" @click="handleSearch" class="search-icon" :searchType="searchType" />
          </div>
        </div>
        <div :style="{display: 'table-cell', width:  '100%' }" v-if="parseBool(props.showDesc)"  >
          <input v-model="desc" disabled class="descbox" style="padding-left:6px" />
        </div>
      </div>

    <el-dialog v-model="searcDialogVisible" :modal="false" :close-on-click-modal="false"
      class="search-input" :close-on-press-escape="false" :destroy-on-close="true" draggable :show-close="false"
      modal-class="searchInput-dialog" @close="hideSearchDialog" append-to-body>

      <template #header>
        <div style="margin:0;padding: 0;background-color: var(--ep-color-primary);height: 32px; width:100%; ">
          <el-icon-close  @click="hideSearchDialog()" style="height: 26px; width:26px; background-color: var(--ep-color-primary); border:none; color:#ffffff;float:right;margin: 3px 3px 0 0; cursor: pointer;" />
          <div style="margin: 0;padding: 0;" >
            <div style="height:6px; width:100%;border: none;"></div>
            <span style="color:#fff; font-size: 18px; height: 32px; padding: 10px; width:100% "> {{ props.title  }} </span>
          </div>
        </div>
      </template>

      <!-- <template #append> -->
      <div class="searchInput-dialog-content">
          <div class="searchInput-dialog-form-inline" >
          <FormRow>
            <ElFormItemProxy label-width="350" style="text-align: left;" 
            v-for="(item,idx) in columns" 
            :label="item.title" >
            <InputText clearable v-model="formInline[item.colName]"
              :disabled="disabled"
              searchField="searchField"
              :onlyLetters="props.onlyLetters"
              :maxlength="idx == 0 ? maxlength: 999"
              @input="clearInput()"
              style="width:280px; margin-top: 5px;" 
              :input-id="item.colName" aria-autocomplete="none"/>
          </ElFormItemProxy>
        </FormRow>
        <slot name="searchPanel" :form="formInline"></slot>
      </div>

        <div class="searchInput-dialog-result">
          <el-form>
          <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="pageSizes"
            :layout="pagePaginationLayout" v-model:total="total" @current-change="handleChange" @size-change="handleChange"
            style="background-color: lightgrey;padding-inline: 10px;" >
            Total {{ total }} records
          </el-pagination>
          </el-form>
          <el-table border :data="tableData" table-layout="auto" :highlight-current-row="true"
            @row-dblclick="handleDbClick" 
            @row-click="handleClick">
            <el-table-column v-for="(item,idx) in columns" 
              :prop="item.colName" 
              :label="item.title" 
              :width="item.width?item.width: idx == 0 ? '300' : '*'" />
            <slot name="tableColumn"></slot>
          </el-table>

          <el-form>
          <el-pagination v-if="!parseBool(props.hideBottomPageination)" v-model:current-page="currentPage"
            v-model:page-size="pageSize" :page-sizes="pageSizes" :layout="pagePaginationLayout" v-model:total="total"
            @size-change="handleChange"
            @current-change="handleChange" style="background-color: lightgrey;padding-inline: 10px;" small>
            Total {{ total }} records
          </el-pagination>
          </el-form>
        </div>
      </div>
      <!-- </template> -->
      <template #footer>
        <div class="dialog-footer" style="text-align: center">
          <span @click="hideSearchDialog" class="ep-button ep-button-custom">Cancel</span>
          <el-button type="primary" @click="handleSave" class="ep-button-custom">OK</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, reactive, getCurrentInstance, nextTick } from 'vue';
import { parseBool } from '~/util/Function';

// Start, LiKunBiao, 2025/7/04, add 'changeRow'
const emit = defineEmits(['update:modelValue', 'update:inpVal', 'changeDesc', 'changeRow']);
// End, LiKunBiao, 2025/7/04
const { proxy } = getCurrentInstance()

const props = defineProps({
  url: '',
  title: '',
  readonly: {
    default: false,
  },
  desc: {
    default: '',
  },
  uppercase: {
    default: null,
  },
  alt: {
    default: undefined,
  },
  value: {
    default: undefined,
  },
  setTitle: {
    default: undefined,
  },
  showDesc: {
    default: true,
  },
  onlyLetters: {
    default: false,
  },
  searchField: {
    default: false,
  },
  style: {
    type: Object,
    default: () => ({})
  },
  disabled: {
    default: false,
  },
  params: {
    type: Object,
    default: () => ({ status: 'A', recordStatus: 'A' })
  },
  searchParams: {
    type: Array,
    default: () => [],
  },
  hideBottomPageination: {
    default: true,
  },
  maxlength: {
    default: 9999,
  },
  searchType: {
    default: '',
  },
  inputStyle: {
    default: undefined,
  },
  specialTxt: {
    default: false
  },
  columns: {
    type: Array,
    default: () => [
    ],
  },
  modelValue: {
    type: [String, Number],
    default: '',
  },
  pageSizes: {
    type: Array,
    default() {
      return [10, 20, 30, 40]
    }
  },
  pagePaginationLayout: {
    type: String,
    default: 'sizes, , jumper, prev, pager, next, ->, slot'
  },
  dbClick: {
    type: Function,
    default() {
      return (row, code, desc) => { }
    },
  },
  change: {
      type: Function,
      default() {
        return (val) => {}
      }
    },
});

const searcDialogVisible = ref(false);
const small = true;
const sty = ref("");
//Start bind v-model
const code = ref(props.modelValue);
const desc = ref(props.desc);
const inputChange = ref(false);
const codeObj = ref();
const isUpper = parseBool(props.uppercase);
const inpVal = ref();

watch(code, (newValue) => {
  if (!newValue || !inpVal.value) {
    inpVal.value = newValue;
  }
  emit('update:modelValue', newValue);
});

watch(inpVal, (newValue)=>{
	emit('update:inpVal', newValue);
});

watch(() => props.modelValue, () => {
  code.value = trim(props.modelValue)
  //Start CAP1-376, Tom.li, 2025-02-26
  if (!inputChange.value && getCodeVal()) {
  //End CAP1-376, Tom.li, 2025-02-26
    delete(formInline[firstColName])
    formInline[firstColName] = getCodeVal();
  }
  if (!getCodeVal()) {
    clearAll();
  }
  inputChange.value=false;
})

nextTick(()=>{
  //Start CAP1-376, Tom.li, 2025-02-26
  if (getCodeVal()) {
  //End CAP1-376, Tom.li, 2025-02-26
    formInline[firstColName] = getCodeVal();
  }
});

const getCodeVal = () => {
  return trim(code.value);
}

const inputStyle = () => {
  if(  typeof props.inputStyle == "object") {
    return Object.assign({width:"110px"}, props.inputStyle);
  }
  return (parseBool(props.showDesc) ? "width:110px;" : "") + props.inputStyle;
}

const clearAll = () => {
  code.value = "";
  desc.value = "";
  inpVal.value = "";
  sty.value = "";
  delete formInline[firstColName];
  delete formInline[secondColName];
} 


const handleInput = (value) => {
  value = inpVal.value;
  if (isUpper) {
    code.value = upper(value)
  } else {
    code.value = value;
  }
  desc.value = "";
  inputChange.value = true;
};

const handleBlur = async () => {
  let codeVal = getCodeVal();
  console.log("handleBlur", "value:"+codeVal, "oldValue:"+formInline[firstColName])
  if (props.change) {
    props.change(codeVal);
  }
  if(codeVal != "" ) {
    inputBlurValidInd.value = true;
    delete formInline[secondColName];
    formInline[firstColName] = codeVal;
  } 
  if (codeVal == "" )  {
    clearAll();
  }
}

const eventChange = async () => {
  let codeVal = getCodeVal();
  console.log("eventChange", "value:"+codeVal, "oldValue:"+formInline[firstColName])
  if (props.eventChange) {
    props.eventChange(codeVal);
  }
  if(codeVal != "" ) {
    inputBlurValidInd.value = true;
    delete formInline[secondColName];
    formInline[firstColName] = codeVal;
  } 
  if (codeVal == "" )  {
    clearAll();
  }
}


//end bind v-model

// close Search Dialog
const hideSearchDialog = () => {
  delete formInline[secondColName];
  delete formInline[firstColName];
  searcDialogVisible.value = false;
}

// show Search Dialog
const openSearchDialog = () => {
  searcDialogVisible.value = true;
}
// bind table data

const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);
const param = ref({});
const inputBlurValidInd = ref(false);
const currentRow = ref();
const firstColName = props.columns[0].colName;
const secondColName = props.columns[1].colName;
const formInline = reactive({});

const loadData = async (afterSearch = undefined) => {
  if (props.url == "COMMONLIST") {
    console.log(props.searchType, props.params, param.value)
    let commCode = proxy.$commonCodeStore.getAll[props.searchType];
    let codeVal = upper(param.value[firstColName]);
    let codeDesc = upper(param.value[secondColName]);
    let commCodes = [];
    if (codeVal != "" || codeDesc != "") {
      commCode.forEach((e, idx) => {
        let codeFlag = codeVal == "" ? true: false;
        let descFlag = codeDesc == "" ? true: false;
        if (codeVal.indexOf("%") > -1 && RegExp("^"+(codeVal.replaceAll('%', '.*')) + "$").test(upper(e.code))) {
          codeFlag = true;
        } else if (codeVal == upper(e.code)) {
          codeFlag = true;
        }
        if (codeDesc.indexOf("%") > -1 &&  RegExp("^"+(codeDesc.replaceAll('%', '.*')) + "$").test(upper(e.codeDesc))) {
          descFlag = true;
        } else if (codeDesc == upper(e.codeDesc)) {
          descFlag = true;
        }
        if (codeFlag && descFlag) {
          commCodes.push(e);
        }
      });
    } else {
      commCodes = commCode;
    }
    total.value = commCodes.length;
    currentPage.value = currentPage.value;
    let start = (Number(currentPage.value) - 1) * pageSize.value;
    let end = Number(currentPage.value) * pageSize.value;
    tableData.value = commCodes.slice(start, end);
    if(afterSearch) {
      afterSearch(props.params, tableData.value);
    }
  } else {
    console.log(props.searchType, props.params, param.value)
    const msg = await proxy.$axios.post(props.url, {
      param: { ...props.params, ...param.value, },
      current: currentPage.value,
      pageSize: pageSize.value,
    });
    if (msg?.success) {
      total.value = msg.data.total;
      currentPage.value = msg.data.page;
      tableData.value = msg.data.data;
      if(afterSearch) {
        afterSearch(props.params, tableData.value);
      }
    }
  }
}

const trim = (val) => {
  if (val) {
    return String(val).trim();
  }
}

const load = () => {
  loadData(afterSearch);
}

const alt = () => {
  return props.alt || props.title; 
}

const validSearch = async (val) => {
  let codeVal = val || getCodeVal();
  
  if (!val && (codeObj.value.input.disabled || !codeVal)) {
    return true;
  }
  let ret = false;
  currentPage.value = 1;
  let ps = pageSize.value;
  pageSize.value = 999;
  param.value[firstColName] = codeVal;
  await loadData((p,d)=>{
    if (d?.length > 0) {
      for (let i = 0; i < d.length; i++) {
        let e = d[i];
        if (upper(codeVal) == upper(e[firstColName])
			|| upper(codeVal) == upper(e['var6'])
			|| upper(codeVal) == upper(e['var5'])) {
          ret = true;
          break;
        }
      }
    } else {
      ret = false;
    }
  });
  pageSize.value = ps;
  return ret;
}

//when change, can search
watch(formInline, (newValue) => {
  if (newValue[firstColName] || newValue[secondColName] ) {
    currentPage.value = 1;
    onSearch();
  }
});

const upper = (str) => {
  if (!str) {
    return "";
  }
  return String(str).toUpperCase();
}

const afterSearch = (param, data) => {
  let row = {};
  let codeVal = upper(getCodeVal());
  if (codeVal) {
    if (data?.length > 0) {
      for (let i = 0; i < data.length; i++) {
        row = data[i];
        if (upper(row[firstColName]) == codeVal || codeVal == upper(row['var6']) ) {
          desc.value = row[secondColName];
          // Start R2411A-58749 LiShaoyi 2024/09/12
          inpVal.value = row[firstColName];
          code.value = row[firstColName];
          // End R2411A-58749 LiShaoyi 2024/09/12
          if (codeVal == upper(row['var6'])) {
            code.value = upper(row[firstColName]);
          }
          if (row['var5']) {
            code.value = row['var5'];
          }
          break;
        }
      }
    }
  }

  if (props.afterSearch) {
    props.afterSearch(param, data);
  }
  
  sty.value= "";
  if (inputBlurValidInd.value ) {
    inputBlurValidInd.value = false;
    if (!desc.value && getCodeVal() ) {
      sty.value = "search-error";
      return;
    }

    if (props.dbClick) {
      props.dbClick(row, codeVal, desc.value);
    }
  }
}
// click search icon
const handleSearch = async () => {
  openSearchDialog();
  // get data
  if (!getCodeVal()) {
    onSearch();
  } else {
    formInline[firstColName] = inpVal.value;
  }
};

// change page size
const handleChange = () => {
  onSearch();
}

const onSearch = () => {
  
  currentRow.value = null;
  param.value = {};
  //formInline? param.value = formInline : ref();
  for (var key in formInline) {
    if (formInline.hasOwnProperty(key) && formInline[key]) {
      param.value[key] = formInline[key];
    }
  }
  load();
}

const handleClick = (row) => {
  currentRow.value = row;
}

const handleSave = () => {
  
  if (currentRow.value) {
    handleDbClick(currentRow.value)
  }
  
}

const handleDbClick = (row) => {
  console.log(row)
  let a = document.querySelector("input[input-id='code']");
  let disa = a?.attributes?.disabled;
  if (!disa) {
    inpVal.value = row[firstColName];
    code.value = row[firstColName];
    desc.value = row[secondColName];
    sty.value = "";
    emit('changeDesc', desc.value);
    // Start, LiKunBiao, 2025/07/04
    emit('changeRow', row);
    // Start, LiKunBiao, 2025/07/04
    props.dbClick(row, code.value, desc.value);
  }

  hideSearchDialog();
}

const clearInput = () => {
  if (isUpper && formInline[firstColName]) {
    formInline[firstColName] = upper(formInline[firstColName]);
  }
  if (!formInline[firstColName] && !formInline[secondColName]) {
    onSearch();
  }
}

defineExpose({
  validSearch,
})
</script>

<style>

.descbox {
  text-transform: none;
  box-shadow: none;
  height: var(--ep-component-size);
  width:100%;
  padding: 0;
  outline: none;
  border:none;
  color:var(--ep-text-color-regular);
  background: #E6E6E6;
}

.searchInput-dialog {
  width: 750px;
  height: 500px;
}

.searchInput-dialog-content {
  max-width: 750px;
  max-height: 500px;
  overflow-y: auto;
}

.searchInput-dialog-content .form-row {
  width: 100% !important;
}

.searchInput-dialog-result {
  margin: 10px;
  max-height: 400px;
  overflow-y: auto;
}

.searchInput-dialog .ep-dialog {
  overflow-y: auto;
  --ep-dialog-width: 750px !important;
}

/* .searchInput-dialog .ep-overlay-dialog .ep-dialog>header { */
/* display: none; */
/*  no show original header*/
/* } */

.searchInput-dialog .ep-pagination .btn-prev {
  margin-left: 16px !important;
}


.searchInput-dialog .ep-pagination .ep-select {
  width: 90px !important;
}

.searchInput-dialog-form-inline {
  padding-inline: 10px;
  margin: 10px;
}

.searchInput-dialog-form-inline .ep-input {
  --ep-input-width: 150px;
}

.search-input {
  padding: 0px !important;
}

.search-input .ep-dialog__header {
  /* background-color: #e66; 
  padding: 5px 0px 5px 10px;
  */
  padding:0;
}

.ep-dialog__title {
  color:#fff;
}

.search-input .ep-dialog__header button {
  height: 32px;
  width: 32px;
}

.search-input .ep-dialog__body .ep-form {
  background-color: #f4f4f5;
  margin: 0px;
  padding: 0px;
}

.search-input .ep-dialog__footer {
  display: grid;
  padding: 10px 20px;
}

.search-icon {
  width: 20px;
  height: 16px;
}

.search-error>div {
  -webkit-box-shadow: 0 0 0 1px var(--ep-color-danger) inset !important; 
}
</style>