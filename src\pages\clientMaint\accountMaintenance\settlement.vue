<template> 
    
    <div >
        
        <Grid url="/datamgmt/api/v1/account/settlement/method/list"
            :params="{ clientAccountOid: ruleForm.form.opearteOid, pendingOid: ruleForm.form.pendingOid }" 
            :onClick="gridClick"
            :beforeSearch="()=>{ pageObj.settleMethodVPO = {}; }"
            :columns="[
                { title: 'csscl.acctCode.exBoardOid', name: 'exBoardCode', },
                { title: 'csscl.acctCode.paymentMethodCode', name: 'paymentMethod', fn:commDesc('SETTLE_METHOD_CODE') },
                { title: 'csscl.acctCode.postMethod', name: 'postMethod', fn:commDesc('CASH_SETTLE_METHOD_CODE') },
                { title: 'csscl.acctCode.postMethodFx', name: 'postMethodFx', fn:commDesc('CASH_SETTLE_METHOD_CODE') },
                { title: 'csscl.acctCode.partialSiDeliverInd', name: 'partialSiDeliverInd', },
                { title: 'csscl.acctCode.partialSiReceiveInd', name: 'partialSiReceiveInd', },
                { title: 'common.title.status', name: 'status', fn:commDesc('STATUS') },
                { title: 'common.title.recordStatus' , name: 'recordStatus', fn:getRecordStatusDesc },
            ]"
            />
        
        <FormRow>
            <FormItemSign :detailsRef="details" :label="$t('csscl.acctCode.exBoardOid')" prop="settleMethodVPO.exBoardCode">
                
                 <GeneralSearchInput v-model="pageObj.settleMethodVPO.exBoardCode"
                    showDesc="false" 
                    searchType="exboardCode" />
            </FormItemSign>
                    <FormItemSign :detailsRef="details" :label="$t('common.title.status')" prop="settleMethodVPO.status">
                <Select v-model="pageObj.settleMethodVPO.status" style="width:170px" type="STATUS" />
            </FormItemSign>
            <ElFormItemProxy></ElFormItemProxy>
        </FormRow>
        <FormRow>
            <FormItemSign :detailsRef="details" :label="$t('csscl.acctCode.paymentMethodCode')" prop="settleMethodVPO.paymentMethod">
                <Select v-model="pageObj.settleMethodVPO.paymentMethod" style="width:300px" type="SETTLE_METHOD_CODE" />
            </FormItemSign>
                    <FormItemSign :detailsRef="details" :label="$t('csscl.acctCode.postMethod')" prop="settleMethodVPO.postMethod">
                <Select v-model="pageObj.settleMethodVPO.postMethod" style="width:300px" type="CASH_SETTLE_METHOD_CODE" />
            </FormItemSign>
                    <ElFormItemProxy></ElFormItemProxy>
            </FormRow>
            <FormRow>
                <FormItemSign :detailsRef="details" :label="$t('csscl.acctCode.postMethodFx')" prop="settleMethodVPO.postMethodFx">
                    <Select v-model="pageObj.settleMethodVPO.postMethodFx" style="width:350px" type="CASH_SETTLE_METHOD_CODE" />
                </FormItemSign>
                <FormItemSign :detailsRef="details" :label="$t('csscl.acctCode.partialSiReceiveInd')" prop="settleMethodVPO.partialSiReceiveInd">
                    <Select v-model="pageObj.settleMethodVPO.partialSiReceiveInd" type="COM_YN" />
                </FormItemSign>
                <FormItemSign :detailsRef="details" :label="$t('csscl.acctCode.partialSiDeliverInd')" prop="settleMethodVPO.partialSiDeliverInd">
                    <Select v-model="pageObj.settleMethodVPO.partialSiDeliverInd" type="COM_YN" />
                </FormItemSign>
            </FormRow>
            <FormRow>
                <FormItemSign :detailsRef="details" :label="$t('csscl.acctCode.meorSiBic')" prop="settleMethodVPO.meorSwiftCode">
                    <el-input v-model="pageObj.settleMethodVPO.meorSwiftCode" style="width:200px" />
                </FormItemSign>
                <ElFormItemProxy></ElFormItemProxy>
                <ElFormItemProxy></ElFormItemProxy>
            </FormRow>
            <FormRow>
                <FormItemSign :detailsRef="details" :label="$t('csscl.acctCode.allowShortSell')" prop="settleMethodVPO.allowShortSell">
                    <Select v-model="pageObj.settleMethodVPO.allowShortSell" type="COM_YN" />
                </FormItemSign>
                <FormItemSign :detailsRef="details" :label="$t('csscl.acctCode.allowSiForNcbo')" prop="settleMethodVPO.allowSiForNcboInd">
                    <Select v-model="pageObj.settleMethodVPO.allowSiForNcboInd" type="COM_YN" />
                </FormItemSign>
                <ElFormItemProxy></ElFormItemProxy>
            </FormRow>
            <FormRow>
                <FormItemSign :detailsRef="details" :label="$t('csscl.acctCode.siForFop')" prop="settleMethodVPO.allowSiForFopInd">
                    <Select v-model="pageObj.settleMethodVPO.allowSiForFopInd" type="COM_YN" />
                </FormItemSign>
                <FormItemSign :detailsRef="details" :label="$t('csscl.acctCode.siForDvp')" prop="settleMethodVPO.allowSiForDvpInd">
                    <Select v-model="pageObj.settleMethodVPO.allowSiForDvpInd" type="COM_YN" />
                </FormItemSign>
                <ElFormItemProxy></ElFormItemProxy>
            </FormRow>

        <div class="splitLine">
            <span>{{  $t("csscl.acctCode.clientCustodianSiTitle") }}</span>
            <el-divider />
        </div> 

        <Grid url="/datamgmt/api/v1/account/settlement/custaccsi/list"
            :params="{ clientAccountOid: ruleForm.form.opearteOid, pendingOid: ruleForm.form.pendingOid }" 
            :onClick="custAccSiClick"
            :beforeSearch="()=>{ pageObj.clientCustAccVPO = {}; }"
            :columns="[
                { title: 'csscl.acctCode.clearingAgentCode', name: 'clearingAgentCode', width:'350'},
                { title: 'csscl.acctCode.custAcctNo', name: 'custodianAccNo', width:'400'},
                { title: 'csscl.acctCode.exchangeOid', name: 'exchangeCode', width:'260'},
                { title: 'csscl.acctCode.exBoardCode', name: 'exBoardCode', width:'200'},
                { title: 'csscl.acctCode.defaultIndicator', name: 'defaultInd', width:'250'},
                { title: 'common.title.status', name: 'status', fn:commDesc('STATUS') },
                { title:'common.title.recordStatus',name:'recordStatus', fn:getRecordStatusDesc },
            ]"
            />

        <FormRow>
            <FormItemSign :detailsRef="details" :label-width="280" :label="$t('csscl.acctCode.clearingAgentCode')" prop="clientCustAccVPO.clearingAgentCode">

                <GeneralSearchInput v-model="pageObj.clientCustAccVPO.clearingAgentCode"
                    showDesc="false"
                    searchType="siAgentCode" />

            </FormItemSign>
            <FormItemSign :detailsRef="details" :label-width="180" :label="$t('csscl.acctCode.custAcctNo')" prop="clientCustAccVPO.custodianAccNo">
                
                <GeneralSearchInput v-model="pageObj.clientCustAccVPO.custodianAccNo" 
                    showDesc="false" 
                    searchType="siAgentAccNum" />

            </FormItemSign>
        </FormRow>
        <FormRow>
            <FormItemSign :detailsRef="details" :label-width="280" :label="$t('csscl.acctCode.exchangeOid')" prop="clientCustAccVPO.exchangeCode">
                 
                <GeneralSearchInput v-model="pageObj.clientCustAccVPO.exchangeCode"
                    showDesc="false" 
                    searchType="exchangeCode" />

            </FormItemSign>
            <FormItemSign :detailsRef="details" :label-width="180" :label="$t('csscl.acctCode.exBoardCode')" prop="clientCustAccVPO.exBoardCode">
                
                 <GeneralSearchInput v-model="pageObj.clientCustAccVPO.exBoardCode"
                    showDesc="false" 
                    searchType="exboardCode" />

            </FormItemSign>
        </FormRow>
        <FormRow>
            <FormItemSign :detailsRef="details" prop="clientCustAccVPO.defaultInd" :isShowTip="false">
                <el-checkbox v-model="pageObj.clientCustAccVPO.defaultInd" true-value="Y" false-value="N" > 
                    {{ $t('csscl.acctCode.defaultIndicator') }}
                </el-checkbox>
            </FormItemSign>
            <FormItemSign :detailsRef="details" :label-width="180" :label="$t('common.title.status')" prop="clientCustAccVPO.status">
                <Select v-model="pageObj.clientCustAccVPO.status" style="width:170px" type="STATUS" />
            </FormItemSign>
        </FormRow>

    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import Grid from '~/pages/base/Grid.vue';

import  { getCommonDesc, getRecordStatusDesc, commDesc, assignUndef } from '~/util/Function.js';


const props = defineProps([ "ruleForm", "details"]);

const details = props.details;
const ruleForm = props.ruleForm;
const pageObj = reactive ({
    settleMethodVPO:{},
    clientCustAccVPO:{},
});

const gridClick = (row) => {
    pageObj.settleMethodVPO = row;
    assignUndef(pageObj.settleMethodVPO, ruleForm.form);
}

const custAccSiClick = (row) => {
    pageObj.clientCustAccVPO = row;
    assignUndef(pageObj.clientCustAccVPO, ruleForm.form);
}

</script>

<style></style>