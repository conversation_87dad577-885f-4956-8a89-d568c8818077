import { defineStore } from "pinia" // 定义容器
import axios from '~/axios';

const generateMenuObj = (objs:[], obj: object, funcIdMap: object) => {
  let el;
  for (let j = 0; j < objs.length; j++) {
    el = objs[j];
    obj[el.name]=el;
    if (el.funcId) {
      funcIdMap[el.funcId] = el.name;
    }
    if (el.children) {
      generateMenuObj(el.children, obj, funcIdMap);
    }
  }
}

export const currentInfo = defineStore('currentInfoStore', {

  state: () => {
    return {
      menus: [],
      menuObj: {},
      funcIdMap: {},
      currentMenu: "",
      currentUUID: "",
      headerTitle: "",
      userInfo: {},
      isEditMain: false,
    }
  },

  getters: {
    getUserInfo(): object {
      return this.userInfo;
    },
    getCurrentMenu(): string {
      return this.currentMenu;
    },
    getCurrentUUID(): string {
      return this.currentUUID;
    },
    getHeaderTitle(): string {
      return this.headerTitle;
    },
    getMenus(): any {
      return this.menus;
    },
    currentPermission(): any {
      if (this.userInfo && this.userInfo.rights && this.currentMenu) {
        return this.userInfo.rights[this.currentMenu]||{};
      }
      return {};
    },
    getIsEditMain(): boolean {
      return this.isEditMain;
    }
  },

  actions: {
    setUserInfo(info: any) {
      this.userInfo = info;
    },
    setCurrentMenu(name: string) {
      this.currentMenu = name;
    },
    setCurrentUUID(currentUUID: string) {
      this.currentUUID = currentUUID;
    },
    setHeaderTitle(headerTitle: string) {
      this.headerTitle = headerTitle;
    },
    setMenus(all: any) {
      this.menus = all;
      let obj = {};
      let funcIdMap = {};
      generateMenuObj(all, obj, funcIdMap);
      this.menuObj = obj;
      this.funcIdMap = funcIdMap;
    },
    setIsEditMain(isEditMain: boolean) {
      this.isEditMain = isEditMain;
    },
    getMenuObj(name :any): object {
      let common = {
        "name": "ALL",
        "path": "",
        "sort": 1,
        "funcId": "ALL",
      };
      if(name){
        return this.menuObj[name];
      } else {
        if(this.currentMenu){
          if(this.currentMenu.indexOf("MENU_DASHBOARD")>=0){
            return {
              "name": this.currentMenu,
              "path": "",
              "sort": 1,
              "funcId": "CSSCL_DASHB001",
            };
          }
          return this.menuObj[this.currentMenu]||common;
        }
        return common;
      }
    },
    getCurrentFuncId(): string {
      return this.getMenuObj(null).funcId;
    },
    havePermission(funcId:string): boolean {
      if (this.userInfo && this.userInfo.rights && funcId && this.funcIdMap[funcId] && this.userInfo.rights[this.funcIdMap[funcId]].Approve) {
        return true;
      }
      return false;
    },

    //	Start SK-COMMON-0123, Tom.Li, 2024/09/02
    async fetchUserInfo() {
      const res = await axios.get('/auth/api/v1/user');
    //	End SK-COMMON-0123, Tom.Li, 2024/09/02
      if (res && res.success) {
        if ( typeof res.data.currentTime === 'string' ) {
          res.data['TimeCorrection']=new Date(res.data.currentTime) - new Date();
          res.data.currentTime=new Date(res.data.currentTime);
        } else {
          res.data['TimeCorrection']=res.data.currentTime - new Date();
          res.data.currentTime=res.data.currentTime;
        }
        this.setUserInfo(res.data);
      } else {
        this.setUserInfo("");
      }
    },
    currentTimeAddSec(n :number) {
      if (this.userInfo&&this.userInfo.currentTime) {
        this.userInfo.currentTime = new Date(new Date().getTime() + this.userInfo['TimeCorrection']);
      }
    },
  }

})