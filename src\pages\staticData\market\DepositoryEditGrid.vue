<template>
  <div id="depository-manager">
    <!-- Depository Section Header -->
    <el-row>
      <span style="font-size: 16px; font-weight: bold;">Location/Depository</span>
      <div style="paddingBlock:5px;width: 100%;display: flex;flex-flow: wrap;border-top: 1px solid;"></div>
    </el-row>

    <!-- Main Depository Table -->
    <el-row style="width: 100%; margin: 0; padding: 0;">
      <el-table
        id="depository-table"
        :data="depositoryTableList"
        row-key="oid"
        :row-class-name="tableRowClassName"
        :cell-style="cellStyle as any"
        default-expand-all
        @row-click="selectedRow"
        table-layout="auto"
        ref="gridRef"
        :border="true"
        scrollbar-always-on
        class-name="multiple-table"
        style="width: calc(100% - 80px); margin: 0; padding: 0; float: left;height: 200px;">
        
        <!-- Depository Code Column -->
        <el-table-column prop="depoCode" :label="$t('csscl.si.common.depoCode')" width="250">
          <template #default="scope">
            <template v-if="editingRow && highlightRow === scope.row">
              <SearchInput 
                v-model="editingRow.depoCode" 
                url="/datamgmt/api/v1/searchinput" 
                title="Sub-Custodian Code"
                :showDesc="false" 
                :dbClick="(row, code, desc) => { depoCodeDbClickCheck(scope.row, code, desc); }"
                :params="{ searchType: 'clearingAgentCode' }" 
                :columns="[
                  {
                    title: $t('csscl.agent.clearingAgentCode'),
                    colName: 'code',
                  },
                  {
                    title: $t('csscl.agent.shortName'),
                    colName: 'codeDesc',
                  }
                ]" 
                :pageSizes="[10, 20, 30]">
              </SearchInput>
            </template>
            <template v-else>
              {{ scope.row.depoCode }}
            </template>
          </template>
        </el-table-column>
     
        <!-- Depository Description Column -->
        <el-table-column prop="depoDesc" :label="$t('csscl.si.common.depoDesc')" width="250">
          <template #default="scope">
            <template v-if="editingRow && highlightRow === scope.row">
              {{ editingRow.depoDesc }}
            </template>
            <template v-else>
              {{ scope.row.depoDesc }}
            </template>
          </template>
        </el-table-column>

        <!-- Primary Swift BIC Code Column -->
        <el-table-column prop="priSwiftBicCode" :label="$t('csscl.si.common.priSwiftBicCode')" width="300">
          <template #default="scope">
            <template v-if="editingRow && highlightRow === scope.row">
              <el-input v-model="editingRow.priSwiftBicCode" />
            </template>
            <template v-else>
              {{ scope.row.priSwiftBicCode }}
            </template>
          </template>
        </el-table-column>

        <!-- Secondary Swift BIC Code Column -->
        <el-table-column prop="secSwiftBicCode" :label="$t('csscl.si.common.secSwiftBicCode')" width="300">
          <template #default="scope">
            <template v-if="editingRow && highlightRow === scope.row">
              <el-input v-model="editingRow.secSwiftBicCode" />
            </template>
            <template v-else>
              {{ scope.row.secSwiftBicCode }}
            </template>
          </template>
        </el-table-column>

        <!-- Status Column -->
        <el-table-column prop="status" label="status" width="250">
          <template #default="scope">
            {{ getCommonDesc('STATUS', scope.row.status) }}
          </template>
        </el-table-column>

        <!-- Record Status Column -->
        <el-table-column prop="recordStatus" label="recordStatus" width="200">
          <template #default="scope">
            <template v-if="editingRow && highlightRow === scope.row">
              {{ getRecordStatusDesc(scope.row) }}
            </template>
            <template v-else>
              {{ getRecordStatusDesc(scope.row) }}
            </template>
          </template>
        </el-table-column>

        <!-- Operations Column -->
        <el-table-column fixed="right" :label="$t('common.table.operation')">
          <template #default="scope">
            <template v-if="editingRow && highlightRow === scope.row">
              <el-button type="primary" size="small" @click="saveEdit(scope.row, scope.$index)">Save</el-button>
              <el-button size="small" @click="cancelEdit">Cancel</el-button>
            </template>
            <template v-else>
              <el-button :icon="Edit" link type="primary" :disabled="isEditing || disabled"
                @click.stop="startEdit(scope.row)"></el-button>
              <el-button :icon="Delete" link type="primary" :disabled="isEditing || disabled || getisDeleteRecord(scope.row)"
                @click.stop="removeRow(scope.row)"></el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- Add Button -->
      <el-space style="margin-left: 10px; float: left;" direction="vertical">
        <el-button :disabled="isEditing || disabled" :icon="Plus" @click="addRow" />
      </el-space>
    </el-row>

    <!-- Currency Table -->
    <el-row style="width: 100%; margin: 0; padding: 0;margin-top: 10px;">
      <el-table
        id="depository-currency-table"
        :data="currencyTableList"
        row-key="oid"
        :row-class-name="currencyTableRowClassName"
        :cell-style="cellStyle as any"
        @row-click="currencySelectedRow"
        default-expand-all
        table-layout="auto"
        ref="currencyGrid"
        :border="true"
        scrollbar-always-on
        class-name="multiple-table"
        style="width: calc(100% - 80px); margin: 0; padding: 0; float: left;height: 200px;">

        <!-- Currency Column -->
        <el-table-column prop="currency" :label="$t('csscl.si.common.currency')" width="250">
          <template #default="scope">
            <template v-if="currencyEditingRow && currencyHighlightRow === scope.row">
              <el-input v-model="currencyEditingRow.currency" :disabled="currencyTableDisabled" />
            </template>
            <template v-else>
              {{ scope.row.currency }}
            </template>
          </template>
        </el-table-column>

        <!-- Charge Suspense Account Column -->
        <el-table-column prop="chargeSuspAcctNo" :label="$t('csscl.si.common.chargeSuspAcctNo')" width="250">
          <template #default="scope">
            <template v-if="currencyEditingRow && currencyHighlightRow === scope.row">
              <el-input v-model="currencyEditingRow.chargeSuspAcctNo" :disabled="currencyTableDisabled" />
            </template>
            <template v-else>
              {{ scope.row.chargeSuspAcctNo }}
            </template>
          </template>
        </el-table-column>

        <!-- Income Suspense Account Column -->
        <el-table-column prop="incomeSuspAcctNo" :label="$t('csscl.si.common.incomeSuspAcctNo')" width="250">
          <template #default="scope">
            <template v-if="currencyEditingRow && currencyHighlightRow === scope.row">
              <el-input v-model="currencyEditingRow.incomeSuspAcctNo" :disabled="currencyTableDisabled" />
            </template>
            <template v-else>
              {{ scope.row.incomeSuspAcctNo }}
            </template>
          </template>
        </el-table-column>

        <!-- Settlement Suspense Account Column -->
        <el-table-column prop="setlSuspAcctNo" :label="$t('csscl.si.common.setlSuspAcctNo')" width="300">
          <template #default="scope">
            <template v-if="currencyEditingRow && currencyHighlightRow === scope.row">
              <el-input v-model="currencyEditingRow.setlSuspAcctNo" :disabled="currencyTableDisabled" />
            </template>
            <template v-else>
              {{ scope.row.setlSuspAcctNo }}
            </template>
          </template>
        </el-table-column>

        <!-- Currency Record Status Column -->
        <el-table-column prop="recordStatus" label="recordStatus" width="300">
          <template #default="scope">
            <template v-if="currencyEditingRow && currencyHighlightRow === scope.row">
              {{ getRecordStatusDesc(scope.row) }}
            </template>
            <template v-else>
              {{ getRecordStatusDesc(scope.row) }}
            </template>
          </template>
        </el-table-column>

        <!-- Currency Operations Column -->
        <el-table-column fixed="right" :label="$t('common.table.operation')">
          <template #default="scope">
            <template v-if="currencyEditingRow && currencyHighlightRow === scope.row">
              <el-button type="primary" size="small"
                @click="currencySaveEdit(scope.row, scope.$index)">Save</el-button>
              <el-button size="small" @click="currencyCancelEdit">Cancel</el-button>
            </template>
            <template v-else>
              <el-button :icon="Edit" link type="primary" :disabled="currencyIsEditing || currencyTableDisabled || disabled"
                @click.stop="currencyStartEdit(scope.row)"></el-button>
              <el-button :icon="Delete" link type="primary" :disabled="currencyIsEditing || currencyTableDisabled || disabled"
                @click.stop="currencyRemoveRow(scope.row)"></el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>

      <!-- Currency Add Button -->
      <el-space style="margin-left: 10px; float: left;" direction="vertical">
        <el-button :disabled="currencyIsEditing || currencyTableDisabled || disabled" :icon="Plus" @click="currencyAddRow" />
      </el-space>
    </el-row>

    <!-- Depository Info Section -->
    <el-container style="margin-top:10px;" id="depository-info">
      <el-aside width="100%">
        <div>
          <FormRow>
            <FormItemSign :detailsRef="detailsRef" :label="$t('csscl.si.common.caPayMethod')" label-width="350px"
              prop="caPayMethodCode">
              <Select v-model="depositoryForm.caPayMethodCode" type='CASH_SETTLE_METHOD_SI_CODE' style="width: 330px"
                :disabled="depositoryInfoDisabled" />
            </FormItemSign>

            <FormItemSign :detailsRef="detailsRef" :label="$t('csscl.si.common.siPayMethod')" label-width="350px"
              prop="setlPayMethodCode">
              <Select v-model="depositoryForm.setlPayMethodCode" type='CASH_SETTLE_METHOD_SI_CODE'
                style="width: 330px" :disabled="depositoryInfoDisabled" />
            </FormItemSign>
          </FormRow>
          <FormRow>
            <FormItemSign :detailsRef="detailsRef" :label="$t('csscl.si.common.genDepoIntfInd')" label-width="350px"
              prop="genDepoIntfInd">
              <Select v-model="depositoryForm.genDepoIntfInd" type='GEN_DEPO_INTERFACE' style="width: 250px"
                :disabled="depositoryInfoDisabled" />
            </FormItemSign>
            <FormItemSign :detailsRef="detailsRef" :label="$t('csscl.si.common.intfFormatCode')" label-width="350px"
              prop="intfFormatCode">
              <Select v-model="depositoryForm.intfFormatCode" type='INTERFACE_FORMAT' style="width: 250px"
                :disabled="depositoryInfoDisabled" />
            </FormItemSign>
          </FormRow>
          <FormRow>
            <FormItemSign :detailsRef="detailsRef" :label="$t('csscl.si.common.depoIntfAddr')" label-width="350px"
              prop="depoIntfAddr">
              <InputText maxlength="40" v-model="depositoryForm.depoIntfAddr" style="width: 250px"
                :disabled="depositoryInfoDisabled" />
            </FormItemSign>
          </FormRow>
        </div>
        <div>
          <el-card style="max-width: 440px">
            <span style="text-decoration: underline;">Days of Settlement Cycle</span>
            <br><br>
            <FormRow>
              <FormItemSign :detailsRef="detailsRef" :label="$t('csscl.si.common.settlePeriodCashRecDay')"
                label-width="120" prop="settlePeriodCashRecDay">
                <InputNumber scale="0" maxlength="2" v-model="depositoryForm.settlePeriodCashRecDay"
                  :disabled="depositoryInfoDisabled" />
              </FormItemSign>
            </FormRow>
            <FormRow>
              <FormItemSign :detailsRef="detailsRef" :label="$t('csscl.si.common.settlePeriodCashDeliverDay')"
                label-width="120" prop="settlePeriodCashDeliverDay">
                <InputNumber scale="0" maxlength="2" v-model="depositoryForm.settlePeriodCashDeliverDay"
                  :disabled="depositoryInfoDisabled" />
              </FormItemSign>
            </FormRow>
            <FormRow>
              <FormItemSign :detailsRef="detailsRef" :label="$t('csscl.si.common.settlePeriodStockRecDay')"
                label-width="120" prop="settlePeriodStockRecDay">
                <InputNumber scale="0" maxlength="2" v-model="depositoryForm.settlePeriodStockRecDay"
                  :disabled="depositoryInfoDisabled" />
              </FormItemSign>
            </FormRow>
            <FormRow>
              <FormItemSign :detailsRef="detailsRef" :label="$t('csscl.si.common.settlePeriodStockDeliverDay')"
                label-width="120" prop="settlePeriodStockDeliverDay">
                <InputNumber scale="0" maxlength="2" v-model="depositoryForm.settlePeriodStockDeliverDay"
                  :disabled="depositoryInfoDisabled" />
              </FormItemSign>
            </FormRow>
          </el-card>
        </div>
      </el-aside>
    </el-container>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, getCurrentInstance, nextTick, watch, onMounted } from 'vue';
import { Plus, Delete, Edit } from '@element-plus/icons-vue';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import SearchInput from '~/components/SearchInput/SearchInput.vue';
import Select from '~/components/Select.vue';
import InputText from '~/components/InputText.vue';
import InputNumber from '~/components/InputNumber.vue';
import FormRow from '~/components/FormRow.vue';
import { getCommonDesc, showErrorMsg, getRecordStatusDesc, highlight,randomHashCode } from '~/util/Function.js';
import { ElMessageBox } from 'element-plus';

// Props definition
interface Props {
  modelValue: any[];
  disabled?: boolean;
  detailsRef?: any;
  cellStyle?: (row: any, column: any, rowIndex: number, columnIndex: number) => any;
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  detailsRef: null
});

// Emits definition
const emit = defineEmits<{
  'update:modelValue': [value: any[]];
  'data-changed': [data: any[]];
}>();

const { proxy } = getCurrentInstance();

// Reactive data
const depositoryTableList = ref<any[]>([]);
const editingRow = ref<any | null>(null);
const newRow = ref<any | null>(null);
const highlightRow = ref<any | null>(null);
const isEditing = ref<boolean>(false);

// Currency table data
const currencyTableList = ref<any[]>([]);
const currencyEditingRow = ref<any | null>(null);
const currencyNewRow = ref<any | null>(null);
const currencyHighlightRow = ref<any | null>(null);
const currencyIsEditing = ref<boolean>(false);
const currencyTableDisabled = ref<boolean>(true);

// Modify data tracking - 修改数据缓存机制
const midifyData = ref<any>({});

// Depository info form
const depositoryForm = reactive({
  caPayMethodCode: null as string | null,
  setlPayMethodCode: null as string | null,
  genDepoIntfInd: null as string | null,
  intfFormatCode: null as string | null,
  depoIntfAddr: null as string | null,
  settlePeriodCashRecDay: null as string | null,
  settlePeriodCashDeliverDay: null as string | null,
  settlePeriodStockRecDay: null as string | null,
  settlePeriodStockDeliverDay: null as string | null,
});

const depositoryInfoDisabled = ref<boolean>(true);
const currentSelectedDepository = ref<any>(null);
const currentSelectedDepositoryForCurrency = ref<any>(null);

// Depository info data management
const loadDepositoryInfoData = (row: any) => {
  if (row) {
    depositoryForm.caPayMethodCode = row.caPayMethodCode || null;
    depositoryForm.setlPayMethodCode = row.setlPayMethodCode || null;
    depositoryForm.genDepoIntfInd = row.genDepoIntfInd || null;
    depositoryForm.intfFormatCode = row.intfFormatCode || null;
    depositoryForm.depoIntfAddr = row.depoIntfAddr || null;
    depositoryForm.settlePeriodCashRecDay = row.settlePeriodCashRecDay || null;
    depositoryForm.settlePeriodCashDeliverDay = row.settlePeriodCashDeliverDay || null;
    depositoryForm.settlePeriodStockRecDay = row.settlePeriodStockRecDay || null;
    depositoryForm.settlePeriodStockDeliverDay = row.settlePeriodStockDeliverDay || null;
    currentSelectedDepository.value = row;
  }
};

const clearDepositoryInfoData = () => {
  depositoryForm.caPayMethodCode = null;
  depositoryForm.setlPayMethodCode = null;
  depositoryForm.genDepoIntfInd = null;
  depositoryForm.intfFormatCode = null;
  depositoryForm.depoIntfAddr = null;
  depositoryForm.settlePeriodCashRecDay = null;
  depositoryForm.settlePeriodCashDeliverDay = null;
  depositoryForm.settlePeriodStockRecDay = null;
  depositoryForm.settlePeriodStockDeliverDay = null;
  currentSelectedDepository.value = null;
};

const saveDepositoryInfoToRow = (row: any) => {
  if (row) {
    row.caPayMethodCode = depositoryForm.caPayMethodCode;
    row.setlPayMethodCode = depositoryForm.setlPayMethodCode;
    row.genDepoIntfInd = depositoryForm.genDepoIntfInd;
    row.intfFormatCode = depositoryForm.intfFormatCode;
    row.depoIntfAddr = depositoryForm.depoIntfAddr;
    row.settlePeriodCashRecDay = depositoryForm.settlePeriodCashRecDay;
    row.settlePeriodCashDeliverDay = depositoryForm.settlePeriodCashDeliverDay;
    row.settlePeriodStockRecDay = depositoryForm.settlePeriodStockRecDay;
    row.settlePeriodStockDeliverDay = depositoryForm.settlePeriodStockDeliverDay;
  }
};

// Currency data management
const loadCurrencyDataForDepository = (depositoryRow: any) => {
  if (depositoryRow) {
    currencyTableList.value = depositoryRow.marketDepoSubVPOList || [];
    currentSelectedDepositoryForCurrency.value = depositoryRow;
    currencyTableDisabled.value = false;
  }
};

const saveCurrencyDataToDepository = () => {
  if (currentSelectedDepositoryForCurrency.value) {
    currentSelectedDepositoryForCurrency.value.marketDepoSubVPOList = [...currencyTableList.value];
  }
};

const clearCurrencyTable = () => {
  currencyTableList.value = [];
  currentSelectedDepositoryForCurrency.value = null;
  currencyTableDisabled.value = true;
  if (currencyIsEditing.value) {
    currencyCancelEdit();
  }
};

// Modify data management - 修改数据管理
const clearModifyData = () => {
  midifyData.value = {};
};

const getModifyRecords = () => {
  return Object.values(midifyData.value);
};

const updateModifyData = (record: any) => {
  if (record && record.oid) {
    midifyData.value[record.oid] = { ...record };
  }
};

const getisDeleteRecord = (row) => {
  return row.mkckAction == 'D';
}

// Emit data changes
const emitDataChange = () => {
  emit('update:modelValue', depositoryTableList.value);
  emit('data-changed', depositoryTableList.value);
};



const tableRowClassName = ({ row }) => {
  let selectedClass = "";
  if (highlightRow.value && highlightRow.value.oid == row.oid) {
    selectedClass = ' selected-row';
  }
  return selectedClass;
};

const currencyTableRowClassName = ({ row }) => {
  let selectedClass = "";
  if (currencyHighlightRow.value && currencyHighlightRow.value.oid == row.oid) {
    selectedClass = ' selected-row';
  }
  return selectedClass;
};

const cellStyle = (row, column, rowIndex, columnIndex) => {
  if (props.cellStyle) {
    let style = props.cellStyle(row, column, rowIndex, columnIndex);
    if (style) {
      return style;
    }
  }
  return highlight(row);
};

// Depository table operations
function selectedRow(row: any) {
  if (isEditing.value) return;
  highlightRow.value = row;
  loadDepositoryInfoData(row);
  depositoryInfoDisabled.value = true;
  loadCurrencyDataForDepository(row);
  currencyTableDisabled.value = true;
}

function startEdit(row: any) {
  if (isEditing.value || props.disabled) return;
  editingRow.value = { ...row };
  highlightRow.value = row;
  isEditing.value = true;

  if (newRow.value && newRow.value !== row) {
    newRow.value = null;
  }

  loadDepositoryInfoData(row);
  depositoryInfoDisabled.value = false;
  loadCurrencyDataForDepository(editingRow.value);
  currencyTableDisabled.value = false;
}

function saveEdit(row: any, rowIndex: number) {
  if (!editingRow.value.depoCode) {
    showErrorMsg('depoCode is not null');
    return;
  }

  const exist = depositoryTableList.value.find(
    (item, idx) => item.depoCode === editingRow.value.depoCode && idx !== rowIndex
  );
  if (exist) {
    showErrorMsg('The same record already exists!');
    return;
  }

  saveDepositoryInfoToRow(editingRow.value);
  saveCurrencyDataToDepository();

  // 设置记录状态
  if (!editingRow.value.sysCreateDate) {
    editingRow.value.mkckAction = 'C';
    editingRow.value.recordStatus = 'PD';
  } else {
    editingRow.value.mkckAction = 'U';
    editingRow.value.recordStatus = 'PD';
  }

  Object.assign(row, editingRow.value, { finishWrite: true });

  // 更新修改数据缓存
  updateModifyData(row);

  editingRow.value = null;
  highlightRow.value = row;
  isEditing.value = false;
  newRow.value = null;

  depositoryInfoDisabled.value = true;
  loadCurrencyDataForDepository(row);
  currencyTableDisabled.value = true;

  emitDataChange();
}

function cancelEdit() {
  const isNewRow = newRow.value && editingRow.value &&
    newRow.value.oid === editingRow.value.oid;

  const wasNewRow = isNewRow;
  const previousRow = highlightRow.value;
  editingRow.value = null;
  highlightRow.value = null;
  isEditing.value = false;

  if (isNewRow && newRow.value) {
    const idx = depositoryTableList.value.indexOf(newRow.value);
    if (idx > -1) depositoryTableList.value.splice(idx, 1);
    newRow.value = null;
    emitDataChange();
  }

  if (wasNewRow) {
    clearDepositoryInfoData();
    depositoryInfoDisabled.value = true;
    clearCurrencyTable();
  } else {
    depositoryInfoDisabled.value = true;
    if (previousRow) {
      highlightRow.value = previousRow;
      loadDepositoryInfoData(previousRow);
      loadCurrencyDataForDepository(previousRow);
      currencyTableDisabled.value = true;
    }
  }
}

function addRow() {
  if (isEditing.value || props.disabled) return;

  const row = {
    oid: Math.trunc(randomHashCode()),
    depoCode: '',
    depoDesc: '',
    priSwiftBicCode: '',
    secSwiftBicCode: '',
    caPayMethodCode: '',
    setlPayMethodCode: '',
    genDepoIntfInd: '',
    intfFormatCode: '',
    depoIntfAddr: '',
    settlePeriodCashRecDay: '',
    settlePeriodCashDeliverDay: '',
    settlePeriodStockRecDay: '',
    settlePeriodStockDeliverDay: '',
    marketDepoSubVPOList: [],
    mkckAction: 'C',
    recordStatus: 'PD',
    sysCreateDate: null,
    finishWrite: false,
    status: 'A',
    level: 1,
  };

  depositoryTableList.value.push(row);
  newRow.value = row;
  editingRow.value = { ...row };
  highlightRow.value = row;
  isEditing.value = true;

  clearDepositoryInfoData();
  depositoryForm.caPayMethodCode = 'A';
  depositoryForm.setlPayMethodCode = 'A';
  depositoryInfoDisabled.value = false;

  clearCurrencyTable();
  currencyTableDisabled.value = false;
  currentSelectedDepositoryForCurrency.value = editingRow.value;

  emitDataChange();
}

function removeRow(row: any) {
  if (props.disabled) return;
  if (row.mkckAction == 'D') {
      return;
    }

  selectedRow(row);

  ElMessageBox.confirm(
        'Delete the record?',
        'Warning',
        {
            confirmButtonText: 'OK',
            cancelButtonText: 'Cancel',
            type: 'warning',
        }
    ).then(async () => {
        if (row?.mkckAction == 'C' && row?.recordStatus == 'PD') {
            // 从显示数据中过滤掉新建记录
            depositoryTableList.value = depositoryTableList.value.filter(function(item){
                return item?.oid !== row?.oid;
            });

            if (!row.sysCreateDate) {
                // 新建未提交记录，从修改缓存中删除
                delete midifyData.value[row.oid];
            } else {
                // 新建已提交记录，在修改缓存中标记为删除
                midifyData.value[row.oid] = {
                    ...row,
                    mkckAction: 'D',
                };
            }

            // 清理UI状态
            // if (highlightRow.value === row) {
            //     highlightRow.value = null;
            //     clearDepositoryInfoData();
            //     depositoryInfoDisabled.value = true;
            //     clearCurrencyTable();
            // }
        } else {
            // 标记为删除状态
            row.mkckAction = 'D';
            row.recordStatus = 'PD';
            midifyData.value[row.oid] = {
                ...row,
                mkckAction: 'D',
                recordStatus: 'PD',
            };
        }

        // 设置删除标记
        row.isDelete = true;

        // 取消编辑状态
        if (editingRow.value && row.oid === editingRow.value.oid) {
            cancelEdit();
        }

      emitDataChange();

    }).catch(() => {
    });


}

const depoCodeDbClickCheck = (row: any, code: string, desc: string) => {
  const exist = depositoryTableList.value.find(
    (item) => item.depoCode === code
  );
  if (exist) {
    showErrorMsg('depoCode is exist');
    return;
  }
  editingRow.value.depoCode = code;
  editingRow.value.depoDesc = desc;
};

// Currency table operations
function currencySelectedRow(row: any) {
  if (currencyIsEditing.value) return;
  currencyHighlightRow.value = row;
}

function currencyStartEdit(row: any) {
  if (currencyIsEditing.value || currencyTableDisabled.value || props.disabled) return;
  currencyEditingRow.value = { ...row };
  currencyHighlightRow.value = row;
  currencyIsEditing.value = true;

  if (currencyNewRow.value && currencyNewRow.value !== row) {
    currencyNewRow.value = null;
  }
}

function currencySaveEdit(row: any, rowIndex: number) {
  if (!currencyEditingRow.value.currency) {
    showErrorMsg('currency is not null');
    return;
  }

  const exist = currencyTableList.value.find(
    (item, idx) => item.currency === currencyEditingRow.value.currency && idx !== rowIndex
  );
  if (exist) {
    showErrorMsg('currency is exist');
    return;
  }

  // 设置货币记录状态
  if (!currencyEditingRow.value.sysCreateDate) {
    currencyEditingRow.value.mkckAction = 'C';
    currencyEditingRow.value.recordStatus = 'PD';
  } else {
    currencyEditingRow.value.mkckAction = 'U';
    currencyEditingRow.value.recordStatus = 'PD';
  }

  Object.assign(row, currencyEditingRow.value, { finishWrite: true });
  currencyEditingRow.value = null;
  currencyHighlightRow.value = null;
  currencyIsEditing.value = false;
  currencyNewRow.value = null;

  saveCurrencyDataToDepository();

  // 更新父级存管机构的修改状态
  if (currentSelectedDepositoryForCurrency.value) {
    updateModifyData(currentSelectedDepositoryForCurrency.value);
  }

  emitDataChange();
}

function currencyCancelEdit() {
  const isNewRow = currencyNewRow.value && currencyEditingRow.value &&
    currencyNewRow.value.oid === currencyEditingRow.value.oid;

  currencyEditingRow.value = null;
  currencyHighlightRow.value = null;
  currencyIsEditing.value = false;

  if (isNewRow && currencyNewRow.value) {
    const idx = currencyTableList.value.indexOf(currencyNewRow.value);
    if (idx > -1) currencyTableList.value.splice(idx, 1);
    currencyNewRow.value = null;
    saveCurrencyDataToDepository();
    emitDataChange();
  }
}

function currencyAddRow() {
  if (currencyIsEditing.value || currencyTableDisabled.value || props.disabled) {
    if (currencyIsEditing.value) {
      showErrorMsg('正在编辑');
    }
    return;
  }

  if (!currentSelectedDepositoryForCurrency.value) {
    showErrorMsg('Please select a depository first');
    return;
  }

  const row = {
    oid: Math.trunc(randomHashCode()),
    currency: '',
    chargeSuspAcctNo: '',
    incomeSuspAcctNo: '',
    setlSuspAcctNo: '',
    depositoryOid: currentSelectedDepositoryForCurrency.value.oid,
    mkckAction: "C",
    recordStatus: "PD",
    sysCreateDate: null,
    finishWrite: false,
    status: "A",
  };

  currencyTableList.value.push(row);
  currencyNewRow.value = row;
  currencyEditingRow.value = { ...row };
  currencyHighlightRow.value = row;
  currencyIsEditing.value = true;

  saveCurrencyDataToDepository();
  emitDataChange();
}

function currencyRemoveRow(row: any) {
  if (props.disabled) return;
  if (row.mkckAction == 'D') {
      return;
  }

  ElMessageBox.confirm(
        'Delete the record?',
        'Warning',
        {
            confirmButtonText: 'OK',
            cancelButtonText: 'Cancel',
            type: 'warning',
        }
    ).then(async () => {
        if (row?.mkckAction == 'C' && row?.recordStatus == 'PD') {
            // 复刻EditGrid逻辑：从显示数据中过滤掉新建记录
            currencyTableList.value = currencyTableList.value.filter(function(item){
                return item?.oid !== row?.oid;
            });

            if (!row.sysCreateDate) {
                // 新建未提交记录，不需要在修改缓存中处理（因为货币数据保存在父级存管机构中）
            } else {
                // 新建已提交记录，标记为删除
                row.mkckAction = 'D';
            }
        } else {
            // 复刻EditGrid逻辑：标记为删除状态
            row.mkckAction = 'D';
            row.recordStatus = 'PD';
        }

        // 复刻EditGrid逻辑：设置删除标记
        row.isDelete = true;

        // DepositoryEditGrid特有逻辑：清理UI状态
        if (currencyHighlightRow.value === row) {
            currencyHighlightRow.value = null;
        }

        if (currencyEditingRow.value && row.oid === currencyEditingRow.value.oid) {
            currencyCancelEdit();
        }

        // DepositoryEditGrid特有逻辑：保存货币数据到父级存管机构
        saveCurrencyDataToDepository();

        // 更新父级存管机构的修改状态
        if (currentSelectedDepositoryForCurrency.value) {
            updateModifyData(currentSelectedDepositoryForCurrency.value);
        }

        emitDataChange();
    }).catch(() => {
    });
}

// Public methods for parent component
const getDepositoryData = () => {
  return depositoryTableList.value;
};

const setDepositoryData = (data: any[]) => {
  depositoryTableList.value = data || [];
  if (data && data.length > 0) {
    nextTick(() => {
      selectedRow(data[0]);
    });
  } else {
    clearDepositoryInfoData();
    clearCurrencyTable();
  }
};

const clearAllData = () => {
  depositoryTableList.value = [];
  clearDepositoryInfoData();
  clearCurrencyTable();
  highlightRow.value = null;
  editingRow.value = null;
  isEditing.value = false;
  clearModifyData(); // 清理修改数据缓存
  emitDataChange();
};


onMounted(() => {
    if (depositoryTableList.value.length > 0) {
        selectedRow(depositoryTableList.value[0]);
    }
});
// Watch for prop changes
watch(() => props.modelValue, (newValue) => {
  if (newValue && Array.isArray(newValue)) {
    depositoryTableList.value = [...newValue];
    // 默认选中第一行
    if (newValue.length > 0) {
      
    } else {
      clearDepositoryInfoData();
      clearCurrencyTable();
    }
  }
}, { immediate: true, deep: true });

// Expose methods to parent component
defineExpose({
  getDepositoryData,
  setDepositoryData,
  clearAllData,
  depositoryTableList,
  isEditing,
  currencyIsEditing,
  // 修改数据管理方法
  getModifyRecords,
  clearModifyData,
  midifyData
});
</script>

<style scoped>
/* 复刻EditGrid的选中行样式 - 使用全局样式中的selected-row */
/* selected-row样式在全局样式src/styles/index.scss中定义 */
</style>
