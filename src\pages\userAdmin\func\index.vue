<template>
  <BasePanel :searchParams="searchParams" :paramListData='paramListData' url="/auth/api/v1/user/roleassignlog/list" :showDetails="showDetails"
    :afterSearch="afterSearch" :deleteRow="deleteRow" :editRow="editRow" ref="tableRef"
    :sortProp="{ 'phoneNo': ['phoneExt', 'phoneNo'] }">
    <template v-slot:searchPanel="slotProps">
      <FormRow>
        <ElFormItemProxy label-width="120" :label="$t('csscl.useradmin.func.funcId')" prop="rightIdList">
          <SearchInput v-model="slotProps.form.rightIdList" maxlength="150" style="width: 500px"
          inputStyle="width:200px" specialTxt
          url="/auth/api/v1/user/function/list" :title="$t('csscl.useradmin.func.function')" :params="{}" :columns="[
              {
                title: $t('csscl.useradmin.func.funcId'),
                colName: 'rightId',
              },
              {
                title: $t('csscl.useradmin.func.funcName'),
                colName: 'rightDesc',
              }
            ]" :pageSizes="[10, 20, 30]">
          </SearchInput>
        </ElFormItemProxy>
        <ElFormItemProxy>
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy label-width="120" :label="$t('csscl.useradmin.func.reportId')" prop="reposrTemplateCodeList">
          <GeneralSearchInput v-model="slotProps.form.reposrTemplateCodeList" 
            :title="$t('csscl.useradmin.func.report')"
            inputStyle="width:200px"
            style="width:500px"
            searchType="reportTemplateCode"  />
        </ElFormItemProxy>
        <ElFormItemProxy label-width="120" :label="$t('common.title.recordStatus')" prop="multipleRecordStatus">
          <MultipleSelect v-model="slotProps.form.multipleRecordStatus" v-model:desc="paramListData.multipleRecordStatus"  />
        </ElFormItemProxy>
      </FormRow>
    </template>
    <template v-slot:tableColumn>
      <el-table-column sortable="custom" prop="reposrTemplateCodeList"
        :label="$t('csscl.useradmin.func.reposrTemplateCodeList')">
        <template #default="scope">
          <!-- scope.row -->
          {{ scope.row.rightIdList ? scope.row.rightIdList + "; " : "" }} {{ scope.row.reposrTemplateCodeList }}
        </template>
      </el-table-column>
      <el-table-column sortable="custom" prop="reposrTemplateNameList"
        :label="$t('csscl.useradmin.func.funcReportName')">
        <template #default="scope">
          <!-- scope.row -->
          {{ scope.row.rightNameList ? scope.row.rightNameList + "; " : "" }} {{ scope.row.reposrTemplateNameList }}
        </template>
      </el-table-column>
      <el-table-column sortable="custom" prop="recordStatus" :label="$t('csscl.useradmin.usr.recordStatus')">
        <template #default="scope">
          {{ getRecordStatusDesc(scope.row) }}
        </template>
      </el-table-column>
    </template>
 
  </BasePanel>
  <Details ref="detailsRef" :reload="reload" />
  <QueryDetails ref="queryDetailsRef" />
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance, watch } from 'vue';
import {
  Search
} from '@element-plus/icons-vue'
import BasePanel from '~/pages/base/index.vue'
import Details from './Details.vue'
import QueryGrid from './QueryGrid.vue'
import QueryDetails from './QueryDetails.vue'
import { getCommonDesc, getRecordStatusDesc } from '~/util/Function.js';
const { proxy } = getCurrentInstance()
const paramListData = ref({});
const searchParams = ref({
  rightIdList: '',
  reposrTemplateCodeList: '',
  multipleRecordStatus:[],
});

const tableRef = ref();
const detailsRef = ref();
const showDetails = (row, disabled) => {
  detailsRef.value.showDetails(row, disabled);
}
const deleteRow = (row) => {
  proxy.$axios.delete("/auth/api/v1/user/roleassignlog?sysRoleAssignLogOid=" + row.sysRoleAssignLogOid).then((body) => {
    if (body.success) {
      tableRef.value.load();
    }
  });
}
const editRow = (row) => {
  detailsRef.value.editRow(row);
}
const reload = () => {
  tableRef.value.load();
}
//-------------------------------
const rightsRef = ref();
const reportsRef = ref();
const queryDetailsRef = ref();

const afterSearch = (param) => {
  // rightsRef.value.onSearch({
  //   rightId: param.rightIdList
  // });
  // reportsRef.value.onSearch({
  //   reportTemplateCode: param.reposrTemplateCodeList
  // });
}

const showQueryDetails = (row, disabled) => {
  queryDetailsRef.value.showDetails(row, disabled);
}
//paramList 参数显示用的
function recordStatusType(value){
  paramListData._value.recordStatus =  getCommonDesc('RECORD_STATUS', value);
}

</script>

<style></style>