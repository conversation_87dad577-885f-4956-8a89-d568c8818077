<template>
  <BasePanel 
    :searchParams="searchParams" 
    url="/datamgmt/api/v1/swift/list" 
    :params="{modeEdit: 'Y'}" 
    :isHideSearch="true"
    :hideOperation="true"
    ref="tableRef"
    >
    
    <template v-slot:searchPanel="slotProps" >
      <FormRow>
        <ElFormItemProxy label-width="180px" :label="$t('csscl.swift.opencloseSwift')" prop="opencloseSwift">
          <el-switch v-model="swiftSts" active-value="Open" inactive-value="Close" @change="sw" />
      </ElFormItemProxy>
    </FormRow>
    </template>
    <template v-slot:tableColumn>
        <el-table-column align="left" header-align="center" sortable="custom" prop="swiftCreateDt" :label="$t('csscl.swift.swiftCreateDt')" width="600" />
        <el-table-column align="left" header-align="center" sortable="custom" prop="swiftUser" :label="$t('csscl.swift.swiftUser')" width="600" />
        <el-table-column align="left" header-align="center" sortable="custom" prop="swiftAction" :label="$t('csscl.swift.swiftAction')" />       
    </template>
  </BasePanel>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance, watch } from 'vue';
import { Search } from '@element-plus/icons-vue';
import BasePanel from '~/pages/base/index.vue';
import { useCookies } from "vue3-cookies";
import { log } from 'console';

const { cookies } = useCookies();
const { proxy } = getCurrentInstance();

const searchParams = ref({
});

const tableRef = ref();
const swiftSts = ref(true);

//-------------------------------


function sw(e){
  
  if (!sw.value) {
    sw.value = true;
    return;
  }
  
  proxy.$axios.post("/eapmgmt/api/v1/datafeed/swift/maint", {"maintFlg":e.substring(0,1)}).then((obj)=>{
    if(obj.respBody.acknowledged){
        proxy.$axios.patch("/datamgmt/api/v1/swift/maint?swiftSwitch="+e+"&userId="+cookies.get('username'))
      .then(()=>{  
        ElMessageBox.alert(proxy.$t('message.swift.opencloseSwift.success'), 'Success');
        tableRef.value.load(); 
        swiftSts.value=e;
      });
    }else{
      swiftSts.value= swiftSts.value === 'Open' ? 'Close' : 'Open';
      ElMessageBox.alert(proxy.$t('message.swift.opencloseSwift.fail'), 'Error');
    }
    
  }).catch(()=>{
    swiftSts.value= swiftSts.value === 'Open' ? 'Close' : 'Open';
    ElMessageBox.alert(proxy.$t('message.swift.opencloseSwift.fail'), 'Error');
  });

}

setTimeout(() => {
  proxy.$axios.post("/datamgmt/api/v1/swift/status").then((body)=>{
    swiftSts.value=body.data;
  });
}, 150);

</script>

<style>

</style>