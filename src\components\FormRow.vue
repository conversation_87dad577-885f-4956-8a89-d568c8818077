<template>
    <div  class="form-row ">
      <slot> </slot>
    </div>
</template>
<script lang="ts" setup>
    import { ref } from 'vue';

</script>
  <style>
  .form-row {
    width: 100%;
    display: flex; 
    flex-direction: row;
    margin-block: 5px;
    min-height: 24px;
  }
  
  .form-row>div {
    display: table-cell !important;
    flex: 1;
    padding-right: 5px !important;
    margin-right: 0px !important;
  }
  
  .form-row .ep-form-item__label {
    height: unset !important;
    line-height: unset !important;
    font-weight: bold !important;
    align-self: center;
    display: inline-table;
    margin: 0px!important;
    width: 220px ;
    vertical-align: middle;
  }
  .form-row .ep-form-item__label-wrap {
    display: inline-table;
    margin: 0px!important;
    width: 220px ;
  }
  .form-row .ep-form-item__content {
    display: inline-table;
    line-height: unset;
    vertical-align: middle;
  }
  .form-row .ep-form-item__error {
    display: contents;
  }
  

  /* change disabled  color ,checkbox background-color */
 .ep-input.is-disabled .ep-input__inner{
    -webkit-text-fill-color: black;
    cursor: not-allowed;
}
.ep-checkbox.is-disabled .ep-checkbox__label {
    -webkit-text-fill-color: black;
    cursor: not-allowed;
}
 .ep-textarea.is-disabled .ep-textarea__inner{
    background: #E6E6E6;
    -webkit-text-fill-color: black;
    cursor: not-allowed;
}
.ep-input.is-disabled .ep-input__wrapper {
    background-color: #E6E6E6;
    box-shadow: 0 0 0 1px var(--ep-disabled-border-color) inset;
}
.ep-select__selected-item {
  padding-left: 6px;
}
.ep-select__wrapper.is-disabled .ep-select__selected-item{
    -webkit-text-fill-color: black;
    cursor: not-allowed;
}
.ep-select__wrapper.is-disabled {
    background-color: #E6E6E6!important;
    box-shadow: 0 0 0 1px var(--ep-disabled-border-color) inset;
}
.is-checked, .ep-tree-node__content:hover {
    background-color: white!important;
}



.form-row .ep-input.is-disabled .ep-input__inner{
    -webkit-text-fill-color: black;
    cursor: not-allowed;
}
.form-row  .ep-textarea.is-disabled .ep-textarea__inner{
    background: #E6E6E6;
    -webkit-text-fill-color: black;
    cursor: not-allowed;
}
.form-row .ep-input.is-disabled .ep-input__wrapper {
    background-color: #E6E6E6;
    box-shadow: 0 0 0 1px var(--ep-disabled-border-color) inset;
}
.form-row .ep-select__wrapper.is-disabled .ep-select__selected-item{
    -webkit-text-fill-color: black;
    cursor: not-allowed;
}
.form-row .ep-select__wrapper.is-disabled {
    background-color: #E6E6E6!important;
    box-shadow: 0 0 0 1px var(--ep-disabled-border-color) inset;
}
.form-row .is-checked, .ep-tree-node__content:hover {
    background-color: white!important;
}

.form-row .form-item-sign .ep-form-item__content .ep-input__wrapper,
.form-item-sign .ep-form-item__content .ep-select__wrapper ,
.form-item-sign .column-sign .el-tooltip__content
{
  background-color: lightyellow !important;
}
  </style>
  