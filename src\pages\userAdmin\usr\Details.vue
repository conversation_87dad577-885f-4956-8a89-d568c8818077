<template>
    <BaseDetails ref="details" :handleSave="handleSave" :reload="props.reload" :viewOriginalForm="viewOriginalForm" :form="ruleForm" :beforeSubmit="beforeSubmit" :beforeSave="beforeSave">
        <el-form :validateOnRuleChange="false" :disabled="formDisabled" ref="ruleFormRef" style="width: 100%" :model="ruleForm.form" :rules="rules"
            status-icon>
            <FormRow>
                <FormItemSign :detailsRef="details" :label="$t('common.title.opCtryRegionCode')"
                    prop="opCtryRegionCode">
                    <GeneralSearchInput :disabled="notEdit" v-model="ruleForm.form.opCtryRegionCode" showDesc="false"
                        style="width: 120px" searchType="opCtryRegion" :change="changeOpCtryRegionCode" :dbClick="changeOpCtryRegionCode" />
                </FormItemSign>
            </FormRow>
            <FormRow>
                <FormItemSign :detailsRef="details" :label="$t('csscl.useradmin.usr.userId')" prop="userId">
                    <InputText :disabled="notEdit" v-model="ruleForm.form.userId" style="width:200px" uppercase maxlength="7" aria-autocomplete="none"/>
                </FormItemSign>
                <FormItemSign :detailsRef="details" :label="$t('csscl.useradmin.usr.countRegionCode')"
                    prop="ctryRegionCode">

                    <CtryRegionSearchInput v-model="ruleForm.form.ctryRegionCode" style="width: 120px"
                        showDesc="false" />
                </FormItemSign>
                <FormItemSign label-width="120" :detailsRef="details" :label="$t('csscl.useradmin.usr.status')"
                    prop="status">
                    <Select v-model="ruleForm.form.status" style="width: 150px" type="USER_STATUS" />
                </FormItemSign>
            </FormRow>
            <FormRow>
                <FormItemSign :detailsRef="details" :label="$t('csscl.useradmin.usr.userName')" prop="userName">
                    <el-input v-model="ruleForm.form.userName" chinese style="width:200px" maxlength="50" aria-autocomplete="none"/>
                </FormItemSign>
                <FormItemSign :detailsRef="details" :label="$t('csscl.useradmin.usr.email')" prop="email">
                    <el-input v-model="ruleForm.form.email" email maxlength="40" style="width: 300px" aria-autocomplete="none"/>
                </FormItemSign>
                <FormItemSign class="user-item-is-required__label" label-width="120"  :detailsRef="details" :label="$t('csscl.useradmin.usr.phoneNo')">
                    <el-space>
                        <FormItemSign :detailsRef="details" prop="phoneExt" hideLabel="Phone Ext">
                            <Select v-model="ruleForm.form.phoneExt" style="width: 100px" type='COUNTRY_AREA' vkEnqual />
                        </FormItemSign>
                        <FormItemSign :detailsRef="details" prop="phoneNo" hideLabel="Phone Number" >
                            <el-input v-model="ruleForm.form.phoneNo" number maxlength="12" :label="$t('csscl.useradmin.usr.phoneNo')" aria-autocomplete="none"/>
                        </FormItemSign>
                    </el-space>
                </FormItemSign>
            </FormRow>
            <FormRow>
                <FormItemSign :detailsRef="details" :label="$t('csscl.useradmin.usr.apprLimitEquiv')" prop="apprLimitAmt">
                  <el-space>
                    <el-input disabled value="HKD" style="width: 50px;"></el-input>
                    <InputNumber v-model="ruleForm.form.apprLimitAmt" style="width: 142px;" :change="changeApprLimitAmt" />
                  </el-space>
                </FormItemSign>

                <FormItemSign :detailsRef="details" :label="$t('csscl.useradmin.usr.mClassCode')" prop="mclassCode">
                    <el-input v-model="ruleForm.form.mclassCode" disabled maxlength="2" style="width: 100px;" aria-autocomplete="none"/>
                </FormItemSign>
                <ElFormItemProxy></ElFormItemProxy>
            </FormRow>
        </el-form>
        <span style="font-weight: bold;">Data Access Level</span>
        <el-divider style="margin: 5px 0px;" />
        <div style="width: 1000px; margin-left: 20px;">
            <div style="display: flow-root;">
                <div style="display: flex;">
                    <span style="width: 100px;align-self: center;">Access Level</span>
                    <Select
                        :disabled="formDisabled"
                        style="width: 200px"
                        v-model="accessLevelSetting"
                        :hideEmpty="true"
                        :source="accessLevelSettingSource"
                        :valueKey="'level'"
                    />
                </div>
                <div v-for="item in accessLevelSettingSource" v-show="item.level<=accessLevelSetting" style="display: flex;margin-top: 20px;">
                    <div style="width:200px"><span>{{ item.codeDesc }}</span></div>
                    <div style="width:100%">
                <!-- Start SIR-HLH-R78,Tom.Li, 2024/08/15 -->
                 <!-- <el-table border row-keys="userAccessLvlOid" default-expand-all @row-click="userAccessClick" :row-class-name="userAccessClickClassName" :data="userAccessLvlsDBFilterByLevel(item.level)" :style="{'anchor-name':'--'+item.code}"
                    style="width: calc(100% - 35px);min-height: 300px;float: left;margin-right: 5px;border: 1px var(--ep-border-color) var(--ep-border-style); ">
                    <el-table-column prop="code" :label="$t('csscl.useradmin.usr.code')" width="300"> -->
                <el-table border row-keys="userAccessLvlOid" default-expand-all @row-click="userAccessClick" :row-class-name="userAccessClickClassName" :data="userAccessLvlsDBFilterByLevel(item.level)" :style="{'anchor-name':'--'+item.code}"
                    style="width: calc(100% - 35px);min-height: 300px;float: left;margin-right: 5px;border: 1px var(--ep-border-color) var(--ep-border-style); " :cell-style="highlight">
                    <el-table-column prop="code" :label="$t('csscl.useradmin.usr.code')" width="250">
                <!-- End SIR-HLH-R78,Tom.Li, 2024/08/15 -->
                        <template #default="scope">
                            <!-- scope.row -->
                            <div v-show="scope.row.finishWrite">{{ scope.row["level" + scope.row.level + "Code"] }}</div>
                                <SearchInput v-show="item.level==2&&!scope.row.finishWrite" :disabled="scope.row.finishWrite" style="width: 240px"
                                    v-model="scope.row['level' + scope.row.level + 'Code']" url="/datamgmt/api/v1/searchinput"
                                    title="Bank Organization" :desc="ruleForm2.form.level4Desc"
                                    :dbClick="(row, code, desc) => {dbClickCheck(scope.row,code,desc);}"
                                    @changeDesc="(val) => scope.row['level' + scope.row.level + 'Desc'] = val"
                                    :params="{ searchType:'ctryRegion' }"
                                    :columns="[
                                        {
                                            title: $t('csscl.ctryRegionManagement.ctryRegionCode'),
                                            colName: 'code',
                                        },
                                        {
                                            title: $t('csscl.ctryRegionManagement.descpt'),
                                            colName: 'codeDesc',
                                        }
                                    ]" :pageSizes="[10, 20, 30]">
                                </SearchInput>
                                <!-- Start SIR-HLH-R78,Tom.Li, 2024/08/15 -->
                                <!-- <Select v-show="item.level==3&&!scope.row.finishWrite" v-model="scope.row['level' + scope.row.level + 'Code']" :disabled="scope.row.finishWrite" style="width: 150px" type="BANK_CODE" :labelKey="'code'" :change="(val)=>{checkCommon(scope.row,null,val)}"/> -->
                                <Select v-show="item.level==3&&!scope.row.finishWrite" v-model="scope.row['level' + scope.row.level + 'Code']" :disabled="scope.row.finishWrite" 
                                    style="width: 150px" type="BANK_CODE" :labelKey="'code'" 
                                    :change="(val)=>{checkCommon(scope.row,null,getCommonDesc('BANK_CODE', val))}"
                                    :source="$commonCodeStore.getAll['BANK_CODE'].filter(function (item) { return item.opCtryRegionCode == scope.row.level2Code})"
                                    />
                                <!-- End SIR-HLH-R78,Tom.Li, 2024/08/15 -->
                                <SearchInput v-show="item.level==4&&!scope.row.finishWrite" :disabled="scope.row.finishWrite" style="width: 240px"
                                    v-model="scope.row['level' + scope.row.level + 'Code']" url="/auth/api/v1/user/bankorgan/list"
                                    title="Bank Organization" :desc="ruleForm2.form.level4Desc"
                                    :dbClick="(row, code, desc) => {dbClickCheck(scope.row,code,desc);}"
                                    @changeDesc="(val) => scope.row['level' + scope.row.level + 'Desc'] = val"
                                    :params="{ status: 'A', opCtryRegionCode: ruleForm2.form.level2Code, bankCode: ruleForm2.form.level3Code }"
                                    :columns="[
                                        {
                                            title: $t('csscl.useradmin.usr.branchOrgan'),
                                            colName: 'branchCode',
                                        },
                                        {
                                            title: $t('csscl.useradmin.usr.branchName'),
                                            colName: 'branchName',
                                        }
                                    ]" :pageSizes="[10, 20, 30]">
                                </SearchInput>
                        </template>
                    </el-table-column>
                    <el-table-column prop="desc" :label="$t('csscl.useradmin.usr.desc')" width="200">
                        <template #default="scope">
                            <!-- scope.row -->
                            {{ scope.row["level" + scope.row.level + "Desc"] }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="recordStatus" :label="$t('csscl.useradmin.usr.recordStatus')">
                        <template #default="scope">
                            <!-- scope.row -->
                            {{ getCommonDesc('RECORD_STATUS', scope.row.recordStatus) }}
                            <span v-if="scope.row.recordStatus && scope.row.recordStatus !== 'A'">
                                for {{ getCommonDesc('MKCK_ACTION', scope.row.mkckAction) }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column fixed="right" :label="$t('common.table.operation')" width="120">
                        <template #default="scope">
                            <!-- Start SIR-HLH-R78,Tom.Li, 2024/08/15 -->
                             <!-- <el-button v-if="scope.row.mkckAction != 'D' && item.level<accessLevelSetting" :icon="Plus" link type="primary"
                                :disabled="formDisabled" style="font-size: 16px;" @click="addItem(scope.row,item.code)"></el-button>
                            <el-button v-if="scope.row.mkckAction != 'D'" :icon="Delete" link type="primary"
                                :disabled="formDisabled" style="font-size: 16px;" @click="removeItem(scope.row)"></el-button> -->
                            <el-button v-if="scope.row.mkckAction != 'D' && item.level<accessLevelSetting" :icon="Plus" link type="primary"
                                :disabled="formDisabled||(isEditing)" style="font-size: 16px;" @click.stop="addItem(scope.row,item.code)"></el-button>
                            <el-button v-if="scope.row.mkckAction != 'D'" :icon="Delete" link type="primary"
                                :disabled="formDisabled||(isEditing&&scope.row.finishWrite)" style="font-size: 16px;" @click="removeItem(scope.row)"></el-button>
                            <!-- End SIR-HLH-R78,Tom.Li, 2024/08/15 -->
                        </template>
                    </el-table-column>
                </el-table>
                <!-- Start SIR-HLH-R78,Tom.Li, 2024/08/15 -->
                 <!-- <el-button v-if="item.level==2" :disabled="formDisabled" :icon="Plus" @click="addItem(null,item.code,item.level)" size="small" style="position: absolute;"/> -->
                <el-button v-if="item.level==2" :disabled="formDisabled||isEditing" :icon="Plus" @click="addItem(null,item.code,item.level)" size="small" style="position: absolute;"/>
                <!-- End SIR-HLH-R78,Tom.Li, 2024/08/15 -->
            </div>
            </div>
            </div>
            <el-divider v-if="showForm2" style="margin: 5px 0px;" />
            <el-form :validateOnRuleChange="false" v-if="showForm2" ref="ruleFormRef2" style="" :model="ruleForm2.form" status-icon :rules="rules2" :style="{position: 'absolute','position-anchor': '--'+addAccessAnchor,'inset-area':'right','margin-left': '2%'}">
                <FormItemSign label-width="100px" :detailsRef="details" label="Company" prop="level1Code">
                    <el-input disabled v-model="ruleForm2.form.level1Code" autocomplete="off" aria-autocomplete="none"/>
                </FormItemSign>
                <FormItemSign label-width="100px" :detailsRef="details" label="Country/Region" prop="level2Code">

                    <GeneralSearchInput v-model="ruleForm2.form.level2Code" :disabled="ruleForm2.form.level != 2"
                        style="width: 240px" :desc="ruleForm2.form.level2Desc"
                        :dbClick="(row, code, desc) => ruleForm2.form.level2Desc = desc"
                        :change="(val) => { ruleForm2.form.level2Desc = val }" :params="{ var1: 'ALL' }"
                        searchType="ctryRegion" />

                </FormItemSign>
                <FormItemSign label-width="100px" :detailsRef="details" v-if="ruleForm2.form.level > 2" label="Bank"
                    prop="level3Code">
                        <Select v-model="ruleForm2.form.level3Code" :disabled="ruleForm2.form.level != 3" style="width: 150px" type="BANK_CODE" :labelKey="'code'" />
                </FormItemSign>
                <FormItemSign label-width="100px" :detailsRef="details" v-if="ruleForm2.form.level > 3"
                    label="csscl.useradmin.usr.branchOrgan" prop="level4Code">
                    <SearchInput :disabled="ruleForm2.form.level != 4" style="width: 240px"
                        v-model="ruleForm2.form.level4Code" url="/auth/api/v1/user/bankorgan/list"
                        title="Bank Organization" :desc="ruleForm2.form.level4Desc"
                        @changeDesc="(val) => ruleForm2.form.level4Desc = val"
                        :params="{ status: 'A', opCtryRegionCode: ruleForm2.form.level2Code, bankCode: ruleForm2.form.level3Code }"
                        :columns="[
                            {
                                title: $t('csscl.useradmin.usr.branchOrgan'),
                                colName: 'branchCode',
                            },
                            {
                                title: $t('csscl.useradmin.usr.branchName'),
                                colName: 'branchName',
                            }
                        ]" :pageSizes="[10, 20, 30]">
                    </SearchInput>
                </FormItemSign>
                <div style="justify-content: right;display: flex;">
                    <el-button @click="cancelGrid" class="ep-button-custom">Cancel</el-button>
                    <el-button type="primary" @click="saveGrid" class="ep-button-custom">OK</el-button>
                </div>
            </el-form>
        </div>
    </BaseDetails>
</template>

<script lang="ts" setup>
import { ref, reactive, getCurrentInstance, watch, nextTick } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import BaseDetails from '~/pages/base/Details.vue';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import {
    Edit,
    Delete,
    Plus,
    Back,
    Minus,
} from '@element-plus/icons-vue';
// Start SIR-HLH-R78,Tom.Li, 2024/08/15
// import { getOid, getCommonDesc, randomHashCode, saveMsgBox, showErrorMsg } from '~/util/Function.js';
import { getOid, getCommonDesc, randomHashCode, saveMsgBox, showErrorMsg, highlight } from '~/util/Function.js';
// End SIR-HLH-R78,Tom.Li, 2024/08/15
import CurrencySearchInput from '~/pages/base/CurrencySearchInput.vue';
import CtryRegionSearchInput from '~/pages/base/CtryRegionSearchInput.vue';

import { commonRules, showValidateMsg } from '~/util/Validators.js';
import { addCustValid, focusType} from "~/util/ModifiedValidate";

const { proxy } = getCurrentInstance();
const props = defineProps(['reload']);
const details = ref();
const formDisabled = ref(false);
const mclassList = ref({});
const userAccessLvlsDB = ref([]);
const userAccessLvlsObj = ref({});
const notEdit = ref(false);
const isApproveDetail = ref(false);
const editRow = (row, disabled,newId) => {
    isApproveDetail.value = false;
    let approveNumber = -1;
    if(row?.isApproveDetail && disabled){
        isApproveDetail.value=row?.isApproveDetail;
        approveNumber = row?.approveNumber;
    }
    const oid = newId || (row?.currentOid && getOid(row, disabled, proxy));
    ruleForm2.form = {};
    showForm2.value = false;
    modifyRecords.value = {};
    proxy.$axios.get("/auth/api/v1/user/profile?userOid=" + oid+ 
        "&isApproveDetail="+isApproveDetail.value +"&approveNumber="+approveNumber).then((body) => {
        if (body.success) {
            if(row?.isApproveDetail && disabled){
                ruleForm.form = row?.afterImage;
            } else {
                body.data.apprCurrency = "HKD";
                ruleForm.form = body.data;
            }
            details.value.currentRow = ruleForm.form;
            const fromDB = body.data.userAccessLvls;
            if (fromDB) {
                userAccessLvlsDB.value = resetALTable(fromDB);
                userAccessLvlsDB.value&&userAccessLvlsDB.value.length>0&&selectedRow(userAccessLvlsDB.value[0]);
            }
            addCustValid(ruleForm.form, ()=>{
                if (Object.values(modifyRecords.value).length > 0) {
                    return true;
                }
                let rows = ruleForm.form.userAccessLvls;
                for (let row in rows) {
                    row = rows[row];
                    if (row.recordStatus != 'A') {
                        focusType.type = focusType.EnterObj;
                        break;
                    }
                }
            });
        }
        //Data initialization completed
        details.value.initWatch({w1:ruleForm,w2:ruleForm2},ruleForm);
    });
    
}

const resetALTable = (rows) => {
    let data = [];
    let obj = {};
    let max = 2;
    for (let i = 0; i < rows.length; i++) {
        rows[i].finishWrite = true;
        rows[i].hasParent = true;
        obj[getCode(rows[i])] = rows[i];
        rows[i].children = [];
        max = Math.max(max,rows[i].level);
    }
    for (let i = 0; i < rows.length; i++) {
        
        if (rows[i].level == 2) {
            data.push(rows[i]);
        } else {
            obj[getCode(rows[i], true)]?.children.push(rows[i]);
        }
    }
    userAccessLvlsObj.value = obj;
    accessLevelSetting.value = max;
    return data;
}

const getCode = (row, isParent) => {
    let num = row.level;
    if (isParent) {
        num = num - 1;
    }
    let code = num;
    
    if(row.hasParent){
        for (let i = 2; i <= num; i++) {
            code +=  "-" + ((row["level" + i + "Code"]&&row.hasParent)?row["level" + i + "Code"]:row.userAccessLvlOid);
        }
    }else{
        if(isParent){
            if(row.parentLink.indexOf("|")==-1){
                code += row.parentLink;
            }else{
                code += row.parentLink.substr(0,row.parentLink.lastIndexOf("|"));
            }
        }else{
            code += row.parentLink
        }
    }
    return code;
}
const viewOriginalForm = (pendingOid, isDisabled) => {
    isApproveDetail.value=false;
  proxy.$axios.get("/auth/api/v1/user/profile?userOid="+pendingOid+ 
        "&isApproveDetail="+isApproveDetail.value +"&approveNumber=1").then((body) => {
        if(body.success) {
            ruleForm.form = body.data;     
            // Start SIR-HLH-R78,Tom.Li, 2024/08/15
            let fromDB = body.data.userAccessLvls;
            if (!fromDB) {
                fromDB=[];
            }
            userAccessLvlsDB.value = resetALTable(fromDB);
            // End SIR-HLH-R78,Tom.Li, 2024/08/15
        }
    });
    formDisabled.value = isDisabled;
}

const handleSave = async (searchValid, isOnlyValidate, unPopping) => {
    let result = await ruleFormRef.value.validate((valid, fields) => {
        if (valid) {

        } else {
            showValidateMsg(details, fields);
        }
    });
    // if (ruleFormRef2.value) {
    //     let result2 = await ruleFormRef2.value.validate((valid, fields) => {
    //         if (valid) {

    //         } else {
    //             console.log('error submit!', fields)
    //         }
    //     });
    //     if (!result2) {
    //         return false;
    //     }
    // }
    if (isOnlyValidate) {
        return result;
    }
    if (result && searchValid && await saveMsgBox(unPopping)) {
        if (ruleForm.form.userOid) {
            const msg = await proxy.$axios.patch("/auth/api/v1/user", {
                ...ruleForm.form,
                userAccessLvls: Object.values(modifyRecords.value)
            });
            if(msg.data) {
                details.value.writebackId(msg.data);
                editRow(null,null,msg.data);
            }
            return msg.success;
        } else {
            const msg = await proxy.$axios.post("/auth/api/v1/user", {
                ...ruleForm.form,
                userAccessLvls: Object.values(modifyRecords.value)
            });
            if (msg.data) {
                details.value.writebackId(msg.data);
                editRow(null,null,msg.data);
            }
            return msg.success;
        }
    }
    return false;
}

const showDetails = (row, isDoubleCheck) => {
    if (isDoubleCheck || row.recordStatus === 'PA') {
        formDisabled.value = true;
        details.value.showDetails(row, true)
    } else {
        formDisabled.value = false;
        details.value.showDetails(row, false)
    }
    ruleForm.form = {apprCurrency:"HKD"};
    modifyRecords.value = {};
    userAccessLvlsDB.value = [];
    userAccessLvlsObj.value = {};
    highlightRow.value = {};
    notEdit.value = false;
	// Start SIR-HLH-R78,Tom.Li, 2024/08/15
    selectLevel2Record.value = {};
    selectLevel3Record.value = {};
    isEditing.value = false;
    editingRow.value = {};
	// End SIR-HLH-R78,Tom.Li, 2024/08/15
    if (row.currentOid) {
        notEdit.value = true;
        editRow(row, isDoubleCheck);
    }else{
        //Data initialization completed
        details.value.initWatch({w1:ruleForm,w2:ruleForm2},ruleForm);
    }
    
}


defineExpose({
    details,
    editRow,
    showDetails,
});
// --------------------------------------------

interface RuleForm {
    opCtryRegionCode: string
    ctryRegionCode: string
    userId: string
    userName: string
    email: string
    phoneExt: string
    phoneNo: string
    apprCurrency: string
    apprLimitAmt: number
    mclassCode: string
    status: string
}

const ruleFormRef = ref<FormInstance>();
const ruleForm = reactive({
    rules:()=>{ return [{ rules:rules }, {rules:rules2, form:ruleForm2.form}] },
    form: {
        opCtryRegionCode: "",
        ctryRegionCode: "",
        userId: "",
        userName: "",
        email: "",
        phoneExt: "",
        phoneNo: "",
        apprCurrency: "",
        apprLimitAmt: "",
        mclassCode: "",
        status: "",
    }
});

const rules = reactive<FormRules<RuleForm>>({
    opCtryRegionCode: [
        commonRules.required,
    ],
    ctryRegionCode: [
        commonRules.required,
    ],
    userId: [
        commonRules.required,
        commonRules.name,
    ],
    userName: [
        commonRules.required,
        commonRules.nameIncChinese,
    ],
    email: [
        commonRules.required,
        commonRules.email,
    ],
    phoneExt: [
        commonRules.required,
    ],
    phoneNo: [
        commonRules.required,
        commonRules.number(12),
    ],
    apprLimitAmt: [
        commonRules.required,
    ],
    mclassCode: [
        commonRules.required,
    ],
    status: [
        commonRules.required,
    ],
})

const changeOpCtryRegionCode = () => {
    defaultMclassCode();
}
const changeApprLimitAmt = () => {
    defaultMclassCode();
}

const defaultMclassCode = () => {
    if(!ruleForm.form.opCtryRegionCode || !ruleForm.form.apprLimitAmt || isNaN(ruleForm.form.apprLimitAmt)){
        return;
    }
    getMclassCode(ruleForm.form.opCtryRegionCode, ruleForm.form.apprLimitAmt);
}
const getMclassCode = async (opCtryRegionCode, apprLimitAmt) => {
    await proxy.$axios.get("/datamgmt/api/v1/sysctrl/mclass/code?opCtryRegionCode="+opCtryRegionCode+"&apprLimitAmt="+apprLimitAmt).then((body) => {
	if (body.success) {
            ruleForm.form.mclassCode = body.data || "0";
        }
    });
}

const modifyRecords = ref({});

const showForm2 = ref(false);
const ruleFormRef2 = ref<FormInstance>();
const ruleForm2 = reactive({
    form: {
        userAccessLvlOid: null,
        userId: null,
        level: 0,
        level1Code: "",
        level2Code: "",
        level3Code: "",
        level4Code: "",
        level5Code: "",
        actionFlag: "",
        level1Desc: "",
        level2Desc: "",
        level3Desc: "",
        level4Desc: "",
        level5Desc: "",
    }
})

const validateUniqueness = (rule: any, value: any, callback: any, level) => {
    let e = {
        ...ruleForm2.form,
        children: [],
    };

    if (e.level == level) {
        if (level == 2) {
            if (userAccessLvlsObj.value[getCode(e)]) {
                callback(new Error("The same record already exists!"))
                return;
            }
        } else {
            let rows = userAccessLvlsObj.value[getCode(e, true)].children.filter(function (item) {
                return item.level1Code == e.level1Code
                    && item.level2Code == e.level2Code
                    && item.level3Code == e.level3Code
                    && item.level4Code == e.level4Code
                    && item.level5Code == e.level5Code
                    ;
            });
            if (rows && rows.length > 0) {
                callback(new Error("The same record already exists!"))
                return;
            }
        }
    }

    callback()
}

const rules2 = reactive<FormRules<typeof ruleForm2>>({
    level1Code: [commonRules.required],
    level2Code: [commonRules.required, { validator: (rule, value, callback) => { validateUniqueness(rule, value, callback, 2) }, trigger: 'blur' }],
    level3Code: [commonRules.required, { validator: (rule, value, callback) => { validateUniqueness(rule, value, callback, 3) }, trigger: 'blur' }],
    level4Code: [commonRules.required, { validator: (rule, value, callback) => { validateUniqueness(rule, value, callback, 4) }, trigger: 'blur' }],
    level5Code: [commonRules.required, { validator: (rule, value, callback) => { validateUniqueness(rule, value, callback, 5) }, trigger: 'blur' }],
})

const deleteItem = (row) => {
    if (userAccessLvlsObj.value[getCode(row)] && userAccessLvlsObj.value[getCode(row)].children.length > 0) {
    	// Start SIR-HLH-R78,Tom.Li, 2024/08/15
    	//for (let j = 0; j < userAccessLvlsObj.value[getCode(row)].children.length; j++) {
        //    deleteItem(userAccessLvlsObj.value[getCode(row)].children[0]);
        //    j--
        let len = userAccessLvlsObj.value[getCode(row)].children.length;
        for (let j = len-1; j >= 0; j--) {
            deleteItem(userAccessLvlsObj.value[getCode(row)].children[j]);
        // End SIR-HLH-R78,Tom.Li, 2024/08/15
        }
    }
    if (row.recordStatus == 'A') {
        row.recordStatus = 'PD';
        row.mkckAction = 'D';
        modifyRecords.value[row.userAccessLvlOid] = row;
    } else if (row.recordStatus == 'PD' && row.mkckAction == "C") {
        if (row.sysCreateDate) {
            row.mkckAction = 'D';
            modifyRecords.value[row.userAccessLvlOid] = row;
        } else {
            delete modifyRecords.value[row.userAccessLvlOid];
        }
        delete userAccessLvlsObj.value[getCode(row)];
        if (userAccessLvlsObj.value[getCode(row, true)]) {
            let rows = userAccessLvlsObj.value[getCode(row, true)].children;
            userAccessLvlsObj.value[getCode(row, true)].children = rows.filter(function (item) {
                return item.userAccessLvlOid !== row.userAccessLvlOid
            });
        }
    }
}

const removeItem = (row) => {
	// Start SIR-HLH-R78,Tom.Li, 2024/08/15
    if (!row.finishWrite) {
        isEditing.value = false;
        // Start SK-COMMON-0149, Tom.Li, 2024/09/09
        editingRow.value = {};
        // End SK-COMMON-0149, Tom.Li, 2024/09/09
        highlightRow.value = {};
    }
    // End SIR-HLH-R78,Tom.Li, 2024/08/15
    deleteItem(row);
    delete userAccessLvlsObj.value["NaN"];
    userAccessLvlsDB.value = Object.values(userAccessLvlsObj.value).filter(function (item) {
        return item.level == 2;
    });
}


const addItem = (row,code,level) => {
    addAccessAnchor.value = code;
    // Start SIR-HLH-R78,Tom.Li, 2024/08/15
    highlightRow.value = {};
    isEditing.value = true;
    // End SIR-HLH-R78,Tom.Li, 2024/08/15
    showForm2.value = false;
    if (row) {
        if(!row.finishWrite){
            showErrorMsg("Subtable data is incomplThe sub table data has not been saved. Please confirm if you want to leave.ete.");
            return;
        }
        ruleForm2.form = {
            ...row,
            level: row.level + 1,
            mkckAction: 'C',
            recordStatus: 'PD',
            sysCreateDate: null,
            finishWrite:false
        };
    } else {
        ruleForm2.form = {
            userAccessLvlOid: null,
            userId: ruleForm.form.userId,
            level1Code: "BOC",
            level: 2,
            mkckAction: 'C',
            recordStatus: 'PD',
            sysCreateDate: null,
            finishWrite:false
        };
    }
    saveGrid();
    selectedRow(row);
}
const selectedRow = (row) => {
    highlightRow.value = row;
    if (row&&row.level) {
        switch (row.level) {
            case 2:
                selectLevel2Record.value = row;
                break;
            case 3:
                selectLevel3Record.value = row;
                break;
        
            default:
                break;
        }

    }
}
const saveGrid = async () => {
    // let result = await ruleFormRef2.value.validate((valid, fields) => {
    //     if (valid) {

    //     } else {
    //         showValidateMsg(details, fields);
    //     }
    // });
    const result = true;
    if (result) {
        let e = {
            ...ruleForm2.form,
            children: [],
        };
        e.userAccessLvlOid = Math.trunc(randomHashCode());
        modifyRecords.value[e.userAccessLvlOid] = e;
        modifyRecords.value = { ...modifyRecords.value };
        if (e.level == 2) {
            e.parentLink=String(e.userAccessLvlOid);
            userAccessLvlsObj.value[getCode(e)] = e;
            userAccessLvlsDB.value.push(e);
        } else {
            e.parentLink=String(e.parentLink+"|"+e.userAccessLvlOid);
            userAccessLvlsObj.value[getCode(e, true)].children.push(e);
            userAccessLvlsObj.value[getCode(e)] = e;
        }
        userAccessLvlsObj.value = userAccessLvlsObj.value;
        userAccessLvlsDB.value = userAccessLvlsDB.value;
        showForm2.value = false;
        // Start SIR-HLH-R78,Tom.Li, 2024/08/15
        editingRow.value = e;
        // End SIR-HLH-R78,Tom.Li, 2024/08/15
    }
}
const cancelGrid = () => {
    showForm2.value = false;
    ruleForm2.form = {};
}
const beforeSubmit = async () => {
    var completeTable = true;
    Object.values(modifyRecords.value).forEach(item=>{
        if(!item.finishWrite){
            showErrorMsg("The sub table data has not been saved. Please confirm if you want to leave.");
            completeTable = false;
            return;
        }
    })
    return completeTable;
}
const beforeSave = async () => {
    var completeTable = true;
    Object.values(modifyRecords.value).forEach(item=>{
        if(!item.finishWrite){
            showErrorMsg("The sub table data has not been saved. Please confirm if you want to leave.");
            completeTable = false;
            return;
        }
    })
    return completeTable;
}
const addAccessAnchor = ref("Level2");
const accessLevelSetting = ref(2);
const accessLevelSettingSource = ref([
                    {code:'Level2', codeDesc:'Level 2 (Country/Region)',level:2},
                    {code:'Level3', codeDesc:'Level 3(Business Unit)',level:3},
                    // Start SIR-HLH-R78,Tom.Li, 2024/08/15
                    // {code:'Level4', codeDesc:'Level 4 (Branch)',level:4},
                    // End SIR-HLH-R78,Tom.Li, 2024/08/15 
                    ])
const userAccessLvlsDBFilterByLevel = (level)=>{
    var data = [];
    searchChildren(userAccessLvlsDB.value,data,level);
    return data;
}
const searchChildren = (sourceData,resultData,level)=>{
	// Start SIR-HLH-R78,Tom.Li, 2024/08/15
    let selectParent;
    switch (level) {
        case 2:
            break;
        case 3:
            selectParent = selectLevel2Record.value;
            break;
    
        default:
            break;
    }
    // End SIR-HLH-R78,Tom.Li, 2024/08/15
    sourceData?.forEach((item)=>{
        if(level==item.level){
        	// Start SIR-HLH-R78,Tom.Li, 2024/08/15
        	// resultData.push(item);
            if(selectParent) {
                if (item["level" + (item.level-1) + "Code"] == selectParent["level" + (item.level-1) + "Code"]) {
                    resultData.push(item);
                }
            } else {
                resultData.push(item);
            }
            // End SIR-HLH-R78,Tom.Li, 2024/08/15
        }else if(level>item.level){
            if(item.children.length>0){
                searchChildren(item.children,resultData,level);
            }
        }
    })
    return resultData;
}
const highlightRow = ref({});
const userAccessClickClassName = ({row,rowIndex}) => {
    // Start SK-COMMON-0149, Tom.Li, 2024/09/09
    let curRow = editingRow.value;
    if (!curRow.level) {
        curRow = highlightRow.value;
    }
    if(curRow.level){
        switch (curRow.level) {
            case 2:
                if (row.level==2&&row.level2Code==curRow.level2Code)
                {
                    return 'selected-row';
                }
                break;
            case 3:
                if ((row.level==3&&row.level3Code==curRow.level3Code)||(row.level==2&&row.level2Code==curRow.level2Code))
                {
                    return 'selected-row';
                }
                break;
        
            default:
                break;
        }
    }
    // End SK-COMMON-0149, Tom.Li, 2024/09/09
    return '';
}
// Start SIR-HLH-R78,Tom.Li, 2024/08/15
// const userAccessClick = (row, index)=> {
//    highlightRow.value = row;
const selectLevel2Record = ref({});
const selectLevel3Record = ref({});
const editingRow = ref({});
const isEditing = ref(false);
const userAccessClick = async (row, index)=> {
    if(
        row.userAccessLvlOid == selectLevel2Record.value.userAccessLvlOid ||
        row.userAccessLvlOid == selectLevel3Record.value.userAccessLvlOid
    ) {
        return false;
    }
    // Start SK-COMMON-0149, Tom.Li, 2024/09/09
    if (editingRow.value.userAccessLvlOid && editingRow.value.userAccessLvlOid != row.userAccessLvlOid&&row.finishWrite) {
    // End SK-COMMON-0149, Tom.Li, 2024/09/09
        ElMessage.closeAll();
        let flag = await beforeSave();
        if (!flag) {
            return false;
        }
    }
    selectedRow(row);
// End SIR-HLH-R78,Tom.Li, 2024/08/15
}

const dbClickCheck = (row,code,desc)=>{
    if(!row['level' + row.level + 'Code']){
    	// Start SIR-HLH-R78,Tom.Li, 2024/08/15
    	// return;
        row['level' + row.level + 'Code'] = code;
        // End SIR-HLH-R78,Tom.Li, 2024/08/15
    }
    checkCommon(row,code,desc);
}
const checkCommon =  (row,code,desc) =>{
    var data = [];
    searchChildren(userAccessLvlsDB.value,data,row.level);
    var result = 0;
    data.forEach(item=>{
        // Start SK-COMMON-0149, Tom.Li, 2024/09/09
        if(item['level' + row.level + 'Code']&&item['level' + row.level + 'Code']==row['level' + row.level + 'Code']){
        // End SK-COMMON-0149, Tom.Li, 2024/09/09
            result += 1;
        }
    })
    if(result==1){
        row['level' + row.level + 'Desc'] = desc;
        userAccessLvlsObj.value[getCode(row)] = row;
        row.finishWrite = true;
        // Start SIR-HLH-R78,Tom.Li, 2024/08/15
        isEditing.value = false;
        // Start SK-COMMON-0149, Tom.Li, 2024/09/09
        editingRow.value = {};
        // End SK-COMMON-0149, Tom.Li, 2024/09/09
        // End SIR-HLH-R78,Tom.Li, 2024/08/15
    }else{
        row['level' + row.level + 'Code'] = null;
        // Start SIR-HLH-R78,Tom.Li, 2024/08/15
        // ElMessage.error("The same record already exists!")
        showErrorMsg("The same record already exists!");
        // End SIR-HLH-R78,Tom.Li, 2024/08/15
    }
}
</script>

<style>
.custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 13px;
    padding-right: 8px;
}
/* Start SIR-HLH-R78,Tom.Li, 2024/08/15 */
/* .highlight-row {
  background-color:var(--ep-table-current-row-bg-color) !important; 
} */
/* End SIR-HLH-R78,Tom.Li, 2024/08/15 */
.user-item-is-required__label>.ep-form-item__label:after {
    content: "*";
    color: var(--ep-color-danger);
    margin-left: 4px;
    vertical-align: middle;
}
</style>