<template>
    <div ref="dateItemRef" :style="style()">
        <!-- Start SK-COMMON-0087, Tom.Li, 2024/08/21 -->
        <el-input :alt="alt" :class="isError ? 'search-date-error date-item-input' : 'date-item-input'" maxlength="10"
        :disabled="parseBool(disabled)" :placeholder="placeholder()" ref="inputRef" input-style="text-transform: none;"
        :style="style()" v-model="dateValue" @input="input" @change="changeInput" v-bind="$attrs">
        <!-- End SK-COMMON-0087, Tom.Li, 2024/08/21 -->
        <template #append>
            <el-button @click="showDatePicker" :icon="Calendar" link />
        </template>
    </el-input>
    <el-date-picker v-bind="$attrs" v-model="datePicker" :value-format="valueFormat()" :class="isError ? 'search-date-error date-item-date' : 'date-item-date'"
        :type="type()" :disabled="parseBool(disabled)" ref="datePickerRef" @change="selectDate" style="width:0;line-height:0;margin-left: 60px;" />
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, defineEmits, watch } from 'vue'
import { Calendar } from '@element-plus/icons-vue'
import { useVModel } from '@vueuse/core'
import moment from "moment"
import  { parseBool } from '~/util/Function.js';

const datePickerRef = ref();
const dateItemRef = ref();
const inputRef = ref();
const alt = ref("");
const isError = ref(false);
const isSysChange = ref(true);
const props = defineProps(['valueFormat', 'modelValue', 'type', 'disabled', 'style', 'change']);
const dateValue = ref(props.modelValue);
const emit = defineEmits(['update:modelValue'])
const datePicker = useVModel(props, "modelValue", emit);
const disa = ref(props.disabled);

watch(()=>props.modelValue,(newVal)=>{
    if (isSysChange.value) {
        dateValue.value = newVal;
        isError.value = false;
    }

    if (newVal) {
        inputRef.value.input.style.fontSize = null ;
    } else if (!newVal && !dateValue.value) {
        inputRef.value.input.style.fontSize = "10px";
    }
    
    isSysChange.value= true;
});

watch(()=>props.disabled,(newVal)=>{
    disa.value = props.disabled;
});

setTimeout(()=>{
    if (!dateValue.value) {
        inputRef.value.input.style.fontSize = "10px";
        disa.value = parseBool(props.disabled) ? true : inputRef.value.input.disabled;
    } 
    // Start SK-COMMON-0104, Tom.Li, 2024-08-24
    let formItem = dateItemRef.value.closest("div.ep-form-item");
    alt.value = formItem.getAttribute("hideLabel")?formItem.getAttribute("hideLabel"):formItem.querySelector("div.ep-form-item__label").innerText;
    // End SK-COMMON-0104, Tom.Li, 2024-08-24
}, 150);

const input = (val) => {
    // Start SK-COMMON-0087, Tom.Li, 2024/08/21
    val = val.replace(/[^0-9\-\/]/g,"");
    dateValue.value = val;
    // End SK-COMMON-0087, Tom.Li, 2024/08/21
    if (val) {
        inputRef.value.input.style.fontSize = null ;
    } else {
        inputRef.value.input.style.fontSize = "10px";
    }
}

const placeholder = () => {
    return disa.value ? "" : valueFormat();
}

const style = () => {
    return props.style || (type() == 'date' ? "width:130px;" : "width:180px;");
}

const valueFormat = () => {
    return props.valueFormat || type() == 'date' ? "YYYY/MM/DD" : 'YYYY/MM/DD HH:mm:ss';
}

const type = () => {
    return props.type || "date";
}

const showDatePicker = () => {
    datePickerRef.value.focus();
}

const selectDate = (val) => {
    dateValue.value = val;
    isError.value = false;
    if (props.change) {
        props.change(val);
    }
}


const dateReg1 = /^(\d{4})-(\d{1,2})-(\d{1,2})$/;
const dateReg2 = /^(\d{4})\/(\d{1,2})\/(\d{1,2})$/;
const dateReg3 = /^(\d{4})(\d{2})(\d{2})$/;

const changeInput = (val) => {
    isSysChange.value= false;
    datePicker.value = "";
    if (val) {
        let formatVal = null;
        if (val.indexOf("-") > 0) {
            formatVal = verifyDate(val, dateReg1);
        } else if (val.indexOf("/") > 0) {
            formatVal = verifyDate(val, dateReg2);
        } else if (val.length == 8) {
            formatVal = verifyDate(val, dateReg3);
        }
        
        if (formatVal) {
            dateValue.value = formatVal;
            datePicker.value = formatVal;
            isError.value = false;
        } else {
            isError.value = true;
            //Start SIR-CSG-R03, ZhuangYifan, 2024/08/12
            //Mandatory date verification
            datePicker.value = ' ';
            //End SIR-CSG-R03, ZhuangYifan, 2024/08/12
        }
    } else {
        isError.value = false;
    }
}

function verifyDate(dateStr, dateReg) {

    if (!dateReg.test(dateStr)) {
        return false;
    }

    var date = {
        year: RegExp.$1,
        month: RegExp.$2,
        day: RegExp.$3,
    }

    var dateObj = new Date(date.year, date.month - 1, date.day);

    if (date.year != dateObj.getFullYear() || date.month != dateObj.getMonth() + 1 || date.day != dateObj.getDate()) {
        return false;
    }

    return moment(dateObj).format(valueFormat());
}

</script>

<style>
.date-item-input {
    position: absolute !important;
    z-index: 9;
}

.date-item-input .ep-input-group__append {
    padding-inline: 10px;
}

.search-date-error>.ep-input__wrapper {
    box-shadow: 0 0 0 1px var(--ep-color-danger) inset !important;
    -webkit-box-shadow: 0 0 0 1px var(--ep-color-danger) inset !important;
}
.date-item-date .ep-input__wrapper {
    padding: 0px;
}
</style>