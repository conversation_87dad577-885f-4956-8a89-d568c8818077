<template>
  <BasePanel :searchParams="searchParams" :paramListData='paramListData' url="/cashmgmt/api/v1/cashinstr/upload/list"
    :params="{ modeEdit: 'Y' }" :showDetails="showDetails" :editRow="editRow" :beforeSearch="beforeSearch"
    :isMultiple="true" :selectable="selectable"
    :handleSelectionChange="handleSelectionChange" :hideEditBtn="hideEditBtn" ref="tableRef" :rules="rules">
    <template v-slot:searchPanel="slotProps">
      <FormRow>
        <ElFormItemProxy>
          <ElFormItemProxy :label="$t('csscl.cashinstr.upload.dateFrom')" prop="inDateFrom" label-width="90px">
            <DateItem :validate-event="false" v-model="slotProps.form.inDateFrom"
                      :title="$t('message.earlier.equal.curdate', [$t('csscl.cashinstr.upload.dateFrom')] ) + '\r' +
                        $t('message.earlier.equal.dateto', [$t('csscl.cashinstr.upload.dateFrom'), $t('csscl.cashinstr.upload.dateTo')] ) + '\r' +
                        $t('message.date.range.error', [7] ) "
                      type="date" style="width: 130px;"/>
          </ElFormItemProxy>
          <ElFormItemProxy :label="$t('common.title.date.to')" :hideLabel="$t('csscl.cashinstr.upload.dateTo')" prop="inDateTo" label-width="45px">
            <DateItem :validate-event="false" v-model="slotProps.form.inDateTo"
                      :title="$t('message.earlier.equal.curdate', [$t('csscl.cashinstr.upload.dateTo')] ) + '\r' +
                        $t('message.date.range.error', [7] ) "
                      type="date" style="width: 130px;"/>
          </ElFormItemProxy>
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.cashinstr.upload.channel')" prop="channel" label-width="110px">
          <Select v-model="slotProps.form.channel" type='CUST_ACC_INCOMING_CHANNEL_PURPOSE_CODE' v-model:desc="paramListData.channel" />
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.cashinstr.upload.processStatus')" prop="processStatus" label-width="110px">
          <Select v-model="slotProps.form.processStatus" type='FILE_PROCESS_STATUS' v-model:desc="paramListData.processStatus" />
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy :label="$t('csscl.cashinstr.upload.ftgidCode')" prop="ftgidCode" label-width="90px">

          <GeneralSearchInput v-model="slotProps.form.ftgidCode" searchType="ftgidCode" style="width: 450px;"/>

        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('common.title.recordStatus')" prop="multipleRecordStatus" label-width="110px">
          <MultipleSelect v-model="slotProps.form.multipleRecordStatus" v-model:desc="paramListData.multipleRecordStatus"  />
        </ElFormItemProxy>
        <ElFormItemProxy></ElFormItemProxy>
      </FormRow>
    </template>
    <template v-slot:tableColumn>
      <el-table-column sortable="custom" prop="inDate" :label="$t('csscl.cashinstr.upload.dateAndTime')"
        header-align="center" width="200" />
      <el-table-column sortable="custom" prop="channel" :label="$t('csscl.cashinstr.upload.channel')"
        header-align="center" width="200" />
      <el-table-column sortable="custom" prop="ftgidCode" :label="$t('csscl.cashinstr.upload.ftgidCode')"
        header-align="center" width="200" />
      <el-table-column sortable="custom" prop="ftgidName" :label="$t('csscl.cashinstr.upload.ftgidName')"
        header-align="center" />
      <el-table-column sortable="custom" prop="processStatus" :label="$t('csscl.cashinstr.upload.processStatus')"
        header-align="center" width="200">
        <template #default="scope">
          <!-- scope.row -->
          {{ getCommonDesc('FILE_PROCESS_STATUS', scope.row.processStatus) }}
        </template>
      </el-table-column>
      <el-table-column sortable="custom" prop="recordStatus" :label="$t('common.title.recordStatus')"
        header-align="center" width="200">
        <template #default="scope">
          {{ getRecordStatusDesc(scope.row) }}
        </template>
      </el-table-column>
    </template>
    <template v-slot:contentBottom>
      <br />
      <el-space style="float:right;">
        <CButtons :action="'Marked as Complete'" type="primary" @click="markAsCompClick"
          style="float:right;" :disabled="tableSelectRowsRef.length == 0">
          {{ $t('csscl.cashinstr.upload.markedAsComplete') }}
        </CButtons>
      </el-space>
    </template>
  </BasePanel>
  <Details ref="detailsRef" :reload="reload" />

  <el-dialog v-model="markedAsComp.showDialog" :title="$t('csscl.cashinstr.upload.markedAsComplete')"
    :close-on-click-modal="false" width="30%" class="mkck-dialog" append-to-body :show-close="false">
    <template #header>
      <div class="mkckTitle">
        <span class="title-name">{{ $t('csscl.cashinstr.upload.markedAsComplete') }}</span>
      </div>
    </template>
    <br>
    <el-form-item label="Remark" style="padding-right: 12px; padding-left: 12px;">
      <el-input type="textarea" v-model="markedAsComp.remark" :rows="6" style="width: 470px;"></el-input>
    </el-form-item>
    <br>
    <div class="button-group" style="text-align:center;">
      <el-button @click="markedAsCompCancel" class="ep-button-custom">Cancel</el-button>
      <el-button type="primary" @click="markedAsCompOk" class="ep-button-custom">OK</el-button>
    </div>
    <br>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, getCurrentInstance, reactive } from 'vue';
import { ElMessageBox } from 'element-plus';
import type { FormRules } from 'element-plus';
import BasePanel from '~/pages/base/index.vue'
import Details from './Details.vue'
import { getCommonDesc, getRecordStatusDesc, checkBeforeCurDt, checkInputDate, showWarningMsg } from '~/util/Function.js';
import { commonRules, showValidateMsg } from '~/util/Validators.js';

const { proxy } = getCurrentInstance()
const paramListData = {};
const searchParams = {
  //顺序和上面绑定参数一致
  inDateFrom: "",
  inDateTo: "",
  channel: "",
  processStatus: "",
  ftgidCode: "",
  multipleRecordStatus:[],
};

const tableRef = ref();
const detailsRef = ref();
const showDetails = (row, disabled) => {
  detailsRef.value.showDetails(row, disabled);
}
const editRow = (row) => {
  detailsRef.value.editRow(row);
}
const reload = () => {
  tableRef.value.load();
}
const hideEditBtn = (row) => {
  if (row?.recordStatus == 'A') {
    return true;
  }
}
//-------------------------------
interface RuleForm {
  inDateFrom: Date
  inDateTo: Date
}
const rules = reactive<FormRules<RuleForm>>({
  inDateFrom: [
    commonRules.selectRequired,
    commonRules.earlierEquCurDate,
    commonRules.earlierEquDt(()=>{ return searchParams.inDateTo }, proxy.$t('csscl.common.dateTo')),
    commonRules.diffDate(7, ()=>{ return searchParams.inDateTo }, proxy.$t('csscl.common.dateTo')),
  ],
  inDateTo: [
    commonRules.selectRequired,
    commonRules.earlierEquCurDate,
  ],
})

const beforeSearch = async() => {
  /*
  let fieldsDtl = {
    fields:{
      inDateFrom: proxy.$t('csscl.cashinstr.upload.dateFrom'),
      inDateTo: proxy.$t('csscl.common.dateTo'),
    }
  }
  let result = await tableRef.value.formRef.validate((valid, fields) => {
    if (!valid) {
      showValidateMsg(fieldsDtl, fields);
    }
  });
  if (!result) {
    return false;
  }

  let chkMsg = await checkInDate();
  return chkMsg;
  */
}
/*
const checkInDate = async() => {
  let msgs = [];
  let msg1 = await checkBeforeCurDt(proxy, proxy.$t('csscl.cashinstr.upload.dateFrom'), tableRef.value.formInline.inDateFrom);
  msgs.push(msg1);
  let msg2 = await checkBeforeCurDt(proxy, proxy.$t('csscl.common.dateTo'), tableRef.value.formInline.inDateTo);
  msgs.push(msg2);
  if(msg1 || msg2){
    return msgs;
  }

  let msg3 = checkInputDate(proxy, tableRef.value.formInline.inDateFrom, tableRef.value.formInline.inDateTo);
  msgs.push(msg3);
  return msgs;
}
*/
const tableSelectRowsRef = ref([]);
const handleSelectionChange = (val) => {
  tableSelectRowsRef.value = val;
}
function selectable(row, index) {
  if(row?.recordStatus == "PA" || (row?.recordStatus == "A" && row?.processStatus == 'R')){
    return false;
  }
  return true;
}

const markedAsComp = ref({
  showDialog: false,
  remark: '',
});
const markAsCompClick = async() => {
  let msg;
  // Start SIR-Cristin-R051,heruiguang 20240812
  let funcId = proxy.$currentInfoStore.getCurrentFuncId();
  // End SIR-Cristin-R051,heruiguang 20240812
  let eventOidList=[];
  for (var row of tableSelectRowsRef.value) {
    // Start SIR-Cristin-R051,heruiguang 20240812
    //  check status when click edit markAsComplete button
    if (row.processStatus !== "N" && row.processStatus !== "F") {
      if (!msg) {
        msg = proxy.$t('message.cash.upload.mkascomp.procsts');
        showWarningMsg(msg);
        return;
      }
    }
    eventOidList.push(row.currentOid);
  }

  let flag = await proxy.$axios.post('/datamgmt/api/v1/handler/lock', {
      eventName: funcId,
      eventOidList: eventOidList,
      eventOid: row.currentOid,
      recordStatus:row.recordStatus,
      version:row.version,
      flag: false,
    });
    if (!flag.data) {
      return;
    }
    // End SIR-Cristin-R051,heruiguang 20240812
    markedAsComp.value.showDialog = true;
}
const markedAsCompOk = async () => {
  let msg;
  let isSubmit = false;
  for (var index in tableSelectRowsRef.value) {
    let row = tableSelectRowsRef.value[index];
    if (row.processStatus !== "N" && row.processStatus !== "F") {
      if (!msg) {
        msg = proxy.$t('message.cash.upload.mkascomp.procsts');
      }
      continue;
    }
    const resp = await proxy.$axios.patch("/datamgmt/api/v1/document/in/status?isQueryVPO=true&processStatus=M" + "&objectIds=" + row.currentOid);
    if (resp.success) {
      if (resp?.data?.length > 0) {
        let editRow = resp?.data[0];
        await handleSubmit(editRow.mkckOid, editRow.jobInDocDtlOid, markedAsComp.value.remark);
      }
      isSubmit = true;
    } else {
      console.log("Fialed update recorde " + row.mkckOid);
    }
  }
  if (isSubmit) {
    ElMessageBox.alert("Submitted successfully.", 'Success', {
      confirmButtonText: 'OK',
      type: 'success',
    });
    
    reload();
  }
  markedAsCompCancel();
  if (msg) {
    showWarningMsg(msg);
  }
};
const markedAsCompCancel = () => {
  markedAsComp.value.remark = '';
  markedAsComp.value.showDialog = false;
}

const handleSubmit = async (mkckOid, eventOid, remark) => {
  let funcId = proxy.$currentInfoStore.getCurrentFuncId();

  const response = await proxy.$axios.post('/datamgmt/api/v1/makerchecker/initial', {
    flowMakerCheckerOid: mkckOid,
    remark: remark,
    status: "A",
    eventOid: eventOid,
    eventType: 'SD',
    funcId: funcId
  });
  return response.success;
};


</script>

<style></style>