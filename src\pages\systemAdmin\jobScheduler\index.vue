<template>
  <BasePanel :searchParams="searchParams" :paramListData='paramListData' url="/rptsched/api/v1/inter/scheduler/job/list" :params="{ modeEdit: 'Y' }"
    :showDetails="showDetails" :deleteRow="deleteRow" :editRow="editRow" ref="tableRef">
    <template v-slot:searchPanel="slotProps">
      <FormRow>
        <ElFormItemProxy :label="$t('common.title.opCtryRegionCode')" prop="opCtryRegionCode">
          <CtryRegionSearchInput v-model="slotProps.form.opCtryRegionCode" 
            showDesc="false" 
            style="width: 100px"
            opCtryRegion />
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.jobScheduler.jobId')" prop="jobId" label-width="100px">
          <GeneralSearchInput v-model="slotProps.form.jobId" style="width: 240px" showDesc="false" searchType="jobId"/>
        </ElFormItemProxy>
        <ElFormItemProxy></ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy :label="$t('csscl.jobScheduler.jobName')" prop="jobName">
          <Select v-model="slotProps.form.jobName" style="width: 370px" valueKey="jobName" vkEnqual :source="jobNameList" :change="jobNameType(slotProps.form.jobName)" />
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.jobScheduler.frequency')" prop="frequency" label-width="100px">
          <Select v-model="slotProps.form.frequency" v-model:desc="paramListData.frequency" style="width: 240px" type='FREQUENCY' />
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('common.title.status')" prop="status" label-width="120px">
          <Select v-model="slotProps.form.status" v-model:desc="paramListData.status" type='STATUS' />
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy :label="$t('csscl.jobScheduler.channel')" prop="channel">
          <Select v-model="slotProps.form.channel" v-model:desc="paramListData.channel" style="width: 240px" type="SCHEDULER_CHANNEL" />
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.jobScheduler.ftgidCode')" prop="ftgidCode" label-width="100px">
          <GeneralSearchInput v-model="slotProps.form.ftgidCode" inputStyle="width:180px" style="width: 480px" searchType="ftgidCode" />

        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('common.title.recordStatus')" prop="multipleRecordStatus" label-width="120px">
          <MultipleSelect v-model="slotProps.form.multipleRecordStatus" v-model:desc="paramListData.multipleRecordStatus" />
        </ElFormItemProxy>
      </FormRow>
    </template>
    <template v-slot:tableColumn>
      <el-table-column align="left" header-align="center" sortable="custom" prop="opCtryRegionCode"
        :label="$t('common.title.opCtryRegionCode')" width="190" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="jobId"
        :label="$t('csscl.jobScheduler.jobId')" width="130" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="jobName"
        :label="$t('csscl.jobScheduler.jobName')" width="250" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="jobType"
        :label="$t('csscl.jobScheduler.type')" width="80">
        <template #default="scope">
          {{ getCommonDesc('JOB_TYPE', scope.row.jobType) }}
        </template>
      </el-table-column>
      <el-table-column align="left" header-align="center" sortable="custom" prop="frequency"
        :label="$t('csscl.jobScheduler.frequency')" width="200" >
        <template #default="scope">
          {{ getCommonDesc('FREQUENCY', scope.row.frequency) }}
        </template>
      </el-table-column>
      <el-table-column align="left" header-align="center" sortable="custom" prop="channel"
        :label="$t('csscl.jobScheduler.channel')" width="120" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="ftgidCode"
        :label="$t('csscl.jobScheduler.ftgidCode')" width="130" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="ftgidName"
        :label="$t('csscl.jobScheduler.ftgidName')" width="170" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="processTimes"
        :label="$t('csscl.jobScheduler.proTimeCutoffTime')" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="status"
        :label="$t('common.title.status')" width="90">
        <template #default="scope">
          {{ getCommonDesc('STATUS', scope.row.status) }}
        </template>
      </el-table-column>
      <el-table-column align="left" header-align="center" sortable="custom" prop="recordStatus"
        :label="$t('common.title.recordStatus')" >
        <template #default="scope">
          {{ getRecordStatusDesc(scope.row) }}
        </template>
      </el-table-column>

    </template>
  </BasePanel>
  <Details ref="detailsRef" :reload="reload" />
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance, watch,watchEffect } from 'vue';
import {
  Search
} from '@element-plus/icons-vue'
import BasePanel from '~/pages/base/index.vue'
import Details from './Details.vue'
import { getOid, getCommonDesc, getRecordStatusDesc } from '~/util/Function.js';
const jobNameList = ref({});
import CtryRegionSearchInput from '~/pages/base/CtryRegionSearchInput.vue';

const { proxy } = getCurrentInstance()
const paramListData = {};
const searchParams = ref({
  //顺序和上面绑定参数一致
  opCtryRegionCode:"",
  jobId:"",
  jobName:"",
  frequency:"",
  status:"",
  channel:"",
  ftgidCode:"",
  multipleRecordStatus:[],
});
const tableRef = ref();
const reload = () => {
  tableRef.value.load();
}
const detailsRef = ref();

const showDetails = (row, disabled) => {
  //disabled 相当于是双击
  detailsRef.value.showDetails(row, disabled, jobNameList.value);
}

const deleteRow = (row) => {
  proxy.$axios.delete("/rptsched/api/v1/inter/scheduler/job?flowJobControlOid="+ getOid(row)).then((body) => {
    if (body.success) {
      tableRef.value.load();
    }
  });
}
const editRow = (row) => {
  detailsRef.value.editRow(row);
}
//-------------------------------

watchEffect(() => {
  proxy.$axios.post("/rptsched/api/v1/inter/scheduler/job/config/list", {
    current: 1,
    pageSize: 1000,
    param: {
      opCtryRegionCode: 'HK'
    },
  }).then((body) => {
    if (body.success) {
      jobNameList.value = body.data.data;
    }
  });
});
//paramList 参数显示用的
function jobNameType(value){
  let desc = "";
  if(jobNameList.value!=null){
    for(let i =0;i<jobNameList.value.length;i++){
      if(value==jobNameList.value[i].jobId){
        desc = jobNameList.value[i].jobName;
        paramListData.codeType =  desc;
      }
    }
  }
  paramListData.jobName =  getCommonDesc('PEND_QUE_STS', value);
}

</script>

<style></style>