<template>
  <BasePanel :searchParams="searchParams" :paramListData='paramListData' url="/auth/api/v1/user/list" :params="{ modeEdit: 'Y' }"
    :showDetails="showDetails" :deleteRow="deleteRow" :editRow="editRow" ref="tableRef" :hideEditBtn="hideEditBtn"
    :sortProp="{ 'phoneNo': ['phoneExt', 'phoneNo'] }">
    <template v-slot:searchPanel="slotProps">
      <FormRow>
        <ElFormItemProxy label-width="220" :label="$t('common.title.opCtryRegionCode')" prop="opCtryRegionCode">
          
          <CtryRegionSearchInput v-model="slotProps.form.opCtryRegionCode" 
            style="width: 120px" showDesc="false" opCtryRegion />

        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy label-width="220" :label="$t('csscl.useradmin.usr.userId')" prop="userId">
          <el-input v-model="slotProps.form.userId" maxlength="7" style="width: 150px" />
        </ElFormItemProxy>
        <ElFormItemProxy label-width="150" :label="$t('csscl.useradmin.usr.countRegionCode')" prop="ctryRegionCode">
          
          <CtryRegionSearchInput v-model="slotProps.form.ctryRegionCode" style="width: 120px" showDesc="false" />
          
        </ElFormItemProxy>
        <ElFormItemProxy label-width="120" :label="$t('csscl.useradmin.usr.status')" prop="userStatus">
          <Select v-model="slotProps.form.status" type="USER_STATUS" :change="statusType(slotProps.form.status)"/>
        </ElFormItemProxy>
        <ElFormItemProxy>
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy label-width="220" :label="$t('csscl.useradmin.usr.userName')" prop="userName">
          <el-input v-model="slotProps.form.userName" chinese maxlength="50" style="width: 500px" input-style="text-transform:none" />
        </ElFormItemProxy>
        <ElFormItemProxy label-width="120" :label="$t('common.title.recordStatus')" prop="multipleRecordStatus">
          <MultipleSelect v-model="slotProps.form.multipleRecordStatus" v-model:desc="paramListData.multipleRecordStatus"  />
        </ElFormItemProxy>
      </FormRow>
    </template>
    <template v-slot:tableColumn>
      <el-table-column sortable="custom" prop="opCtryRegionCode" :label="$t('common.title.opCtryRegionCode')" width="200" />
      <el-table-column sortable="custom" prop="ctryRegionCode" :label="$t('csscl.useradmin.usr.countRegionCode')" width="150" />
      <el-table-column sortable="custom" prop="userId" :label="$t('csscl.useradmin.usr.userId')"  width="100" />
      <el-table-column sortable="custom" prop="userName" :label="$t('csscl.useradmin.usr.userName')" />
      <el-table-column sortable="custom" prop="email" :label="$t('csscl.useradmin.usr.email')"  width="200" />
      <el-table-column sortable="custom" prop="phoneNo" :label="$t('csscl.useradmin.usr.phoneNo')">
        <template #default="scope">
          <!-- scope.row -->
          {{ scope.row.phoneExt }}-{{ scope.row.phoneNo }}
        </template>
      </el-table-column>
      <el-table-column sortable="custom" prop="apprCurrency" :label="$t('csscl.useradmin.usr.currency')"  width="110"/>
      <el-table-column sortable="custom" prop="apprLimitAmt" :label="$t('csscl.useradmin.usr.apprLimit')" align="right">
        <template #default="scope">
          {{ thousFormat(scope.row.apprLimitAmt) }}
        </template>
      </el-table-column>
      <el-table-column sortable="custom" prop="mclassCode" :label="$t('csscl.useradmin.usr.mClassCode')" width="90"/>
      <el-table-column sortable="custom" prop="status" :label="$t('csscl.useradmin.usr.status')" width="90">
        <template #default="scope">
          {{ getCommonDesc('USER_STATUS', scope.row.status) }}
        </template>
      </el-table-column>
      <el-table-column sortable="custom" prop="recordStatus" :label="$t('csscl.useradmin.usr.recordStatus')" width="200">
        <template #default="scope">
          {{ getRecordStatusDesc(scope.row) }}  
        </template>
      </el-table-column>
    </template>
  </BasePanel>
  <Details ref="detailsRef" :reload="reload" />
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance, watch } from 'vue';
import {
  Search
} from '@element-plus/icons-vue'
import BasePanel from '~/pages/base/index.vue'
import Details from './Details.vue'
import { getOid } from '~/util/Function.js';
import  { getCommonDesc, getRecordStatusDesc, thousFormat } from '~/util/Function.js';

import CtryRegionSearchInput from '~/pages/base/CtryRegionSearchInput.vue';

const { proxy } = getCurrentInstance()
const paramListData = ref({});
const searchParams = ref({
  //顺序和上面绑定参数一致
  opCtryRegionCode:"",
  userId:"",
  ctryRegionCode:"",
  status:"",
  userName:"",
  multipleRecordStatus:[],
});

const tableRef = ref();
const detailsRef = ref();
const showDetails = (row, disabled) => {
  detailsRef.value.showDetails(row, disabled);
}
const deleteRow = (row) => {
  proxy.$axios.delete("/auth/api/v1/user?userOid=" +  getOid(row, false,null,true)).then((body) => {
    if (body.success) {
      tableRef.value.load();
    }
  });
}
// Start ISSUE 669, LiShaoyi, 2025/04/07
const hideEditBtn = (row) => {
  if (row?.status == 'C') {
    return true;
  } else {
    return false;
  }
}
// End ISSUE 669, LiShaoyi, 2025/04/07
const editRow = (row) => {
  detailsRef.value.editRow(row);
}
const reload = () => {
  tableRef.value.load();
}
//paramList 参数显示用的
function statusType(value){
  paramListData._value.status =  getCommonDesc('USER_STATUS', value);
}
function recordStatusType(value){
  paramListData._value.recordStatus =  getCommonDesc('RECORD_STATUS', value);
}

</script>

<style></style>