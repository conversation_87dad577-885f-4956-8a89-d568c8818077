<template>
  <div class="search-input-wrap search-input-extend-component">
    <el-input
      v-model="inpVal"
      :disabled="disabled"
      :onsearch="
        () => {
          return validSearch();
        }
      "
      v-bind="{
        ...inputProps,
        searchType: apiParams.searchType,
        maxlength: maxlength,
      }"
      @change="handleChange"
      @input="handleChange"
      @blur="handleBlur"
    ></el-input>
    <div class="search-icon-wrap">
      <el-icon-search
        style="width: 20px"
        @click="handleSearchIconClick"
        :searchType="apiParams.searchType"
        class="search-icon"
      />
    </div>
    <div class="input-desc" v-if="showDesc">
      <el-input
        v-model="desc"
        disabled
        class="descbox"
        style="padding-left: 6px"
      />
    </div>
  </div>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    @open="handleDialogOpen"
    class="ca-search-dialog-wrapper search-input"
    modal-class="searchInput-dialog"
    :close-on-click-modal="false"
    :modal="false"
    :close-on-press-escape="false"
    :destroy-on-close="true"
    draggable
    :show-close="false"
    append-to-body
  >
    <template #header>
      <div class="dialog-title-wrapper">
        <el-icon-close @click="close()" class="close-icon" />
        <div class="title">
          {{ props.title }}
        </div>
      </div>
    </template>
    <div class="ca-dialog-content-wrapper">
      <DynamicForm
        ref="dynamicFormRef"
        v-model="formModel"
        :schemas="schemasData"
        :disabled="disabled"
        @change="handleDynamicFormChangeCallback"
      >
      </DynamicForm>
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        v-model:total="total"
        @current-change="loadTableData"
        @size-change="loadTableData"
        style="background-color: lightgrey; padding-inline: 10px"
        v-bind="paginationProps"
      >
        Total {{ total }} records
      </el-pagination>
      <el-scrollbar always max-height="342">
        <el-table
          ref="dialogTableRef"
          :data="tableData"
          border
          table-layout="auto"
          :highlight-current-row="true"
          @row-dblclick="handleTableRowDBClick"
          @row-click="handleTableRowClick"
        >
          <el-table-column
            v-for="column in schemas"
            :key="column.field"
            :prop="column.field"
            :label="column.label"
            v-bind="column.columnProps"
          >
            <template #default="scope">
              <slot
                :name="'table-column-' + column.field"
                :scope="{ value: scope.row }"
              >
                {{ scope.row[column.field] }}
              </slot>
            </template>
          </el-table-column>
        </el-table>
      </el-scrollbar>

      <el-pagination
        v-if="!hideBottomPageination"
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        v-model:total="total"
        @current-change="loadTableData"
        @size-change="loadTableData"
        v-bind="paginationProps"
      >
        Total {{ total }} records
      </el-pagination>
    </div>
    <template #footer>
      <div class="dialog-footer" style="text-align: center">
        <span @click="close" class="ep-button ep-button-custom">Cancel</span>
        <el-button type="primary" @click="confirm" class="ep-button-custom"
          >OK</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { computed, ComputedRef, getCurrentInstance, ref, watch } from "vue";
import type { FormSchema, FormSchemaVO, FormVO, TableVO } from "./types";
import DynamicForm from "./DynamicForm.vue";
import { ElTable } from "element-plus";

const { proxy } = getCurrentInstance();
const props = defineProps({
  modelValue: {
    type: [String, Number],
    required: true,
    default: "",
  },
  // dialog title
  title: {
    type: String,
    default: "Code",
  },
  url: {
    type: String,
    default: "/datamgmt/api/v1/searchinput",
  },
  inputProps: {
    type: Object,
    default: () => {},
  },
  // 最大输入长度
  maxlength: {
    type: [String, Number],
    default: undefined,
  },
  // 是否显示desc
  showDesc: {
    type: Boolean,
    default: true,
  },
  // desc 的默认值
  deDesc: {
    type: String,
    default: "",
  },
  // 是否只读 (包含输入框，dialog的动态表单)
  disabled: {
    type: Boolean,
    default: false,
  },
  // 是否隐藏dialog底部分页
  hideBottomPageination: {
    type: Boolean,
    default: true,
  },
  // 默认值字段名
  deFieldName: {
    type: String,
    default: "code",
  },
  // 默认desc值字段名
  deDescFieldName: {
    type: String,
    default: "codeDesc",
  },
  // 接口默认参数
  apiParams: {
    type: Object,
    default: () => ({ status: "A", recordStatus: "A" }),
  },
  // pagination 相关配置
  paginationProps: {
    type: Object,
    default: () => {
      return {
        pageSizes: [10, 20, 30, 40],
        layout: "sizes, , jumper, prev, pager, next, ->, slot",
      };
    },
  },
  // 表单配置
  schemas: {
    type: Array as () => FormSchema[],
    default: () => [],
  },
});
const initCode = [
  "code",
  "codeDesc",
  "var1",
  "var2",
  "var3",
  "var4",
  "var5",
  "var6",
];
const emit = defineEmits([
  "update:modelValue",
  "change",
  "change-row",
  "change-desc",
]);
const dialogVisible = ref<boolean>(false);
const desc = ref<string>(props.deDesc);
const formModel = ref<FormVO>({}); //  "code",  "codeDesc",  "var1",  "var2",  "var3",  "var4",  "var5",  "var6",
const dynamicFormRef = ref<HTMLElement>();
const dialogTableRef = ref<InstanceType<typeof ElTable>>();
const currentPage = ref<number>(1);
const pageSize = ref<number>(10);
const total = ref<number>(0);
const oldInpVal = ref<string | number>("");
const currentRow = ref<TableVO | null>();
const tableData = ref<TableVO[]>([]);
const schemasData: ComputedRef<FormSchemaVO[]> = computed(() => {
  // 超过initCode.length长度不展示
  return props.schemas.slice(0, initCode.length).map((schema, i) => {
    // 传入数据默认没有field字段，顺序配对initCode
    schema.field = initCode[i];
    
    // 为el-input组件添加maxlength配置
    if (schema.component === 'el-input') {
      if (!schema.componentProps) {
        schema.componentProps = {};
      }
      // 只有当schema中明确配置了maxlength时才设置
      if (schema.maxlength !== undefined && schema.maxlength !== null) {
        schema.componentProps.maxlength = schema.maxlength;
      }
    }
    
    return schema;
  });
});
const inpVal = computed({
  get() {
    return props.modelValue;
  },
  set(val) {
    emit("update:modelValue", val);
  },
});

// 监听modelValue变化，当被清空时自动清空组件内部数据
watch(() => props.modelValue, (newValue) => {
  if (!newValue || newValue === '') {
    inpVal.value = '';
    desc.value = '';
    formModel.value = {};
    dialogVisible.value = false;
  }
});

const fieldName = props.deFieldName as keyof TableVO;
const descFieldName = props.deDescFieldName as keyof TableVO;

const handleChange = (val: string) => {
  inpVal.value = val;
  desc.value = "";
  emit("change", val);
};
const handleBlur = async (event: Event) => {
  // 输入框内容 改变 且 有值 时触发
  if (!inpVal.value || inpVal.value === oldInpVal.value) return;
  oldInpVal.value = inpVal.value;
  await setFormModelDefault();
  await loadTableData();
  // 匹配desc的值，不存在就清空
  const item = tableData.value.find((item) => item[fieldName] === inpVal.value);
  desc.value = item && item[descFieldName] ? item[descFieldName] : "";
};

const setFormModelDefault = () => {
  // 取动态表单的配置字段名
  const item = schemasData.value?.find((s) => s.field === props.deFieldName);
  const key = item?.field ?? props.deFieldName;
  // 设置动态表单的配置字段名的默认值，
  Object.assign(formModel.value, { [key]: inpVal.value });
};

const handleSearchIconClick = async () => {
  await setFormModelDefault();
  dialogVisible.value = true;
};

const loadTableData = async () => {
  const res = await proxy.$axios.post(props.url, {
    param: { ...props.apiParams, ...formModel.value },
    current: currentPage.value,
    pageSize: pageSize.value,
  });
  if (res?.success) {
    total.value = res.data.total;
    currentPage.value = res.data.page;
    tableData.value = res.data.data;
  }
};

/** dialog 动态表单条件触发回调 */
const handleDynamicFormChangeCallback = (data: FormSchema) => {
  // TODO 防抖节流
  loadTableData();
};

/** 校验input 输入框的值是否正确 */
const validSearch = async (val?: string): Promise<boolean> => {
  const modelValue = val || String(inpVal.value).trim();
  // 方法传入的值不为空，且input框disabled为true或者modelValue有值，则返回ture
  if (!val && (props.disabled || !modelValue)) {
    return true;
  }
  let oldPageSize = pageSize.value;
  currentPage.value = 1;
  // 目的是为了获取所有数据，校验完成要恢复pageSize
  pageSize.value = 999;
  Object.assign(formModel.value, { [props.deFieldName]: modelValue });

  await loadTableData();

  pageSize.value = oldPageSize;
  // 如果没有数据不需要匹配数据校验，则返回false
  if (tableData.value?.length <= 0) return false;
  // 查找列表数据与modelValue是否一致,这里需要转换成大写判断，因为modelValue可能为数字
  const tableItem = tableData.value.find(
    (item) =>
      String(item[fieldName]).toUpperCase() === String(modelValue).toUpperCase()
  );

  return !!tableItem;
};

const handleDialogOpen = () => {
  loadTableData();
};
const close = () => {
  currentRow.value = null;
  dialogVisible.value = false;
};
const handleTableRowDBClick = (row?: TableVO) => {
  // 禁用状态下，双击不回显数据，直接关闭弹窗
  if (!props.disabled) {
    const inputValue = currentRow.value?.[fieldName];
    const descValue = currentRow.value?.[descFieldName];
    inpVal.value = inputValue ?? "";
    desc.value = descValue ?? "";
    emit("change-desc", desc.value);
    emit("change-row", row);
  }
  close();
};
const handleTableRowClick = (row: TableVO) => {
  currentRow.value = { ...row };
};

const confirm = () => {
  if (currentRow.value) {
    handleTableRowDBClick();
  }
};

defineExpose({
  validSearch,
  clearData: () => {
    inpVal.value = '';
    desc.value = '';
    formModel.value = {};
    dialogVisible.value = false;
  }
});
</script>
<style scoped lang="scss">
.search-input-wrap {
  display: flex;
  align-items: center;
  .input-desc {
    display: "table-cell";
    width: "100%";
  }
  .descbox {
    text-transform: none;
    box-shadow: none;
    height: var(--ep-component-size);
    width: 100%;
    padding: 0;
    outline: none;
    border: none;
    color: var(--ep-text-color-regular);
    background: #e6e6e6;
  }
  .search-icon-wrap {
    display: flex;
    width: 20px;
    padding: 0 5px;
    vertical-align: middle;
    .search-icon {
      height: 16px;
      cursor: pointer;
    }
  }
}
.ca-search-dialog-wrapper {
  padding: 0;
  .dialog-title-wrapper {
    height: 32px;
    line-height: 32px;
    padding: 0 10px;
    background-color: var(--ep-color-primary);
    .close-icon {
      height: 26px;
      width: 26px;
      background-color: var(--ep-color-primary);
      border: none;
      color: #ffffff;
      float: right;
      margin: 3px 3px 0 0;
      cursor: pointer;
    }
    .title {
      color: #fff;
      font-size: 18px;
      height: 32px;
      padding: 10px;
      width: 100%;
      margin: 0;
      padding: 0;
      height: 6px;
      width: 100%;
      border: none;
    }
  }
}
.ca-dialog-content-wrapper {
  max-width: 750px;
  // max-height: 500px;
  padding: 10px;
  overflow: hidden;
  .ep-form.ep-form--default {
    background-color: transparent;
    margin-bottom: 10px;
    padding: 0 10px;
    :deep(.ep-form-item) {
      width: 280px;
      .ep-form-item__label {
        margin-bottom: 2px;
        font-weight: bold;
      }
    }
  }
}
</style>
