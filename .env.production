VITE_APP_VERSION = 1.0.0
VITE_SYSTEM = Custody and Clearing Platform
VITE_BASEPATH=/
VITE_FRONTEND_HOME=http://csscl-api.bochk.com.hk:31680
VITE_FRONTEND=http://csscl-api.bochk.com.hk:31680
VITE_OIDCURL= http://localhost:3333
VITE_REDIRECTURL= http://csscl-api.bochk.com.hk:31680/logout
VITE_SERVICE= https://dev-apigw.ftn.bochkuatclout.com
# 接口请求地址，会设置到 axios 的 baseURL 参数上
VITE_API_BASE_URL = http://csscl-api.bochk.com.hk:31679
# message 请求的网关地址
VITE_API_DEV_URL = http://csscl-api.bochk.com.hk:31679
VITE_WS_PROTOCOL = ws
VITE_AUTO_LOGIN = N