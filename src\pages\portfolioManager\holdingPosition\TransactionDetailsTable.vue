<template> 
    <Grid 
        url="/bff/portfolio/api/v1/holding-position/get-holding-details-page-list"
        isShowSearch="false"
        ref="gridRef"
        :params="params"
        :selectable="false"
        :cellStyle="cellStyle"
    >
        <template v-slot:tableColumnFront="slotProps">
            <el-table-column sortable="custom" prop="generationBusinessDate" :label="$t('csscl.portfolio.common.generationBusinessDate')" align="center" width="150"/>
            <el-table-column sortable="custom" prop="statementDate" :label="$t('csscl.portfolio.common.statementDate')" align="center" width="150"/>
            <el-table-column sortable="custom" prop="quantityType" :label="$t('csscl.si.common.quantityType')" align="center" width="120"/>
            <el-table-column sortable="custom" prop="holding" :label="$t('csscl.portfolio.common.holding')" align="center" width="150"/>
            <el-table-column sortable="custom" prop="registrationName" :label="$t('csscl.portfolio.common.registrationName')" align="center" width="150"/>
            <el-table-column sortable="custom" prop="mnemonicName" :label="$t('csscl.portfolio.common.mnemonicName')" align="center" width="150"/>
            <el-table-column sortable="custom" prop="marketPriceCurrency" :label="$t('csscl.portfolio.common.marketPriceCurrency')" align="center" width="150"/>
            <el-table-column sortable="custom" prop="marketUnitPrice" :label="$t('csscl.portfolio.common.marketUnitPrice')" align="center" width="120"/>
            <el-table-column sortable="custom" prop="marketValue" :label="$t('csscl.portfolio.common.marketValue')" align="center" width="120"/>
            <el-table-column sortable="custom" prop="exchangeCurrencyPair" :label="$t('csscl.portfolio.common.exchangeCurrencyPair')" align="center" width="150"/>
            <el-table-column sortable="custom" prop="exchangeRate" :label="$t('csscl.portfolio.common.exchangeRate')" align="center" width="120"/>
            <el-table-column sortable="custom" prop="equivalentCurrency" :label="$t('csscl.portfolio.common.equivalentCurrency')" align="center" width="120"/>
            <el-table-column sortable="custom" prop="equivalentMarketValue" :label="$t('csscl.portfolio.common.equivalentMarketValue')" align="center" width="150"/>
            <el-table-column sortable="custom" prop="marketUnitPriceLastUpdateDate" :label="$t('csscl.portfolio.common.marketUnitPriceLastUpdateDate')" align="center" width="200"/>
        </template>
    </Grid>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, defineExpose} from 'vue';
import Grid from '~/components/Si/SiGrid.vue';

const gridRef = ref<InstanceType<typeof Grid> | null>(null);

const params = ref({
  holdingOid: ''
});

const showDetails = async (holdingOid: any) => {
  // 即使holdingOid相同，也需要重新加载数据
  if (params.value.holdingOid === holdingOid) {
    gridRef.value?.load();
  } else {
    params.value = { holdingOid: holdingOid };  // params值变更会自动加载数据
  }
};

// 清空数据
const clearData = () => {
  // 清空Grid组件数据
  if (gridRef.value && gridRef.value.clearData) {
    gridRef.value.clearData();
  }
};

const cellStyle = (row: any, column: any, rowIndex: any, columnIndex: any) => {
  return {};
}

defineExpose({
  clearData,
  showDetails
});
</script>

<style scoped>
/* 表格样式 */
</style> 