import { defineStore } from "pinia";

export const previewFile = defineStore('previewFileStore', {

  state: () => {
    return {
        name: "",
        excelFile: {},
        file: "",
        fileType: "",
        enable: false,
    }
  },

  getters: {
    getExcel(): object {
      return this.excelFile;
    },
    isEnable(): boolean {
      return this.enable;
    }
  },

  actions: {
    setExcel(file: object) {
      this.excelFile = file;
    },
    setFile(file: string){
      this.file = file;
    },
    setFileType(type: string){
      this.fileType = type;
    },
    setEnable(enable:boolean) {
      this.enable = enable;
    },
    setName(name:string){
      this.name = name;
    },
    clear() {
      this.excelFile = {};
      this.file = "";
      this.fileType = "";
      this.name = "";
    },
  }

})