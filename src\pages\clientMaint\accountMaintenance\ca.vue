<template> 

    <!-- CA Grid -->
    <Grid url="/datamgmt/api/v1/account/ca/list"
        :params="{ clientAccountOid: ruleForm.form.opearteOid, pendingOid: ruleForm.form.pendingOid  }" 
        :onClick="gridClick"
        :beforeSearch="()=>{ pageObj.caPayMethodVPO = {}; }"
        :columns="[
            { title: 'csscl.acctCode.exBoardCode' , name: 'exBoardCode'},
            { title: 'csscl.acctCode.paymentMethodCode' , name: 'paymentMethod', fn:commDesc('SETTLE_METHOD_CODE') },
            { title: 'csscl.acctCode.postMethod' , name: 'postMethod', fn:commDesc('CASH_SETTLE_METHOD_CODE') },
            { title: 'common.title.status' , name: 'status', fn:commDesc('STATUS') },
            { title: 'common.title.recordStatus' , name: 'recordStatus', fn:getRecordStatusDesc },
        ]"
        />

    <FormRow>
        <FormItemSign :detailsRef="details" label-width="150" :label="$t('csscl.acctCode.exBoardCode')" prop="caPayMethodVPO.exBoardCode">
            
             <GeneralSearchInput v-model="pageObj.caPayMethodVPO.exBoardCode"
                showDesc="false"
                searchType="exboardCode" />

        </FormItemSign>
        <FormItemSign :detailsRef="details" label-width="130" :label="$t('common.title.status')" prop="caPayMethodVPO.status">
            <Select v-model="pageObj.caPayMethodVPO.status" style="width:170px" type="STATUS" />
        </FormItemSign>
    </FormRow>
    <FormRow>
        <FormItemSign :detailsRef="details" label-width="150" :label="$t('csscl.acctCode.paymentMethod')" prop="caPayMethodVPO.paymentMethod">
            <Select v-model="pageObj.caPayMethodVPO.paymentMethod" style="width:300px" type="SETTLE_METHOD_CODE" />
        </FormItemSign>
        <FormItemSign :detailsRef="details" label-width="130" :label="$t('csscl.acctCode.postMethod')" prop="caPayMethodVPO.postMethod">
            <Select v-model="pageObj.caPayMethodVPO.postMethod" style="width:300px" type="CASH_SETTLE_METHOD_CODE" />
        </FormItemSign>
    </FormRow>

    <div class="splitLine">
        <span>{{  $t("csscl.acctCode.caProxy") }}</span>
        <el-divider />
    </div>

    <FormRow>
        <FormItemSign :detailsRef="details" label-width="180" :label="$t('csscl.acctCode.proxyAgentInd')" prop="proxyAgentInd">
            <Select v-model="ruleForm.form.proxyAgentInd" type="COM_YN" />
        </FormItemSign>
        <FormItemSign :detailsRef="details" label-width="200" :label="$t('csscl.acctCode.broadridgeInd')" prop="broadridgeInd">
            <Select v-model="ruleForm.form.broadridgeInd" type="COM_YN" />
        </FormItemSign>
        <FormItemSign :detailsRef="details" label-width="240" :label="$t('csscl.acctCode.issInd')" prop="issInd">
            <Select v-model="ruleForm.form.issInd" type="COM_YN" />
        </FormItemSign>
    </FormRow>
    <FormRow>
        <FormItemSign :detailsRef="details" label-width="180" :label="$t('csscl.acctCode.glassLewisInd')" prop="glassLewisInd">
            <Select v-model="ruleForm.form.glassLewisInd" type="COM_YN" />
        </FormItemSign>
        <FormItemSign :detailsRef="details" label-width="200" :label="$t('csscl.acctCode.entryTypeCode')" prop="entryType">
            <Select v-model="ruleForm.form.entryType" type="ENTRY_TYPE_CODE" />
        </FormItemSign>
        <FormItemSign :detailsRef="details" label-width="240" :label="$t('csscl.acctCode.uniqueRefTypeCode')" prop="uniqueRefType">
            <Select v-model="ruleForm.form.uniqueRefType" type="UNIQUE_REF_TYPE_CODE" />
        </FormItemSign>
    </FormRow>
    <FormRow>
        <FormItemSign :detailsRef="details" label-width="180" :label="$t('csscl.acctCode.uniqueRefTypeNo')" prop="uniqueRefTypeNo">
            <el-input v-model="ruleForm.form.uniqueRefTypeNo" style="width:350px" />
        </FormItemSign>
        <FormItemSign :detailsRef="details" label-width="200" :label="$t('csscl.acctCode.proptryIdIssuer')" prop="proptryIdIssuer">
            <el-input v-model="ruleForm.form.proptryIdIssuer" style="width:350px" />
        </FormItemSign>
        <FormItemSign :detailsRef="details" label-width="240" :label="$t('csscl.acctCode.proptryIdScheme')" prop="proptryIdScheme">
            <el-input v-model="ruleForm.form.proptryIdScheme" style="width:350px" />
        </FormItemSign>
    </FormRow>
    <FormRow>
        <FormItemSign :detailsRef="details" label-width="180" showBef="false" :label="$t('csscl.acctCode.ctryRegionCode')" prop="proxyCtryCode">
            <CtryRegionSearchInput v-model="ruleForm.form.proxyCtryCode" showDesc="false" />
        </FormItemSign>
        <ElFormItemProxy></ElFormItemProxy> 
        <ElFormItemProxy></ElFormItemProxy> 
    </FormRow>

</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import Grid from '~/pages/base/Grid.vue';
import  { getCommonDesc, getRecordStatusDesc, commDesc } from '~/util/Function.js';
import CtryRegionSearchInput from '~/pages/base/CtryRegionSearchInput.vue';


const props = defineProps([ "ruleForm", "details"]);

const details = props.details;
const ruleForm = props.ruleForm;
const pageObj = reactive ({
    caPayMethodVPO:{}
});

const gridClick = (row) => {
    pageObj.caPayMethodVPO = row;
}

</script>

<style></style>