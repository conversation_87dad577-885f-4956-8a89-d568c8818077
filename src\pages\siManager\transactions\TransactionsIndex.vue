<template>
  <BasePanel :searchParams="searchParams" :paramListData="paramListData" 
    url="/bff/si//api/v1/settlement-instruction/get-settlement-instruction-page-list"
    selectFirstRecord="true" :clickRow="handleClick"
    ref="tableRef" :hideOperation="true" :isHideAdd="true" :params="{ modeEdit: 'Y' }" :beforeSearch= "beforeSearch" :rules="rules" 
    :onExportCSV="handleExportCSV">
    <template v-slot:searchPanel="slotProps">
      <FormRow>
        <ElFormItemProxy label-width="200" :label="$t('csscl.si.common.custodyAccountGroup')" prop="custodyAccountGroup">
          <MultipleGeneralSearchInput v-model="slotProps.form.custodyAccountGroup" searchType="custodyAcct" style="width:310px"/>
        </ElFormItemProxy>
        <ElFormItemProxy label-width="200" :label="$t('csscl.si.common.numOfCustodyAccount')" prop="numOfCustodyAccount">
          <el-input v-model="slotProps.form.numOfCustodyAccount" style="width: 180px" :disabled="true"></el-input>
        </ElFormItemProxy>
        <ElFormItemProxy label-width="200" :label="$t('csscl.si.common.custodyLevel')" prop="custodyLevel">
          <Select v-model="slotProps.form.custodyLevel" type="CUSTODY_LEVEL" style="width: 180px;" />
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <el-col>
          <ElFormItemProxy label-width="200" :label="$t('csscl.si.common.tradeDate')" prop="tradeDateFrom">
            <DateItem v-model="slotProps.form.tradeDateFrom" type="date" style="width: 120px;"/>
          </ElFormItemProxy>
          <ElFormItemProxy :label="$t('common.title.date.to')" :hideLabel="$t('csscl.common.dateTo')" prop="tradeDateTo" label-width="20px">
            <DateItem v-model="slotProps.form.tradeDateTo" type="date" style="width: 125px;"/>
          </ElFormItemProxy>
        </el-col>
        <ElFormItemProxy label-width="200" :label="$t('csscl.si.common.siType')" prop="siType">
          <Select v-model="slotProps.form.siType" type="SI_TYPE" style="width: 180px;" />
        </ElFormItemProxy>
        <ElFormItemProxy label-width="200" :label="$t('csscl.si.common.custodyMarket')" prop="custodyMarket">
          <GeneralSearchInput v-model="slotProps.form.custodyMarket" input-style="width:190px" style="width:380px" 
            searchType="custodyMarketP1" showDesc="false" :disabled="searchParams.custodyLevel === 'GC'"/>
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <el-col>
          <ElFormItemProxy label-width="200" :label="$t('csscl.si.common.instructionSettlementDate')" prop="instructionSettlementDateFrom">
            <DateItem v-model="slotProps.form.instructionSettlementDateFrom" type="date" style="width: 120px;"/>
          </ElFormItemProxy>
          <ElFormItemProxy :label="$t('common.title.date.to')" :hideLabel="$t('csscl.common.dateTo')" prop="instructionSettlementDateTo" label-width="20px">
            <DateItem v-model="slotProps.form.instructionSettlementDateTo" type="date" style="width: 125px;"/>
          </ElFormItemProxy>
        </el-col>
        <ElFormItemProxy label-width="200" :label="$t('csscl.si.common.exchangeBoard')" prop="marketCode">
          <GeneralSearchInput v-model="slotProps.form.marketCode" input-style="width:190px" style="width:380px" searchType="exboardCode" showDesc="true" :maxlength="20"/>
        </ElFormItemProxy>
        <ElFormItemProxy />
      </FormRow>
      <FormRow>
        <el-col>
          <ElFormItemProxy label-width="200" :label="$t('csscl.si.common.marketSettlementDate')" prop="marketSettlementDateFrom">
            <DateItem v-model="slotProps.form.marketSettlementDateFrom" type="date" style="width: 120px;"/>
          </ElFormItemProxy>
          <ElFormItemProxy :label="$t('common.title.date.to')" :hideLabel="$t('csscl.common.dateTo')" prop="marketSettlementDateTo" label-width="20px">
            <DateItem v-model="slotProps.form.marketSettlementDateTo" type="date" style="width: 125px;"/>
          </ElFormItemProxy>
        </el-col>
        <ElFormItemProxy label-width="200" :label="$t('csscl.si.common.instrumentCode')" prop="instrumentCode">
          <SearchInputExtend
            ref="searchInputExtendRef"
            v-model="slotProps.form.instrumentCode"
            :title="$t('csscl.si.common.instrumentCode')"
            :apiParams="{ searchType: 'instrumentCodeWithAttrs', status: null }"
            :inputProps="{
              style: 'width: 160px'
            }"
            :maxlength="20"
            :schemas="[
              {
                label: $t('csscl.si.common.instrumentCode'),
                component: 'el-input',
                maxlength: 20,
              },
              {
                label: $t('csscl.si.common.description'),
                component: 'el-input',
              },
              {
                label: $t('csscl.si.common.instrumentType'),
                component: 'Select',
                componentProps: {
                  type: 'IDENTIFIER_TYPE_CODE',
                  style: 'width: 280px',
                },
                slot: 'select',
              }
            ]"
          >
          </SearchInputExtend>
        </ElFormItemProxy>
        <ElFormItemProxy />
      </FormRow>
      <FormRow>
        <ElFormItemProxy label-width="200" :label="$t('csscl.si.common.bankReference')" prop="bankReference">
            <el-input v-model="slotProps.form.bankReference" style="width: 180px" maxlength="50"></el-input>
        </ElFormItemProxy>
        <ElFormItemProxy label-width="200" :label="$t('csscl.si.common.siStatus')" prop="siStatus">
          <Select v-model="slotProps.form.siStatus" type="SI_STATUS" style="width: 180px;" />
        </ElFormItemProxy>
        <ElFormItemProxy />
      </FormRow>
      <FormRow>
        <ElFormItemProxy label-width="200" :label="$t('csscl.si.common.clientReference')" prop="clientReference">
          <el-input v-model="slotProps.form.clientReference" style="width: 180px" maxlength="50"></el-input>
        </ElFormItemProxy>
        <ElFormItemProxy label-width="200" :label="$t('csscl.si.common.outputSecuritiesType')" prop="outputInstrumentType">
          <Select v-model="slotProps.form.outputInstrumentType" type='IDENTIFIER_TYPE_CODE' style="width: 180px;" />
        </ElFormItemProxy>
        <ElFormItemProxy />
      </FormRow>
    </template>  
    <template v-slot:tableHeaderTitle>
      <div style="margin: 0px; padding: 3px; border-bottom: 2px solid #1AA4A4;">
        <el-text style="color: #1AA4A4; font: 14px bold;">
          {{ $t('csscl.si.common.settlementInstruction') }}
        </el-text>
      </div>
    </template>
    <template v-slot:tableColumn>
      <el-table-column sortable="custom" prop="recordType" :label="$t('csscl.si.common.recordType')" align="center">
        <template #default="scope">
          {{ getCommonDesc('SI_RECORD_TYPE', scope.row.recordType) }}
        </template>
      </el-table-column>
      <el-table-column sortable="custom" prop="custodyAccountNumber" :label="$t('csscl.si.common.custodyAccountNumber')" align="center"/>
      <el-table-column sortable="custom" prop="custodyAccountName" :label="$t('csscl.si.common.custodyAccountName')" align="center"/>
      <el-table-column sortable="custom" prop="marketCode" :label="$t('csscl.si.common.exchangeBoard')" align="center"/>
      <el-table-column sortable="custom" prop="marketName" :label="$t('csscl.si.common.exchangeBoardName')" align="center"/>

      <el-table-column sortable="custom" prop="bankReference" :label="$t('csscl.si.common.bankReference')" align="center"/>
      <el-table-column sortable="custom" prop="clientReference" :label="$t('csscl.si.common.clientReference')" align="center"/>
      <el-table-column sortable="custom" prop="tradeDate" :label="$t('csscl.si.common.tradeDate')" align="center"/>

      <el-table-column sortable="custom" prop="instructionSettlementDate" :label="$t('csscl.si.common.instructionSettlementDate')" align="center"/>
      <el-table-column sortable="custom" prop="marketSettlementDate" :label="$t('csscl.si.common.marketSettlementDate')" align="center"/>
      <el-table-column sortable="custom" prop="expectedSettlementDate" :label="$t('csscl.si.common.expectedSettlementDate')" align="center"/>
      <el-table-column sortable="custom" prop="actualSettlementDate" :label="$t('csscl.si.common.actualSettlementDate')" align="center"/>
      
      <el-table-column sortable="custom" prop="securitiesIdType" :label="$t('csscl.si.common.securitiesIdType')" align="center"/>
      <el-table-column sortable="custom" prop="securitiesId" :label="$t('csscl.si.common.securitiesId')" align="center"/>
      <el-table-column sortable="custom" prop="tradeQuantity" :label="$t('csscl.si.common.quantityOfSecurities')" align="center"/>
    </template>

    <template v-slot:contentBottom>
      <br />
      <!-- Settlement Instruction Details ------------------------------>
      <div style="margin: 0px; padding: 3px; border-bottom: 2px solid #1AA4A4;">
        <el-text style="color: #1AA4A4; font: 14px bold;">
          {{ $t('csscl.si.common.details') }}
        </el-text>
      </div>
      <br />
      <SettlementDetailsTable ref="settlementDetailsTableRef" />
    </template>
  </BasePanel>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance, watch } from 'vue';
import type { FormRules } from 'element-plus';
import BasePanel from '~/components/Si/SIIndex.vue'
import SettlementDetailsTable from "~/pages/siManager/transactions/SettlementDetailsTable.vue";
import { ElMessage, ElLoading, ElMessageBox } from 'element-plus';
import { getTimeZone } from "~/util/DateUtils";
import { getCommonDesc, downloadFilePost } from '~/util/Function.js';
import { commonRules } from '~/util/Validators.js';
import SearchInputExtend from "~/components/SearchInput/Extends/SearchInputExtend.vue";
import Select from '~/components/Select.vue';
import {registerComponent} from "~/components/SearchInput/Extends/componentMap";
// 注册自定义组件
registerComponent('Select', Select)

const { proxy } = getCurrentInstance() as any;
const tableRef = ref<any>(null);
const securityDetailsTableRef = ref<InstanceType<typeof SecurityDetailsTable> | null>(null);
const settlementDetailsTableRef = ref<InstanceType<typeof SettlementDetailsTable> | null>(null);
const chargesTableRef = ref<InstanceType<typeof ChargesTable> | null>(null);
const finalSettlementTableRef = ref<InstanceType<typeof FinalSettlementTable> | null>(null);
const searchInputExtendRef = ref<any>(null);
const paramListData = reactive({
  tableData: []
});

// 搜索参数 - 按DOM中表单元素顺序排列，确保与BasePanel的字段标签映射一致
const searchParams = reactive({
  custodyAccountGroup: '',
  numOfCustodyAccount: '',
  custodyLevel: 'GC',
  tradeDateFrom: '',
  tradeDateTo: '',
  siType: '',
  custodyMarket: '',
  instructionSettlementDateFrom: '',
  instructionSettlementDateTo: '',
  marketCode: '',
  marketSettlementDateFrom: '',
  marketSettlementDateTo: '',
  instrumentCode: '',
  bankReference: '',
  siStatus: '',
  clientReference: '',
  outputInstrumentType: '',
  tradingAccountCodes: [] as string[],
});

interface RuleForm {
  outputInstrumentType: String,
  tradeDateFrom: Date
  tradeDateTo: Date
  instructionSettlementDateFrom: Date
  instructionSettlementDateTo: Date
  marketSettlementDateFrom: Date
  marketSettlementDateTo: Date
  custodyMarket: String
}

// 表单校验规则 - TBC
const rules = reactive<FormRules<RuleForm>>({
    outputInstrumentType:[
      commonRules.selectRequired,
    ],
    tradeDateFrom: [
      commonRules.earlierEquDt(() => { return searchParams.tradeDateTo }, proxy.$t('csscl.common.dateTo')),
    ],
    tradeDateTo: [
    ],
    instructionSettlementDateFrom: [
      commonRules.earlierEquDt(() => { return searchParams.instructionSettlementDateTo }, (proxy as any)?.$t('csscl.common.dateTo')),
    ],
    instructionSettlementDateTo: [
    ],
    marketSettlementDateFrom: [
      commonRules.earlierEquDt(() => { return searchParams.marketSettlementDateTo }, (proxy as any)?.$t('csscl.common.dateTo')),
    ],
    marketSettlementDateTo: [
    ],
    custodyMarket: [
      {
        validator: (rule: any, value: any, callback: any) => {
          // 当 Custody Level = Local (LC) 时，custody market 可以为空
          callback();
        },
        trigger: ['blur', 'change']
      }
    ],
});

watch(() => searchParams.custodyAccountGroup, (newValue) => {
  if (newValue) {
    searchParams.tradingAccountCodes = newValue.split(',').filter(item => item !== '');
    searchParams.numOfCustodyAccount = searchParams.tradingAccountCodes.length.toString();
  } else {
    searchParams.tradingAccountCodes = [];
    searchParams.numOfCustodyAccount = '';
  }
});

// 监听custodyLevel变化，触发custodyMarket字段重新验证
watch(() => searchParams.custodyLevel, (newValue) => {
  // 当custodyLevel为GC时，清空custodyMarket
  if (newValue === 'GC') {
    searchParams.custodyMarket = '';
  }
  // 触发custodyMarket字段的验证
  tableRef.value?.validateField?.('custodyMarket');
});

const generateRequestparam = (data: any) => {
  let params = {
    header: {timezone: getTimeZone(), lang:"en_US"},
    data: data
  };
  return params;
};

const isResponseSuccess = (response: any) => {
  if (!response || !response.header) {
    return false;
  }
  return response.header.code === '000000';
};

const getErrorMessage = (errorMessage: any) => {
  return errorMessage || 'Unknown error';
};

// 搜索前的处理函数 - TBC
const beforeSearch = async (params: any) => {
  
  // // 校验：以下条件中必须至少输入一个 - TBC
  // const requiredFields = [
  //   { field: 'custodyAccountGroup', label: 'Custody Account Group' },
  //   { field: 'bankReference', label: 'Bank Reference' },
  //   { field: 'siType', label: 'SI Type' },
  //   { field: 'market', label: 'Market' },
  //   { field: 'instrumentCode', label: 'Instrument Code' },
  //   { field: 'siStatus', label: 'SI Status' }
  // ];
  
  // // 检查是否至少有一个字段有值
  // const hasValue = requiredFields.some(item => {
  //   const value = searchParams[item.field as keyof typeof searchParams];
  //   return value && value.toString().trim() !== '';
  // });
  
  // if (requiredFields.length > 0 && !hasValue) {
  //   const fieldList = requiredFields.map((item, index) => `${index + 1}. ${item.label}`).join('<br>');
  //   const errorMessage = `Any one of the following selection criteria must be inputted:<br>${fieldList}`;
    
  //   ElMessage({
  //     dangerouslyUseHTMLString: true,
  //     message: errorMessage,
  //     type: 'error',
  //     duration: 10000,
  //     offset: 100,
  //     showClose: true,
  //   });
    
  //   return false;
  // }
  
  // 清空其他组件的数据
  clearComponentsData();

  return true;
};

// 清空所有子组件数据
const clearComponentsData = () => {
  securityDetailsTableRef.value?.clearData?.();
  settlementDetailsTableRef.value?.clearData?.();
  chargesTableRef.value?.clearData?.();
  finalSettlementTableRef.value?.clearData?.();
};

const handleExportCSV = async () => {
  // 显示加载中状态
  const loading = ElLoading.service({
      lock: true,
      text: '',
      background: 'rgba(255, 255, 255, 0.3)',
      fullscreen: false,
      target: document.querySelector('.el-main') as HTMLElement || undefined,
      body: false,
      customClass: 'loading-position'
    });
  try {
    await downloadFilePost("/bff/si/api/v1/settlement-instruction/export-csv", generateRequestparam(searchParams));
  } catch (error) {
    ElMessage.error('Failed to export CSV');
  } finally {
    loading.close();
  }
};

// 处理行点击事件
const handleClick = async (row: any) => {
  if (!row || !row.bankReference) {
    return;
  }

  // 清空其他组件的数据
  clearComponentsData();
  
  // 加载中
  const loading = ElLoading.service({
    lock: true,
    text: 'Loading',
    background: 'rgba(255, 255, 255, 0.3)',
    fullscreen: false,
    target: document.querySelector('.el-main') as HTMLElement || undefined,
    body: false,
    customClass: 'loading-position'
  });
  
  try {
    // 加载结算指令详情
    const siDetailsResponse = await proxy.$axios.post(
      "/bff/si/api/v1/settlement-instruction/get-settlement-instruction-details",
      generateRequestparam({ tradeOid: row.tradeOid })
    );
    
    if (isResponseSuccess(siDetailsResponse) && settlementDetailsTableRef.value) {
      settlementDetailsTableRef.value.setData(siDetailsResponse.data);
    } else if (!isResponseSuccess(siDetailsResponse)) {
      const errorMsg = getErrorMessage(siDetailsResponse?.header?.message);
      ElMessage.error(errorMsg);
    }
  } catch (error) {
    ElMessage.error('Failed to load detail data');
  } finally {
    loading.close();
  }
};
</script>

<style>
/* 强制所有loading-position类的遮罩覆盖整个视口 */
.loading-position {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 9999 !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* 强制所有loading-position内的spinner居中并放大 */
.loading-position .el-loading-spinner {
  position: fixed !important;
  top: 50vh !important;
  left: 50vw !important;
  transform: translate(-50%, -50%) scale(1.8) !important;
  margin: 0 !important;
  z-index: 10000 !important;
}

/* 放大loading图标并改色 */
.loading-position .el-loading-spinner svg {
  width: 50px !important;
  height: 50px !important;
  color: #409eff !important;
}

/* 加粗loading圆圈并改色 */
.loading-position .el-loading-spinner svg circle,
.loading-position .el-loading-spinner svg path {
  stroke: #409eff !important;
  stroke-width: 4 !important;
}

/* 改进loading文本样式 */
.loading-position .el-loading-text {
  color: #409eff !important;
  font-size: 16px !important;
  font-weight: 500 !important;
}

/* 强制所有loading-position内的文本居中 */
.loading-position .el-loading-text {
  position: fixed !important;
  top: calc(50vh + 40px) !important;
  left: 50vw !important;
  transform: translateX(-50%) !important;
  margin: 0 !important;
  z-index: 10000 !important;
}

/* 确保页面内容区域有固定宽度 */
.el-main {
  overflow-x: hidden;
}
</style>
