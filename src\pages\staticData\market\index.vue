<template> 
  <BasePanel :searchParams="searchParams" :paramListData='paramListData' url="/datamgmt/api/v1/market/list" :params="{ modeEdit: 'Y' }"
    :showDetails="showDetails" :deleteRow="deleteRow" :editRow="editRow" ref="tableRef" :sortProp="{}">
    <template v-slot:searchPanel="slotProps">
      <FormRow>
        <ElFormItemProxy :label="$t('common.title.opCtryRegionCode')" label-width="220" prop="opCtryRegionCode">
        <CtryRegionSearchInput v-model="slotProps.form.opCtryRegionCode" 
            showDesc="false"
            opCtryRegion />
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('common.title.status')" label-width="180" prop="status">
          <Select v-model="slotProps.form.status" style="width: 150px" type='STATUS' :change="statusType(slotProps.form.status)"/>
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('common.title.recordStatus')" label-width="120" prop="multipleRecordStatus">
          <MultipleSelect v-model="slotProps.form.multipleRecordStatus" v-model:desc="paramListData.multipleRecordStatus"  />
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy :label="$t('csscl.si.common.market.marketCode')" label-width="220" prop="marketCode">
          <SearchInput v-model="slotProps.form.marketCode"
            style="width:110px"
            showDesc="false"
            maxlength="3"
            searchField
            url="/datamgmt/api/v1/market/list" 
            :title="$t('csscl.si.common.market.marketCode')"
            :params="{}" 
            :columns="[
              {
                title: $t('csscl.si.common.market.marketCode'),
                colName: 'marketCode',
              },
              {
                title: $t('csscl.si.common.marketDesc'),
                colName: 'marketDesc',
              }
            ]"
          >
          </SearchInput>
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.si.common.marketDesc')" label-width="180" prop="marketDesc">
          <el-input v-model="slotProps.form.marketDesc" maxlength="200" style="width: 150px" />
        </ElFormItemProxy>
        <ElFormItemProxy></ElFormItemProxy>
      </FormRow>
    </template>
    <template v-slot:tableColumn>
      <el-table-column align="left" header-align="center" sortable="custom" prop="opCtryRegionCode" :label="$t('common.title.opCtryRegionCode')" width="300" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="marketCode" :label="$t('csscl.si.common.market.marketCode')" width="300" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="marketDesc" :label="$t('csscl.si.common.marketDesc')" width="300" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="status" :label="$t('csscl.useradmin.usr.status')" width="300" >
        <template #default="scope">
          {{ getCommonDesc('STATUS', scope.row.status) }}
        </template>
      </el-table-column>
      <el-table-column align="left" header-align="center" sortable="custom" prop="recordStatus" :label="$t('csscl.useradmin.usr.recordStatus')" >
        <template #default="scope">
          {{ getRecordStatusDesc(scope.row) }}
        </template>
      </el-table-column>
    </template>
  </BasePanel>
  <Details ref="detailsRef" :reload="reload" />
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance, watch } from 'vue';
import {
Search
} from '@element-plus/icons-vue'
import BasePanel from '~/pages/base/index.vue'
import Details from './Details.vue'
import CtryRegionSearchInput from '~/pages/base/CtryRegionSearchInput.vue'
import CurrencySearchInput from '~/pages/base/CurrencySearchInput.vue'
import  { getCommonDesc, getRecordStatusDesc } from '~/util/Function.js';
import { getOid } from '~/util/Function.js';

const { proxy } = getCurrentInstance()
const paramListData = ref({});
const searchParams = ref({
  opCtryRegionCode:"",
  status:"",
  multipleRecordStatus:[],
  marketCode:'',
  marketDesc:''});

const tableRef = ref();
const detailsRef = ref();
const showDetails = (row, disabled) => {
  detailsRef.value.showDetails(row, disabled);
}
const deleteRow = (row) => {
  proxy.$axios.delete("/datamgmt/api/v1/market?objectId="+ getOid(row, false,null,true)).then((body) => {
    if (body.success) {
      tableRef.value.load();
    }
  });
}

//paramList 参数显示用的
function recordType(value){
  console.log(searchParams)
  paramListData._value.recordStatus =  getCommonDesc('RECORD_STATUS', value);
}
function statusType(value){
  paramListData._value.status =  getCommonDesc('STATUS', value);
}
const editRow = (row) => {
  detailsRef.value.editRow(row);
}
const reload = () => {
  tableRef.value.load();
}
//-------------------------------

</script>

<style>

</style>
