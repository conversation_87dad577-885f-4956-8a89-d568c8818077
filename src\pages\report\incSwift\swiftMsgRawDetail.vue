<template>
  <el-dialog v-model="visible" :show-close="false" width="60%" >
    <template #header="{ close }">
      <div class="my-header">
        <h1>Raw Message</h1>
        <el-button :icon="CloseBold" @click="close" type="primary" style="height:30px;"/>
      </div>
    </template>
    <div style="height:500px; overflow-y: scroll; background-color: #E6E6E6; white-space: pre-wrap;">{{ rawMsg }}</div>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { CloseBold } from '@element-plus/icons-vue'

const visible = ref(false)
let rawMsg = "";

const showSMsgRawDetail = (swiftMsgRaw, disabled) => {
    visible.value = disabled;
    rawMsg = swiftMsgRaw;
}

defineExpose({
    showSMsgRawDetail,
});
</script>

<style scoped>
.my-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: 16px;
}
</style>