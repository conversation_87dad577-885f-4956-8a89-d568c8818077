/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    ApproveButton: typeof import('./components/Mkck/ApproveButton.vue')['default']
    CaEditGrid: typeof import('./components/Ca/CaEditGrid.vue')['default']
    CaSectionTitle: typeof import('./components/Ca/CaSectionTitle.vue')['default']
    CButtons: typeof import('./components/Mkck/CButtons.vue')['default']
    DateItem: typeof import('./components/DateItem.vue')['default']
    DynamicForm: typeof import('./components/SearchInput/Extends/DynamicForm.vue')['default']
    EditGrid: typeof import('./components/EditGrid.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElAside: typeof import('element-plus/es')['ElAside']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElContainer: typeof import('element-plus/es')['ElContainer']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElFooter: typeof import('element-plus/es')['ElFooter']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElFormItemProxy: typeof import('./components/ElFormItemProxy.vue')['default']
    ElHeader: typeof import('element-plus/es')['ElHeader']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElIconClose: typeof import('@element-plus/icons-vue')['Close']
    ElIconDownload: typeof import('@element-plus/icons-vue')['Download']
    ElIconFolderOpened: typeof import('@element-plus/icons-vue')['FolderOpened']
    ElIconSearch: typeof import('@element-plus/icons-vue')['Search']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElMain: typeof import('element-plus/es')['ElMain']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSpace: typeof import('element-plus/es')['ElSpace']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElText: typeof import('element-plus/es')['ElText']
    ElTimePicker: typeof import('element-plus/es')['ElTimePicker']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    Flooter: typeof import('./components/layouts/Flooter.vue')['default']
    FormRow: typeof import('./components/FormRow.vue')['default']
    GeneralSearchInput: typeof import('./components/SearchInput/GeneralSearchInput.vue')['default']
    Header: typeof import('./components/layouts/Header.vue')['default']
    InputNumber: typeof import('./components/InputNumber.vue')['default']
    InputText: typeof import('./components/InputText.vue')['default']
    MultipleGeneralSearchInput: typeof import('./components/SearchInput/MultipleGeneralSearchInput.vue')['default']
    MultipleSearchInput: typeof import('./components/SearchInput/MultipleSearchInput.vue')['default']
    MultipleSelect: typeof import('./components/MultipleSelect.vue')['default']
    PreviewFile: typeof import('./components/PreviewFile.vue')['default']
    RejectButton: typeof import('./components/Mkck/RejectButton.vue')['default']
    RemarkForm: typeof import('./components/Mkck/RemarkForm.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SearchInput: typeof import('./components/SearchInput/SearchInput.vue')['default']
    SearchInputExtend: typeof import('./components/SearchInput/Extends/SearchInputExtend.vue')['default']
    Select: typeof import('./components/Select.vue')['default']
    SIDetails: typeof import('./components/Si/SIDetails.vue')['default']
    SIDialog: typeof import('./components/Si/SIDialog.vue')['default']
    SIEditGrid: typeof import('./components/Si/SIEditGrid.vue')['default']
    SiGrid: typeof import('./components/Si/SiGrid.vue')['default']
    SIIndex: typeof import('./components/Si/SIIndex.vue')['default']
    SISearchInput: typeof import('./components/Si/SISearchInput.vue')['default']
    SubmitButton: typeof import('./components/Mkck/SubmitButton.vue')['default']
    UploadItem: typeof import('./components/UploadItem.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
