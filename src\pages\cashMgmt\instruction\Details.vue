<template>
    <BaseDetails ref="details" :beforeSubmit="beforeSubmit" :reload="props.reload" :viewOriginalForm="viewOriginalForm">
        <el-form :validateOnRuleChange="false" :disabled="formDisabled" ref="dataFormRef" style="width: 85%" :model="dataForm.form" status-icon label-position="left" label-width="220px">
            <FormRow>
                <FormItemSign :detailsRef="details" :label="$t('csscl.cashinstr.upload.fileType')" prop="fileType">
                    <Select v-model="dataForm.form.fileType" :type="formDisabled?'ITF_FILE_TYPE':'UPLD_FILE_TYPE'" style="width:auto" :hideEmpty="true" :disabled="dataForm.form.currentOid"/>
                </FormItemSign>
                <FormItemSign :detailsRef="details" :label="$t('csscl.cashinstr.upload.processStatus')" prop="processStatus" label-width="110px">
                    <el-input v-model="dataForm.form.processStatusDesc" :disabled="true" style="width: 160px;" input-style="text-transform:none"/>
                </FormItemSign>
            </FormRow>
            <FormRow>
                <FormItemSign :detailsRef="details" label=" ">
                    <UploadItem :show-file-list="false" class="upload-demo" drag :file-list="dataForm.form.fileList"  :auto-upload="false" :on-change="handleUpload" :disabled="isUploading"
                        style="width: 500px;" accept=".csv">
                        <el-icon><Upload /></el-icon>
                      {{ isUploading ? 'processing...' : 'Click or drag file to this area to upload' }}
                    </UploadItem>
                </FormItemSign>
            </FormRow>
            <FormRow>
                <FormItemSign :detailsRef="details" :label="$t('csscl.cashinstr.upload.fileName')" prop="fileName" class="text-none">
                    <el-space>
                        <el-input v-model="dataForm.form.fileName" :disabled="true" style="width: 310px;"/>
                        <el-icon-folder-opened  style="width:20px;height:20px;color:red" @click="downloadAttach" v-if="dataForm.form.fileName"/>
                        <el-icon-download style="width:20px;height:20px;color:red;" @click="downloadAttach" v-if="dataForm.form.fileName"/>
                        <el-button type="primary" @click="downloadTemplate">{{$t('csscl.cashinstr.upload.downloadTemplate')}}</el-button>
                    </el-space>
                </FormItemSign>
            </FormRow>
            <FormRow>
                <FormItemSign :detailsRef="details" :label="$t('csscl.cashinstr.upload.totalNoOfRec')" prop="uploadRcCnt">
                    <el-col :span="4"><el-input v-model="dataForm.form.uploadRcCnt" :disabled="true" style="width: 150px;"/></el-col>
                </FormItemSign>
            </FormRow>
            <FormRow>
                <FormItemSign :detailsRef="details" :label="$t('csscl.cashinstr.upload.logStatus')" prop="logStatus">
                    <el-col :span="4"><el-input v-model="dataForm.form.logStatusDesc" :disabled="true" style="width: 250px;" input-style="text-transform:none"/></el-col>
                </FormItemSign>
            </FormRow>
            <div>
                <FormItemSign :detailsRef="details" :label="$t('csscl.cashinstr.upload.logDetails')" prop="errorLog">
                    <el-input v-model="dataForm.form.errorLogDesc" type="textarea" rows="10" :disabled="true" style="width: 500px;"/>
                </FormItemSign>
            </div>
        </el-form>
    </BaseDetails>
</template>

<script lang="ts" setup>
import { ref, reactive, getCurrentInstance } from 'vue';
import {Upload} from '@element-plus/icons-vue'
import type { FormInstance } from 'element-plus';
import BaseDetails from '~/pages/base/Details.vue';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import { getOid, getCommonDesc, downloadFile } from '~/util/Function.js';
const { proxy } = getCurrentInstance();
const props = defineProps(['reload']);
const details = ref();
const formDisabled = ref(false);

const dataFormRef = ref<FormInstance>()
const dataForm = reactive({
    form: {
        channel: "",
        fileType: "",
        fileName: "",
        uploadRcCnt: "",
        fileList: [],
    }
})
//start yesh 250509 BAU-57
const isUploading = ref(false);
const editRow = (row, disabled,newId) => {
    const oid = newId || (row?.currentOid && getOid(row, disabled, proxy));
    if (oid) {
        proxy.$axios.get("/cashmgmt/api/v1/cashinstr/upload/details?objectId=" + oid).then((body) => {
            if(body.success) {
                dataForm.form = body.data;
                details.value.currentRow = body.data;
                details.value.currentRow.bankBkCloseBal = body.data.amount;
                details.value.currentRow.currencyCode = body.data.currency;
                dataForm.form.fileList = [];

                dataForm.form.processStatusDesc = getCommonDesc('FILE_PROCESS_STATUS', dataForm.form.processStatus);
                dataForm.form.logStatus = getUploadStatus(oid, dataForm.form.processStatus);
                dataForm.form.logStatusDesc = getCommonDesc('FILE_UPLOAD_STATUS', dataForm.form.logStatus);
                if(dataForm.form.errorLog) {
                    dataForm.form.errorLogDesc = proxy.$t(dataForm.form.errorLog);
                    dataForm.form.errorLogDesc = dataForm.form.errorLogDesc.replaceAll("\\n", "\n");
                }

                if(upldFormDisabled(dataForm.form)){
                    formDisabled.value = true;
                }
            }
        });
        // proxy.$axios.get("/cashmgmt/api/v1/cashinstr/upload/online").then((body) => {
        //     if(body.success) {
        //         //这里是为了PA2审批的时候做校验用
                // details.value.currentRow.bankBkCloseBal = body.data.amout;
                // details.value.currentRow.currencyCode = body.data.ccy;
        // });
        details.value.initWatch(dataForm);
    } else {
        dataForm.form.fileList = [];
        dataForm.form.channel = "MANUAL";
        dataForm.form.fileType = "MANUAL";
        details.value.initWatch(dataForm);
    }
}
const viewOriginalForm = (pendingOid, isDisabled) => {
    formDisabled.value = isDisabled;
    proxy.$axios.get("/cashmgmt/api/v1/cashinstr/upload/details?objectId=" + pendingOid).then((body) => {
        if (body.success) {
            dataForm.form = body.data;
            dataForm.form.processStatusDesc = getCommonDesc('FILE_PROCESS_STATUS', dataForm.form.processStatus);
            dataForm.form.logStatus = getUploadStatus(pendingOid, dataForm.form.processStatus);
            dataForm.form.logStatusDesc = getCommonDesc('FILE_UPLOAD_STATUS', dataForm.form.logStatus);
            if (dataForm.form.errorLog) {
                dataForm.form.errorLogDesc = proxy.$t(dataForm.form.errorLog);
                dataForm.form.errorLogDesc = dataForm.form.errorLogDesc.replaceAll("\\n", "\n");
            }
        }
    });
}
const handleSave = async (searchValid, isOnlyValidate, unPopping) => {
    if (isOnlyValidate) {
        return true;
    }
    if (searchValid) {
        const formData = new FormData();
        formData.append('channel', dataForm.form.channel);
        formData.append('fileType', dataForm.form.fileType);
        if(dataForm.form.fileList.length > 0){
            formData.append('file', dataForm.form.fileList[0].raw);
        }
        if(dataForm.form?.currentOid){
            formData.append('objectId', dataForm.form.currentOid);
        }
      try {
        const msg = await proxy.$axios.post("/datamgmt/api/v1/document/in/upload", formData);
        if(msg.success){
            if(msg.data.currentOid){
                details.value.writebackId(msg.data.currentOid);
                editRow(null,null,msg.data.currentOid);
                await proxy.$axios.get("/datamgmt/api/v1/handler?eventName=" +"CSSCL_CASHM002"+ "&eventOid=" +msg.data.currentOid + "&userName=" + "");
            }else{
                dataForm.form.fileList = [];

                dataForm.form.logStatus = getUploadStatus(msg.data.currentOid, msg.data.processStatus);
                dataForm.form.logStatusDesc = getCommonDesc('FILE_UPLOAD_STATUS', dataForm.form.logStatus);
                dataForm.form.fileName = msg.data.fileName
                dataForm.form.uploadRcCnt = msg.data.uploadRcCnt
                dataForm.form.errorLog = msg.data.errorLog;
                let errorLog = proxy.$t(dataForm.form.errorLog);
                dataForm.form.errorLogDesc = errorLog.concat(msg.data.custAccNo==null?"":msg.data.custAccNo);
            }
        }
        //Start SIR-Cristin-R047,SIR-Cristin-R043 AMOR,20240814
        // await proxy.$axios.get("/datamgmt/api/v1/handler?eventName=" +"CSSCL_CASHM002"+ "&eventOid=" +msg.data.currentOid + "&userName=" + "");
        //End SIR-Cristin-R047,SIR-Cristin-R043 AMOR,20240814
        return msg.success;
        } catch (error) {
        return false;
      } finally {
        isUploading.value = false; // The upload is complete. Enable the upload component
      }
    }
    return false;
}
const beforeSubmit = () => {
    let row = details.value.currentRow;
    if(row.logStatus !== "P") {
        return proxy.$t("message.system.no.valid.file");
    }
}
const showDetails = (row, isdoubleCheck) => {
    if(isdoubleCheck || upldFormDisabled(row)){
        formDisabled.value = true;
        details.value.showDetails(row, true)
    }else{
        formDisabled.value = false;
        details.value.showDetails(row, false)
    }
    dataForm.form = {};
    details.value.currentRow={};
    editRow(row, isdoubleCheck); 
}
const upldFormDisabled = (row) => {
    if(row.recordStatus==='PA'||row.recordStatus==='PA1'||row.recordStatus==='PA2'
        || (row.fileType && row.fileType != "MANUAL" && row.fileType != "REUPLOAD")
    ){
        return true;
    }
    return false;
}
defineExpose({
    details,
    editRow,
    showDetails,
    viewOriginalForm,
});
// --------------------------------------------
const handleUpload = async(file) => {
    if(details.value.currentRow.recordStatus == "PA" ||details.value.currentRow.recordStatus == "PA1"||details.value.currentRow.recordStatus == "PA2"){
        return;
    }
    isUploading.value = true;
    dataForm.form.fileList = [];
    dataForm.form.fileList.push(file);
    handleSave(true);
}

const downloadAttach = () => {
    var row = dataForm.form;
    var params = {
        filePath: row.filePath,
        fileName: row.fileName,
    }
    downloadFile("/datamgmt/api/v1/document/in/download", params);
}

const downloadTemplate = () => {
    downloadFile("/datamgmt/api/v1/document/in/template", {});
}

const getUploadStatus = (currentOid, processStatus) => {
    if(processStatus){
        if(currentOid && processStatus === "R"){
            return "P";
        }
        if(processStatus === "F"){
            if(currentOid){
                return "F";
            }else{
                return "A";
            }
        }
    }
    return null;
}

</script>

<style></style>