<template>
    <BaseDetails ref="details" :handleSave="handleSave" :reload="props.reload" :form="ruleForm" :viewOriginalForm="viewOriginalForm"  >
        <el-form :validateOnRuleChange="false" :disabled="formDisabled" ref="ruleFormRef" style="width: 100%" :model="ruleForm.form" :rules="rules"
            status-icon>
            <FormRow>
                <FormItemSign :detailsRef="details" :label="$t('common.title.opCtryRegionCode')" prop="opCtryRegionCode">
                    <CtryRegionSearchInput v-model="ruleForm.form.opCtryRegionCode" 
                        showDesc="false"
                        style="width: 120px"
                        :disabled="notEdit"
                        opCtryRegion />
                </FormItemSign>
            </FormRow>
            <FormRow>
                <FormItemSign :detailsRef="details" :label="$t('csscl.useradmin.role.roleId')" prop="roleId">
                    <InputText :disabled="notEdit" v-model="ruleForm.form.roleId" uppercase maxlength="7" />
                </FormItemSign>
                <FormItemSign :detailsRef="details" :label="$t('csscl.useradmin.usr.status')" prop="status">
                    <Select v-model="ruleForm.form.status" style="width: 120px" type='STATUS' />
                </FormItemSign>
                <div></div>
            </FormRow>
            <FormRow>
                <FormItemSign :detailsRef="details" :label="$t('csscl.useradmin.role.roleName')" prop="roleName">
                    <el-input v-model="ruleForm.form.roleName" maxlength="100" style="width: 300px;" />
                </FormItemSign>
            </FormRow>
        </el-form>
        <span style="font-weight: bold;"> {{ $t('csscl.useradmin.role.funcReportAssign') }} </span>
        <el-divider style="margin: 5px 0px;" />
        <el-space direction="vertical" alignment="start" style="width: 100%;">
            <DataGrid :showName="formartMenu" :disabled="formDisabled" ref="rightGridRef" leftOid="rightId"
                rightOid="rightId">
                <template #name>
                    {{ $t('csscl.useradmin.role.funcAssign') }}
                </template>
                <template #leftCols>
                    <el-table-column :label="$t('csscl.useradmin.role.availFunc')">
                        <el-table-column prop="menuPath" :label="$t('csscl.useradmin.role.func')" width="600">
                            <template #default="scope">
                                {{ formartMenu(scope.row) }}
                            </template>
                        </el-table-column>
                    </el-table-column>
                </template>
                <template #rightCols>
                    <el-table-column :label="$t('csscl.useradmin.role.assignTo')">
                        <el-table-column prop="menuPath" :label="$t('csscl.useradmin.role.func')" width="600">
                            <template #default="scope">
                                {{ formartMenu(scope.row) }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="recordStatus" :label="$t('csscl.useradmin.usr.recordStatus')"
                            width="200">
                            <template #default="scope">
                                <!-- scope.row -->
                                {{ getCommonDesc('RECORD_STATUS', scope.row.recordStatus) }}
                                <span v-if="scope.row.recordStatus && scope.row.recordStatus !== 'A'">
                                    for {{ getCommonDesc('MKCK_ACTION', scope.row.mkckAction) }}
                                </span>
                            </template>
                        </el-table-column>
                    </el-table-column>
                </template>
            </DataGrid>
            <DataGrid :showName="formatReportShowName" :disabled="formDisabled" ref="reportGridRef"
                leftOid="reportTemplateCode" rightOid="reportTemplateCode">
                <template #name>
                    {{ $t('csscl.useradmin.role.reportAssign') }}
                </template>
                <template #leftCols>
                    <el-table-column :label="$t('csscl.useradmin.role.availReport')">
                        <el-table-column prop="reportTemplateCode" :label="$t('csscl.useradmin.role.report')"
                            width="600">
                            <template #default="scope">
                                {{ formatReportShowName(scope.row) }}
                            </template>
                        </el-table-column>
                    </el-table-column>
                </template>
                <template #rightCols>
                    <el-table-column :label="$t('csscl.useradmin.role.assignTo')">
                        <el-table-column prop="reportTemplateCode" :label="$t('csscl.useradmin.role.report')"
                            width="600">
                            <template #default="scope">
                                {{ formatReportShowName(scope.row) }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="recordStatus" :label="$t('csscl.useradmin.usr.recordStatus')"
                            width="200">
                            <template #default="scope">
                                <!-- scope.row -->
                                {{ getCommonDesc('RECORD_STATUS', scope.row.recordStatus) }}
                                <span v-if="scope.row.recordStatus && scope.row.recordStatus !== 'A'">
                                    for {{ getCommonDesc('MKCK_ACTION', scope.row.mkckAction) }}
                                </span>
                            </template>
                        </el-table-column>
                    </el-table-column>
                </template>
            </DataGrid>
        </el-space>

        <el-row>
            <span style="font-weight: bold;margin-top: 5px;"> {{ $t('csscl.si.common.DataAccessLevel') }} </span>
            <el-divider style="margin: 5px 0px;" />
        </el-row>
        <el-row style="margin-left: 10px; margin-bottom: 5px;">
            <FormItemSign :detailsRef="details" prop="globalCustody">
                <el-checkbox label="GC" style="width: 80px" true-label="Y" false-label="N" v-model="ruleForm.form.globalCustody" :disabled="formDisabled" />
            </FormItemSign>
        </el-row>
        <el-row style="margin-left: 10px;">
            <FormItemSign :detailsRef="details" prop="localCustody">
                <el-checkbox label="LC" style="width: 80px" true-label="Y" false-label="N" v-model="ruleForm.form.localCustody" 
                :disabled="formDisabled" @change="changeLC" />
            </FormItemSign>    
        </el-row>
        <br/>
      <el-container>
        <el-aside width="40%">
            <SIEditGrid v-model="dataAccessLevelMarketVpos"
                oid="roleMarketRelId" 
                ref="dataAccessLevelMarketGridRef"
                uniqueKey="marketCode"
                :form="dataAccessLevelMarketForm" 
                :rules="dataAccessLevelMarketRules" 
                :details="details" 
                :buttonDisabled="formDisabled || (ruleForm.form.localCustody !== 'Y')"
                tableStyle="overflow: auto; height: 250px;"
                @showData="changeShowData"
                >
                <template #columns>
                    <el-table-column prop="marketCode" width="150" :label="$t('csscl.si.common.market.marketCode')" />    
                    <el-table-column prop="marketDesc" :label="$t('csscl.commonCode.description')"/>
                    <el-table-column prop="recordStatus" width="220" :label="$t('common.title.recordStatus')">
                        <template #default="scope">
                            {{ getCommonDesc('RECORD_STATUS', scope.row.recordStatus) }}
                            <span v-if="scope.row.recordStatus && scope.row.recordStatus !== 'A'">
                                for {{ getCommonDesc('MKCK_ACTION', scope.row.mkckAction) }}
                            </span>
                        </template>
                    </el-table-column>
                </template>
                <template #form>
                    <FormItemSign :detailsRef="details" label-width="150px" :label="$t('csscl.si.common.market.marketCode')" prop="marketCode">
                        <SISearchInput style="width: 400px" v-model="dataAccessLevelMarketForm.marketCode" @changeRow="changeMarketGridRefDataRow"
                            url="/datamgmt/api/v1/searchinput" showDesc="true" readonly="true"
                            maxlength="3"
                            :title="$t('csscl.si.common.market.marketCode')"
                            :params="{searchType: 'custodyMarketApprove'}"
                            :columns="[
                                {
                                    title: $t('csscl.si.common.market.marketCode'),
                                    colName: 'code',
                                },
                                {
                                    title: $t('csscl.si.common.marketDesc'),
                                    colName: 'codeDesc',
                                }
                            ]"
                            :pageSizes="[10, 20, 30]">
                        </SISearchInput>
                    </FormItemSign>
                </template>
            </SIEditGrid>
        </el-aside>
      </el-container>
    </BaseDetails>
</template>

<script lang="ts" setup>
import { ref, reactive, getCurrentInstance } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import BaseDetails from '~/pages/base/Details.vue';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import DataGrid from './DataGrid.vue';
// Start R2411A-29119, Tom.Li, 2024/08/12
// import { getOid, getCommonDesc, saveMsgBox } from '~/util/Function.js';
import { getOid, getCommonDesc, saveMsgBox, showErrorMsg } from '~/util/Function.js';
// End R2411A-29119, Tom.Li, 2024/08/12
import CtryRegionSearchInput from '~/pages/base/CtryRegionSearchInput.vue';
import { commonRules, showValidateMsg } from '~/util/Validators.js';
import { timestampToDateTime } from '~/util/DateUtils.js';
import { addCustValid, focusType } from "~/util/ModifiedValidate";
const { proxy } = getCurrentInstance();
const props = defineProps(['reload']);
const details = ref();
const rightGridRef = ref();
const reportGridRef = ref();
const formDisabled = ref(false);
const notEdit = ref(false);
const dataAccessLevelMarketVpos = ref([]);
const dataAccessLevelMarketGridRef = ref();
const validMarket = ref(true);
const dataAccessLevelMarketForm = reactive({
  marketOid: null,
  marketCode: '',
  marketDesc: ''
});

const dataAccessLevelMarketRules = reactive({
  marketCode: [
    commonRules.required,
  ]
});


const editRow = (row, disabled,newId) => {
    if(row?.isApproveDetail && disabled){
        ruleForm.form = row.afterImage;
        if(row.afterImage.sysCreateDate){
            ruleForm.form.sysCreateDate=timestampToDateTime(row.afterImage.sysCreateDate);
        }
        if(row.afterImage.sysUpdateDate){
            ruleForm.form.sysUpdateDate=timestampToDateTime(row.afterImage.sysUpdateDate);
        }
        details.value.currentRow = ruleForm.form;
        let reqParams = {
            roleId: ruleForm.form.roleId,
            approveNumber: row?.approveNumber
        };
        proxy.$axios.post("/auth/api/v1/user/role/function/approve/list", reqParams).then((body) => {
            if (body.success) {
                rightGridRef.value.loadData(2, body.data);
            }
        });
        proxy.$axios.post("/auth/api/v1/user/role/report/approve/list", reqParams).then((body) => {
            if (body.success) {
                reportGridRef.value.loadData(2, body.data);
            }
        });
        loadGrid();
    } else {
        const oid = newId || (row?.currentOid && getOid(row, disabled, proxy));
        proxy.$axios.get("/auth/api/v1/user/role/desc?sysRoleOid=" + oid).then((body) => {
            if (body.success) {
                ruleForm.form = body.data;
                details.value.currentRow = body.data;
                var p1 = proxy.$axios.post("/auth/api/v1/user/role/function/list", {
                    roleId: body.data.roleId,
                    isApproved: body.data.recordStatus == 'A' ? "A" : "P"
                }).then((body) => {
                    if (body.success) {
                        rightGridRef.value.loadData(2, body.data);
                    }
                });
                var p2 = proxy.$axios.post("/auth/api/v1/user/role/report/list", {
                    roleId: body.data.roleId,
                    isApproved: body.data.recordStatus == 'A' ? "A" : "P"
                }).then((body) => {
                    if (body.success) {
                        reportGridRef.value.loadData(2, body.data);
                    }
                });
                // Start, LiKunBiao, 2025/07/15
                dataAccessLevelMarketGridRef.value.clearModifyData();
                loadGrid();
                // End, LiKunBiao, 2025/07/15
                addCustValid (ruleForm.form, () => {
                  if (Object.values(rightGridRef.value.modifyRecords).length > 0) {
                    return true;
                  }
                  if (Object.values(reportGridRef.value.modifyRecords).length > 0) {
                    return true;
                  }
                  // Start, LiKunBiao, 2025/07/15
                  if (dataAccessLevelMarketGridRef.value.getModifyRecords().length > 0) {
                    return true;
                  }
                  // End, LiKunBiao, 2025/07/15
                  let rightData = Object.values(rightGridRef.value.getData().currentRightData);
                  if (rightData.length > 0) {
                    for ( let right in rightData ) {
                      right = rightData[right];
                      if (right.recordStatus != 'A') {
                        focusType.type = focusType.EnterObj;
                      }
                    }
                  }
                  let reportData = Object.values(reportGridRef.value.getData().currentRightData);
                  if (reportData.length > 0) {
                    for ( let report in reportData ) {
                      report = reportData[report];
                      if (report.recordStatus != 'A') {
                        focusType.type = focusType.EnterObj;
                      }
                    }
                  }
                  // Start, LiKunBiao, 2025/07/15
                  let leg = dataAccessLevelMarketGridRef.value.getModifyRecords()?.length;
                  leg = leg > 0 ? true : false;
                  if (!leg) {
                    let datas = dataAccessLevelMarketGridRef.value.showData;
                    for (let i = 0; i < datas.length; i++) {
                        let data = datas[i];
                        if (data.recordStatus != 'A') {
                            focusType.type = focusType.EnterObj;
                        }
                    }
                  }
                  // End, LiKunBiao, 2025/07/15
                  return false;
                })
            }
            Promise.all([p1,p2]).then((res)=>{
                details.value.initWatch({w1:ruleForm,w2:rightGridRef.value.modifyRecords,w3:reportGridRef.value.modifyRecords}, ruleForm);
            })
        });
    }
}

const handleSave = async (searchValid, isOnlyValidate, unPopping) => {
    let result = await ruleFormRef.value.validate((valid, fields) => {
        if (valid) {

        } else {
            showValidateMsg(details, fields);
        }
    });
    // Start R2411A-29119, Tom.Li, 2024/08/12
    // if (result ) {
    if (result &  ruleForm.form.status == 'A') {
    // End R2411A-29119, Tom.Li, 2024/08/12
        let rightGridData = rightGridRef.value.getData();
        let reportGridData = reportGridRef.value.getData();
        let isExist = false;
        if (rightGridData?.showRightData) {
            let recs = Object.values(rightGridData?.showRightData);
            for (let i = 0; i < recs.length; i++) {
                // Start R2411A-29119, Tom.Li, 2024/08/12
                // if ( recs[i].recordStatus != 'D') {
                if ( recs[i].mkckAction != 'D') {
                // End R2411A-29119, Tom.Li, 2024/08/12
                    isExist = true;
                    break;
                }
            }
        }
        if (reportGridData?.showRightData) {
            let recs = Object.values(reportGridData?.showRightData);
            for (let i = 0; i < recs.length; i++) {
                // Start R2411A-29119, Tom.Li, 2024/08/12
                // if (recs[i].recordStatus != 'D') {
                if (recs[i].mkckAction != 'D') {
                // End R2411A-29119, Tom.Li, 2024/08/12
                    isExist = true;
                    break;
                }
            }
        }
        if (!isExist) {
            // Start R2411A-29119, Tom.Li, 2024/08/12
            // ElMessage({
            //     message: "At least assign 1 function or 1 report if the status = Active",
            //     type: 'error',
            // });
            showErrorMsg("At least assign 1 function or 1 report if the status = Active");
            // End R2411A-29119, Tom.Li, 2024/08/12
        }
        let isValid = validLocalCustody(true);
        if (!isExist || !isValid) {
            return false;
        }
    }
    if (isOnlyValidate) {
        return result;
    }
    if (result && searchValid && await saveMsgBox(unPopping)) {
        if (!ruleForm.form.globalCustody) {
            ruleForm.form.globalCustody = 'N';
        }
        if (!ruleForm.form.localCustody) {
            ruleForm.form.localCustody = 'N';
        }
        if (ruleForm.form.sysRoleOid) {
            const msg = await proxy.$axios.patch("/auth/api/v1/user/role", {
                ...ruleForm.form,
                rightInfos: Object.values(rightGridRef.value.modifyRecords),
                reportTempRels: Object.values(reportGridRef.value.modifyRecords),
                roleMarketRels: dataAccessLevelMarketGridRef.value.getModifyRecords()
            });
            if (msg.data) {
                details.value.writebackId(msg.data);
                editRow(null,null,msg.data);
                dataAccessLevelMarketGridRef.value.clearModifyData()
                loadGrid();
            }
            return msg.success;
        } else {
            const msg = await proxy.$axios.post("/auth/api/v1/user/role", {
                ...ruleForm.form,
                rightInfos: Object.values(rightGridRef.value.modifyRecords),
                reportTempRels: Object.values(reportGridRef.value.modifyRecords),
                roleMarketRels: dataAccessLevelMarketGridRef.value.getModifyRecords()
            });
            if (msg.data) {
                details.value.writebackId(msg.data);
                editRow(null,null,msg.data);
                dataAccessLevelMarketGridRef.value.clearModifyData()
                loadGrid();
            }
            return msg.success;
        }

    }
    return false;
}

const showDetails = (row, isDoubleCheck) => {
    if (isDoubleCheck || row.recordStatus === 'PA') {
        formDisabled.value = true;
        details.value.showDetails(row, true)
    } else {
        formDisabled.value = false;
        details.value.showDetails(row, false)
    }
        ruleForm.form = {};
        let isApproved="P";
        if(isDoubleCheck){
            isApproved="A"
        }
        if(isDoubleCheck&&row.mkckAction == 'C'){
            isApproved="P"
        }
        console.log(isApproved)
        var p1 = proxy.$axios.post("/auth/api/v1/user/role/function/list", {
        unRoleId: row.roleId,
        isApproved: isApproved
        }).then((body) => {
        if (body.success) {
            rightGridRef.value.loadData(1, body.data);
        }
        });
        var p2 =proxy.$axios.post("/auth/api/v1/user/role/report/list", {
        unRoleId: row.roleId,
         isApproved: isApproved
    }).then((body) => {
        if (body.success) {
            reportGridRef.value.loadData(1, body.data);
        }
    });
    notEdit.value = false;
    if (row.currentOid) {
        editRow(row, isDoubleCheck);
        notEdit.value = true;
    }else{
        Promise.all([p1,p2]).then((res)=>{
            details.value.initWatch({w1:ruleForm,w2:rightGridRef.value.modifyRecords,w3:reportGridRef.value.modifyRecords}, ruleForm);
        })
    }
}

const viewOriginalForm = (pendingOid, isDisabled) => {
    formDisabled.value = isDisabled;
    proxy.$axios.get("/auth/api/v1/user/role/desc?sysRoleOid=" + pendingOid).then((body) => {
        if (body.success) {
            ruleForm.form = body.data;
            // details.value.currentRow = body.data;
            proxy.$axios.post("/auth/api/v1/user/role/function/list", {
                unRoleId: body.data.roleId,
                isApproved:  body.data.recordStatus == 'A' ? "A" : "P"
                }).then((body) => {
                if (body.success) {
                    rightGridRef.value.loadData(1, body.data);
                }
            });
            proxy.$axios.post("/auth/api/v1/user/role/report/list", {
                unRoleId: body.data.roleId,
                isApproved:  body.data.recordStatus == 'A' ? "A" : "P"
            }).then((body) => {
                if (body.success) {
                    reportGridRef.value.loadData(1, body.data);
                }
            });
            proxy.$axios.post("/auth/api/v1/user/role/function/list", {
                roleId: body.data.roleId,
                isApproved: body.data.recordStatus == 'A' ? "A" : "P"
            }).then((body) => {
                if (body.success) {
                    rightGridRef.value.loadData(2, body.data);
                }
            });
            proxy.$axios.post("/auth/api/v1/user/role/report/list", {
                roleId: body.data.roleId,
                isApproved: body.data.recordStatus == 'A' ? "A" : "P"
            }).then((body) => {
                if (body.success) {
                    reportGridRef.value.loadData(2, body.data);
                }
            });
            loadGrid();
        }
    });
}

const loadGrid = async () => {
  const msg = await proxy.$axios.post("/auth/api/v1/user/role/market/list", {
        sysRoleOid: ruleForm.form.sysRoleOid, 
        pendingOid: ruleForm.form.pendingOid
  });
  if (msg?.success) {
      dataAccessLevelMarketVpos.value = msg.data;
  }
}

const changeShowData = (newVal: any[]) => {
    newVal = newVal.filter((obj: any) => obj.mkckAction != 'D');
    if (newVal.length > 1) {
        const keys = newVal.map(obj => obj.marketCode);
        if (keys.includes('ALL')) {
            showErrorMsg("Please remove other markets when you select 'ALL'");
            validMarket.value = false;
            return;
        }
    }
    validMarket.value = true;
}

const changeMarketGridRefDataRow = (row: any) => {
    dataAccessLevelMarketForm.marketDesc = row.codeDesc;
    dataAccessLevelMarketForm.marketOid = row.var1;
}

const validLocalCustody = (isValidMarket: boolean) => {
    let result = true;
    let showData = dataAccessLevelMarketGridRef.value.showData;
    showData = showData.filter((obj: any) => obj.mkckAction != 'D');
    if (ruleForm.form.localCustody === 'Y') {
        if (showData.length === 0) {
            showErrorMsg("Please select a market for local custody access rights");
            result = false;
        }
    } else {
        if (showData.length > 0) {
            showErrorMsg("Please remove all markets when the LC checkbox is deselected");
            ruleForm.form.localCustody = 'Y';
            result = false;
        }
    }
    if (isValidMarket && !validMarket.value) {
        showErrorMsg("Please remove other markets when you select 'ALL'");
        result = false;
    }
    return result;
}

const changeLC = (val: string) => {
    if (val === 'N') {
        validLocalCustody(false);
    }
}

defineExpose({
    details,
    editRow,
    showDetails,
});
// --------------------------------------------

interface RuleForm {
    opCtryRegionCode: string
    roleId: string
    roleName: string
    status: string
}

const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive({
    rules:()=>{ return [{ rules:rules }] },
    form: {
        opCtryRegionCode: "",
        roleId: "",
        roleName: "",
        status: "",
        globalCustody: "",
        localCustody: "",
        sysCreateDate: "",
        sysUpdateDate: ""
    }
})

const rules = reactive<FormRules<RuleForm>>({
    opCtryRegionCode: [
        commonRules.required,
    ],
    roleId: [
        commonRules.required,
        commonRules.name,
    ],
    roleName: [
        commonRules.required,
        commonRules.name,
    ],
    status: [
        commonRules.required,
    ],
})

const formartMenu = (row) => {
    //return row.menuId + " - " + row.menuName + " - " + row.rightName;
    return  row.rightId + " - " + row.menuName + " - " + row.rightName;
}

const formatReportShowName = (record) => {
    //return record.reportCategory + " - " + record.reportTemplateCode + " - " + record.reportDesc;
    return record.reportTemplateCode + (record.reportTemplateCodeExt||"") + " - " + record.reportDesc + (record.reportDescExt||"");
}
</script>

<style></style>