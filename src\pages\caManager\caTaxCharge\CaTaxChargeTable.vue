<template>
    <el-row>
      <el-space style="padding-top: 10px;">
        <el-button :icon="Plus" type="primary" @click="handleNewClick" />
      </el-space>
    </el-row>
    <Grid :data="tableData" isShowSearch="false" ref="gridRef" :selectable="false"
      :onDbClick="onDbClick">
      <template #tableColumnFront>
        <el-table-column prop="taxChargePayTypeCode" :label="$t('csscl.ca.common.paymentType')" align="center">
          <template #default="scope">
            {{ getCommonDesc('CA_TAX_CHARGE_PAY_TYPE_CODE', scope.row.taxChargePayTypeCode) }}
          </template>
        </el-table-column>
        <el-table-column prop="drCrIndicator" :label="$t('csscl.ca.common.actionType')" align="center">
          <template #default="scope">
            {{ getCommonDesc('CA_ACTION_TYPE', scope.row.drCrIndicator) }}
          </template>
        </el-table-column>
        <el-table-column prop="allocationMethodCode" :label="$t('csscl.ca.common.allocationType')" align="center"/>
        <el-table-column prop="totalTaxChargeCcy1Code" :label="$t('csscl.ca.common.totalTransactionCurrency')" align="center"/>
        <el-table-column prop="totalTaxChargeAmount1" :label="$t('csscl.ca.common.totalTransactionAmount')" align="center"/>
        <el-table-column prop="totalTaxChargeCcy1Code" :label="$t('csscl.ca.common.DRCRCurrency')" align="center"/>
        <el-table-column prop="valueDate" :label="$t('csscl.ca.common.originalValueDate')" align="center"/>
        <el-table-column prop="status" :label="$t('csscl.ca.common.status')" align="center">
          <template #default="scope">
            {{ getCommonDesc('STATUS', scope.row.status) }}
          </template>
        </el-table-column>
        <el-table-column prop="recordStatus" :label="$t('csscl.ca.common.recordStatus')" align="center">
          <template #default="scope">
            {{ getCommonDesc('RECORD_STATUS', scope.row.recordStatus) }}
            <span v-if="scope.row.recordStatus && scope.row.recordStatus !== 'A'">
              for {{ getCommonDesc('MKCK_ACTION', scope.row.mkckAction) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('common.table.operation')" width="120">
              <template #default="scope">
                <CButtons :action="'Edit'"
                          :icon="Edit" link type="primary" style="font-size: 16px;" @click="handleEdit(scope.row)"></CButtons>
              </template>
            </el-table-column>
      </template>
    </Grid>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue';
import { ElMessage, ElMessageBox, type FormInstance, type FormRules, ElTable } from 'element-plus';
import { getCommonDesc } from '~/util/Function.js';
import Grid from '~/pages/caManager/caClientResponse/CaClientResponseGrid.vue';
import { getTimeZone } from "~/util/DateUtils";
import { Edit, Plus, CirclePlusFilled, CirclePlus } from '@element-plus/icons-vue'
const { proxy } = getCurrentInstance();
const props = defineProps(['handleHeaderClick']);
// 定义表格数据
const tableData = ref([]);
const gridRef = ref();
const generateRequestparam = (data:any) => {
  let params = {
    header: {timezone: getTimeZone(), lang:"en_US"},
    data: data
  };
  return params;
}
const onDbClick = (row:any) => {
  props.handleHeaderClick(row);
}
const handleNewClick = () => {
  props.handleHeaderClick({});
}
const handleEdit = (row:any) => {
  props.handleHeaderClick(row);
}
const isResponseSuccess = (response:any) => {
  if (!response || !response.header) {
    return false;
  }
  return response.header.code === '000000';
}
const showDetails = async (row:any, isdoubleCheck:any) => {
  if (gridRef.value) {
    if (row.caEventReferenceNumber) {
      loadTaxChargeHeaderData(row);
    }
  }
}
const getErrorMessage = (errorMessage:any) => {
  return errorMessage || 'Unknown error';
}
const loadTaxChargeHeaderData = async (row:any) => {
  if (!row?.caEventReferenceNumber) return;
  let params = generateRequestparam({
      caEventReferenceNumber: row?.caEventReferenceNumber,
      caEventTaxChargeHdrOid: row?.caEventTaxChargeHdrOid,
  });
  const response = await proxy.$axios.post("/bff/ca/api/v1/ca-event-tax-charge/get-tax-charge-header-page-list", params);
  if (isResponseSuccess(response)) {
        gridRef.value.tableData = response.data.items;
  } else {
      const errorMsg = getErrorMessage(response?.header?.message);
      ElMessage.error(errorMsg);
  }
};
const clearData = async() => {
  tableData.value = [];
}
defineExpose({
  clearData,
  showDetails,
});
</script>

<style></style>