VITE_APP_VERSION = v0.13.0
VITE_SYSTEM = Custody and Clearing Platform(USMF)
VITE_BASEPATH=/
VITE_FRONTEND_HOME=https://csscl-usmf-web.apps.bochkuatcloud.com
VITE_FRONTEND=https://csscl-usmf-web.apps.bochkuatcloud.com
VITE_OIDCURL= https://uat-int-apigw.bochkuatcloud.com/eaphk/bapi/v1/csscl-usmf
VITE_REDIRECTURL=https://csscl-usmf-web.apps.bochkuatcloud.com
VITE_SERVICE= https://uat-int-apigw.bochkuatclout.com
# 接口请求地址，会设置到 axios 的 baseURL 参数上
VITE_API_BASE_URL = https://uat-int-apigw.bochkuatclout.com/eaphk/bapi/v1/csscl-usmf/protected
# message 请求的网关地址
VITE_API_DEV_URL = https://csscl-usmf.apps.bochkuatcloud.com/csscl-gateway
VITE_WS_PROTOCOL = wss