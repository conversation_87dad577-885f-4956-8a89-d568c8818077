<template>
  <div class="test-container">
    <h2>DepositoryEditGrid 功能测试页面</h2>
    
    <!-- 测试控制面板 -->
    <el-card class="test-panel" style="margin-bottom: 20px;">
      <template #header>
        <span>🧪 测试控制面板 - 数据状态管理功能</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <el-button type="primary" @click="getModifyRecords">
            📋 获取修改记录
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="warning" @click="clearModifyData">
            🗑️ 清空修改缓存
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="info" @click="showCurrentData">
            📊 显示当前数据
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="success" @click="checkPageStatus">
            🔍 检查页面状态
          </el-button>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 10px;">
        <el-col :span="6">
          <el-button type="danger" @click="testHighlightBehavior">
            🎨 测试Highlight行为
          </el-button>
        </el-col>
      </el-row>
      
      <el-divider />
      
      <!-- 测试结果显示区域 -->
      <div class="test-results">
        <h4>🔍 测试结果:</h4>
        <el-alert 
          v-if="testMessage" 
          :title="testMessage" 
          :type="testMessageType" 
          style="margin-bottom: 10px;"
          show-icon 
          closable 
          @close="testMessage = ''" />
        
        <!-- 修改记录显示 -->
        <div v-if="modifyRecords.length > 0" class="modify-records">
          <h5>📝 修改记录缓存 ({{ modifyRecords.length }} 条):</h5>
          <el-table :data="modifyRecords" border size="small" style="margin-bottom: 10px;">
            <el-table-column prop="oid" label="ID" width="100" />
            <el-table-column prop="depoCode" label="存管代码" width="120" />
            <el-table-column prop="depoDesc" label="存管描述" width="150" />
            <el-table-column prop="mkckAction" label="操作类型" width="80">
              <template #default="scope">
                <el-tag :type="getActionTagType(scope.row.mkckAction)">
                  {{ getActionText(scope.row.mkckAction) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="recordStatus" label="记录状态" width="80">
              <template #default="scope">
                <el-tag :type="getStatusTagType(scope.row.recordStatus)">
                  {{ scope.row.recordStatus }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="状态说明" width="200">
              <template #default="scope">
                <span style="font-size: 12px; color: #666;">
                  {{ getStatusDescription(scope.row) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="sysCreateDate" label="创建时间" width="100">
              <template #default="scope">
                <span style="font-size: 12px;">
                  {{ scope.row.sysCreateDate || '新建' }}
                </span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        
        <!-- 当前数据显示 -->
        <div v-if="currentDataInfo" class="current-data">
          <h5>📊 当前数据统计:</h5>
          <p>总记录数: {{ currentDataInfo.total }}</p>
          <p>新建记录: {{ currentDataInfo.created }}</p>
          <p>更新记录: {{ currentDataInfo.updated }}</p>
          <p>删除记录: {{ currentDataInfo.deleted }}</p>
        </div>
      </div>
    </el-card>

    <!-- 组件测试区域 -->
    <el-card>
      <template #header>
        <span>🏗️ DepositoryEditGrid 组件</span>
      </template>
      
      <!-- 使用说明 -->
      <el-alert
        title="测试说明"
        type="info"
        style="margin-bottom: 15px;"
        :closable="false">
        <p><strong>预置测试数据说明:</strong></p>
        <p>📊 页面已预置4条不同状态的存管机构数据：</p>
        <p>• <strong>NORMAL001</strong>: 正常状态 (mkckAction: 'A', recordStatus: 'A')</p>
        <p>• <strong>NEW001</strong>: 新建状态 (mkckAction: 'C', recordStatus: 'PD')</p>
        <p>• <strong>UPDATE001</strong>: 更新状态 (mkckAction: 'U', recordStatus: 'PD')</p>
        <p>• <strong>DELETE001</strong>: 删除状态 (mkckAction: 'D', recordStatus: 'PD')</p>
        <br>
        <p><strong>功能测试:</strong></p>
        <p>1. 点击不同行查看选中行高亮效果</p>
        <p>2. 点击"获取修改记录"查看不同状态的数据</p>
        <p>3. 点击 ➕ 按钮新增记录，观察新记录的状态</p>
        <p>4. 双击编辑按钮修改记录，观察状态变化</p>
        <p>5. 点击删除按钮，观察删除状态的处理</p>
        <br>
        <p><strong>视觉反馈系统:</strong></p>
        <p>🔵 选中行显示高亮背景色 (复刻EditGrid的selected-row样式)</p>
        <p>🟡 非Active状态记录显示黄色背景 (recordStatus != 'A')</p>
        <p>📋 支持cellStyle属性和highlight()函数的单元格样式</p>
        <p>⚠️ <strong>重要</strong>: 新建记录(mkckAction: 'C')在CreatePage状态下不显示黄色背景</p>
        <p>🎨 点击"测试Highlight行为"按钮查看不同页面状态下的highlight效果</p>
      </el-alert>
      
      <!-- DepositoryEditGrid 组件 -->
      <DepositoryEditGrid 
        v-model="testData"
        :disabled="false"
        :details-ref="detailsRef"
        @data-changed="onDataChanged"
        ref="depositoryEditGridRef" />
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, getCurrentInstance, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import DepositoryEditGrid from './DepositoryEditGrid.vue';
import { currentPageInfo, highlight } from '~/util/Function.js';

const { proxy } = getCurrentInstance();

// 测试数据 - 生成不同状态的数据用于测试
const testData = ref([
  // 1. 正常状态的存管机构 (Active)
  {
    oid: 1001,
    depoCode: 'BNY MELLON',
    depoDesc: '正常状态存管机构',
    priSwiftBicCode: 'NORMALBIC001',
    secSwiftBicCode: 'NORMALBIC002',
    caPayMethodCode: 'A',
    setlPayMethodCode: 'A',
    genDepoIntfInd: 'Y',
    intfFormatCode: 'XML',
    depoIntfAddr: '<EMAIL>',
    settlePeriodCashRecDay: '2',
    settlePeriodCashDeliverDay: '2',
    settlePeriodStockRecDay: '3',
    settlePeriodStockDeliverDay: '3',
    marketDepoSubVPOList: [
      {
        oid: 2001,
        currency: 'USD',
        chargeSuspAcctNo: '1001001',
        incomeSuspAcctNo: '1001002',
        setlSuspAcctNo: '1001003',
        depositoryOid: 1001,
        mkckAction: 'A',
        recordStatus: 'A',
        sysCreateDate: '2024-01-01',
        status: 'A'
      },
      {
        oid: 2002,
        currency: 'EUR',
        chargeSuspAcctNo: '1001004',
        incomeSuspAcctNo: '1001005',
        setlSuspAcctNo: '1001006',
        depositoryOid: 1001,
        mkckAction: 'A',
        recordStatus: 'A',
        sysCreateDate: '2024-01-01',
        status: 'A'
      }
    ],
    mkckAction: 'A',
    recordStatus: 'A',
    sysCreateDate: '2024-01-01',
    finishWrite: true,
    status: 'A',
    level: 1
  },

  // 2. 新建状态的存管机构 (Created)
  {
    oid: 1002,
    depoCode: 'BOCBJ',
    depoDesc: '新建状态存管机构',
    priSwiftBicCode: 'NEWBIC001',
    secSwiftBicCode: 'NEWBIC002',
    caPayMethodCode: 'A',
    setlPayMethodCode: 'A',
    genDepoIntfInd: 'Y',
    intfFormatCode: 'XML',
    depoIntfAddr: '<EMAIL>',
    settlePeriodCashRecDay: '1',
    settlePeriodCashDeliverDay: '1',
    settlePeriodStockRecDay: '2',
    settlePeriodStockDeliverDay: '2',
    marketDepoSubVPOList: [
      {
        oid: 2003,
        currency: 'JPY',
        chargeSuspAcctNo: '1002001',
        incomeSuspAcctNo: '1002002',
        setlSuspAcctNo: '1002003',
        depositoryOid: 1002,
        mkckAction: 'C',
        recordStatus: 'PD',
        sysCreateDate: null,
        status: 'A'
      }
    ],
    mkckAction: 'C',
    recordStatus: 'PD',
    sysCreateDate: null,
    finishWrite: true,
    status: 'A',
    level: 1
  },

  // 3. 更新状态的存管机构 (Updated)
  {
    oid: 1003,
    depoCode: 'CCASSSSE',
    depoDesc: '更新状态存管机构',
    priSwiftBicCode: 'UPDATEBIC001',
    secSwiftBicCode: 'UPDATEBIC002',
    caPayMethodCode: 'B',
    setlPayMethodCode: 'B',
    genDepoIntfInd: 'N',
    intfFormatCode: 'CSV',
    depoIntfAddr: '<EMAIL>',
    settlePeriodCashRecDay: '3',
    settlePeriodCashDeliverDay: '3',
    settlePeriodStockRecDay: '4',
    settlePeriodStockDeliverDay: '4',
    marketDepoSubVPOList: [
      {
        oid: 2004,
        currency: 'GBP',
        chargeSuspAcctNo: '1003001',
        incomeSuspAcctNo: '1003002',
        setlSuspAcctNo: '1003003',
        depositoryOid: 1003,
        mkckAction: 'U',
        recordStatus: 'PD',
        sysCreateDate: '2024-01-01',
        status: 'A'
      }
    ],
    mkckAction: 'U',
    recordStatus: 'PD',
    sysCreateDate: '2024-01-01',
    finishWrite: true,
    status: 'A',
    level: 1
  },

  // 4. 删除状态的存管机构 (Deleted)
  {
    oid: 1004,
    depoCode: 'CCASS2',
    depoDesc: '删除状态存管机构',
    priSwiftBicCode: 'DELETEBIC001',
    secSwiftBicCode: 'DELETEBIC002',
    caPayMethodCode: 'A',
    setlPayMethodCode: 'A',
    genDepoIntfInd: 'Y',
    intfFormatCode: 'XML',
    depoIntfAddr: '<EMAIL>',
    settlePeriodCashRecDay: '2',
    settlePeriodCashDeliverDay: '2',
    settlePeriodStockRecDay: '3',
    settlePeriodStockDeliverDay: '3',
    marketDepoSubVPOList: [
      {
        oid: 2005,
        currency: 'CHF',
        chargeSuspAcctNo: '1004001',
        incomeSuspAcctNo: '1004002',
        setlSuspAcctNo: '1004003',
        depositoryOid: 1004,
        mkckAction: 'D',
        recordStatus: 'PD',
        sysCreateDate: '2024-01-01',
        status: 'A'
      }
    ],
    mkckAction: 'D',
    recordStatus: 'PD',
    sysCreateDate: '2024-01-01',
    finishWrite: true,
    status: 'A',
    level: 1
  }
]);

// 组件引用
const depositoryEditGridRef = ref();
const detailsRef = ref({
  fields: {
    depoCode: '存管代码',
    depoDesc: '存管描述',
    priSwiftBicCode: '主SWIFT BIC代码',
    secSwiftBicCode: '备SWIFT BIC代码',
    currency: '货币',
    chargeSuspAcctNo: '费用暂记账户',
    incomeSuspAcctNo: '收入暂记账户',
    setlSuspAcctNo: '结算暂记账户'
  }
});

// 测试状态
const testMessage = ref('');
const testMessageType = ref<'success' | 'warning' | 'info' | 'error'>('info');
const modifyRecords = ref([]);
const currentDataInfo = ref(null);

// 测试方法
const getModifyRecords = () => {
  if (depositoryEditGridRef.value) {
    const records = depositoryEditGridRef.value.getModifyRecords();
    modifyRecords.value = records;
    
    if (records.length > 0) {
      testMessage.value = `成功获取到 ${records.length} 条修改记录`;
      testMessageType.value = 'success';
    } else {
      testMessage.value = '当前没有修改记录';
      testMessageType.value = 'info';
    }
    
    ElMessage.success(`获取修改记录: ${records.length} 条`);
  }
};

const clearModifyData = () => {
  if (depositoryEditGridRef.value) {
    depositoryEditGridRef.value.clearModifyData();
    modifyRecords.value = [];
    testMessage.value = '修改数据缓存已清空';
    testMessageType.value = 'warning';
    ElMessage.warning('修改数据缓存已清空');
  }
};

const showCurrentData = () => {
  if (depositoryEditGridRef.value) {
    const data = depositoryEditGridRef.value.getDepositoryData();
    const stats = {
      total: data.length,
      created: data.filter((item: any) => item.mkckAction === 'C').length,
      updated: data.filter((item: any) => item.mkckAction === 'U').length,
      deleted: data.filter((item: any) => item.mkckAction === 'D').length
    };

    currentDataInfo.value = stats;
    testMessage.value = `当前数据统计: 总计${stats.total}条, 新建${stats.created}条, 更新${stats.updated}条, 删除${stats.deleted}条`;
    testMessageType.value = 'info';
  }
};

const checkPageStatus = () => {
  const pageStatus = currentPageInfo.getPageStatus();
  const isCreatePage = currentPageInfo.isCreatePage();

  testMessage.value = `页面状态: ${pageStatus}, 是否为创建页面: ${isCreatePage}`;
  testMessageType.value = 'info';

  ElMessage.info(`页面状态: ${pageStatus || '未设置'}, isCreatePage: ${isCreatePage}`);
};

// 测试不同页面状态下的highlight效果
const testHighlightBehavior = () => {
  const testRecord = testData.value.find(r => r.mkckAction === 'C'); // 新建记录

  // 测试CreatePage状态
  currentPageInfo.setCreatePage();
  const createPageResult = highlight({ row: testRecord });
  console.log('CreatePage状态下的highlight结果:', createPageResult);

  // 测试EditPage状态
  currentPageInfo.setEditPage();
  const editPageResult = highlight({ row: testRecord });
  console.log('EditPage状态下的highlight结果:', editPageResult);

  testMessage.value = `Highlight测试完成 - CreatePage: ${JSON.stringify(createPageResult)}, EditPage: ${JSON.stringify(editPageResult)}`;
  testMessageType.value = 'info';

  ElMessage.info('请查看控制台输出，了解不同页面状态下highlight函数的行为');
};

// 数据变化回调
const onDataChanged = (data: any[]) => {
  console.log('数据已变化:', data);
  // 自动更新统计信息
  showCurrentData();
};

// 初始化测试数据到修改缓存
const initializeTestData = () => {
  if (depositoryEditGridRef.value) {
    // 将不同状态的记录添加到修改缓存中
    testData.value.forEach(record => {
      if (record.mkckAction !== 'A') {
        // 只有非正常状态的记录才添加到修改缓存
        depositoryEditGridRef.value.midifyData[record.oid] = { ...record };
      }
    });

    // 自动显示初始统计
    setTimeout(() => {
      getModifyRecords();
      showCurrentData();
    }, 500);
  }
};

// 组件挂载后初始化测试数据
onMounted(() => {
  // 根据Details.vue的逻辑，应该根据数据状态来设置页面状态
  // 对于测试页面，我们模拟编辑现有记录的场景，所以设置为EditPage
  // 注意：新建记录(mkckAction: 'C')在真实场景中应该在CreatePage状态下不显示黄色背景
  currentPageInfo.setEditPage();

  setTimeout(() => {
    initializeTestData();
  }, 1000);
});

// 辅助方法
const getActionTagType = (action: string) => {
  switch (action) {
    case 'C': return 'success';
    case 'U': return 'warning';
    case 'D': return 'danger';
    default: return 'info';
  }
};

const getActionText = (action: string) => {
  switch (action) {
    case 'C': return '新建';
    case 'U': return '更新';
    case 'D': return '删除';
    case 'A': return '正常';
    default: return action;
  }
};

const getStatusDescription = (record: any) => {
  if (record.mkckAction === 'A' && record.recordStatus === 'A') {
    return '正常状态 - 已生效的记录';
  } else if (record.mkckAction === 'C' && record.recordStatus === 'PD') {
    return '新建状态 - 待审核的新记录';
  } else if (record.mkckAction === 'U' && record.recordStatus === 'PD') {
    return '更新状态 - 待审核的修改记录';
  } else if (record.mkckAction === 'D' && record.recordStatus === 'PD') {
    return '删除状态 - 待审核的删除记录';
  }
  return `${record.mkckAction}/${record.recordStatus}`;
};

const getStatusTagType = (status: string) => {
  switch (status) {
    case 'A': return 'success';
    case 'PD': return 'warning';
    default: return 'info';
  }
};
</script>

<style scoped>
.test-container {
  padding: 50px;
  margin: 0 auto;
}

.test-panel {
  background-color: #f8f9fa;
}

.test-results {
  margin-top: 15px;
}

.modify-records, .current-data {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.modify-records h5, .current-data h5 {
  margin: 0 0 10px 0;
  color: #409eff;
}

.current-data p {
  margin: 5px 0;
  font-size: 14px;
}
</style>
