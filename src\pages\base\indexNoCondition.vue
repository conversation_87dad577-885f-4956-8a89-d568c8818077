<template>
  <el-form :validateOnRuleChange="false" ref="formRef" :inline="true" :model="formInline" class="demo-form-inline">
    <slot name="searchPanel" :form="formInline"></slot>
  </el-form>
  <el-row :gutter="24" class="demo-form-inline" style="margin: 0px;padding-block: 5px;">
    <el-col :span="18">
      <div style="width: 100%;display: table;border-bottom: 2px solid #e6e6e6;min-height: 26px;">
        <div style="color: lightgray; font-weight: bold;display: table-cell;width: 60px;align-content: center;"> Order By: </div>
        <el-space
          style="color: lightgray; width: calc(100% - 60px); padding-bottom: 2px;">
          <el-tag v-for="(tag, index) in orderByDesc" :key="tag.code" closable type="info" @close="deleteOrder(tag)">
            {{ tag.name + " " + tag.order }}
          </el-tag> </el-space>
      </div>
    </el-col>
  </el-row>
  <el-form>
  <!-- <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[15, 25, 50, 100]"
    layout="sizes, , jumper, prev, pager, next, ->, slot" v-model:total="total" @current-change="handleChange" @size-change="handleChange"
    style="background-color: lightgrey;padding-inline: 10px;" >
    Total {{ total }} records
  </el-pagination> -->
  </el-form>
  <el-table border @selection-change="handleSelectionChange" :data="tableData" table-layout="fixed" @row-dblclick="handleDbClick" @sort-change="handleSort" ref="tableRef"
    @row-click="clickRow" :row-class-name="gridRowClassName" class-name="multiple-table" @select-all="selectAll"
    :header-cell-class-name="(params: any) => { setHeaderClass(params) }" class="no-page-table" style="overflow: auto; height: 700px;">
    <el-table-column type="index" class-name="data-grid-selection-index-cell" width="1px" />
    <slot name="tableColumn"></slot>
    <el-table-column v-if="!props.hideOperation" fixed="right" :label="$t('common.table.operation')" width="120">
      <template #default="scope">
        <el-button :icon="Edit" link type="primary" style="font-size: 16px;" @click="handleEdit(scope.row)" />
        <el-button :icon="Delete" link type="primary" style="font-size: 16px;" @click="handleDelete(scope.row)" />
      </template>
    </el-table-column>
    <el-table-column v-if="props.isMultiple" type="selection" width="55" :selectable="props.selectable">
      <template #default="scope">
        <label v-if="!props.selectable||props.selectable(scope.row)" class="ep-checkbox">
          <span class="ep-checkbox__input">
            <span class="multiple-checkbox__inner">
              <Select class="multiple-checked" />
            </span>
          </span>
        </label>
      </template>
    </el-table-column>
  </el-table>
  <el-form>
  <!-- <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[15, 25, 50, 100]"
    layout="sizes, |, jumper, prev, pager, next, ->, slot" v-model:total="total" @current-change="handleChange" @size-change="handleChange"
    style="background-color: lightgrey;padding-inline: 10px;" >
    Total {{ total }} records
  </el-pagination> -->
  </el-form>
</template>

<script lang="ts" setup>
import { ref, reactive, getCurrentInstance } from 'vue'
import type { FormInstance } from 'element-plus'
import { ElMessageBox } from 'element-plus';
import {
  Edit,
  Delete,
  Plus,
  Select
} from '@element-plus/icons-vue'
import { useRouter } from 'vue-router';
import { useCookies } from "vue3-cookies";
import { selectedRow, clearSelectedCache, selectedAllRows } from '~/util/Function.js';

const router = useRouter()
const { cookies } = useCookies()
const { proxy } = getCurrentInstance()
const formRef = ref<FormInstance>()
const props = defineProps(['url', 'params', 'searchParams', 'hideOperation', 'showDetails', 'editRow', 'deleteRow', 'sortProp','handleSelectionChange', 'isMultiple', 'selectable']);
const currentPage = ref(1);
const pageSize = ref(9999999);
const total = ref(0);

const tableRef = ref();
const tableData = ref([]);
const param = ref({});
const orderBy = ref("");
const orderByDesc = ref([]);
const sortField = ref({});
const sortFieldName = ref({});

const formInline = reactive(props.searchParams || {});

const baseLoad = async () => {
  const msg = await proxy.$axios.post(props.url, {
    param: {
      ...param.value,
      ...props.params,
    },
    current: currentPage.value,
    pageSize: 9999999,
    orderBy: orderBy.value,
  });
  if (msg.success) {
    selectAll([]);
    total.value = msg.data.total;
    currentPage.value = msg.data.page;
    tableData.value = msg.data.data;
  }
}

const load = async () => {
  baseLoad();
}

const resetTable = () => {
  total.value = 0;
  currentPage.value = 1;
  tableData.value = [];
  return;
}

const onSearch = () => {
  param.value = formInline;
  load();
}
const onReset = (ref) => {
  orderBy.value = "";
  orderByDesc.value = [];
  sortField.value = {};
  sortFieldName.value = {};
  if (ref) {
    ref.resetFields();
    tableRef.value.clearSort();
  }
}
const handleEdit = (row) => {
  props.showDetails(row);
}
const handleDelete = (row) => {
  ElMessageBox.confirm('Are you sure to delete this record?')
    .then(() => {
      props.deleteRow(row);
    })
    .catch(() => {
      // catch error
    });
}
const handleChange = () => {
  load();
}
const handleSort = (obj) => {
  let sts = {
    ...sortField.value
  };
  let stsName = {
    ...sortFieldName.value
  };
  if (sts[obj.prop] && !obj.order) {
    delete sts[obj.prop];
    delete stsName[obj.prop];
  } else {
    sts[obj.prop] = obj.order;
    stsName[obj.prop] = obj.column.label;
  }
  sortField.value = sts;
  sortFieldName.value = stsName;
  changeSort(sts, stsName);
  load();
}
const hadnleAdd = () => {
  props.showDetails({});
}
const handleDbClick = (row) => {
  props.showDetails(row, true);
}

const setHeaderClass = (params: any) => {
  params.column.order = sortField.value[params.column.property];
}

const changeSort = (sts, stsName) => {
  if (Object.keys(sts).length == 0) {
    orderBy.value = "";
    orderByDesc.value = [];
  } else {
    let obv = "";
    let obvNames = [];
    for (let key in sts) {
      obvNames.push({
        name: stsName[key],
        order: sts[key],
        code: key
      });
      if (props.sortProp && props.sortProp[key]) {
        let o = sts[key].charAt(0).toUpperCase();
        let d = "";
        for (let i = 0; i < props.sortProp[key].length; i++) {
          let ele = props.sortProp[key][i];
          d += ";" + ele + "-" + o;
        }
        obv += ";" + d.substring(1);
      } else {
        obv += ";" + key + "-" + sts[key].charAt(0).toUpperCase();
      }
    }
    orderBy.value = obv.substring(1);
    orderByDesc.value = obvNames;
  }
}

const deleteOrder = (tag) => {
  let sts = {
    ...sortField.value
  };
  let stsName = {
    ...sortFieldName.value
  };
  delete sts[tag.code];
  delete stsName[tag.code];
  sortField.value = sts;
  sortFieldName.value = stsName;
  changeSort(sts, stsName);
  console.log(sortField.value);
  load();
}

const tableSelectRowsData  = ref([]);
const handleSelectionChange = (rows) => {
  tableSelectRowsData.value = rows;
}
function getRowsData(){
  return tableSelectRowsData;
}

const getTableData = () => {
  return tableData.value;
}

const setTableData = (data) => {
  tableData.value = data;
  return tableData.value;
}


const selectedRecord = ref({});
const lastSelected = ref(0);


const gridRowClassName = ({
  row,
  rowIndex,
}) => {
    let oid = row["oid"];
  if (selectedRecord.value[oid]) {
    return 'selected-row'
  }
  return ''
}

const clickRow = (row: any, column: any, event: Event) => {
    selectedRow("oid", selectedRecord, tableRef, lastSelected, row, column, event, props.selectable, props.isMultiple);
}

const selectAll = (selection) => {
  if(selection&&selection.length>0) {
    selectedAllRows("oid", selection, selectedRecord, tableRef, lastSelected, props.selectable);
  } else {
    clearSelectedCache(selectedRecord, tableRef, lastSelected);
  }
  
}

const setPageSize = (num:number) => {
  pageSize.value = num;
}

defineExpose({
  load,
  resetTable,
  getRowsData,
  getTableData,
  setTableData,
  setPageSize,
})
</script>

<style>
.demo-form-inline .el-input {
  --el-input-width: 220px;
}

.demo-form-inline {
}

.demo-form-inline .el-select {
  --el-select-width: 220px;
}

/* 确保滚动条不会在所有操作系统上显示，只在需要时显示 */
.no-page-table .el-table__body-wrapper::-webkit-scrollbar {
  display: none;
}

</style>