/**
 * 格式化：custodyAccountNumber 
 * 输出格式为：xxx-xxx-xx-xxxxxx-x
 * 
 * @param {*} custodyAccountNumber 
 * @returns 
 */
export const formatCustodyAccountNumber = (custodyAccountNumber) => {
    let str = "";
    if(custodyAccountNumber && custodyAccountNumber.length !== 0){
        let checkFlag = custodyAccountNumber.includes('-');
        if(checkFlag){
            return custodyAccountNumber;
        }
        if(isNumber(custodyAccountNumber) && !checkFlag 
            && custodyAccountNumber.length > 14
            && isExist(custodyAccountNumber.substring(0,3))){
            return insertAtMultiplePositions(custodyAccountNumber, '-', [3, 6, 8, 14]);
        }
        return custodyAccountNumber;
    }
    return str;
}

/**
 * 反格式化：custodyAccountNumber 
 * 输出格式为：xxxxxxxxxxxxxxx
 * 
 * @param {*} custodyAccountNumber 
 * @returns 
 */
export const parseCustodyAccountNumber = (val) => {
    return  (val || "").replaceAll('-', '');
}

/**
 * 格式化：cashAccountNumber
 * 输出格式为：xxx-xxx-xxxxxxxxxxxx
 * 
 * @param {*} cashAccountNumber 
 * @returns 
 */
export const formatCashAccountNumber = (cashAccountNumber) => {
    let str = "";
    if(cashAccountNumber && cashAccountNumber.length !== 0){
        let checkFlag = cashAccountNumber.includes("-");
        if(checkFlag){
            return cashAccountNumber;
        }
        if(isNumber(cashAccountNumber) && !checkFlag 
            && cashAccountNumber.length > 6
            && isExist(cashAccountNumber.substring(0,3))){
            return insertAtMultiplePositions(cashAccountNumber, '-', [3, 6]);
        }
        return cashAccountNumber;
    }
    return str;
}

function isExist(val){
    let arrHeards = ['012','039','043','243'];
    return arrHeards.includes(val);
}

function isNumber(str) {
    return /^\d+(\.\d+)?$/.test(str); // 检查字符串是否全数字
}

function insertAtMultiplePositions(str, char, positions) {
    let result = str.slice(0, 1); // 取出字符串的第一个字符
    positions.sort((a, b) => a - b); // 确保位置数组是排序的
    for (let i = 1, j = 0; i < str.length; i++) {
        if (j < positions.length && positions[j] === i) {
        result += char + str.slice(i, i + 1); // 在指定位置插入字符
        j++; // 移动到下一个位置
        } else {
        result += str.slice(i, i + 1); // 不需要插入，直接连接字符
        }
    }
    return result;
}
