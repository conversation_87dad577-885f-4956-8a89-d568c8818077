<template>
  <BasePanel :searchParams="searchParams" :paramListData='paramListData' url="/bff/ca/api/v1/ca-online-cash-settlement/get-ca-online-cash-settlement-page-list"
             :params="{ modeEdit: 'Y' }"  ref="tableRef" :sortProp="{}" :isHideAdd="true" :hide-operation=true :show-details="showDetails"
             :isMultiple="true">
    <template v-slot:searchPanel="slotProps">
      <FormRow>
        <ElFormItemProxy label-width="150" :label="$t('csscl.ca.common.eventRefNo')" prop="caEventReferenceNumber" >
          <el-input v-model="slotProps.form.caEventReferenceNumber" input-style="text-transform:none" />
        </ElFormItemProxy>
        <ElFormItemProxy label-width="150" :label="$t('csscl.ca.common.eventCategoryCode')" prop="eventCategory">
          <SearchInput style="width: 335px" v-model="slotProps.form.caEventCategory"
                       url="/datamgmt/api/v1/searchinput" showDesc="false"
                       :title="$t('csscl.ca.common.eventCategory')"
                       :params="{searchType: 'caEventCategoryCode'}"
                       :columns="[
                          {
                              title: $t('csscl.ca.common.eventCategoryCode'),
                              colName: 'code',
                          },
                          {
                              title: $t('csscl.ca.common.eventTypeDescription'),
                              colName: 'codeDesc',
                          }
                       ]"
                       :pageSizes="[10, 20, 30]">
          </SearchInput>
        </ElFormItemProxy>
        <ElFormItemProxy label-width="150" :label="$t('csscl.ca.common.recordStatus')" prop="recordStatusList">
          <MultipleSelect v-model="slotProps.form.recordStatus"  />
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy label-width="150" :label="$t('csscl.ca.common.recordDate')" prop="recordDate" >
          <DateItem :validate-event="false" v-model="slotProps.form.recordDate" type="date" style="width: 190px;" />
        </ElFormItemProxy>
        <ElFormItemProxy label-width="150" :label="$t('csscl.ca.common.securityId')" prop="instrumentCode">
          <SearchInput style="width: 300px" v-model="slotProps.form.instrumentCode"
                       url="/datamgmt/api/v1/searchinput" showDesc="true"
                       :title="$t('csscl.ca.common.instrument')"
                       :params="{searchType: 'instrumentCode'}"
                       :columns="[
                          {
                              title: $t('csscl.ca.common.instrumentCode'),
                              colName: 'code',
                          },
                          {
                              title: $t('csscl.ca.common.instrumentDescription'),
                              colName: 'codeDesc',
                          }
                       ]"
                       :pageSizes="[10, 20, 30]">
          </SearchInput>
        </ElFormItemProxy>
        <ElFormItemProxy label-width="150" :label="$t('csscl.ca.common.swiftEventCode')" prop="swiftEventType">
          <SearchInput style="width: 210px" v-model="slotProps.form.swiftEventCode"
                       url="/datamgmt/api/v1/searchinput" showDesc="false"
                       :title="$t('csscl.ca.common.swiftEventType')"
                       :params="{searchType: 'swiftEventCode'}"
                       :columns="[
                          {
                              title: $t('csscl.ca.common.eventTypeCode'),
                              colName: 'code',
                          },
                          {
                              title: $t('csscl.ca.common.eventTypeDescription'),
                              colName: 'codeDesc',
                          }
                       ]"
                       :pageSizes="[10, 20, 30]">
          </SearchInput>
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy label-width="150" :label="$t('csscl.ca.common.subCustodianClearingAgentCode')" prop="clearingAgent">
          <SearchInput style="width: 200px" v-model="slotProps.form.clearingAgentCode"
                       url="/datamgmt/api/v1/searchinput" showDesc="false"
                       :title="$t('csscl.ca.common.subCustodianClearingAgentCode')"
                       :params="{searchType: 'clearingAgentCode'}"
                       :columns="[
                          {
                              title: $t('csscl.ca.common.clearingAgentCode'),
                              colName: 'code',
                          },
                          {
                              title: $t('csscl.ca.common.clearingAgentName'),
                              colName: 'codeDesc',
                          }
                       ]"
                       :pageSizes="[10, 20, 30]">
          </SearchInput>
        </ElFormItemProxy>
        <ElFormItemProxy label-width="150" :label="$t('csscl.ca.common.custodyAccountNo')" prop="custodyAccountNo">
          <SearchInput style="width: 335px" v-model="slotProps.form.tradingAccountCode"
                       url="/datamgmt/api/v1/searchinput" showDesc="false"
                       :title="$t('csscl.acctCode.custodyAcctNumber')"
                       :params="{searchType: 'custodyAcct'}"
                       :columns="[
                          {
                              title: $t('csscl.acctCode.custodyAcctNumber'),
                              colName: 'code',
                          },
                          {
                              title: $t('csscl.acctCode.accountShortName'),
                              colName: 'codeDesc',
                          }
                       ]"
                       :pageSizes="[10, 20, 30]">
          </SearchInput>
        </ElFormItemProxy>
        <ElFormItemProxy label-width="150" :label="$t('csscl.ca.common.accountType')" prop="custodianAccountNumber">
          <Select v-model="slotProps.form.accountType" type="ACCOUNT_TYPE" style="width: 180px;"/>
        </ElFormItemProxy>
      </FormRow>
    </template>
    <template v-slot:tableHeaderTitle>
      <div style="margin: 0px; padding: 3px; border-bottom: 2px solid #FFAB2D;">
        <el-text style="color: #FFAB2D; font: 14px bold;">
          CA Online Cash Settlement
        </el-text>
      </div>
    </template>
    <template v-slot:tableColumn>
      <el-table-column align="left" header-align="center" sortable="custom" prop="paymentGenerateChannel"
                       :label="$t('csscl.ca.common.function')" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="caEventReferenceNumber"
                       :label="$t('csscl.ca.common.eventRefNo')" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="swiftEventCode"
                       :label="$t('csscl.ca.common.swiftEventCode')" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="instrumentCode"
                       :label="$t('csscl.ca.common.securityId')" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="recordDate"
                       :label="$t('csscl.ca.common.recordDate')" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="eventValueDate"
                       :label="$t('csscl.ca.common.valueDate')" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="clearingAgentCode"
                       :label="$t('csscl.ca.common.subCustodianClearingAgentCode')" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="tradingAccountCode"
                       :label="$t('csscl.ca.common.custodyAccountNo')" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="custodianAccountNumber"
                       :label="$t('csscl.ca.common.custodianAccount')" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="selectedEntitlementQuantity"
                       :label="$t('csscl.ca.common.entitledQuantity')" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="cashPaymentRemark"
                       :label="$t('csscl.ca.common.txRemarks')" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="cashPaymentDrCrIndDesc"
                       :label="$t('csscl.ca.common.drcrAction')" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="cashPaymentCurrencyCode"
                       :label="$t('csscl.ca.common.currency')" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="cashPaymentAmount"
                       :label="$t('csscl.ca.common.amount')" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="cashPaymentAccountType"
                       :label="$t('csscl.ca.common.accountType')" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="cashPaymentErrorMessage"
                       :label="$t('csscl.ca.common.failedReason')" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="cashPaymentDate"
                       :label="$t('csscl.ca.common.lastSendDate')" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="recordStatus"
                       :label="$t('csscl.ca.common.recordStatus')" />
    </template>
    <template v-slot:contentBottom>
      <br />
      <br />
      <el-space>
        <el-button type="primary" @click="">
          {{ $t('csscl.ca.common.retry') }}
        </el-button>
      </el-space>
      <el-space>
        <el-button style="margin-left: 15px;" type="primary" @click="">
          {{ $t('csscl.ca.common.retryTimeout') }}
        </el-button>
      </el-space>
      <el-space>
        <el-button style="margin-left: 15px;" type="primary" @click="">
          {{ $t('csscl.ca.common.retryValueToday') }}
        </el-button>
      </el-space>
      <el-space>
        <el-button color="darkblue" :dark="isDark" style="margin-left: 15px;" @click="">
          {{ $t('csscl.ca.common.debit') }}
        </el-button>
      </el-space>
      <el-space>
        <el-button color="darkblue" :dark="isDark" style="margin-left: 15px;" @click="">
          {{ $t('csscl.ca.common.debitWithOD') }}
        </el-button>
      </el-space>
      <el-space>
        <el-button color="darkblue" :dark="isDark" style="margin-left: 15px;" @click="">
          {{ $t('csscl.ca.common.credit') }}
        </el-button>
      </el-space>
      <el-space>
        <el-button style="margin-left: 15px;" type="success" @click="">
          {{ $t('csscl.ca.common.markGoodFund') }}
        </el-button>
      </el-space>
    </template>
  </BasePanel>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance, watch } from 'vue';
import { Search } from '@element-plus/icons-vue'
import BasePanel from '~/pages/base/CaIndex.vue'
import FormItemSign from "~/pages/base/FormItemSign.vue";
import {isDark} from "~/composables";


const { proxy } = getCurrentInstance()
const paramListData = {};
const searchParams = ref({
  caEventReferenceNumber: '',
  caEventCategory: '',
  swiftEventCode: '',
  recordDate: '',
  instrumentCode: '',
  paymentStatus: '',
  clearingAgentCode: '',
  tradingAccountCode: '',
  custodianAccountNumber: ''

});

const tableRef = ref();
const reload = () => {
  tableRef.value.load();
}
const showDetails = async (row) => {

}

defineExpose({
  showDetails
});
</script>

<style></style>