<template> 
    <div>
        <el-form :validateOnRuleChange="false" :disabled="disabled" :rules="rules" :model="ruleForm.form" ref="generalFormRef">
        <FormRow>
            <FormItemSign :detailsRef="details" label-width="150" :label="$t('csscl.acctCode.clientGroupCode')" prop="clientGroupCode">
                <CommonSearchInput v-model="ruleForm.form.clientGroupCode"
                    commType="CLIENT_GROUP" 
                    style="width:350px"
                    maxlength="50"
                    codeTitle="csscl.acctCode.clientGroupCode"
                    showDesc="false" />
                </FormItemSign>
            <ElFormItemProxy></ElFormItemProxy>
            <FormItemSign :detailsRef="details" label-width="140" :label="$t('csscl.acctCode.datetimeOpen')" prop="dateOpen">
                <DateItem v-model="ruleForm.form.dateOpen"  disabled />
            </FormItemSign>
        </FormRow>
        <FormRow>
            <FormItemSign :detailsRef="details" label-width="150" :label="$t('csscl.acctCode.fundMgrCode')" prop="fundMgrCode">
                <CommonSearchInput v-model="ruleForm.form.fundMgrCode" 
                    commType="FUND_MANAGER"
                    style="width:350px"
                    maxlength="50"
                    codeTitle="csscl.acctCode.fundMgrCode"
                    showDesc="false" />
            </FormItemSign>
            <FormItemSign :detailsRef="details" label-width="200" :label="$t('csscl.acctCode.ctryRegionCode')" prop="proxyCtryCode">
                <CtryRegionSearchInput v-model="ruleForm.form.proxyCtryCode" style="width:100px" showDesc="false" />
            </FormItemSign>
            <FormItemSign  :detailsRef="details" label-width="140" :label="$t('csscl.acctCode.datetimeClose')" prop="dateClose">
                <DateItem v-model="ruleForm.form.dateClose" disabled />
            </FormItemSign>
        </FormRow>
        <FormRow>
            <FormItemSign :detailsRef="details" label-width="150" :label="$t('csscl.acctCode.clientFundId')" prop="clientFundId">
                <el-input v-model="ruleForm.form.clientFundId" maxlength="50" />
            </FormItemSign>
            <FormItemSign :detailsRef="details" label-width="200" :label="$t('common.title.opCtryRegionCode')" prop="opCtryRegionCode">
                <CtryRegionSearchInput v-model="ruleForm.form.opCtryRegionCode" 
                    showDesc="false" 
                    disabled
                    opCtryRegion />
            </FormItemSign>
            <!-- Start BAU-56 LiShaoyi 2025/04/15 -->
            <FormItemSign :detailsRef="details" label-width="140" :label="$t('csscl.acctCode.creationDate')" prop="sysCreateDate">
                <el-input v-model="ruleForm.form.sysCreateDate" :formatter="dateFormat" disabled />
            </FormItemSign>
            <!-- End BAU-56 LiShaoyi 2025/04/15 -->
        </FormRow>
        <FormRow>
            <FormItemSign :detailsRef="details" label-width="150" :label="$t('csscl.acctCode.clientTypeOid')" prop="clientType">
                <Select v-model="ruleForm.form.clientType" style="width:220px" type="CLIENT_TYPE_CODE" disabled />
            </FormItemSign>
            <FormItemSign :detailsRef="details" label-width="200" :label="$t('csscl.acctCode.accountNature')" prop="accountNature">
                <Select v-model="ruleForm.form.accountNature" style="width:220px" type="ACCOUNT_NATURE_CODE" disabled />
            </FormItemSign>
            <FormItemSign :detailsRef="details" label-width="140" :label="$t('csscl.acctCode.busRegNo')" prop="busRegNo"><el-input v-model="ruleForm.form.busRegNo" disabled /></FormItemSign>
        </FormRow>
        <FormRow>
            <FormItemSign :detailsRef="details" label-width="150" :label="$t('csscl.acctCode.valueAddService')" prop="valueAddService">
                <Select v-model="ruleForm.form.valueAddService" type="COM_YN" disabled />
            </FormItemSign>
            <FormItemSign :detailsRef="details" label-width="200" :label="$t('csscl.acctCode.servicePlan')" prop="servicePlan">
                <CommonSearchInput v-model="ruleForm.form.servicePlan"
                    commType="SERVICE_PLAN_CODE"
                    showDesc="false"
                    disabled
                    codeTitle="csscl.acctCode.servicePlan" />
            </FormItemSign>
            <FormItemSign :detailsRef="details" label-width="140" :label="$t('csscl.acctCode.aeCode')" prop="aeCode">
                <Select v-model="ruleForm.form.aeCode" vkEnqual type="AE_CODE" disabled />
            </FormItemSign>
        </FormRow>
        <FormRow>
            <FormItemSign :detailsRef="details" label-width="150" :label="$t('csscl.acctCode.productCode')" prop="productCode" style="flex: 2;">
                <CommonSearchInput v-model="ruleForm.form.productCode"
                    commType="PRODUCT_CODE"
                    codeTitle="csscl.acctCode.productCode"
                    inputStyle="width:150px"
                    disabled
                    style="width:850px" />
            </FormItemSign>
            <FormItemSign :detailsRef="details" label-width="140" :label="$t('csscl.acctCode.inheritTypeInd')" prop="sourceSys" >
                <el-input v-model="ruleForm.form.sourceSys" style="width:100px" disabled />
            </FormItemSign>
        </FormRow>
        <FormRow>
            <FormItemSign :detailsRef="details" label-width="150" :label="$t('csscl.acctCode.accountStatus')" prop="accountStatus">
                <Select v-model="ruleForm.form.accountStatus" type="ACCOUNT_STATUS" disabled />
            </FormItemSign>
            <FormItemSign :detailsRef="details" label-width="200" :label="$t('csscl.acctCode.bankCode')" prop="bankCode" style="flex: 2;">
                <CommonSearchInput  v-model="ruleForm.form.bankCode" disabled
                    commType="BANK_CODE"
                    codeTitle="csscl.acctCode.bankCode"
                    style="width:750px"  />
            </FormItemSign>
        </FormRow>
      <!-- Start SET-41 chengchengfu 2025/06/20 -->
        <FormRow>
            <FormItemSign :detailsRef="details" label-width="150" :label="$t('csscl.acctCode.custodyLevel')" prop="custodyLevel">
                <Select v-model="ruleForm.form.custodyLevel" type="CUSTODY_LEVEL" @update:modelValue="changeCustodyLevel" />
            </FormItemSign>
            <FormItemSign :detailsRef="details" label-width="200" :label="$t('csscl.acctCode.custodyMarket')" prop="custodyMarket" style="flex: 2;">
                <CommonSearchInput v-model="ruleForm.form.custodyMarket"
                    commType="CUSTODY_MARKET"
                    url="/datamgmt/api/v1/searchinput"
                    params='{ "searchType": "custodyMarket", "status": null}'
                    codeTitle="csscl.acctCode.custodyMarket"
                    style="width:750px"
                    :disabled="ruleForm.form.custodyLevel === 'GC'"
                    @click="handleSearchInputClick" />
            </FormItemSign>
        </FormRow>
          <FormRow>
            <FormItemSign :detailsRef="details" label-width="150" :label="$t('csscl.acctCode.processingSystem')" prop="processingSystem">
              <Select v-model="ruleForm.form.processingSystem" type="PROCESSING_SYSTEM" :disabled="true"  />
            </FormItemSign>
            <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.omnibusSegregate')" label-width="200" prop="omnibusSegregate">
              <Select v-model="ruleForm.form.omnibusSegregate" type='OMNIBUS_SEGREGATE' style="width: 125px"/>
            </FormItemSign>
            <FormItemSign></FormItemSign>
          </FormRow>
        </el-form>

      <!-- End SET-41 chengchengfu 2025/06/20 -->  

        <!-- csscl.acctCode.cashManagement -->
        <div class="splitLine">
            <span>{{  $t("csscl.acctCode.cashManagement") }}</span>
            <el-divider />
        </div>
        
        <el-form :validateOnRuleChange="false" :disabled="disabled" :rules="rules" :model="ruleForm.form" ref="dataFormRef" >
        <FormRow>
            <FormItemSign :detailsRef="details" label-width="180" :label="$t('csscl.acctCode.cashMgmtServiceCode')" prop="cashMgmtServiceInd">
                <Select v-model="ruleForm.form.cashMgmtServiceInd" type="COM_YN" :change="cashMgmtServiceIndChange" />
            </FormItemSign>
            <FormItemSign :detailsRef="details" label-width="140" :label="$t('csscl.acctCode.startDate')" prop="startDate">
                <DateItem v-model="ruleForm.form.startDate" style="width:130px" />
            </FormItemSign>
            <FormItemSign :detailsRef="details" label-width="130" :label="$t('csscl.acctCode.endDate')" prop="endDate">
                <DateItem v-model="ruleForm.form.endDate" style="width:130px" />
            </FormItemSign>
        </FormRow>
        </el-form>
        
        <div class="splitLine">
            <span>{{  $t("csscl.acctCode.dormantAcctControl") }}</span>
            <el-divider />
        </div>

        <FormRow>
            <FormItemSign :detailsRef="details" :label="$t('csscl.acctCode.dormantAccountInd')" prop="dormantAccountInd">
                <Select v-model="ruleForm.form.dormantAccountInd" type="COM_YN" />
            </FormItemSign>
            <FormItemSign :detailsRef="details" label-width="140" :label="$t('csscl.acctCode.lienAccountInd')" prop="lienAccountInd">
                <Select v-model="ruleForm.form.lienAccountInd" type="COM_YN" />
            </FormItemSign>
            <ElFormItemProxy></ElFormItemProxy>
        </FormRow>
        <FormRow>
            <FormItemSign :detailsRef="details" :label="$t('csscl.acctCode.contractRemarks')" prop="contractRemarks">
                <el-input v-model="ruleForm.form.contractRemarks" type="textarea" rows="4" style="width:950px" />
            </FormItemSign>
        </FormRow>
        <FormRow>
            <FormItemSign :detailsRef="details" :label="$t('csscl.acctCode.changedBy')" prop="changedBy"><el-input v-model="ruleForm.form.changedBy" /></FormItemSign>
            <!-- Start BAU-56 LiShaoyi 2025/04/15 -->
            <FormItemSign :detailsRef="details" label-width="140" :label="$t('csscl.acctCode.changeTimestamp')" prop="changeTimestamp"><el-input v-model="ruleForm.form.changeTimestamp" /></FormItemSign> 
            <!-- End BAU-56 LiShaoyi 2025/04/15 -->
            <ElFormItemProxy></ElFormItemProxy>
        </FormRow>
        <FormRow>
            <FormItemSign :detailsRef="details" :label="$t('csscl.acctCode.requesterUser')" prop="requesterUser"><el-input v-model="ruleForm.form.requesterUser" /></FormItemSign>
            <FormItemSign :detailsRef="details" label-width="140" :label="$t('csscl.acctCode.approverUser')" prop="approverUser"><el-input v-model="ruleForm.form.approverUser" /></FormItemSign>
            <ElFormItemProxy></ElFormItemProxy>
        </FormRow>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, getCurrentInstance, onMounted, onUnmounted } from 'vue';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import CtryRegionSearchInput from '~/pages/base/CtryRegionSearchInput.vue';
import CommonSearchInput from '~/pages/base/CommonSearchInput.vue';
import { dateFormat, showErrorMsg } from '~/util/Function.js';
import { commonRules, showValidateMsg } from '~/util/Validators.js';

const props = defineProps([ "ruleForm", "details", "disabled"]);
const { proxy } = getCurrentInstance();
const details = props.details;
const ruleForm = props.ruleForm;
const generalFormRef = ref();
const dataFormRef = ref();

// 方案2：使用事件监听判断请求来源
const requestSource = ref('normal'); // 'normal', 'remarkForm'

// 监听RemarkForm按钮点击事件
const handleRemarkFormClick = (event: Event) => {
    const target = event.target as HTMLElement;
    if (target && target.textContent &&
        (target.textContent.includes('View Original') || target.textContent.includes('View Latest'))) {
        console.log('检测到RemarkForm按钮点击，设置请求来源为remarkForm');
        requestSource.value = 'remarkForm';

        // 设置一个定时器，在短时间后重置来源标记
        setTimeout(() => {
            requestSource.value = 'normal';
        }, 5000); // 5秒后重置
    }
}

// 获取动态参数的方法
const getDynamicParams = () => {
    const baseParams = { "searchType": "custodyMarket", "status": null };

    // 根据来源添加不同的参数
    if (requestSource.value === 'remarkForm') {
        return JSON.stringify({
            ...baseParams,
            "source": "remarkForm",
            "requestId": Date.now() // 添加时间戳确保参数不同
        });
    }

    return JSON.stringify(baseParams);
}

// 监听CommonSearchInput的搜索事件
const handleSearchInputClick = () => {
    // 在CommonSearchInput触发搜索时，检查当前的请求来源
    if (requestSource.value === 'remarkForm') {
        console.log('CommonSearchInput请求来源：RemarkForm');
        // 这里可以添加特殊处理逻辑
        // 比如修改请求参数、显示不同的提示等
    } else {
        console.log('CommonSearchInput请求来源：正常操作');
    }
}

// 组件挂载时添加事件监听
onMounted(() => {
    // 监听整个文档的点击事件
    document.addEventListener('click', handleRemarkFormClick);
})

// 组件卸载时移除事件监听
onUnmounted(() => {
    document.removeEventListener('click', handleRemarkFormClick);
})

const rules = reactive({
    cashMgmtServiceInd: [
        commonRules.selectRequired
    ],
    startDate: [
        commonRules.earlierEquDt(()=>{ return ruleForm.form.endDate }, proxy.$t('csscl.acctCode.endDate'))
    ],
    endDate:[],
    clientFundId: [
        commonRules.name
    ],
    custodyLevel: [
        commonRules.required
    ],
    omnibusSegregate: [
        commonRules.required
    ],
});

const showDetails = () => {
    cashMgmtServiceIndChange(ruleForm.form.cashMgmtServiceInd)
}

const cashMgmtServiceIndChange = (v) => {
    if (v == 'Y') {
        if (rules['startDate'].includes(commonRules.notRequired)) {
            rules['startDate'].pop(commonRules.notRequired);
        }
        if (rules['endDate'].includes(commonRules.notRequired)) {
            rules['endDate'].pop(commonRules.notRequired);
        }
        rules['startDate'].push(commonRules.required);
        rules['endDate'].push(commonRules.required);
    } else {
        if (rules['startDate'].includes(commonRules.required)) {
            rules['startDate'].pop(commonRules.required);
        }
        if (rules['endDate'].includes(commonRules.required)) {
            rules['endDate'].pop(commonRules.required);
        }
        rules['startDate'].push(commonRules.notRequired);
        rules['endDate'].push(commonRules.notRequired);
    }
}

const handleSave = async () => {
    let result = await generalFormRef.value.validate((valid, fields) => {
            if (!valid) {
                showValidateMsg(details, fields);
            }
        });
    let result1 = await dataFormRef.value.validate((valid, fields) => {
            if (!valid) {
                showValidateMsg(details, fields);
            }
        });
    let customValidResult = true;
    if (ruleForm.form.custodyLevel === "GC") {
        if (ruleForm.form.custodyMarket) {
            showErrorMsg("Custody market is only for local custody");
            customValidResult = false;
        }
    } else {
        if (!ruleForm.form.custodyMarket) {
            showErrorMsg("Custody market must be selected for Local Custody accounts");
            customValidResult = false;
        }
    }
    return result && result1 && customValidResult;
}

//Start SET-41 chengchengfu 2025/07/02

const changeCustodyLevel = (newVal: string) => {
    if (newVal === "GC" && ruleForm.form.custodyMarket) {
        ruleForm.form.custodyMarket = "";
    }
}

// End SET-41 chengchengfu 2025/07/02

defineExpose({
    handleSave,
    showDetails,
});

</script>

<style></style>