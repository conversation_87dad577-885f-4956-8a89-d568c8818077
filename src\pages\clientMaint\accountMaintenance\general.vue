<template> 
    <div>
        <el-form :validateOnRuleChange="false" :disabled="disabled" :rules="rules" :model="ruleForm.form" ref="generalFormRef">
        <FormRow>
            <FormItemSign :detailsRef="details" label-width="150" :label="$t('csscl.acctCode.clientGroupCode')" prop="clientGroupCode">
                <CommonSearchInput v-model="ruleForm.form.clientGroupCode"
                    commType="CLIENT_GROUP" 
                    style="width:350px"
                    maxlength="50"
                    codeTitle="csscl.acctCode.clientGroupCode"
                    showDesc="false" />
                </FormItemSign>
            <ElFormItemProxy></ElFormItemProxy>
            <FormItemSign :detailsRef="details" label-width="140" :label="$t('csscl.acctCode.datetimeOpen')" prop="dateOpen">
                <DateItem v-model="ruleForm.form.dateOpen"  disabled />
            </FormItemSign>
        </FormRow>
        <FormRow>
            <FormItemSign :detailsRef="details" label-width="150" :label="$t('csscl.acctCode.fundMgrCode')" prop="fundMgrCode">
                <CommonSearchInput v-model="ruleForm.form.fundMgrCode" 
                    commType="FUND_MANAGER"
                    style="width:350px"
                    maxlength="50"
                    codeTitle="csscl.acctCode.fundMgrCode"
                    showDesc="false" />
            </FormItemSign>
            <FormItemSign :detailsRef="details" label-width="200" :label="$t('csscl.acctCode.ctryRegionCode')" prop="proxyCtryCode">
                <CtryRegionSearchInput v-model="ruleForm.form.proxyCtryCode" style="width:100px" showDesc="false" />
            </FormItemSign>
            <FormItemSign  :detailsRef="details" label-width="140" :label="$t('csscl.acctCode.datetimeClose')" prop="dateClose">
                <DateItem v-model="ruleForm.form.dateClose" disabled />
            </FormItemSign>
        </FormRow>
        <FormRow>
            <FormItemSign :detailsRef="details" label-width="150" :label="$t('csscl.acctCode.clientFundId')" prop="clientFundId">
                <el-input v-model="ruleForm.form.clientFundId" maxlength="50" />
            </FormItemSign>
            <FormItemSign :detailsRef="details" label-width="200" :label="$t('common.title.opCtryRegionCode')" prop="opCtryRegionCode">
                <CtryRegionSearchInput v-model="ruleForm.form.opCtryRegionCode" 
                    showDesc="false" 
                    disabled
                    opCtryRegion />
            </FormItemSign>
            <!-- Start BAU-56 LiShaoyi 2025/04/15 -->
            <FormItemSign :detailsRef="details" label-width="140" :label="$t('csscl.acctCode.creationDate')" prop="sysCreateDate">
                <el-input v-model="ruleForm.form.sysCreateDate" :formatter="dateFormat" disabled />
            </FormItemSign>
            <!-- End BAU-56 LiShaoyi 2025/04/15 -->
        </FormRow>
        <FormRow>
            <FormItemSign :detailsRef="details" label-width="150" :label="$t('csscl.acctCode.clientTypeOid')" prop="clientType">
                <Select v-model="ruleForm.form.clientType" style="width:220px" type="CLIENT_TYPE_CODE" disabled />
            </FormItemSign>
            <FormItemSign :detailsRef="details" label-width="200" :label="$t('csscl.acctCode.accountNature')" prop="accountNature">
                <Select v-model="ruleForm.form.accountNature" style="width:220px" type="ACCOUNT_NATURE_CODE" disabled />
            </FormItemSign>
            <FormItemSign :detailsRef="details" label-width="140" :label="$t('csscl.acctCode.busRegNo')" prop="busRegNo"><el-input v-model="ruleForm.form.busRegNo" disabled /></FormItemSign>
        </FormRow>
        <FormRow>
            <FormItemSign :detailsRef="details" label-width="150" :label="$t('csscl.acctCode.valueAddService')" prop="valueAddService">
                <Select v-model="ruleForm.form.valueAddService" type="COM_YN" disabled />
            </FormItemSign>
            <FormItemSign :detailsRef="details" label-width="200" :label="$t('csscl.acctCode.servicePlan')" prop="servicePlan">
                <CommonSearchInput v-model="ruleForm.form.servicePlan"
                    commType="SERVICE_PLAN_CODE"
                    showDesc="false"
                    disabled
                    codeTitle="csscl.acctCode.servicePlan" />
            </FormItemSign>
            <FormItemSign :detailsRef="details" label-width="140" :label="$t('csscl.acctCode.aeCode')" prop="aeCode">
                <Select v-model="ruleForm.form.aeCode" vkEnqual type="AE_CODE" disabled />
            </FormItemSign>
        </FormRow>
        <FormRow>
            <FormItemSign :detailsRef="details" label-width="150" :label="$t('csscl.acctCode.productCode')" prop="productCode" style="flex: 2;">
                <CommonSearchInput v-model="ruleForm.form.productCode"
                    commType="PRODUCT_CODE"
                    codeTitle="csscl.acctCode.productCode"
                    inputStyle="width:150px"
                    disabled
                    style="width:850px" />
            </FormItemSign>
            <FormItemSign :detailsRef="details" label-width="140" :label="$t('csscl.acctCode.inheritTypeInd')" prop="sourceSys" >
                <el-input v-model="ruleForm.form.sourceSys" style="width:100px" disabled />
            </FormItemSign>
        </FormRow>
        <FormRow>
            <FormItemSign :detailsRef="details" label-width="150" :label="$t('csscl.acctCode.accountStatus')" prop="accountStatus">
                <Select v-model="ruleForm.form.accountStatus" type="ACCOUNT_STATUS" disabled />
            </FormItemSign>
            <FormItemSign :detailsRef="details" label-width="200" :label="$t('csscl.acctCode.bankCode')" prop="bankCode" style="flex: 2;">
                <CommonSearchInput  v-model="ruleForm.form.bankCode" disabled
                    commType="BANK_CODE"
                    codeTitle="csscl.acctCode.bankCode"
                    style="width:750px"  />
            </FormItemSign>
        </FormRow>
      <!-- Start SET-41 chengchengfu 2025/06/20 -->
        <FormRow>
            <FormItemSign :detailsRef="details" label-width="150" :label="$t('csscl.acctCode.custodyLevel')" prop="custodyLevel">
                <Select v-model="ruleForm.form.custodyLevel" type="CUSTODY_LEVEL" @update:modelValue="changeCustodyLevel" />
            </FormItemSign>
            <FormItemSign :detailsRef="details" label-width="200" :label="$t('csscl.acctCode.custodyMarket')" prop="custodyMarket" style="flex: 2;">
                <CommonSearchInput v-model="ruleForm.form.custodyMarket"
                    :commType="getCommType()"
                    url="/datamgmt/api/v1/searchinput"
                    params='{ "searchType": "custodyMarket", "status": null }'
                    codeTitle="csscl.acctCode.custodyMarket"
                    style="width:750px" 
                    :disabled="ruleForm.form.custodyLevel === 'GC'" />
            </FormItemSign>
        </FormRow>
          <FormRow>
            <FormItemSign :detailsRef="details" label-width="150" :label="$t('csscl.acctCode.processingSystem')" prop="processingSystem">
              <Select v-model="ruleForm.form.processingSystem" type="PROCESSING_SYSTEM" :disabled="true"  />
            </FormItemSign>
            <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.omnibusSegregate')" label-width="200" prop="omnibusSegregate">
              <Select v-model="ruleForm.form.omnibusSegregate" type='OMNIBUS_SEGREGATE' style="width: 125px"/>
            </FormItemSign>
            <FormItemSign></FormItemSign>
          </FormRow>
        </el-form>

      <!-- End SET-41 chengchengfu 2025/06/20 -->  

        <!-- csscl.acctCode.cashManagement -->
        <div class="splitLine">
            <span>{{  $t("csscl.acctCode.cashManagement") }}</span>
            <el-divider />
        </div>
        
        <el-form :validateOnRuleChange="false" :disabled="disabled" :rules="rules" :model="ruleForm.form" ref="dataFormRef" >
        <FormRow>
            <FormItemSign :detailsRef="details" label-width="180" :label="$t('csscl.acctCode.cashMgmtServiceCode')" prop="cashMgmtServiceInd">
                <Select v-model="ruleForm.form.cashMgmtServiceInd" type="COM_YN" :change="cashMgmtServiceIndChange" />
            </FormItemSign>
            <FormItemSign :detailsRef="details" label-width="140" :label="$t('csscl.acctCode.startDate')" prop="startDate">
                <DateItem v-model="ruleForm.form.startDate" style="width:130px" />
            </FormItemSign>
            <FormItemSign :detailsRef="details" label-width="130" :label="$t('csscl.acctCode.endDate')" prop="endDate">
                <DateItem v-model="ruleForm.form.endDate" style="width:130px" />
            </FormItemSign>
        </FormRow>
        </el-form>
        
        <div class="splitLine">
            <span>{{  $t("csscl.acctCode.dormantAcctControl") }}</span>
            <el-divider />
        </div>

        <FormRow>
            <FormItemSign :detailsRef="details" :label="$t('csscl.acctCode.dormantAccountInd')" prop="dormantAccountInd">
                <Select v-model="ruleForm.form.dormantAccountInd" type="COM_YN" />
            </FormItemSign>
            <FormItemSign :detailsRef="details" label-width="140" :label="$t('csscl.acctCode.lienAccountInd')" prop="lienAccountInd">
                <Select v-model="ruleForm.form.lienAccountInd" type="COM_YN" />
            </FormItemSign>
            <ElFormItemProxy></ElFormItemProxy>
        </FormRow>
        <FormRow>
            <FormItemSign :detailsRef="details" :label="$t('csscl.acctCode.contractRemarks')" prop="contractRemarks">
                <el-input v-model="ruleForm.form.contractRemarks" type="textarea" rows="4" style="width:950px" />
            </FormItemSign>
        </FormRow>
        <FormRow>
            <FormItemSign :detailsRef="details" :label="$t('csscl.acctCode.changedBy')" prop="changedBy"><el-input v-model="ruleForm.form.changedBy" /></FormItemSign>
            <!-- Start BAU-56 LiShaoyi 2025/04/15 -->
            <FormItemSign :detailsRef="details" label-width="140" :label="$t('csscl.acctCode.changeTimestamp')" prop="changeTimestamp"><el-input v-model="ruleForm.form.changeTimestamp" /></FormItemSign> 
            <!-- End BAU-56 LiShaoyi 2025/04/15 -->
            <ElFormItemProxy></ElFormItemProxy>
        </FormRow>
        <FormRow>
            <FormItemSign :detailsRef="details" :label="$t('csscl.acctCode.requesterUser')" prop="requesterUser"><el-input v-model="ruleForm.form.requesterUser" /></FormItemSign>
            <FormItemSign :detailsRef="details" label-width="140" :label="$t('csscl.acctCode.approverUser')" prop="approverUser"><el-input v-model="ruleForm.form.approverUser" /></FormItemSign>
            <ElFormItemProxy></ElFormItemProxy>
        </FormRow>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, getCurrentInstance } from 'vue';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import CtryRegionSearchInput from '~/pages/base/CtryRegionSearchInput.vue';
import CommonSearchInput from '~/pages/base/CommonSearchInput.vue';
import { dateFormat, showErrorMsg } from '~/util/Function.js';
import { commonRules, showValidateMsg } from '~/util/Validators.js';
import { debug } from 'console';

const props = defineProps([ "ruleForm", "details", "disabled"]);
const { proxy } = getCurrentInstance();
const details = props.details;
const ruleForm = props.ruleForm;
const generalFormRef = ref();
const dataFormRef = ref();

const isViewOriginal = ref(false);

const rules = reactive({
    cashMgmtServiceInd: [
        commonRules.selectRequired
    ],
    startDate: [
        commonRules.earlierEquDt(()=>{ return ruleForm.form.endDate }, proxy.$t('csscl.acctCode.endDate'))
    ],
    endDate:[],
    clientFundId: [
        commonRules.name
    ],
    custodyLevel: [
        commonRules.required
    ],
    omnibusSegregate: [
        commonRules.required
    ],
});

// 初始化isViewOriginal=false
const showDetails = () => {
    isViewOriginal.value = false;
}

const setIsViewOriginal = () => {
    isViewOriginal.value = true;
}

// 动态参数构造参数
const getCommType = () => {
    if (isViewOriginal.value) {
        return "custodyMarket";
    }
    return "custodyMarketWithOperation";
}

const cashMgmtServiceIndChange = (v) => {
    if (v == 'Y') {
        if (rules['startDate'].includes(commonRules.notRequired)) {
            rules['startDate'].pop(commonRules.notRequired);
        }
        if (rules['endDate'].includes(commonRules.notRequired)) {
            rules['endDate'].pop(commonRules.notRequired);
        }
        rules['startDate'].push(commonRules.required);
        rules['endDate'].push(commonRules.required);
    } else {
        if (rules['startDate'].includes(commonRules.required)) {
            rules['startDate'].pop(commonRules.required);
        }
        if (rules['endDate'].includes(commonRules.required)) {
            rules['endDate'].pop(commonRules.required);
        }
        rules['startDate'].push(commonRules.notRequired);
        rules['endDate'].push(commonRules.notRequired);
    }
}

const handleSave = async () => {
    let result = await generalFormRef.value.validate((valid, fields) => {
            if (!valid) {
                showValidateMsg(details, fields);
            }
        });
    let result1 = await dataFormRef.value.validate((valid, fields) => {
            if (!valid) {
                showValidateMsg(details, fields);
            }
        });
    let customValidResult = true;
    if (ruleForm.form.custodyLevel === "GC") {
        if (ruleForm.form.custodyMarket) {
            showErrorMsg("Custody market is only for local custody");
            customValidResult = false;
        }
    } else {
        if (!ruleForm.form.custodyMarket) {
            showErrorMsg("Custody market must be selected for Local Custody accounts");
            customValidResult = false;
        }
    }
    return result && result1 && customValidResult;
}

//Start SET-41 chengchengfu 2025/07/02

const changeCustodyLevel = (newVal: string) => {
    if (newVal === "GC" && ruleForm.form.custodyMarket) {
        ruleForm.form.custodyMarket = "";
    }
}

// End SET-41 chengchengfu 2025/07/02

defineExpose({
    handleSave,
    showDetails,
    setIsViewOriginal,
});

</script>

<style></style>