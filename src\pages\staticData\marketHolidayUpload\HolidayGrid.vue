<template>
    <div>
        <el-space alignment="flex-start">
            <HolidaySubGrid v-bind="$attrs" ref="gridRef" :loadData="loadData" :onClick="onClick" />
            <el-space style="margin-top: 10px;" direction="vertical">
                <el-button :icon="Plus" @click="addRecord" />
                <el-button :icon="Minus" @click="deleteRecord" />
            </el-space>
        </el-space>
        <el-form :validateOnRuleChange="false" ref="ruleFormRef" :rules="props.rules" :model="props.form" v-show="isShowDetail"
            :disabled="$attrs.disabled || isDeleteRecord">
            <slot></slot>
            <div v-if="!$attrs.disabled && !isDeleteRecord" style="justify-content: right;display: flex;">
                <el-button @click="cancelGrid" class="ep-button-custom">Cancel</el-button>
                <el-button type="primary" @click="saveGrid" class="ep-button-custom">OK</el-button>
            </div>
        </el-form>
    </div>
</template>

<script lang="ts" setup>
import { reactive, ref, getCurrentInstance, onMounted, onUnmounted, toRaw } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessageBox } from 'element-plus'
import HolidaySubGrid from './HolidaySubGrid.vue';
import {
    Plus,
    Minus,
} from '@element-plus/icons-vue'
import { commonRules, showValidateMsg } from '~/util/Validators.js';
import { randomHashCode } from '~/util/Function';

const { proxy } = getCurrentInstance()
const isShowDetail = ref(false);
const props = defineProps(['rules', 'form', 'details', 'params', 'url', 'oid', 'funcMod']);

const ruleFormRef = ref<FormInstance>();
const gridRef = ref();
const showData = ref([]);
const originalOrderBy = ref("");
const midifyData = ref([]);
const isDeleteRecord = ref(false);
const isEditRecord = ref(false);
const pageSize = 9999;

const options = Array.from({ length: 10000 }).map((_, idx) => ({
    value: `${idx + 1}`,
    label: `${idx + 1}`,
}))

const addRecord = () => {
    clearForm();
    isShowDetail.value = true;
    // gridRef.value?.selectAll([]);
    isDeleteRecord.value = false;
    isEditRecord.value = false;
}

const deleteRecord = async () => {
    let n = getIndex();
    let rec = showData.value[n];
    if (rec?.mkckAction == 'C' && rec?.recordStatus == 'PD') {
        showData.value.splice(n, 1);
        if (!rec.sysCreateDate) {
            delete midifyData.value[rec[props.oid]];
        } else {
            midifyData.value[rec[props.oid]] = {
                ...rec,
                mkckAction: 'D',
            };
        }
    } else {
        if (rec?.mkckAction == 'U' && rec?.recordStatus == 'PD') {
            midifyData.value[rec[props.oid]] = {
                ...rec,
                mkckAction: 'D',
            };
            let searchParams = {};
            searchParams[props.oid] = rec[props.oid];
            const msg = await proxy.$axios.post(props.url, {
                param: searchParams,
                current: 1,
                pageSize: 1,
                orderBy: null,
            });
            if (msg?.success && msg.data.data[0]) {
                for (let key in rec) {
                    if (key != props.oid) {
                        rec[key] = msg.data.data[0][key];
                    }
                }
            } else {
                rec.mkckAction = 'D';
                rec.recordStatus = 'PD';
            }
        } else if (rec?.recordStatus == 'A') {
            midifyData.value[rec[props.oid]] = {
                ...rec,
                mkckAction: 'D',
            };
            rec.mkckAction = 'D';
            rec.recordStatus = 'PD';
        }
    }
    cancelGrid();
    loadData();
}

const getIndex = () => {
    let start = (gridRef.value.currentPage - 1) * gridRef.value.pageSize;
    let end = gridRef.value.currentPage * gridRef.value.pageSize;
    if (end > showData.value.length) {
        end = showData.value.length;
    }
    let n;
    for (let i = start; i < end; i++) {
        const ele = showData.value[i];
        if (gridRef.value && gridRef.value.currentRow && ele[props.oid] == gridRef.value.currentRow[props.oid]) {
            n = start + i;
            break;
        }
    }
    return n;
}

const cancelGrid = () => {
    clearForm();
    isShowDetail.value = false;
}

const saveGrid = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    let result = await ruleFormRef.value.validate((valid, fields) => {
        if (valid) {

        } else {
            showValidateMsg(props.details, fields);
        }
    });
    if (result) {
        let rec = {
            ...props.form,
        };
        if(props?.funcMod === "ctryHoliday") {
            let listData = gridRef.value.tableData;
            if(isEditRecord.value && (listData[getIndex()]?.holidayDate == rec?.holidayDate)){
            } else {
                if(listData.some(item => item?.holidayDate === rec?.holidayDate)){
                    ElMessageBox.alert("Holiday has already existed!", 'Warning');
                    return;
                }
            }
        }
        
        if (!rec.sysCreateDate) {
            rec.mkckAction = 'C';
            rec.recordStatus = 'PD';
            rec.sysCreateDate = null;
            if (rec[props.oid]) {
                let n = getIndex();
                showData.value[n] = rec;
            } else {
                rec[props.oid] = Math.trunc(randomHashCode());
                rec.oid = rec[props.oid];
                rec["_sort_"] = showData.value&&showData.value.length>0?showData.value[0]["_sort_"]+1:1;
                showData.value.unshift(rec);
            }
        } else {
            if(rec.checkWay && rec.checkWay === 'batch'){
                rec.mkckAction = 'C';
            } else {
                rec.mkckAction = 'U';
            }
            rec.recordStatus = 'PD';
            let n = getIndex();
            showData.value[n] = rec;
        }
        midifyData.value[rec[props.oid]] = rec;
        loadData();
        cancelGrid();
    }
}

const loadData = async () => {
    if (gridRef.value.currentPage && showData.value) {
        if (gridRef.value.orderBy) {
            if (originalOrderBy.value != gridRef.value.orderBy) {
                let orders = gridRef.value.orderBy.split(";");
                originalOrderBy.value = gridRef.value.orderBy;
                showData.value = showData.value.sort(function (a, b) {
                    for (let i = 0; i < orders.length; i++) {
                        const ele = orders[i];
                        const eles = ele.split("-");
                        if (eles[1] == 'D') {
                            if (a[eles[0]] != b[eles[0]]) {
                                return a[eles[0]] > b[eles[0]] ? -1 : 1;
                            }
                        } else {
                            if (a[eles[0]] != b[eles[0]]) {
                                return a[eles[0]] > b[eles[0]] ? 1 : -1;
                            }
                        }
                    }
                });
            }

        } else {
            showData.value = showData.value.sort(function (a, b) {
                if (a["_sort_"] != b["_sort_"]) {
                    return a["_sort_"] > b["_sort_"] ? -1 : 1;
                }
            });
        }
        let start = (gridRef.value.currentPage - 1) * gridRef.value.pageSize;
        let end = gridRef.value.currentPage * gridRef.value.pageSize;
        if (end > showData.value.length) {
            end = showData.value.length;
        }
        gridRef.value.tableData = showData.value.slice(start, end);
        gridRef.value.total = showData.value.length;
    }
}
const load = async () => {
    let param = {
        ...props.params
    };
    const msg = await proxy.$axios.post(props.url, {
        param: param,
        current: gridRef.value.currentPage,
        pageSize: pageSize,
        orderBy: gridRef.value.orderBy,
    });
    if (msg?.success) {
        let data = [];
        for (let j = 0; j < msg.data.data.length; j++) {
            data.push({
                _sort_: pageSize - j,
                ...msg.data.data[j],
            });
        }
        showData.value = data;
        loadData();
    }
}

onMounted(() => {
    onload();
});

const onload = () => {
    let allParams = false;
    let keys = Object.keys(props.params);
    for (let i = 0; i < keys.length; i++) {
        let e = props.params[keys[i]];
        if (e) {
            allParams = true;
            break;
        }
    }
    if (allParams) {
        if (gridRef.value)
            load();
    } else {
        setTimeout(onload, 200);
    }
}

onUnmounted(() => {
    for (let key in props.params) {
        delete props.params[key];
    }
});

const onClick = (row) => {
    clearForm();
    isShowDetail.value = true;
    isEditRecord.value = true;
    for (let key in row) {
        props.form[key] = row[key];
    }
    if (row.mkckAction && row.mkckAction == 'D') {
        isDeleteRecord.value = true;
    } else {
        isDeleteRecord.value = false;
    }
}

const clearForm = () => {
    for (let key in props.form) {
        props.form[key] = null;
    }
}

const getModifyRecords = () => {
    return Object.values(midifyData.value);
}

const addBatch = (data, comDatas) => {
    const ctryHolidayVPOList = toRaw(showData.value);
    if(ctryHolidayVPOList){
        comDatas = getDifference(data, ctryHolidayVPOList);
    }

    for(let i = 0; i < comDatas.length; i++){
        const rec = comDatas[i];
        if(rec?.ctryHolidayOid && rec?.holidayDate){
            rec.mkckAction = 'D';
            midifyData.value[rec[props.oid]] = rec;
            showData.value = showData.value.filter(function(item){
                return item?.holidayDate !== rec?.holidayDate;
            });
        }
    }
    for (let i = 0; i < data.length; i++) {
        const rec = data[i];
        rec.mkckAction = 'C';
        rec.recordStatus = 'PD';
        rec.checkWay = 'batch';
        rec.sysCreateDate = null;
        rec[props.oid] = Math.trunc(randomHashCode());
        rec["_sort_"] = showData.value&&showData.value.length>0?showData.value[0]["_sort_"]+1:1;
        showData.value.unshift(rec);
        midifyData.value[rec[props.oid]] = rec;
    }
    loadData();
    cancelGrid();
}

function getDifference(list1, list2) {
  const comDatas = list2.reduce((acc, item2) => {
    if (list1.find(item1 => item2?.holidayDate === item1?.holidayDate)) {
      acc.push(item2);
    } 
    return acc;
  }, []);
  return comDatas;
}

const loadDataGrid = () => {
    midifyData.value = [];
    loadData();
    cancelGrid();
    onload();
}

defineExpose({
    getModifyRecords,
    addBatch,
    loadDataGrid,
    load,
})
</script>