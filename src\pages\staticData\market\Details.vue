<template>
  <BaseDetails ref="details" :handleSave="handleSave" :reload="reload" :viewOriginalForm="viewOriginalForm"
    :form="ruleForm">
    <el-form :validateOnRuleChange="false" :disabled="formDisabled" ref="ruleFormRef" style="width: 100%"
      :model="ruleForm.form" :rules="rules" status-icon>
      <FormRow>
        <FormItemSign :detailsRef="details" :label="$t('common.title.opCtryRegionCode')" label-width="210"
          prop="opCtryRegionCode">
          <CtryRegionSearchInput v-model="ruleForm.form.opCtryRegionCode" showDesc="false" opCtryRegion />
        </FormItemSign>
        <FormItemSign :detailsRef="details" :label="$t('common.title.status')" label-width="270" prop="status">
          <Select v-model="ruleForm.form.status" type='STATUS' style="width: 80px" />
        </FormItemSign>
      </FormRow>
      <FormRow>
        <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.market.marketCode')" label-width="210"
          prop="marketCode">
          <el-input v-model="ruleForm.form.marketCode" maxlength="3" style="width: 80px" />
        </FormItemSign>
        <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.marketDesc')" label-width="270"
          prop="marketDesc">
          <InputText v-model="ruleForm.form.marketDesc" maxlength="50"  style="width: 600px" />
        </FormItemSign>
      </FormRow>
      <FormRow>
        <FormItemSign :detailsRef="details" label-width="210" :label="$t('csscl.ctryRegionManagement.ctryRegionCode')"
          prop="ctryRegionCode">
          <CommonSearchInput v-model="ruleForm.form.ctryRegionCode" commType="CUSTODY_MARKET"
            url="/datamgmt/api/v1/searchinput" params='{ "searchType": "ctryRegion", "status": null }'
            codeTitle="csscl.ctryRegionManagement.ctryRegionCode" style="width:550px" />
        </FormItemSign>

        <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.market.operationInd')" label-width="270"
          prop="operationInd">
          <el-checkbox v-model="ruleForm.form.operationInd" true-value="Y" false-value="N" style="width:180px" />
        </FormItemSign>
      </FormRow>

      <FormRow>
        <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.daylightInd')" label-width="210"
          prop="daylightInd">
          <el-checkbox v-model="ruleForm.form.daylightInd" true-value="Y" false-value="N" style="width:180px" />
        </FormItemSign>
        <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.gmtOffset')" label-width="270" prop="gmtOffset">
          <!-- <CurrencySearchInput v-model="ruleForm.form.currencyCode" showDesc="false" /> -->
          <InputNumber v-model="ruleForm.form.gmtOffset" precision="5" scale="2" isNegative="true"
            style="width: 70px" />
        </FormItemSign>
      </FormRow>

      <!--      <FormRow>-->
      <!--        <el-col>-->
      <!--          <div style="width: 700px; border: 1px solid lightgray; padding: 10px 5px 10px 5px">-->
      <!--            <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.cashInstrctnDlDayOffset')" label-width="210" prop="cashInstrctnDlDayOffset">-->
      <!--                <el-space>-->
      <!--                  <InputNumber v-model="ruleForm.form.cashInstrctnDlDayOffset" style="width: 50px;" scale="0" maxlength="2"/>-->
      <!--                  <span style="background-color: lightgray; display: inline-block; padding: 4px 10px;">GMT+Market</span>-->
      <!--                    <el-time-picker v-model="ruleForm.form.cashInstrctnDlTime" format="HH:mm" :disabled="editDis" value-format="HH:mm" style="width: 80px;"></el-time-picker>-->
      <!--                </el-space>-->
      <!--          </FormItemSign>-->
      <!--          <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.tradeInstrctnDlDayOffset')" label-width="210" style="margin-top: 5px;" prop="tradeInstrctnDlDayOffset">-->
      <!--          <el-space>-->
      <!--                  <InputNumber v-model="ruleForm.form.tradeInstrctnDlDayOffset" style="width: 50px;"  scale="0" maxlength="2"/>-->
      <!--                  <span style="background-color: lightgray; display: inline-block; padding: 4px 10px;">GMT+Market</span>-->
      <!--                  <el-time-picker v-model="ruleForm.form.tradeInstrctnDlTime" format="HH:mm" :disabled="editDis" value-format="HH:mm" style="width: 80px;"></el-time-picker>-->
      <!--          </el-space>-->
      <!--          </FormItemSign>-->
      <!--          </div>-->
      <!--        </el-col>-->
      <!--      <el-col>-->
      <!--          <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.dayOfDormant')" label-width="220" prop="dayOfDormant">-->
      <!--          <InputNumber scale="0" v-model="ruleForm.form.dayOfDormant" maxlength="6" :disabled="editDis" />-->
      <!--      </FormItemSign>-->
      <!--      <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.dayOfDormantUnclaim')" label-width="220" prop="dayOfDormantUnclaim" style="margin-top: 5px;">-->
      <!--          <InputNumber scale="0" v-model="ruleForm.form.dayOfDormantUnclaim" maxlength="6" :disabled="editDis" />-->
      <!--      </FormItemSign>-->
      <!--      </el-col>-->
      <!--      </FormRow>-->
      <FormRow>
        <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.startOfOpsOnline')" label-width="210"
          prop="startOfOpsOnline">
          <el-space>
            <span style="background-color: lightgray; display: inline-block; padding: 4px 10px;">GMT+Market</span>
            <el-time-picker v-model="ruleForm.form.startOfOpsOnline" format="HH:mm" value-format="HH:mm"
              style="width: 80px;" @change="timeChange('Online')"></el-time-picker>
          </el-space>
        </FormItemSign>

        <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.retentionTranHis')" label-width="270"
          prop="retentionTranHis">
          <InputNumber v-model="ruleForm.form.retentionTranHis" :disabled="true" scale="0" precision="5"
            style="width: 70px" />
        </FormItemSign>
        <!--      <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.market.primary.bic.code')" label-width="220" prop="primaryBicCode" style="margin-top: 5px;">-->
        <!--        <el-input  v-model="ruleForm.form.primaryBicCode" maxlength="11" :disabled="editDis" />-->
        <!--      </FormItemSign>-->
      </FormRow>
      <FormRow style="height: 25px">
        <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.endOfOpsOnline')" label-width="210"
          prop="endOfOpsOnline">
          <el-space>
            <span style="background-color: lightgray; display: inline-block; padding: 4px 10px;">GMT+Market</span>
            <el-time-picker v-model="ruleForm.form.endOfOpsOnline" format="HH:mm" value-format="HH:mm"
              style="width: 80px;" @change="timeChange('Online')"></el-time-picker>
          </el-space>
        </FormItemSign>
        <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.dayOfDormant')" label-width="270"
          prop="dayOfDormant">
          <InputNumber scale="0" v-model="ruleForm.form.dayOfDormant" precision="5" style="width: 70px" />
        </FormItemSign>
        <!--      <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.market.secondary.bic.code')" label-width="220" prop="secondaryBicCode" style="margin-top: 5px;">-->
        <!--        <el-input v-model="ruleForm.form.secondaryBicCode" maxlength="11" :disabled="editDis" />-->
        <!--      </FormItemSign>-->
      </FormRow>

      <FormRow>
        <FormItemSign>

        </FormItemSign>
        <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.dayOfDormantUnclaim')" label-width="270"
          prop="dayOfDormantUnclaim">
          <InputNumber scale="0" v-model="ruleForm.form.dayOfDormantUnclaim" precision="5" style="width: 70px" />
        </FormItemSign>
      </FormRow>
      <el-row>
        <span style="font-size: 16px; font-weight: bold; color: #3fb2b2; margin-bottom: 5px;">Trade Instruction
          Deadline</span>
      </el-row>
      <el-container>
        <el-aside width="30%">
          <EditGrid v-model="tradeInstructionVpos" oid="marketTradeInstrOid" ref="tradeInstructionRef"
            :form="tradeInstructionForm" :rules="tradeInstructionRules" :details="details" :disabled="formDisabled"
            tableStyle="overflow: auto; height: 200px;">
            <template #columns>
              <el-table-column prop="transactionType" width="150" :label="$t('csscl.si.common.transactionType')" />
              <el-table-column prop="cutoffTime" :label="$t('csscl.si.common.cutoffTime')" />
              <el-table-column prop="recordStatus" :label="$t('common.title.recordStatus')">
                <template #default="scope">
                  {{ getCommonDesc('RECORD_STATUS', scope.row.recordStatus) }}
                  <span v-if="scope.row.recordStatus && scope.row.recordStatus !== 'A'">
                    for {{ getCommonDesc('MKCK_ACTION', scope.row.mkckAction) }}
                  </span>
                </template>
              </el-table-column>
            </template>
            <template #form>
              <FormItemSign :detailsRef="details" label-width="150px" :label="$t('csscl.si.common.transactionType')"
                prop="transactionType">
                <div style="position:relative">
                  <el-input v-model="tradeInstructionForm.transactionType" maxlength="4" style="width: 150px" />
                </div>
              </FormItemSign>
              <FormItemSign :detailsRef="details" label-width="150px" :label="$t('csscl.si.common.cutoffTime')"
                prop="cutoffTime">
                <el-time-picker v-model="tradeInstructionForm.cutoffTime" format="HH:mm" value-format="HH:mm"
                  style="width: 60px;"></el-time-picker>
              </FormItemSign>
            </template>
          </EditGrid>
        </el-aside>
        <el-aside width="45%">
          <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.taxRelief')" label-width="445"
            prop="taxRelief">
            <Select v-model="ruleForm.form.taxRelief" type='TAX_RELIEF' style="margin-left: 190px; width: 125px" />
          </FormItemSign>
          <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.marketSanctioned')" label-width="500"
            prop="marketSanctioned">
            <Select v-model="ruleForm.form.marketSanctioned" type='MARKET_SANCTIONED'
              style="margin-left: 135px; width: 125px" />
          </FormItemSign>
          <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.supportBroadridgeExtraction')"
            label-width="565" prop="supBroadridgeExtraction">
            <Select v-model="ruleForm.form.supBroadridgeExtraction" type='SUP_BROADRIDGE_EXTRACTION'
              style="margin-left: 70px; width: 125px" />
          </FormItemSign>
          <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.omnibusSegregate')" label-width="505"
            prop="omnibusSegregate">
            <Select v-model="ruleForm.form.omnibusSegregate" type='OMNIBUS_SEGREGATE'
              style="margin-left: 130px; width: 125px" />
          </FormItemSign>
        </el-aside>
      </el-container>
      <br>
      <el-row>
        <span style="font-size: 16px; font-weight: bold; color: #3fb2b2; margin-bottom: 5px;">Cash Cutoff Time</span>
      </el-row>

      <el-container>
        <el-aside width="40%">
          <FormRow>
            <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.cutoffType')" label-width="100"
              prop="cutoffType" :disabled="false">
              <Select v-model="cashCutoffTimeFormSearch.cutoffType" type='CUTOFF_TYPE' :disabled="false" />
            </FormItemSign>
            <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.currency')" label-width="100"
              prop="currency">
              <SearchInput v-model="cashCutoffTimeFormSearch.currency" style="width:110px" showDesc="false"
                maxlength="3" onlyLetters searchField url="/datamgmt/api/v1/currency/list"
                :title="$t('csscl.si.common.currency')" :params="{}" :columns="[
                  {
                    title: $t('csscl.currencyManagement.currencyCode'),
                    colName: 'currencyCode',
                  },
                  {
                    title: $t('csscl.currencyManagement.descpt'),
                    colName: 'descpt',
                  }
                ]">
              </SearchInput>
            </FormItemSign>
          </FormRow>
          <div style="text-align: left; margin: 5px 0 5px 0 ">
            <el-button @click="onReset">{{ $t('csscl.common.btn.clear')
            }}</el-button>
            <el-button type="primary" @click="onSearch">{{ $t('csscl.common.btn.search')
            }}</el-button>
          </div>
          <div style="color: lightgray; font-weight: bold;display: table-cell;width: 80px; align-content: center;">
            Order By: </div>
          <div
            style="margin: 0px; margin-bottom: 10px; padding: 3px 0; border-bottom: 2px solid lightgrey; width: 500px">
          </div>
          <EditGrid v-model="cashCutoffTimeVpos" oid="marketCashCutoffOid" ref="cashCutoffTimeRef"
            :form="cashCutoffTimeForm" :rules="cashCutoffTimeRules" :details="details" :disabled="formDisabled" @handle-search="handleSearch"
            tableStyle="overflow: auto; height: 200px;">
            <template #columns>
              <el-table-column prop="cutoffType" width="150" :label="$t('csscl.si.common.cutoffType')" />
              <el-table-column prop="currency" :label="$t('csscl.si.common.currency')" />
              <el-table-column prop="cutoffTime" :label="$t('csscl.si.common.cutoffTime')" />
              <el-table-column prop="recordStatus" :label="$t('common.title.recordStatus')">
                <template #default="scope">
                  {{ getCommonDesc('RECORD_STATUS', scope.row.recordStatus) }}
                  <span v-if="scope.row.recordStatus && scope.row.recordStatus !== 'A'">
                    for {{ getCommonDesc('MKCK_ACTION', scope.row.mkckAction) }}
                  </span>
                </template>
              </el-table-column>
            </template>
            <template #form>
              <FormItemSign :detailsRef="details" label-width="150px" :label="$t('csscl.si.common.cutoffType')"
                prop="cutoffType">
                <div style="position:relative">
                  <Select v-model="cashCutoffTimeForm.cutoffType" type='CUTOFF_TYPE' />
                </div>
              </FormItemSign>
              <FormItemSign :detailsRef="details" label-width="150px" :label="$t('csscl.si.common.currency')"
                prop="currency">
                <div style="position:relative">
                  <SearchInput v-model="cashCutoffTimeForm.currency" maxlength="3" onlyLetters searchField
                    showDesc="false" style="width: 500px" input-style="width:110px" url="/datamgmt/api/v1/currency/list"
                    :title="$t('csscl.currencyManagement.currencyCode')" :params="{ status: '', recordStatus: '' }" readonly="true"
                    :columns="[
                      {
                        title: $t('csscl.currencyManagement.currencyCode'),
                        colName: 'currencyCode',
                      },
                      {
                        title: $t('csscl.currencyManagement.descpt'),
                        colName: 'descpt',
                      }
                    ]" :dbClick="(row) => {
              cashCutoffTimeForm.currencyDesc = row.descpt
            }">
                  </SearchInput>
                </div>
              </FormItemSign>
              <FormItemSign :detailsRef="details" label-width="150px" :label="$t('csscl.si.common.cutoffTime')"
                prop="cutoffTime">
                <el-time-picker v-model="cashCutoffTimeForm.cutoffTime" format="HH:mm" value-format="HH:mm"
                  style="width: 60px;"></el-time-picker>
              </FormItemSign>
            </template>
          </EditGrid>
        </el-aside>
      </el-container>


      <el-row>
        <span style="font-size: 16px; font-weight: bold; color: #3fb2b2;">Settlement</span>
        <div
          style="paddingBlock:5px;width: 100%;display: flex;flex-flow: wrap;border-top: 2px solid; border-color: #9ad7d7;">
        </div>
      </el-row>

      <div style="height: 190px;">
        <FormRow>
          <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.siPayMethod')" label-width="220"
            prop="siPayMethod">
            <Select v-model="ruleForm.form.siPayMethod" type='CASH_SETTLE_METHOD_SI_CODE' style="width: 330px" />
          </FormItemSign>
        </FormRow>
        <FormRow>
          <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.caPayMethod')" label-width="220"
            prop="caPayMethod">
            <Select v-model="ruleForm.form.caPayMethod" type='CASH_SETTLE_METHOD_SI_CODE' style="width: 330px" />
          </FormItemSign>
        </FormRow>
        <FormRow>
          <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.cashStockSettleMethod')" label-width="220"
            prop="cashStockSettleMethod">
            <Select v-model="ruleForm.form.cashStockSettleMethod" type='CASH_STOCK_SETTLE_METHOD_CODE' />
          </FormItemSign>
        </FormRow>
        <!--      <FormRow> -->

        <!--        <FormItemSign :detailsRef="details" prop="rdvpInd">-->
        <!--          <el-checkbox v-model="ruleForm.form.rdvpInd" true-value="Y" false-value="N" style="width:18px">-->
        <!--            {{  $t("csscl.si.common.rdvpInd") }}-->
        <!--          </el-checkbox>-->
        <!--      </FormItemSign>-->
        <!--      </FormRow>-->

        <FormRow>
          <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.contractIncomeStart')" label-width="220"
            prop="contractIncomeStart">
            <!-- <InputText v-model="ruleForm.form.startContractualIncome" maxlength="3" uppercase :disabled="editDis" /> -->
            <el-space>
              <span style="background-color: lightgray; display: inline-block; padding: 4px 10px;">GMT+Market</span>
              <el-time-picker v-model="ruleForm.form.contractIncomeStart" format="HH:mm"
                value-format="HH:mm" style="width: 80px;" @change="timeChange('Income')"></el-time-picker>
            </el-space>
          </FormItemSign>
          <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.contractIncomeNotifyDays')" label-width="300"
            prop="contractIncomeNotifyDays">
            <InputNumber scale="0" v-model="ruleForm.form.contractIncomeNotifyDays" maxlength="2" uppercase />
          </FormItemSign>
        </FormRow>
        <FormRow>
          <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.endContractualIncome')" label-width="220"
            prop="contractIncomeEnd">
            <!-- <InputText v-model="ruleForm.form.endContractualIncome" maxlength="3" uppercase :disabled="editDis" /> -->
            <el-space>
              <span style="background-color: lightgray; display: inline-block; padding: 4px 10px;">GMT+Market</span>
              <el-time-picker v-model="ruleForm.form.contractIncomeEnd" format="HH:mm"
                value-format="HH:mm" style="width: 80px;" @change="timeChange('Income')"></el-time-picker>
            </el-space>
          </FormItemSign>
          <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.contractSetteNotifyDays')" label-width="300"
            prop="contractSetteNotifyDays">
            <InputNumber scale="0" v-model="ruleForm.form.contractSetteNotifyDays" maxlength="2" />
          </FormItemSign>
        </FormRow>
        <FormRow>
          <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.contractSetteStart')" label-width="220"
            prop="contractSetteStart">
            <!-- <InputText v-model="ruleForm.form.startContractualSettlement" maxlength="3" uppercase :disabled="editDis" /> -->
            <el-space>
              <span style="background-color: lightgray; display: inline-block; padding: 4px 10px;">GMT+Market</span>
              <el-time-picker v-model="ruleForm.form.contractSetteStart" format="HH:mm"
                value-format="HH:mm" style="width: 80px;" @change="timeChange('Settlement')"></el-time-picker>
            </el-space>
          </FormItemSign>
          <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.contractIncomeReverseDays')" label-width="300"
            prop="contractIncomeReverseDays">
            <InputNumber scale="0" v-model="ruleForm.form.contractIncomeReverseDays" maxlength="2"
              />
          </FormItemSign>
        </FormRow>

        <FormRow>
          <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.contractSetteEnd')" label-width="220"
            prop="contractSetteEnd">
            <!-- <InputText v-model="ruleForm.form.endContractualSettlement" maxlength="3" uppercase :disabled="editDis" /> -->
            <el-space>
              <span style="background-color: lightgray; display: inline-block; padding: 4px 10px;">GMT+Market</span>
              <el-time-picker v-model="ruleForm.form.contractSetteEnd" format="HH:mm"
                value-format="HH:mm" style="width: 80px;" @change="timeChange('Settlement')"></el-time-picker>
            </el-space>
          </FormItemSign>
          <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.contractSetteReverseDays')" label-width="300"
            prop="contractSetteReverseDays">
            <InputNumber scale="0" v-model="ruleForm.form.contractSetteReverseDays" maxlength="2"  />
          </FormItemSign>
        </FormRow>

        <!--      <FormRow>-->
        <!--        <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.market.settlement.custoff.time')" label-width="220" prop="settlementCutoffTime">-->
        <!--          &lt;!&ndash; <InputText v-model="ruleForm.form.endContractualSettlement" maxlength="3" uppercase :disabled="editDis" /> &ndash;&gt;-->
        <!--          <el-space>-->
        <!--            <span style="background-color: lightgray; display: inline-block; padding: 4px 10px;">GMT+Market</span>-->
        <!--            <el-time-picker v-model="ruleForm.form.settlementCutoffTime" format="HH:mm" :disabled="editDis" value-format="HH:mm" style="width: 80px;"  @change="timeChange('Settlement')"></el-time-picker>-->
        <!--          </el-space>-->
        <!--        </FormItemSign>-->
        <!--      </FormRow>-->
      </div>

      <el-row>
        <span style="font-size: 16px; font-weight: bold; color: #3fb2b2;">Daylight Savings</span>
        <div
          style="paddingBlock:5px;width: 100%;display: flex;flex-flow: wrap;border-top: 2px solid; border-color: #9ad7d7;">
        </div>
      </el-row>

      <el-container>
        <el-aside width="40%">
          <EditGrid v-model="dayNightSavingVpos" oid="marketDaylightOid" ref="dayNightSavingRef" uniqueKey="gmtOffset"
            :form="dayNightForm" :rules="holidayRules" :details="details" :disabled="formDisabled"
            tableStyle="overflow: auto; height: 200px;" :readonly="ruleForm.form.daylightInd != 'Y'">
            <template #columns>
              <el-table-column prop="effectiveDate" width="150" :label="$t('csscl.si.common.effectiveDate')" />
              <el-table-column prop="gmtOffset" :label="$t('csscl.si.common.gmtOffset')" />
              <!--              <el-table-column prop="status" width="220" :label="$t('common.title.status')">-->
              <!--                <template #default="scope">-->
              <!--                  {{ getCommonDesc('STATUS', scope.row.status) }}-->
              <!--                </template>-->
              <!--              </el-table-column>-->
              <el-table-column prop="recordStatus" :label="$t('common.title.recordStatus')">
                <template #default="scope">
                  {{ getCommonDesc('RECORD_STATUS', scope.row.recordStatus) }}
                  <span v-if="scope.row.recordStatus && scope.row.recordStatus !== 'A'">
                    for {{ getCommonDesc('MKCK_ACTION', scope.row.mkckAction) }}
                  </span>
                </template>
              </el-table-column>
            </template>
            <template #form>
              <FormItemSign :detailsRef="details" label-width="150px" :label="$t('csscl.si.common.effectiveDate')"
                prop="effectiveDate">
                <div style="position:relative">
                  <DateItem v-model="dayNightForm.effectiveDate" style="width: 150px" />
                </div>
              </FormItemSign>
              <FormItemSign :detailsRef="details" label-width="150px" :label="$t('csscl.si.common.gmtOffset')"
                prop="gmtOffset">
                <InputNumber v-model="dayNightForm.gmtOffset" style="width: 150px" class="text-none" precision="5"
                  scale="2" isNegative="true" />
              </FormItemSign>
              <!--              <FormItemSign :detailsRef="details" label-width="150px" :label="$t('common.title.status')"-->
              <!--                prop="status">-->
              <!--                &lt;!&ndash; <el-input v-model="dayNightForm.status" maxlength="70" style="width: 450px" class="text-none" /> &ndash;&gt;-->
              <!--                <Select v-model="dayNightForm.status" style="width: 100px" type='MARKET_DAYNIGHT_STATUS' />-->
              <!--              </FormItemSign>-->
            </template>
          </EditGrid>
        </el-aside>
      </el-container>

      <br><br>
<!--      <el-row>-->
<!--        <span style="font-size: 16px; font-weight: bold;">Location/Depository</span>-->
<!--        <div style="paddingBlock:5px;width: 100%;display: flex;flex-flow: wrap;border-top: 1px solid;"></div>-->
<!--      </el-row>-->

<!--      <el-container>-->
<!--        <el-aside width="100%">-->
<!--          <EditGrid v-model="depositoryVpos" oid="marketDepoOid" ref="depositoryRef" uniqueKey="depositoryCode"-->
<!--            :form="depostitoryForm" :rules="depositoryRules" :details="details" :disabled="formDisabled"-->
<!--            tableStyle="overflow: auto; height: 200px;" @add-record="handleAddSubForm" @on-db-click="handleMainDbClick">-->
<!--            <template #columns>-->
<!--              <el-table-column prop="depoCode" width="150" :label="$t('csscl.si.common.depoCode')" />-->
<!--              <el-table-column prop="depoDesc" :label="$t('csscl.si.common.depoDesc')" />-->
<!--              <el-table-column prop="priSwiftBicCode" width="350" :label="$t('csscl.si.common.priSwiftBicCode')">-->
<!--              </el-table-column>-->
<!--              <el-table-column prop="secSwiftBicCode" :label="$t('csscl.si.common.secSwiftBicCode')" />-->
<!--              &lt;!&ndash;              <el-table-column prop="chargeSuspAcctNo" :label="$t('csscl.si.common.chargeSuspAcctNo')"/>&ndash;&gt;-->
<!--              &lt;!&ndash;              <el-table-column prop="incomeSuspAcctNo" :label="$t('csscl.si.common.incomeSuspAcctNo')"/>&ndash;&gt;-->
<!--              &lt;!&ndash;              <el-table-column prop="setlSuspAcctNo" :label="$t('csscl.si.common.setlSuspAcctNo')"/>&ndash;&gt;-->
<!--              <el-table-column prop="status" :label="$t('common.title.status')">-->
<!--                <template #default="scope">-->
<!--                  {{ getCommonDesc('STATUS', scope.row.status) }}-->
<!--                </template>-->
<!--              </el-table-column>-->
<!--              <el-table-column prop="recordStatus" :label="$t('common.title.recordStatus')">-->
<!--                <template #default="scope">-->
<!--                  {{ getCommonDesc('RECORD_STATUS', scope.row.recordStatus) }}-->
<!--                  <span v-if="scope.row.recordStatus && scope.row.recordStatus !== 'A'">-->
<!--                    for {{ getCommonDesc('MKCK_ACTION', scope.row.mkckAction) }}-->
<!--                  </span>-->
<!--                </template>-->
<!--              </el-table-column>-->
<!--            </template>-->
<!--            <template #form>-->
<!--              <el-container>-->
<!--                <el-aside width="100%">-->
<!--                  <EditGrid v-model="depositorySubVpos" oid="marketDepoSubOid" ref="depositorySubRef"-->
<!--                    uniqueKey="marketDepoSubOid" :form="depostitoryForm.subForm" :rules="subFormRules"-->
<!--                    :details="details" :disabled="formDisabled" tableStyle="overflow: auto; height: 200px;"-->
<!--                    @row-click="handleSubRowClick" @save="handleSaveSubForm">-->
<!--                    <template #columns>-->
<!--                      <el-table-column prop="currency" width="320" :label="$t('csscl.si.common.currency')" />-->
<!--                      <el-table-column prop="chargeSuspAcctNo" width="320"-->
<!--                        :label="$t('csscl.si.common.chargeSuspAcctNo')" />-->
<!--                      <el-table-column prop="incomeSuspAcctNo" width="350"-->
<!--                        :label="$t('csscl.si.common.incomeSuspAcctNo')" />-->
<!--                      <el-table-column prop="setlSuspAcctNo" width="350"-->
<!--                        :label="$t('csscl.si.common.setlSuspAcctNo')" />-->
<!--                      <el-table-column prop="recordStatus" :label="$t('common.title.recordStatus')">-->
<!--                        <template #default="scope">-->
<!--                          {{ getCommonDesc('RECORD_STATUS', scope.row.recordStatus) }}-->
<!--                          <span v-if="scope.row.recordStatus && scope.row.recordStatus !== 'A'">-->
<!--                            for {{ getCommonDesc('MKCK_ACTION', scope.row.mkckAction) }}-->
<!--                          </span>-->
<!--                        </template>-->
<!--                      </el-table-column>-->
<!--                    </template>-->
<!--                    <template #form>-->
<!--                      <FormItemSign :detailsRef="details" label-width="150px" :label="$t('csscl.si.common.currency')"-->
<!--                        prop="currency">-->
<!--                        <div style="position:relative">-->
<!--                          <SearchInput v-model="depostitoryForm.subForm.currency" maxlength="3" onlyLetters searchField-->
<!--                            showDesc="false" style="width: 500px" input-style="width:110px"-->
<!--                            url="/datamgmt/api/v1/currency/list" :title="$t('csscl.currencyManagement.currencyCode')"-->
<!--                            :params="{ status: '', recordStatus: '' }" :columns="[-->
<!--                              {-->
<!--                                title: $t('csscl.currencyManagement.currencyCode'),-->
<!--                                colName: 'currencyCode',-->
<!--                              },-->
<!--                              {-->
<!--                                title: $t('csscl.currencyManagement.descpt'),-->
<!--                                colName: 'descpt',-->
<!--                              }-->
<!--                            ]">-->
<!--                          </SearchInput>-->
<!--                        </div>-->
<!--                      </FormItemSign>-->
<!--                      <FormItemSign :detailsRef="details" label-width="350px"-->
<!--                        :label="$t('csscl.si.common.chargeSuspAcctNo')" prop="chargeSuspAcctNo">-->
<!--                        <el-input v-model="depostitoryForm.subForm.chargeSuspAcctNo" maxlength="20" style="width: 250px"-->
<!--                          class="text-none" />-->
<!--                      </FormItemSign>-->
<!--                      <FormItemSign :detailsRef="details" label-width="350px"-->
<!--                        :label="$t('csscl.si.common.incomeSuspAcctNo')" prop="incomeSuspAcctNo">-->
<!--                        <el-input v-model="depostitoryForm.subForm.incomeSuspAcctNo" maxlength="20" style="width: 250px"-->
<!--                          class="text-none" />-->
<!--                      </FormItemSign>-->
<!--                      <FormItemSign :detailsRef="details" label-width="350px"-->
<!--                        :label="$t('csscl.si.common.setlSuspAcctNo')" prop="setlSuspAcctNo">-->
<!--                        <el-input v-model="depostitoryForm.subForm.setlSuspAcctNo" maxlength="20" style="width: 250px"-->
<!--                          class="text-none" />-->
<!--                      </FormItemSign>-->
<!--                    </template>-->
<!--                  </EditGrid>-->
<!--                </el-aside>-->
<!--              </el-container>-->
<!--              <br>-->
<!--              <FormItemSign :detailsRef="details" label-width="350px" :label="$t('csscl.si.common.depoCode')"-->
<!--                prop="depoCode">-->
<!--                <div style="position:relative">-->
<!--                  <el-input maxlength="10" v-model="depostitoryForm.depoCode" style="width: 250px" />-->
<!--                </div>-->
<!--              </FormItemSign>-->
<!--              <FormItemSign :detailsRef="details" label-width="350px" :label="$t('csscl.si.common.depoDesc')"-->
<!--                prop="depository">-->
<!--                <el-input v-model="depostitoryForm.depoDesc" maxlength="50" style="width: 250px" class="text-none" />-->
<!--              </FormItemSign>-->
<!--              <FormItemSign :detailsRef="details" label-width="350px" :label="$t('csscl.si.common.priSwiftBicCode')"-->
<!--                prop="priSwiftBicCode">-->
<!--                <el-input v-model="depostitoryForm.priSwiftBicCode" maxlength="11" style="width: 250px"-->
<!--                  class="text-none" />-->
<!--              </FormItemSign>-->
<!--              <FormItemSign :detailsRef="details" label-width="350px" :label="$t('csscl.si.common.secSwiftBicCode')"-->
<!--                prop="secSwiftBicCode">-->
<!--                <el-input v-model="depostitoryForm.secSwiftBicCode" maxlength="11" style="width: 250px"-->
<!--                  class="text-none" />-->
<!--              </FormItemSign>-->
<!--              &lt;!&ndash;              <FormItemSign :detailsRef="details" label-width="350px" :label="$t('csscl.si.common.chargeSuspAcctNo')"&ndash;&gt;-->
<!--              &lt;!&ndash;                prop="chargeSuspAcctNo">&ndash;&gt;-->
<!--              &lt;!&ndash;                <el-input v-model="depostitoryForm.chargeSuspAcctNo" maxlength="20" style="width: 250px" class="text-none" />&ndash;&gt;-->
<!--              &lt;!&ndash;              </FormItemSign>&ndash;&gt;-->
<!--              &lt;!&ndash;              <FormItemSign :detailsRef="details" label-width="350px" :label="$t('csscl.si.common.incomeSuspAcctNo')"&ndash;&gt;-->
<!--              &lt;!&ndash;                prop="incomeSuspAcctNo">&ndash;&gt;-->
<!--              &lt;!&ndash;                <el-input v-model="depostitoryForm.incomeSuspAcctNo" maxlength="20" style="width: 250px" class="text-none" />&ndash;&gt;-->
<!--              &lt;!&ndash;              </FormItemSign>&ndash;&gt;-->
<!--              &lt;!&ndash;              <FormItemSign :detailsRef="details" label-width="350px" :label="$t('csscl.si.common.setlSuspAcctNo')"&ndash;&gt;-->
<!--              &lt;!&ndash;                prop="setlSuspAcctNo">&ndash;&gt;-->
<!--              &lt;!&ndash;                <el-input v-model="depostitoryForm.setlSuspAcctNo" maxlength="20" style="width: 250px" class="text-none" />&ndash;&gt;-->
<!--              &lt;!&ndash;              </FormItemSign>&ndash;&gt;-->
<!--              <FormItemSign :detailsRef="details" label-width="350px" :label="$t('common.title.status')" prop="status">-->
<!--                <Select v-model="depostitoryForm.status" style="width: 250px" type='STATUS' />-->
<!--              </FormItemSign>-->

<!--              <div>-->
<!--                <FormRow>-->
<!--                  <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.caPayMethod')" label-width="350px"-->
<!--                    prop="caPayMethodCode">-->
<!--                    <Select v-model="depostitoryForm.caPayMethodCode" type='CASH_SETTLE_METHOD_SI_CODE'-->
<!--                      style="width: 330px" />-->
<!--                  </FormItemSign>-->
<!--                  <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.siPayMethod')" label-width="350px"-->
<!--                    prop="setlPayMethodCode">-->
<!--                    <Select v-model="depostitoryForm.setlPayMethodCode" type='CASH_SETTLE_METHOD_SI_CODE'-->
<!--                      style="width: 330px" />-->
<!--                  </FormItemSign>-->
<!--                </FormRow>-->
<!--                <FormRow>-->
<!--                  <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.genDepoIntfInd')" label-width="350px"-->
<!--                    prop="genDepoIntfInd">-->
<!--                    <Select v-model="depostitoryForm.genDepoIntfInd" type='GEN_DEPO_INTERFACE' style="width: 250px" />-->
<!--                  </FormItemSign>-->
<!--                  <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.intfFormatCode')" label-width="350px"-->
<!--                    prop="intfFormatCode">-->
<!--                    <Select v-model="depostitoryForm.intfFormatCode" type='INTERFACE_FORMAT' style="width: 250px" />-->
<!--                  </FormItemSign>-->
<!--                </FormRow>-->
<!--                <FormRow>-->
<!--                  <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.depoIntfAddr')" label-width="350px"-->
<!--                    prop="depoIntfAddr">-->
<!--                    <InputText maxlength="40" v-model="depostitoryForm.depoIntfAddr" style="width: 250px" />-->
<!--                  </FormItemSign>-->
<!--                </FormRow>-->
<!--              </div>-->
<!--              <div>-->
<!--                <el-card style="max-width: 440px">-->
<!--                  <span style="text-decoration: underline;">Days of Settlement Cycle</span>-->
<!--                  <br><br>-->
<!--                  <FormRow>-->
<!--                    <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.settlePeriodCashRecDay')"-->
<!--                      label-width="120" prop="settlePeriodCashRecDay">-->
<!--                      <InputNumber scale="0" maxlength="2" v-model="depostitoryForm.settlePeriodCashRecDay" />-->
<!--                    </FormItemSign>-->
<!--                  </FormRow>-->
<!--                  <FormRow>-->
<!--                    <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.settlePeriodCashDeliverDay')"-->
<!--                      label-width="120" prop="settlePeriodCashDeliverDay">-->
<!--                      <InputNumber scale="0" maxlength="2" v-model="depostitoryForm.settlePeriodCashDeliverDay" />-->
<!--                    </FormItemSign>-->
<!--                  </FormRow>-->
<!--                  <FormRow>-->
<!--                    <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.settlePeriodStockRecDay')"-->
<!--                      label-width="120" prop="settlePeriodStockRecDay">-->
<!--                      <InputNumber scale="0" maxlength="2" v-model="depostitoryForm.settlePeriodStockRecDay" />-->
<!--                    </FormItemSign>-->
<!--                  </FormRow>-->
<!--                  <FormRow>-->
<!--                    <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.settlePeriodStockDeliverDay')"-->
<!--                      label-width="120" prop="settlePeriodStockDeliverDay">-->
<!--                      <InputNumber scale="0" maxlength="2" v-model="depostitoryForm.settlePeriodStockDeliverDay" />-->
<!--                    </FormItemSign>-->
<!--                  </FormRow>-->
<!--                </el-card>-->
<!--              </div>-->
<!--            </template>-->

<!--          </EditGrid>-->

<!--        </el-aside>-->
<!--      </el-container>-->
      <br><br>
      <el-row>
        <el-icon style="color: #0099cc; width: 1em; height: 1em; margin-right: 4px ;font-size: 150%;">
          <Tickets />
        </el-icon>
        <span style="font-size: 16px; font-weight: bold; color: #3fb2b2;">Processing Time (File Generation Time)</span>
        <div
          style="paddingBlock:5px;width: 100%;display: flex;flex-flow: wrap;border-top: 2px solid; border-color: #9ad7d7;">
        </div>
      </el-row>

      <el-container>
        <el-aside width="40%">
          <EditGrid v-model="processingVpos" oid="marketProcessTimeOid" ref="processingRef" :form="processingForm"
            :rules="holidayRules" :details="details" :disabled="formDisabled"
            tableStyle="overflow: auto; height: 200px;">
            <template #columns>
              <el-table-column prop="cutoffNo" width="150" :label="$t('csscl.si.common.cutoffNo')" />
              <el-table-column prop="cutoffTime" :label="$t('csscl.si.common.cutoffTime')" />
              <el-table-column prop="recordStatus" :label="$t('common.title.recordStatus')">
                <template #default="scope">
                  {{ getCommonDesc('RECORD_STATUS', scope.row.recordStatus) }}
                  <span v-if="scope.row.recordStatus && scope.row.recordStatus !== 'A'">
                    for {{ getCommonDesc('MKCK_ACTION', scope.row.mkckAction) }}
                  </span>
                </template>
              </el-table-column>
            </template>
            <template #form>
              <FormItemSign :detailsRef="details" label-width="150px" :label="$t('csscl.si.common.cutoffNo')"
                prop="cutoffNo">
                <div style="position:relative">
                  <InputNumber maxlength="2" v-model="processingForm.cutoffNo" style="width: 150px" scale="0" />
                </div>
              </FormItemSign>
              <FormItemSign :detailsRef="details" label-width="150px" :label="$t('csscl.si.common.cutoffTime')"
                prop="cutoffTime">
                <!-- <DateItem v-model="processingForm.cutoffTime" style="width: 150px" class="cutoffTime" /> -->
                <el-time-picker v-model="processingForm.cutoffTime" format="HH:mm" value-format="HH:mm"
                  style="width: 60px;"></el-time-picker>
              </FormItemSign>
            </template>
          </EditGrid>
        </el-aside>
      </el-container>

      <DepositoryEditGrid
          v-model="depositoryVpos"
          :disabled="false"
          :details-ref="detailsRef"
          ref="depositoryRef" />
      <br><br>
      <el-row>
        <el-icon style="color: #0099cc; width: 1em; height: 1em; margin-right: 4px ;font-size: 150%;">
          <Tickets />
        </el-icon>
        <span style="font-size: 16px; font-weight: bold; color: #3fb2b2;">Depository Account</span>
        <div
          style="paddingBlock:5px;width: 100%;display: flex;flex-flow: wrap;border-top: 2px solid; border-color: #9ad7d7;">
        </div>
      </el-row>

      <el-container>
        <el-aside width="60%">
          <EditGrid v-model="depositoryAccountVpos" oid="marketDepoCustodianAcctOid" ref="depositoryAccountRef"
            :form="depositoryAccountForm" :rules="holidayRules" :details="details" :disabled="formDisabled"
            tableStyle="overflow: auto; height: 200px;">
            <template #columns>
              <el-table-column prop="custodianAccountName" width="220"
                :label="$t('csscl.si.common.custodianAccountName')" />
              <el-table-column prop="subAccountName" width="220" :label="$t('csscl.si.common.subAccountName')" />
              <el-table-column prop="accountType" width="150" :label="$t('csscl.si.common.accountType')">
                <template #default="scope">
                  {{ getCommonDesc('ACCOUNT_TYPE', scope.row.accountType) }}
                </template>
              </el-table-column>
              <el-table-column prop="custodianAccountType" width="220"
                :label="$t('csscl.si.common.custodianAccountType')">
                <template #default="scope">
                  {{ getCommonDesc('CUSTODIAN_ACCOUNT_TYPE', scope.row.custodianAccountType) }}
                </template>
              </el-table-column>
              <el-table-column prop="status" :label="$t('common.title.status')">
                <template #default="scope">
                  {{ getCommonDesc('STATUS', scope.row.status) }}
                </template>
              </el-table-column>
              <el-table-column prop="recordStatus" width="220" :label="$t('common.title.recordStatus')">
                <template #default="scope">
                  {{ getCommonDesc('RECORD_STATUS', scope.row.recordStatus) }}
                  <span v-if="scope.row.recordStatus && scope.row.recordStatus !== 'A'">
                    for {{ getCommonDesc('MKCK_ACTION', scope.row.mkckAction) }}
                  </span>
                </template>
              </el-table-column>
            </template>
            <template #form>
              <FormItemSign :detailsRef="details" label-width="250px"
                :label="$t('csscl.si.common.custodianAccountName')" prop="custodianAccountName">
                <div style="position:relative">
                  <!-- <el-input v-model="depositoryAccountForm.custodianAccountName" style="width: 150px"/> -->
                  <GeneralSearchInput v-model="depositoryAccountForm.custodianAccountName" showDesc="false" readonly="true"
                    style="width: 160px" searchType="clearingAgentCodeAndAcc" :dbClick="(row) => {
                      depositoryAccountForm.custodianAccOid = row.var2,
                        depositoryAccountForm.clearingAgentOid = row.var1,
                        depositoryAccountForm.subAccountName = row.var4
                    }" />
                </div>
              </FormItemSign>
              <!-- <FormItemSign :detailsRef="details" label-width="250px" :label="$t('Sub-Account Name (external)')"
                prop="subAccountName">
                <el-input v-model="depositoryAccountForm.subAccountName" maxlength="70" style="width: 250px" class="subAccountName" />
              </FormItemSign> -->
              <FormItemSign :detailsRef="details" label-width="250px" :label="$t('csscl.si.common.accountType')"
                prop="accountType">
                <Select v-model="depositoryAccountForm.accountType" style="width: 100px" type='ACCOUNT_TYPE' />
              </FormItemSign>
              <FormItemSign :detailsRef="details" label-width="250px"
                :label="$t('csscl.si.common.custodianAccountType')" prop="custodianAccountType">
                <Select v-model="depositoryAccountForm.custodianAccountType" style="width: 100px"
                  type='CUSTODIAN_ACCOUNT_TYPE' />
              </FormItemSign>
              <FormItemSign :detailsRef="details" label-width="250px" :label="$t('common.title.status')" prop="status">
                <Select v-model="depositoryAccountForm.status" style="width: 100px" type='STATUS' />
              </FormItemSign>
            </template>
          </EditGrid>
        </el-aside>
      </el-container>

      <br><br>
      <!--      <el-row>-->
      <!--        <span style="font-size: 16px; font-weight: bold;">Holiday</span>-->
      <!--        <div style="paddingBlock:5px;width: 100%;display: flex;flex-flow: wrap;border-top: 1px solid;" ></div>-->
      <!--      </el-row>-->
      <!--      <el-container>-->
      <!--        <el-aside width="40%">-->
      <!--          <EditGrid v-model="holidayVpos"-->
      <!--            oid="marketHolidayOid" -->
      <!--            ref="holidayGridRef"-->
      <!--            uniqueKey="holidayDate"-->
      <!--            :form="holidayForm" -->
      <!--            :rules="holidayRules" -->
      <!--            :details="details" -->
      <!--            :disabled="formDisabled"-->
      <!--            tableStyle="overflow: auto; height: 200px;"-->
      <!--             >-->
      <!--            <template #columns>-->
      <!--              <el-table-column prop="holidayDate" width="150" :label="$t('csscl.ctryRegionManagement.holiday')" />-->
      <!--              <el-table-column prop="holidayDesc" :label="$t('csscl.ctryRegionManagement.description')"/>-->
      <!--              <el-table-column prop="recordStatus" width="220" :label="$t('common.title.recordStatus')">-->
      <!--                  <template #default="scope">-->
      <!--                      {{ getCommonDesc('RECORD_STATUS', scope.row.recordStatus) }}-->
      <!--                      <span v-if="scope.row.recordStatus && scope.row.recordStatus !== 'A'">-->
      <!--                          for {{ getCommonDesc('MKCK_ACTION', scope.row.mkckAction) }}-->
      <!--                      </span>-->
      <!--                  </template>-->
      <!--              </el-table-column>-->
      <!--            </template>-->
      <!--            <template #form>-->
      <!--              <FormItemSign :detailsRef="details" label-width="150px" :label="$t('csscl.ctryRegionManagement.holiday')" prop="holidayDate">-->
      <!--                <div style="position:relative">-->
      <!--                  <DateItem v-model="holidayForm.holidayDate" style="width: 150px"/>-->
      <!--                </div>-->
      <!--              </FormItemSign>-->
      <!--              <FormItemSign :detailsRef="details" label-width="150px" :label="$t('csscl.ctryRegionManagement.description')"-->
      <!--                prop="holidayDesc">-->
      <!--                <el-input v-model="holidayForm.holidayDesc" maxlength="70" style="width: 450px" class="text-none" />-->
      <!--              </FormItemSign>-->
      <!--            </template>-->
      <!--          </EditGrid>-->
      <!--        </el-aside>-->
      <!--        <el-aside width="15%"></el-aside>-->
      <!--        <el-main >-->
      <!--          <ElFormItemProxy></ElFormItemProxy>-->
      <!--          <el-text tag="P" class="form-item-sign">{{  $t("csscl.ctryRegionManagement.uploadHolidayFile") }}</el-text>-->
      <!--          <FormRow>-->
      <!--            <ElFormItemProxy label=" ">-->
      <!--              <UploadItem :show-file-list="false" class="upload-demo" drag :file-list="ruleForm.form.fileList" :auto-upload="false"-->
      <!--                accept=".xlsx" :on-change="handleUpload" style="width: 800px">-->
      <!--                <el-icon><Upload /></el-icon>-->
      <!--                <div class="el-upload__text">Browse or drop file</div>-->
      <!--              </UploadItem>-->
      <!--            </ElFormItemProxy>-->
      <!--          </FormRow>-->
      <!--          <FormRow>-->
      <!--            <FormItemSign :detailsRef="details" :label="$t('csscl.ctryRegionManagement.fileName')" prop="fileName">-->
      <!--              <el-space>-->
      <!--              <el-input v-model="ruleForm.form.fileName" :disabled="true" style="width: 400px" class="text-none">-->
      <!--                <template #append>-->
      <!--                  <el-button type="primary" @click="handleDownload" :icon="Download" v-if="ruleForm.form.fileName"/>-->
      <!--                </template>-->
      <!--              </el-input>-->
      <!--                <ElFormItemProxy>-->
      <!--                    <el-button type="primary" @click="downloadTemplate">{{$t('csscl.cashinstr.upload.downloadTemplate')}}</el-button>-->
      <!--                </ElFormItemProxy>-->
      <!--              </el-space>              -->
      <!--            </FormItemSign>-->
      <!--          </FormRow>-->
      <!--          <FormRow>-->
      <!--            <FormItemSign :detailsRef="details" :label="$t('csscl.ctryRegionManagement.totalRecord')" prop="uploadRcCnt">-->
      <!--              <el-input v-model="ruleForm.form.uploadRcCnt" disabled style="width: 100px" />-->
      <!--            </FormItemSign>-->
      <!--          </FormRow>-->
      <!--          <FormRow>-->
      <!--            <FormItemSign :detailsRef="details" :label="$t('csscl.ctryRegionManagement.logSatus')" prop="processStatus">-->
      <!--              <el-input v-model="ruleForm.form.processStatusDesc" disabled style="width: 150px" />-->
      <!--            </FormItemSign>-->
      <!--          </FormRow>-->
      <!--          <FormRow>-->
      <!--            <FormItemSign :detailsRef="details" :label="$t('csscl.ctryRegionManagement.logDetails')" prop="errorLog">-->
      <!--              <el-input v-model="ruleForm.form.errorLog" type="textarea" disabled style="width: 550px" :rows="6" />-->
      <!--            </FormItemSign>-->
      <!--          </FormRow>-->
      <!--        </el-main>-->
      <!--      </el-container>-->


    </el-form>
  </BaseDetails>
</template>

<script lang="ts" setup>
import { ref, reactive, getCurrentInstance, watch, onMounted } from 'vue';
import type { FormInstance, } from 'element-plus'
import { ElMessageBox } from 'element-plus'
import { Upload, Download } from '@element-plus/icons-vue';
import BaseDetails from '~/pages/base/Details.vue';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import CtryRegionSearchInput from '~/pages/base/CtryRegionSearchInput.vue'
import CurrencySearchInput from '~/pages/base/CurrencySearchInput.vue'
import CommonSearchInput from '~/pages/base/CommonSearchInput.vue';
import { getCommonDesc, showErrorMsg } from '~/util/Function.js';
import { getOid, downloadFile, saveMsgBox } from '~/util/Function.js';
import { commonRules, showValidateMsg } from '~/util/Validators.js';
import { addCustValid, focusType } from '~/util/ModifiedValidate.js';
import { Row } from 'element-plus/es/components/table-v2/src/components';
import { fa } from "element-plus/es/locale";
import DepositoryEditGrid from './DepositoryEditGrid.vue';


const { proxy } = getCurrentInstance();
const props = defineProps(['reload']);
const details = ref();
const formDisabled = ref(false);
const editDis = ref(false);
const isRead = ref(false);
const ruleFormRef = ref()
const ruleForm = reactive({
  rules: () => { return [{ rules: rules }] },
  form: {
    fileList: [],
  }
});
// const handleGridClick = (row) => {
//   depostitoryForm = { ...row }; // 同步到详情表单
// }

const reqParams = reactive({
  marketOid: ruleForm.form.marketOid,
  pendingOid: ruleForm.form.pendingOid,
  isApproveDetail: false,
  approveNumber: -1,

});

const holidayVpos = ref([]);
const dayNightSavingVpos = ref([]);
const depositoryVpos = ref([]);
const processingVpos = ref([]);
const depositoryAccountVpos = ref([]);
const depositorySubVpos = ref([]);
const tradeInstructionVpos = ref([]);
const cashCutoffTimeVpos = ref([]);
const holidayGridRef = ref();
const dayNightSavingRef = ref();
const tradeInstructionRef = ref();
const cashCutoffTimeRef = ref();
const depositoryRef = ref();
const processingRef = ref();
const depositoryAccountRef = ref();
const depositorySubRef = ref();

const loadGrid = async () => {
  const msg = await proxy.$axios.post("/datamgmt/api/v1/market/holiday/list", {
    param: {
      ...reqParams,
      marketCode: ruleForm.form.marketCode
    },
    current: 1,
    pageSize: 999999,
    // orderBy: ,
  });
  if (msg?.success) {
    holidayVpos.value = msg.data.data;
  }
}

const loadDaylightGrid = async () => {
  const msg = await proxy.$axios.post("/datamgmt/api/v1/market/daylight/list", {
    param: {
      ...reqParams,
      marketCode: ruleForm.form.marketCode
    },
    current: 1,
    pageSize: 999999,
    // orderBy: ,
  });
  if (msg?.success) {
    dayNightSavingVpos.value = msg.data.data;
  }
}
const loadProcessGrid = async () => {
  const msg = await proxy.$axios.post("/datamgmt/api/v1/market/process/list", {
    param: {
      ...reqParams,
      marketCode: ruleForm.form.marketCode
    },
    current: 1,
    pageSize: 999999,
    // orderBy: ,
  });
  if (msg?.success) {
    processingVpos.value = msg.data.data;
  }
}

const loadDepositoryAccountGrid = async () => {
  const msg = await proxy.$axios.post("/datamgmt/api/v1/market/depository-cust-acc/list", {
    param: {
      ...reqParams,
      marketCode: ruleForm.form.marketCode
    },
    current: 1,
    pageSize: 999999,
    // orderBy: ,
  });
  if (msg?.success) {
    depositoryAccountVpos.value = msg.data.data;
  }
}
const loadLocationDepositoryGrid = async () => {
  const msg = await proxy.$axios.post("/datamgmt/api/v1/market/location-depository/list", {
    param: {
      ...reqParams,
      marketCode: ruleForm.form.marketCode
    },
    current: 1,
    pageSize: 999999,
    // orderBy: ,
  });
  if (msg?.success) {
    depositoryVpos.value = msg.data.data;
  }
}
const loadTradeInstrGrid = async () => {
  const msg = await proxy.$axios.post("/datamgmt/api/v1/market/trade-instr/list", {
    param: {
      ...reqParams,
      marketCode: ruleForm.form.marketCode
    },
    current: 1,
    pageSize: 999999,
    // orderBy: ,
  });
  if (msg?.success) {
    tradeInstructionVpos.value = msg.data.data;
  }
}

const loadCashCutoffGrid = async () => {
  const msg = await proxy.$axios.post("/datamgmt/api/v1/market/cash-cutoff/list", {
    param: {
      ...reqParams,
      marketCode: ruleForm.form.marketCode
    },
    current: 1,
    pageSize: 999999,
    // orderBy: ,
  });
  if (msg?.success) {
    cashCutoffTimeVpos.value = msg.data.data;
  }
}

const editRow = (row, disabled, newId) => {
  if (row?.isApproveDetail && disabled) {
    ruleForm.form = row.afterImage;
    reqParams.isApproveDetail = true;
    reqParams.approveNumber = row?.approveNumber;
    reqParams.marketOid = row?.eventPkey;
    details.value.currentRow = ruleForm.form;
    loadGrid();
    loadDaylightGrid();
    loadProcessGrid();
    loadDepositoryAccountGrid();
    // loadLocationDepositoryGrid();
    loadTradeInstrGrid();
    loadCashCutoffGrid();
  } else {
    const oid = newId || (row?.currentOid && getOid(row, disabled, proxy));
    if (oid) {
      proxy.$axios.get("/datamgmt/api/v1/market?objectId=" + oid).then((body) => {
        if (body.success) {
          ruleForm.form = body.data;
          reqParams.marketOid = body.data?.marketOid;
          reqParams.pendingOid = body.data?.pendingOid;
          details.value.currentRow = body.data;
          // holidayGridRef.value.clearModifyData();
          dayNightSavingRef.value.clearModifyData();
          depositoryAccountRef.value.clearModifyData();
          tradeInstructionRef.value.clearModifyData();
          cashCutoffTimeRef.value.clearModifyData();
          processingRef.value.clearModifyData();
          // depositoryRef.value.clearModifyData()

          loadGrid();
          loadDaylightGrid();
          loadProcessGrid();
          loadDepositoryAccountGrid();
          // loadLocationDepositoryGrid();
          loadTradeInstrGrid();
          loadCashCutoffGrid();
          addCustValid(ruleForm.form, () => {
            // let leg = holidayGridRef.value.getModifyRecords()?.length;
            // leg = leg > 0 ? true : false;
            // if (!leg){
            //     let datas = holidayGridRef.value.showData;
            //     for (let i = 0; i < datas.length; i++) {
            //         let data = datas[i];
            //         if (data.recordStatus != 'A') {
            //             focusType.type = focusType.EnterObj;
            //             break;
            //         }
            //     }
            // }
            let legNighgt = dayNightSavingRef.value.getModifyRecords()?.length;
            legNighgt = legNighgt > 0 ? true : false;
            if (!legNighgt) {
              let datas = dayNightSavingRef.value.showData;
              for (let i = 0; i < datas.length; i++) {
                let data = datas[i];
                if (data.recordStatus != 'A') {
                  focusType.type = focusType.EnterObj;
                  break;
                }
              }
            }
            // let legDepository = depositoryRef.value.getModifyRecords()?.length;
            // legDepository = legDepository > 0 ? true : false;
            // if (!legDepository) {
            //   let datas = depositoryRef.value.showData;
            //   for (let i = 0; i < datas.length; i++) {
            //     let data = datas[i];
            //     if (data.recordStatus != 'A') {
            //       focusType.type = focusType.EnterObj;
            //       break;
            //     }
            //   }
            // }
            let legProcess = processingRef.value.getModifyRecords()?.length;
            legProcess = legProcess > 0 ? true : false;
            if (!legProcess) {
              let datas = processingRef.value.showData;
              for (let i = 0; i < datas.length; i++) {
                let data = datas[i];
                if (data.recordStatus != 'A') {
                  focusType.type = focusType.EnterObj;
                  break;
                }
              }
            }
            let legDesitoryAccount = depositoryAccountRef.value.getModifyRecords()?.length;
            legDesitoryAccount = legDesitoryAccount > 0 ? true : false;
            if (!legDesitoryAccount) {
              let datas = depositoryAccountRef.value.showData;
              for (let i = 0; i < datas.length; i++) {
                let data = datas[i];
                if (data.recordStatus != 'A') {
                  focusType.type = focusType.EnterObj;
                  break;
                }
              }
            }
            let tradeInstruction = tradeInstructionRef.value.getModifyRecords()?.length;
            tradeInstruction = tradeInstruction > 0;
            if (!tradeInstruction) {
              let datas = tradeInstructionRef.value.showData;
              for (let i = 0; i < datas.length; i++) {
                let data = datas[i];
                if (data.recordStatus != 'A') {
                  focusType.type = focusType.EnterObj;
                  break;
                }
              }
            }
            let cashCutoffTime = cashCutoffTimeRef.value.getModifyRecords()?.length;
            cashCutoffTime = cashCutoffTime > 0;
            if (!cashCutoffTime) {
              let datas = cashCutoffTimeRef.value.showData;
              for (let i = 0; i < datas.length; i++) {
                let data = datas[i];
                if (data.recordStatus != 'A') {
                  focusType.type = focusType.EnterObj;
                  break;
                }
              }
            }
            // let depoSub = depositorySubRef.value.getModifyRecords()?.length;
            // depoSub = depoSub > 0;
            // if (!cashCutoffTime) {
            //   let datas = depositorySubRef.value.showData;
            //   for (let i = 0; i < datas.length; i++) {
            //     let data = datas[i];
            //     if (data.recordStatus != 'A') {
            //       focusType.type = focusType.EnterObj;
            //       break;
            //     }
            //   }
            // }
            return legNighgt || legProcess || legDesitoryAccount || tradeInstruction || cashCutoffTime;
          });
        }
        details.value.initWatch({
          w1: ruleForm,
          w2: holidayGridRef,
          w3: holidayForm,
          w4: dayNightSavingRef,
          w5: dayNightForm,
          w8: processingRef,
          w9: processingForm,
          w10: depositoryAccountRef,
          w11: depositoryAccountForm,
          w12: tradeInstructionRef,
          w13: tradeInstructionForm,
          w14: cashCutoffTimeRef,
          w15: cashCutoffTimeForm
        }, ruleForm);
      });
      editDis.value = true;
    } else {
      details.value.initWatch({
        w1: ruleForm,
        w2: holidayGridRef,
        w3: holidayForm,
        w4: dayNightSavingRef,
        w5: dayNightForm,
        w8: processingRef,
        w9: processingForm,
        w10: depositoryAccountRef,
        w11: depositoryAccountForm,
        w12: tradeInstructionRef,
        w13: tradeInstructionForm,
        w14: cashCutoffTimeRef,
        w15: cashCutoffTimeForm
      }, ruleForm);
    }
  }
}


const viewOriginalForm = (pendingOid, isDisabled) => {
  formDisabled.value = isDisabled;
  proxy.$axios.get("/datamgmt/api/v1/market?objectId=" + pendingOid).then((body) => {
    if (body.success) {
      ruleForm.form = body.data;
      details.value.currentRow.value = body.data;
      reqParams.marketOid = body.data?.marketOid;
      reqParams.pendingOid = body.data?.pendingOid;
      loadGrid();
      loadDaylightGrid();
      loadProcessGrid();
      loadDepositoryAccountGrid();
      // loadLocationDepositoryGrid();
      loadTradeInstrGrid();
      loadCashCutoffGrid();
    }
  });
}

const handleSave = async (searchValid, isOnlyValidate, unPopping) => {
  // if (holidayGridRef.value.isEditing()) {
  //   showErrorMsg("The sub table data has not been saved. Please confirm if you want to leave.");
  //   return false;
  // }
  const time1 = timeChange('Online')
  const time2 = timeChange('Income')
  const time3 = timeChange('Settlement')
  let result = await ruleFormRef.value.validate((valid, fields) => {
    if (valid) {
    } else {
      showValidateMsg(details, fields);
    }
  });

  result = result && time1 && time2 && time3;
  if (isOnlyValidate) {
    return result;
  }
  if (result && searchValid && await saveMsgBox(unPopping)) {
    if (ruleForm.form.marketOid) {
      const msg = await proxy.$axios.patch("/datamgmt/api/v1/market", {
        ...ruleForm.form,
        // marketHolidayVPOList: holidayGridRef.value.getModifyRecords(),
        marketDaylightVPOList: dayNightSavingRef.value.getModifyRecords(),
        marketProcessTimeVPOList: processingRef.value.getModifyRecords(),
        marketDepositoryCustaccVPOList: depositoryAccountRef.value.getModifyRecords(),
        // marketExBoardVPOList: depositoryRef.value.getModifyRecords(),
        marketTradeInstrVPOList: tradeInstructionRef.value.getModifyRecords(),
        marketCashCutoffVPOList: cashCutoffTimeRef.value.getModifyRecords(),
      });
      if (msg.success) {
        details.value.writebackId(msg.data);
        editRow(null, null, msg.data);
        // holidayGridRef.value.clearModifyData();
        dayNightSavingRef.value.clearModifyData();
        processingRef.value.clearModifyData()
        depositoryAccountRef.value.clearModifyData()
        // depositoryRef.value.clearModifyData()
        loadGrid();
        loadDaylightGrid();
        loadProcessGrid();
        loadDepositoryAccountGrid();
        // loadLocationDepositoryGrid();
        loadTradeInstrGrid();
        loadCashCutoffGrid();
      }
      return msg.success;
    } else {
      const msg = await proxy.$axios.post("/datamgmt/api/v1/market", {
        ...ruleForm.form,
        // marketHolidayVPOList: holidayGridRef.value.getModifyRecords(),
        marketDaylightVPOList: dayNightSavingRef.value.getModifyRecords(),
        marketProcessTimeVPOList: processingRef.value.getModifyRecords(),
        marketDepositoryCustaccVPOList: depositoryAccountRef.value.getModifyRecords(),
        // marketExBoardVPOList: depositoryRef.value.getModifyRecords(),
        marketTradeInstrVPOList: tradeInstructionRef.value.getModifyRecords(),
        marketCashCutoffVPOList: cashCutoffTimeRef.value.getModifyRecords(),
      });
      if (msg.success) {
        details.value.writebackId(msg.data);
        editRow(null, null, msg.data);
        // holidayGridRef.value.clearModifyData()
        dayNightSavingRef.value.clearModifyData()
        processingRef.value.clearModifyData()
        depositoryAccountRef.value.clearModifyData()
        // depositoryRef.value.clearModifyData()
        loadGrid();
        loadDaylightGrid();
        loadProcessGrid();
        loadDepositoryAccountGrid();
        // loadLocationDepositoryGrid();
        loadTradeInstrGrid();
        loadCashCutoffGrid();
      }
      return msg.success;
    }
  }
  return false;
}

const resData = ref([]);
const handleSearch = () => {
  onReset();
  resData.value = []
}
const onReset = () => {
  cashCutoffTimeFormSearch.cutoffType = '',
    cashCutoffTimeFormSearch.currency = ''
  if (resData.value.length > 0){
    cashCutoffTimeVpos.value = resData.value;
  }
}

const onSearch = async () => {
  // if (!ruleForm.form.marketOid){
  //   return false;
  // }
  // const msg = await proxy.$axios.post("/datamgmt/api/v1/market/cash-cutoff/list", {
  //   param: {
  //     marketOid: ruleForm.form.marketOid,
  //     ...cashCutoffTimeFormSearch,
  //   },
  //   current: 1,
  //   pageSize: 999999,
  //   // orderBy: ,
  // });
  // if (msg?.success) {
  //   cashCutoffTimeVpos.value = msg.data.data;
  //   console.log(cashCutoffTimeRef.value.showData,'cashCutoffTimeRef.value.showData')
  // }

  // 过滤掉那两个参数的
  if (resData.value.length == 0){
    resData.value = cashCutoffTimeRef.value.showData
  }
  let filteredData = resData.value
  if (cashCutoffTimeFormSearch.currency && cashCutoffTimeFormSearch.cutoffType) {
    filteredData = filteredData.filter(item =>
        item.currency === cashCutoffTimeFormSearch.currency &&
        item.cutoffType === cashCutoffTimeFormSearch.cutoffType
    );
  }
  // 如果只有 currency 有值
  else if (cashCutoffTimeFormSearch.currency) {
    filteredData = filteredData.filter(item =>
        item.currency === cashCutoffTimeFormSearch.currency
    );
  }
  // 如果只有 cutoffType 有值
  else if (cashCutoffTimeFormSearch.cutoffType) {
    filteredData = filteredData.filter(item =>
        item.cutoffType === cashCutoffTimeFormSearch.cutoffType
    );
  }
  cashCutoffTimeVpos.value = filteredData

}

const showDetails = (row, isdoubleCheck) => {
  if (isdoubleCheck || row.recordStatus === 'PA') {
    formDisabled.value = true;
    details.value.showDetails(row, true)
  } else {
    formDisabled.value = false;
    details.value.showDetails(row, false)
  }
  if (isdoubleCheck) {
    isRead.value = true
  } else {
    isRead.value = false
  }
  ruleForm.form = {};
  details.value.currentRow = {};
  editDis.value = false;
  editRow(row, isdoubleCheck);
}

defineExpose({
  details,
  editRow,
  showDetails,
});
// --------------------------------------------
interface RuleForm {
  currencyCode: String
  status: String
}

const requiredFields = [
  'retentionTranHis',
  'cashInstrctnDlDayOffset',
  'tradeInstrctnDlDayOffset',
  'dayOfDormant',
  'dayOfDormantUnclaim',
  'startOfOpsOnline',
  'endOfOpsOnline',
  'siPayMethod',
  'caPayMethod',
  'cashStockSettleMethod',
  'contractIncomeStart',
  'contractIncomeNotifyDays',
  'contractIncomeEnd',
  'contractSetteNotifyDays',
  'contractSetteStart',
  'contractIncomeReverseDays',
  'contractSetteEnd',
  'contractSetteReverseDays',
  'taxRelief',
  'marketSanctioned',
  'supBroadridgeExtraction',
  'omnibusSegregate',
]
const rules = reactive({
  opCtryRegionCode: [
    commonRules.required,
  ],
  ctryRegionCode: [
    commonRules.required,
    commonRules.name,
  ],
  ctryRegionName: [
    commonRules.name,
  ],
  isoCode: [
    commonRules.required,
    commonRules.name,
  ],
  currencyCode: [
    commonRules.required,
  ],
  status: [
    commonRules.selectRequired,
  ],
  marketCode: [
    commonRules.required,
  // {
  //   validator: (rule, value, callback) => {
  //       const regex = /^[a-zA-Z0-9\/-\?:\(\)\.,'+]*$/;
  //       if (!regex.test(value)) {
  //         callback(new Error('Invalid characters in Market Code'));
  //       } else {
  //         callback();
  //       }
  //   },
  //   trigger: 'blur'
  // }

  ],
  marketDesc: [
    commonRules.required,
  ],
  gmtOffset: [
    commonRules.required,
  ],
  ...Object.fromEntries(
    requiredFields.map(field => [
      field,
      commonRules.required,
    ]),

  )
})

const value = ref('')

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      console.log('submit!')
    } else {
      console.log('error submit!', fields)
    }
  })
}

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
}

const downloadTemplate = (e) => {
  downloadFile("/datamgmt/api/v1/market/holiday/template", {});
}

const handleDownload = () => {
  var row = ruleForm.form;
  var params = {
    filePath: row.filePath,
    fileName: row.fileName,
  }
  downloadFile("/datamgmt/api/v1/market/holiday/download", params);
}

const handleUpload = async (file) => {
  let result = await ruleFormRef.value.validate((valid, fields) => {
    if (valid) {
    } else {
      showValidateMsg(details, fields);
    }
  });
  if (result) {
    ruleForm.form.fileList = [];
    ruleForm.form.fileList.push(file);

    if (file.size === 0) {
      ElMessageBox.alert("Please upload a file.", 'Warning');
    }

    let formData = new FormData();
    formData.append('channel', 'MANUAL');
    formData.append('fileType', 'CTRYHOLDAY');
    formData.append('file', file.raw);
    formData.append('ctryRegionCode', ruleForm.form?.ctryRegionCode);
    const resp = await proxy.$axios.post("/datamgmt/api/v1/market/holidayUploadXlsx", formData);
    if (resp?.success) {
      if (resp.data) {
        ruleForm.form.filePath = resp.data?.filePath;
        ruleForm.form.fileName = resp.data?.fileName;
        ruleForm.form.uploadRcCnt = resp.data?.uploadRcCnt;
        ruleForm.form.errorLog = resp.data?.errorLog;
        ruleForm.form.processStatus = resp.data?.processStatus;
        ruleForm.form.processStatusDesc = resp.data?.processStatusDesc;
        let datas = resp.data?.listData;
        if (datas) {
          let rows = [];
          datas.forEach(elem => {
            rows.push({
              holidayDate: elem.holidayDate,
              holidayDesc: elem.holidayDesc,
            });
          });
          holidayGridRef.value.addBatch(rows);
        }
      }
    }
    return resp?.success;
  } else {
    return false;
  }
}
const holidayForm = reactive({
  holidayDate: null,
  holidayName: '',
  holidayDesc: ''
});
const tradeInstructionForm = reactive({
  transactionType: '',
  cutoffTime: ''
});

const cashCutoffTimeForm = reactive({
  cutoffType: '',
  currency: '',
  cutoffTime: '',
  currencyDesc: ''
});
const cashCutoffTimeFormSearch = reactive({
  cutoffType: '',
  currency: '',
});

const dayNightForm = reactive({
  effectiveDate: null,
  gmtOffset: '',
  status: ''
});
const depostitoryForm = reactive({
  depoCode: '',
  depoDesc: '',
  priSwiftBicCode: '',
  secSwiftBicCode: '',
  chargeSuspAcctNo: '',
  incomeSuspAcctNo: '',
  setlSuspAcctNo: '',
  status: '',
  caPayMethodCode: '',
  setlPayMethodCode: '',
  genDepoIntfInd: '',
  intfFormatCode: '',
  depoIntfAddr: '',
  settlePeriodCashRecDay: '',
  settlePeriodCashDeliverDay: '',
  settlePeriodStockRecDay: '',
  settlePeriodStockDeliverDay: '',
  marketDepoSubVPOList: [], // 子表数据列表
  subForm: { // 临时表单
    currency: '',
    chargeSuspAcctNo: '',
    incomeSuspAcctNo: '',
    setlSuspAcctNo: '',
  },
  editingSubIndex: undefined,
});
const subFormRules = {
  currency: [
    { required: true, message: 'Currency is required', trigger: 'blur' }
  ]
};
const handleMainDbClick = (row) => {
  // 假设子表数据在 row.marketDepoSubVPOList 中
  if (row.marketDepoSubVPOList && row.marketDepoSubVPOList.length > 0) {
    depositorySubVpos.value = [...row.marketDepoSubVPOList];
  }
};
const handleSubRowClick = (row, index) => {
  depostitoryForm.subForm = {
    currency: row.currency,
    chargeSuspAcctNo: row.chargeSuspAcctNo,
    incomeSuspAcctNo: row.incomeSuspAcctNo,
    setlSuspAcctNo: row.setlSuspAcctNo,
  };
  depostitoryForm.editingSubIndex = index;
};
const handleSaveSubForm = () => {
  // if (!depostitoryForm.subForm || !depostitoryForm.subForm.currency) {
  //   return;
  // }
  //
  // 如果 subFormList 不存在，初始化
  if (!depostitoryForm.marketDepoSubVPOList) {
    depostitoryForm.marketDepoSubVPOList = [];
  }
  //
  // // 判断是否是新增还是编辑
  // if (depostitoryForm.editingSubIndex) {
  //   // 编辑已有子表数据
  //   depostitoryForm.marketDepoSubVPOList[depostitoryForm.editingSubIndex] = {
  //     ...depostitoryForm.subForm
  //   };
  //   delete depostitoryForm.editingSubIndex;
  // } else {
  //
  // }
  // // 新增子表数据
  depostitoryForm.marketDepoSubVPOList.push({
    ...depostitoryForm.subForm
  });

  // 清空临时表单
  // depostitoryForm.subForm.currency = '';
  // depostitoryForm.subForm.chargeSuspAcctNo = '';
  // depostitoryForm.subForm.incomeSuspAcctNo = '';
  // depostitoryForm.subForm.setlSuspAcctNo = '';
};
const handleAddSubForm = () => {
  depostitoryForm.subForm = reactive({
    currency: '',
    chargeSuspAcctNo: '',
    incomeSuspAcctNo: '',
    setlSuspAcctNo: '',
  });

  if (!depostitoryForm.marketDepoSubVPOList) {
    depostitoryForm.marketDepoSubVPOList = [];
  }
};
const processingForm = reactive({
  cutoffNo: null,
  cutoffTime: ''
});
const depositoryAccountForm = reactive({
  custodianAccountName: '',
  subAccountName: '',
  custodianAccOid: '',
  clearingAgentOid: '',
  accountType: '',
  custodianAccountType: '',
  status: ''
});
const holidayRules = reactive({
  holidayDate: [
    commonRules.required,
  ],
  holidayName: [
    commonRules.required,
    commonRules.name,
  ],
  cutoffType: [
    commonRules.required,
  ]
});
const tradeInstructionRules = reactive({
  transactionType: [
    commonRules.required,
    {
      validator: (rule, value, callback) => {
        if (value && !tradeInstructionForm.cutoffTime) {
          callback(new Error('Please input the cutoff time for transaction type.'));
        } else {
          callback();
        }
      },
      trigger: 'change'
    }
  ],
  cutoffTime: [
    {
      validator: (rule, value, callback) => {
        if (value && !tradeInstructionForm.transactionType) {
          callback(new Error('Please input the transaction type for the cutoff time.'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ]
});
const cashCutoffTimeRules = reactive({
  cutoffType: [
    commonRules.required,
      {
        validator: (rule, value, callback) => {
          if (value && !cashCutoffTimeForm.currency) {
            callback(new Error('Please input the currency for Cutoff Type'));
          } else {
            callback();
          }
        },
        trigger: 'change'
      },
      {
        validator: (rule, value, callback) => {
          if (value && !cashCutoffTimeForm.cutoffTime) {
            callback(new Error('Please input the cutoff time for Cutoff Type'));
          } else {
            callback();
          }
        },
        trigger: 'blur'
      }
  ]
});
const depositoryRules = reactive({
  depoCode: [
    commonRules.required,
  ],
  depoDesc: [
    commonRules.required,
  ],
  priSwiftBicCode: [
    commonRules.required,
  ],
  status: [
    commonRules.required,
  ],
  caPayMethodCode: [
    commonRules.required,
  ],
  setlPayMethodCode: [
    commonRules.required,
  ],
  genDepoIntfInd: [
    commonRules.required,
  ],
  intfFormatCode: [
    commonRules.required,
  ],
  settlePeriodCashRecDay: [
    commonRules.required,
  ],
  settlePeriodCashDeliverDay: [
    commonRules.required,
  ],
  settlePeriodStockRecDay: [
    commonRules.required,
  ],
  settlePeriodStockDeliverDay: [
    commonRules.required,
  ],
});


onMounted(() => {
});
watch(
  () => ruleForm.form.retentionTranHis,
  (newVal) => {
    if (!isRead.value && !newVal) {
      ruleForm.form.retentionTranHis = '9999';
    }
  },
  { immediate: true }
);
watch(
  () => ruleForm.form.siPayMethod,
  (newVal) => {
    if (!isRead.value && !newVal) {
      ruleForm.form.siPayMethod = 'A';
    }
  },
  { immediate: true }
);
watch(
  () => ruleForm.form.caPayMethod,
  (newVal) => {
    if (!isRead.value && !newVal) {
      ruleForm.form.caPayMethod = 'A';
    }
  },
  { immediate: true }
);
watch(
  () => ruleForm.form.cashStockSettleMethod,
  (newVal) => {
    if (!isRead.value && !newVal) {
      ruleForm.form.cashStockSettleMethod = 'S';
    }
  },
  { immediate: true }
);
watch(
  () => depostitoryForm.caPayMethodCode,
  (newVal) => {
    if (!isRead.value && !newVal) {
      depostitoryForm.caPayMethodCode = 'A';
    }
  },
  { immediate: true }
);
watch(
  () => depostitoryForm.setlPayMethodCode,
  (newVal) => {
    if (!isRead.value && !newVal) {
      depostitoryForm.setlPayMethodCode = 'A';
    }
  },
  { immediate: true }
);
watch(
  () => ruleForm.form.marketSanctioned,
  (newVal) => {
    if (!isRead.value && !newVal) {
      ruleForm.form.marketSanctioned = 'N'
    }
  },
  { immediate: true, deep: true }

);

//结束时间必须晚于开始时间
const timeChange = (type) => {
  if (type === 'Online') {
    if (ruleForm.form.startOfOpsOnline && ruleForm.form.endOfOpsOnline) {
      if (ruleForm.form.startOfOpsOnline > ruleForm.form.endOfOpsOnline) {
        showErrorMsg('End of Ops Online must be equal to or later than Start of Ops Online.');
        return false;
      }
    }
  }

  if (type === 'Income') {
    if (ruleForm.form.contractIncomeStart && ruleForm.form.contractIncomeEnd) {
      if (ruleForm.form.contractIncomeStart > ruleForm.form.contractIncomeEnd) {
        showErrorMsg('End of Contractual Income must be equal to or later than Start of Contractual Income.');
        return false;
      }
    }

    if (ruleForm.form.startOfOpsOnline && ruleForm.form.contractIncomeStart) {
      if (ruleForm.form.startOfOpsOnline >= ruleForm.form.contractIncomeStart) {
        showErrorMsg('Start of Contractual Income must be later than the Start of Ops Online.');
        return false;
      }
    }

    if (ruleForm.form.endOfOpsOnline && ruleForm.form.contractIncomeEnd) {
      if (ruleForm.form.endOfOpsOnline <= ruleForm.form.contractIncomeEnd) {
        showErrorMsg('End of Contractual Income must be earlier than the End of Ops Online.');
        return false;
      }
    }
  }

  if (type === 'Settlement') {
    if (ruleForm.form.contractSetteStart && ruleForm.form.contractSetteEnd) {
      if (ruleForm.form.contractSetteStart > ruleForm.form.contractSetteEnd) {
        showErrorMsg('End of Contractual Settlement must be equal to or later than Start of Contractual Settlement.');
        return false;
      }
    }

    if (ruleForm.form.startOfOpsOnline && ruleForm.form.contractSetteStart) {
      if (ruleForm.form.startOfOpsOnline >= ruleForm.form.contractSetteStart) {
        showErrorMsg('Start of Contractual Settlement must be later than the Start of Ops Online.');
        return false;
      }
    }
    if (ruleForm.form.endOfOpsOnline && ruleForm.form.contractSetteEnd) {
      if (ruleForm.form.endOfOpsOnline <= ruleForm.form.contractSetteEnd) {
        showErrorMsg('End of Contractual Settlement must be earlier than the End of Ops Online.');
        return false;
      }
    }
  }


  return true;
};
</script>

<style scoped>
.form-row {
  margin-block: 3px;
}
</style>