<template>
  <el-container style="height:100%; position: relative; z-index: 98;">
    <el-header>
      <el-row :gutter="24" style="margin: 0px; padding: 3px 10px; border-bottom: 2px solid lightgrey; ">
        <el-col :span="10" style="padding:0px;align-content: center;">
          <el-text style="color: #b31a25;font-weight: bold;margin:0px;" class="mx-1" size="large">{{
              $t($currentInfoStore.getHeaderTitle) }}</el-text>
        </el-col>
        <el-col :span="10" :offset="4" style="padding:0px;">
          <el-space style="float:right;">
            <el-button :icon="CloseBold" type="primary" @click="goHome" v-if="$route.path != '/'" />
            <span v-if="$route.path == '/'">Refreshed as at {{ refreshDate }}</span>
          </el-space>
        </el-col>
      </el-row>
      <el-container class="search-container" style="padding-inline: 10px;">
        <el-aside :width="props.topLeftWidth ? props.topLeftWidth : '0px'" style="padding: 0px;">
          <slot name="topLeft"></slot>
        </el-aside>
        <el-main style="padding: 0px;position: relative;">
          <el-form :validateOnRuleChange="false" ref="formRef" :rules="rules" :inline="true" :model="formInline" style="max-width: 1880px;" :show-message="false"
                   class="demo-form-inline">
            <slot name="searchPanel" :form="formInline"></slot>
          </el-form>
          <div style="text-align: right;">
            <el-button v-if="!props.isHideSearch" @click="onReset(formRef)">{{ $t('csscl.common.btn.clear')
              }}</el-button>
            <el-button v-if="!props.isHideSearch" type="primary" @click="onSearch">{{ $t('csscl.common.btn.search')
              }}</el-button>
          </div>
          <span v-if="isDashBoard" style="position: absolute;top: 0px;right: 0px;">
            <el-button v-if="!props.isHideSearch" style="font-size: 22px;" size="large" :icon="List" link
                       @click="cacheUserSearchParam" />
            <el-button style="font-size: 22px;margin-left:0px;" size="large" :icon="Refresh" link
                       @click="refreshPage" />
          </span>
          <span v-if="!isDashBoard" style="position: absolute;top: 0px;right: 0px;">
            <el-button v-if="!props.isHideParamList" style="font-size: 22px;" size="large" :icon="List" link
                       @click="cacheUserSearchParam" />
          </span>
          <el-row>
            <el-col>
              <slot name="tableHeaderTitle"></slot>
            </el-col>
          </el-row>
          <el-row :gutter="24" class="demo-form-inline" style="margin: 0px;padding-block: 5px;">
            <el-col :span="18" style="padding: 0px">
              <div style="width: 100%;display: table;border-bottom: 2px solid #e6e6e6; min-height: 26px;">
                <div
                    style="color: lightgray; font-weight: bold;display: table-cell;width: 60px; align-content: center;">
                  Order By: </div>
                <el-space style="color: lightgray;width: calc(100% - 60px); padding-bottom: 2px; padding-left: 10px;"
                          wrap>
                  <el-tag v-for="(tag, index) in orderByDesc" :key="tag.code" closable type="info"
                          @close="deleteOrder(tag)">
                    {{ tag.name + " " + tag.order }}
                  </el-tag> </el-space>
              </div>
            </el-col>
            <el-col :span="6" style="padding: 0px" v-if="!props.isHideAdd">
              <el-space style="float:right;">
                <!-- <CButtons :action="'New'" :icon="Plus" type="primary" @click="hadnleAdd" /> -->
                <!-- CButtons's "slot" may causes one more Span(6px) component -->
                <el-button :icon="Plus" type="primary" @click="hadnleAdd"
                           v-if="$currentInfoStore.currentPermission && $currentInfoStore.currentPermission['New'] && !isDashBoard" />
              </el-space>
            </el-col>
          </el-row>
        </el-main>

        <el-dialog draggable overflow class="parameter-list" :close-on-click-modal="false"
                   :close-on-press-escape="false" :destroy-on-close="true" v-model="showParam"
                   :title="$t('csscl.common.paramList')" style="width: 1500px;height:430px">
          <div style="max-width: 800px;width: 500px;height:35px;padding-top: 22px;padding-left:10px;">
            <el-space>
              <span style="padding-right: 20px;">Function</span><el-input disabled v-model="functionId"
                                                                          style="width: 190px;" /><el-input :value="$t($currentInfoStore.getHeaderTitle)" disabled
                                                                                                            style="width: 550px;" />
              <span style="padding-left: 60px;width: 150px;">Parameter Name</span>
              <el-input v-model="parameter" input-style="text-transform:none"
                        style="margin-left: -30px;width: 280px;" />
              <el-button style="margin-left: 25px;width: 70px;" @click="saveParam">Add</el-button>
            </el-space>
          </div>
          <div>
            <el-button :icon="Minus" style="margin-left: 96%; margin-bottom: -40px;" @click="deleteRow"></el-button>
          </div>
          <el-table highlight-current-row scrollbar-always-on @current-change="handleSelectionChange" border
                    :data="paramData" ref="parameterTableRef" style="width: 95%; height:243px; margin-left: 10px">
            <el-table-column prop="paraName" label="Parameter" width="90" />
            <el-table-column prop="queryShow" label="Search" />
            <el-table-column prop="sortShow" label="Order By" />
            <el-table-column prop="pageShow" label="Records per page" width="140" />
            <el-table-column prop="defaultInd" label="Default" width="70">
              <template v-slot="scope">
                <el-checkbox v-model="scope.row.defaultInd" @change="changeDefaultInd(scope.row)" true-value="Y"
                             false-value="N" />
              </template>
            </el-table-column>
          </el-table>
          <template #footer>
            <div style="padding: 20px 10px;text-align: center;height: 35px">
              <el-space>
                <el-button @click="exit" class="ep-button-custom">Exit</el-button>
                <el-button type="primary" @click="saveDefault" :loading="OKLoading" class="ep-button-custom">OK</el-button>
              </el-space>
            </div>
          </template>
        </el-dialog>

      </el-container>
    </el-header>
    <el-main>
      <el-container :class="$attrs.contentClass" style="height:100%; padding-inline: 10px;">
        <el-aside :width="cLeftWidth"
                  style="padding: 0px;transition: width 0.4s ease-out, opacity 0.2s ease-in, visibility 0.2s ease-in; ">
          <slot name="contentLeft"></slot>
        </el-aside>
        <el-button style="border-radius: 0px !important;" v-if="props.contentLeftWidth && !isCollect" :icon="ArrowLeftBold" type="info" plain
                   class="aside-button" @click="collectContentLeft" />
        <el-button style="border-radius: 0px !important;" v-if="props.contentLeftWidth && isCollect" :icon="ArrowRightBold" type="info" plain
                   class="aside-button" @click="shrinkContentLeft" />
        <el-main style="padding: 0px; overflow: auto;">
          <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[15, 25, 50, 100]"
                         layout="sizes, , jumper, prev, pager, next, ->, slot" v-model:total="total" @current-change="handleChange"
                         @size-change="handleChange"
                         style="background-color: lightgrey;padding-inline: 10px;">
            {{ totalTitle }}
          </el-pagination>
          <el-table id="mainTable" border resizable @selection-change="handleSelectionChange" v-loading="loading"
                    element-loading-background="rgba(0, 0, 0, 0)"
                    :data="tableData" :scrollbar-always-on="true" table-layout="auto" @row-dblclick="handleDbClick"
                    @row-click="handleClick" @sort-change="handleSort" ref="tableRef" class-name="multiple-table"
                    :header-cell-class-name="(params: any) => { setHeaderClass(params) }" @select-all="selectAll"
                    :row-class-name="tableRowClassName">
            <el-table-column type="index" class-name="data-grid-selection-index-cell" width="1px" />
            <slot name="tableColumn"></slot>
            <el-table-column v-if="!props.hideOperation" fixed="right" :label="$t('common.table.operation')"
                             width="120">
              <template #default="scope">
                <CButtons :action="(scope.row.recordStatus?.startsWith('PA') ? 'Approve' : 'Edit')" v-if="showBtn(scope.row)"
                          :icon="Edit" link type="primary" style="font-size: 16px;" @click="handleEdit(scope.row)"></CButtons>
                <CButtons :action="'Delete'" v-if="showBtn(scope.row)" :icon="Delete" link type="primary"
                          style="font-size: 16px;" @click="handleDelete(scope.row)"></CButtons>
              </template>
            </el-table-column>
            <el-table-column v-if="props.isMultiple" fixed="right" type="selection" width="55" align="center" :selectable="props.selectable">
              <template #default="scope">
                <label v-if="!props.selectable || props.selectable(scope.row)" class="ep-checkbox">
                  <span class="ep-checkbox__input">
                    <span class="multiple-checkbox__inner">
                      <Select class="multiple-checked" />
                    </span>
                  </span>
                </label>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination id="mainTableBottomPage" v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[15, 25, 50, 100]"
                         layout="sizes, |, jumper, prev, pager, next, ->, slot" v-model:total="total" @current-change="handleChange"
                         @size-change="handleChange"
                         style="background-color: lightgrey;padding-inline: 10px;">
            {{ totalTitle }}
          </el-pagination>
          <slot name="contentBottom"></slot>
        </el-main>
      </el-container>
    </el-main>
  </el-container>
</template>

<script lang="ts" setup>
import { ref, reactive, getCurrentInstance, watch,nextTick } from 'vue'
import type { FormInstance } from 'element-plus'
import { ElMessageBox, ElMessage } from 'element-plus';
import {
  Edit,
  Delete,
  Plus,
  List,
  ArrowLeftBold,
  ArrowRightBold,
  Refresh,
  Minus,
  Select,
  CloseBold
} from '@element-plus/icons-vue'
import { useRouter } from 'vue-router';
import { useCookies } from "vue3-cookies";
import { json } from 'stream/consumers';
import axios from 'axios';
import { setInputLabel, getCommonDesc, getDateAndTime, selectedRow, clearSelectedCache, selectedAllRows, currentPageInfo } from '~/util/Function.js';
import { commonRules, showValidateMsg } from '~/util/Validators.js';
import { useRoute } from 'vue-router';
import * as func from '~/util/Function.js';

const route = useRoute()
const router = useRouter()
const { cookies } = useCookies()
const { proxy } = getCurrentInstance()
const formRef = ref<FormInstance>()
const props = defineProps(['url', 'params', 'searchParams', 'paramListData', 'hideOperation', 'showDetails', 'beforeEdit', 'isHideSearch', 'clickRow', 'dbClickRow', 'rules',
  'editRow', 'deleteRow', 'sortProp', 'beforeSearch', 'afterSearch', 'handleSelectionChange', 'hideEditBtn', 'topLeftWidth', 'contentLeftWidth', 'selectable', 'isHideParamList',
  'isMultiple', 'selectFirstRecord', 'changePageSize', 'keyName', 'isHideAdd'
]);
const currentPage = ref(1);
const pageSize = ref(15);
const total = ref(0);
const totalTitle = ref("Total 0 records")
const tableRef = ref();
const tableData = ref([]);
const paramData = ref([]);
const parameterTableRef  = ref(null);
let OKLoading = ref(false);
let functionId = "";
let paramList = {
  paraName: "",
  queryShow: "",
  sortShow: "",
  pageShow: null,
  defaultInd: "",
  sortOrder: "",
  parameter: ""
};
const parameter = ref("");
const param = ref({});
const orderBy = ref("");
const orderByDesc = ref([]);
const sortField = ref({});
const sortFieldName = ref({});
const showParam = ref(false);
const loading = ref(true) // mask layer
let favoriteOid = "";
const defaultIndData = ref("")
const rules = reactive(props.rules || {});
const formInline = reactive(props.searchParams);
const paramListData = reactive(props.paramListData);
const currentRow = ref()
const isDashBoard = ref(false)
const eventOid = ref(0);
const funcId = ref('');
const selectedRecord = ref({});
const lastSelected = ref(0);
const selectValue = ref(0);
const tmpParam= ref({});
import cloneDeep from 'lodash/cloneDeep';
import { commonCode } from '~/store/modules/commonCode'
import { clearEnterObjects } from "~/util/ModifiedValidate";
import { getTimeZone } from "~/util/DateUtils";

const commonCodeStore = commonCode();
currentPageInfo.setSearchPage();
const baseLoad = async (changePage) => {
  if ('/' === route.path) {
    isDashBoard.value = true;
  } else {
    isDashBoard.value = false;
  }

  commonCodeStore.fetchAll();

  loading.value = true;
  try {
    if(changePage){
      // tmpParam.value=props.params;
    }else{
      const tmp2=cloneDeep(param);
      tmpParam.value=tmp2.value;
    }

    let params = {
      data: {
        ...tmpParam.value,
        ...props.params,
        pageNumber: currentPage.value,
        pageSize: pageSize.value == null ? 15 : pageSize.value,
        orderBy: orderBy.value,
      },
      header: {timezone: getTimeZone(), lang:"en_US"}
    };

    const msg = await proxy.$axios.post(props.url, params);
    if (msg?.header.code === '000000') {
      selectAll([]);
      total.value = msg.data.totalCount;
      currentPage.value = msg.data.pageNumber;
      tableData.value = msg.data.items;
      if (router.currentRoute._value.name == 'MENU_USERADMIN_FORCE') {
        //forcelogout 自定义文案
        totalTitle.value = 'Select ' + selectValue.value+ ' out of ' + total.value + ' records';
      } else {
        totalTitle.value = 'Total ' + total.value + ' records';
      }
      if (props.afterSearch){
        props.afterSearch(param.value, tableData.value);
      }
      selectFirstRecord();
    } else {
      const errorMsg = msg?.header.message || 'Retrieve data failed'; 
      ElMessage.error(errorMsg);
    }
  } finally {
    loading.value = false;
    await nextTick();
    resizeTableScroll();
  }
}

const selectFirstRecord =  () => {
  if (props.selectFirstRecord) {
    setTimeout(() => {
      if (tableRef.value.data?.length > 0) {
        tableRef.value.$el.querySelector("tbody tr").click();
      }
    }, 150);
  }
}

const load = async () => {
  ElMessage.closeAll();
  await baseLoad();
}
const initParms = () => {
  proxy.$axios.get("/auth/api/v1/user/cache/byfuncid?funcId=" + router.currentRoute._value.name).then((body) => {
    if (body.success && body.data?.length > 0) {
      let data = body.data;
      paramData.value = body.data;
      let obj = "";
      let defaultData = {};
      for (var index in data) {
        if (data[index].defaultInd == 'Y') {
          obj = JSON.parse(data[index].queryCriteria);
          defaultData = data[index];
        }
      }
      if (obj == null || obj == "") {
        obj = JSON.parse(data[0].queryCriteria);
      }
      for (let key in obj.param) {
        formInline[key] = obj.param[key];
      }
      param.value = formInline;
      pageSize.value = obj.pageSize;
      props.changePageSize&&props.changePageSize(obj.pageSize);
      orderByDesc.value = obj.orderByDesc;
      sortField.value = obj.sortField;
      sortFieldName.value = obj.sortFieldName;
      orderBy.value = defaultData.sortOrder;
      defaultIndData.value = obj.defaultInd;
    }
    tmpParam.value=null;
    onSearch();
  });
}
const initLoad = () => {
  if(Object.keys(router.currentRoute.value.query).length > 0){
    param.value = router.currentRoute.value.query;
    baseLoad();
  }else{
    initParms();
  }
}


proxy.$axios.get("/auth/api/v1/user/menu?menuId=" + router.currentRoute._value.name).then((body) => {
  if (body.success && body.data) {
    functionId = body.data.funcId;
    clearEnterObjects();
  }
});
// rename the method name
const validInputedValue = async () => {
  let ret = func.validSearchInputValue("section.search-container input[searchtype]");
  // The original code move to Function.js file, method named validDateItemValue
  ret = func.validDateItemValue("section.search-container .search-date-error input[alt]") == false ? false : ret;
  return ret;
}

const emit = defineEmits(['search']);
const onSearch = async () => {
  ElMessage.closeAll();
  loading.value = true;
  refreshDate.value = getDateAndTime();
  emit('search');

  param.value = formInline;
  let ret = await beforeSearch(param.value);
  if (ret) {
    ret = await validInputedValue()
    ret = await formRef.value?.validate((valid, fields) => {
      if (!valid) {
        showValidateMsg(fieldsLabels, fields);
      }
    }) == false ? false : ret;
    if (ret) {
      await load();
    }
  }
  loading.value = false;
}

const beforeSearch = async (params) => {
  if (props.beforeSearch) {
    let ret = await props.beforeSearch(params);
    if (typeof ret === 'boolean') {
      if (!ret) {
        loading.value = false;
      }
      return ret;
    } else if (ret) {
      let msgs = [];
      if (Array.isArray(ret)) {
        msgs = ret;
      } else {
        msgs.push(ret);
      }
      let cnt = 0;
      for (let msg of msgs) {
        if (!msg) continue;
        ElMessage({
          message: msg,
          type: 'error',
          duration: 10000,
          offset: 100,
          showClose: true,
        });
        cnt = cnt + 1;
      }
      if (cnt > 0) return false;
    }
  }
  return true;
}

const onReset = (ref) => {
  // orderBy.value = "";
  // orderByDesc.value = [];
  // sortField.value = {};
  // sortFieldName.value = {};
  formInline.value = null;
  for (var i in formInline) {
    formInline[i] = null;
  }
  if (ref) {
    ref.resetFields();
    // tableRef.value.clearSort();
    let inputs = ref.$el.querySelectorAll("div.date-item-input input");
    for (let j = 0; j < inputs.length; j++) {
      inputs[j].value='';
    }
  }
}
const handleEdit = async (row) => {
  //row.currentOid = row.eventCategoryOid;
  //row.mkckOid="36";
  //debugger;
   console.log(row);
  if (props.beforeEdit) {
    let result = await props.beforeEdit(row);
    if (!result) {
      return;
    }
  }
  if (row.mkckOid) {
    let oid = row.pendingOid != null ? row.pendingOid : row.currentOid;
    let result = await proxy.$axios.post('/datamgmt/api/v1/makerchecker/lock', {
      flowMakerCheckerOid: row.mkckOid,
      eventOid: oid,
      recordStatus:row.recordStatus
    });
    if (!result.data) {
      return;
    }
  }

  if (row.recordStatus == 'R' || row.recordStatus == 'PD' || row.recordStatus == 'A') {
    let userName = row.sysUpdater;
    let funcId = proxy.$currentInfoStore.getCurrentFuncId();
    if("CSSCL_CASHM005"==funcId){
      funcId=row.eventName;
    }
    // 这里是为了判断有没有被锁起来
    let flag = await proxy.$axios.post('/datamgmt/api/v1/handler/lock', {
      eventName: funcId,
      eventOid: row.currentOid,
      recordStatus:row.recordStatus,
      version:row.version,
      flag: false,
      recordOid: row.pendingOid ? row.pendingOid : row.currentOid,
    });
    //当前用户和lastupdater不一样
    if (!flag.data) {
      return;
    }
    if (row.recordStatus != 'A') {
      let result = await proxy.$axios.get("/datamgmt/api/v1/handler?eventName=" + funcId + "&eventOid=" + row.currentOid + "&userName=" + userName);
      if (result.success) {
        let userData = result.data;
        if (!userData?.flag) {
          if (userData?.handlerId != null) {
            userName = userData.handlerId;
          }
          let alertMsg = result.alertMessage.toString();
          alertMsg = alertMsg.replace(/"/g, '');
          ElMessageBox.confirm(
            alertMsg,
            'Warning',
            {
              customStyle: {
                'max-width': '26%',
              },
              confirmButtonText: 'OK',
              cancelButtonText: 'Cancel',
              type: 'warning',
              closeOnClickModal: false
            })
            .then(() => {
              proxy.$axios.post('/datamgmt/api/v1/handler/lock', {
                eventName: funcId,
                eventOid: row.currentOid,
                recordStatus:row.recordStatus,
                version:row.version,
                flag: false,
                recordOid: row.pendingOid ? row.pendingOid : row.currentOid,
              }).then((body) => {
                if(!body.success){
                  return;
                }else{
                  props.showDetails(row);
                  proxy.$axios.post('/datamgmt/api/v1/handler', {
                  eventName: funcId,
                  eventOid: row.currentOid,
                  handlerId: proxy.$currentInfoStore.getUserInfo.userId
              });
                }
              });
             
             
            })
            .catch(() => {

            })
        } else {
          proxy.$axios.post('/datamgmt/api/v1/handler/lock', {
                eventName: funcId,
                eventOid: row.currentOid,
                recordStatus:row.recordStatus,
                version:row.version,
                flag: true,
                recordOid: row.pendingOid ? row.pendingOid : row.currentOid,
              })
          props.showDetails(row);
        }
      }
    } else {
      proxy.$axios.post('/datamgmt/api/v1/handler/lock', {
                eventName: funcId,
                eventOid: row.currentOid,
                recordStatus:row.recordStatus,
                version:row.version,
                flag: true,
                recordOid: row.pendingOid ? row.pendingOid : row.currentOid,
              })
      props.showDetails(row);
    }

  } else {
    props.showDetails(row);
  }

}

const handleDelete = (row) => {
  ElMessageBox.confirm('Are you sure to delete this record?')
      .then(() => {
        props.deleteRow(row);
      })
      .catch(() => {
        // catch error
      });
}
const handleChange = () => {
  baseLoad(true);
}
const handleSort = (obj) => {
  let sts = {
    ...sortField.value
  };
  let stsName = {
    ...sortFieldName.value
  };
  if (sts[obj.prop] && !obj.order) {
    delete sts[obj.prop];
    delete stsName[obj.prop];
  } else {
    sts[obj.prop] = obj.order;
    stsName[obj.prop] = obj.column.label;
  }
  sortField.value = sts;
  sortFieldName.value = stsName;
  changeSort(sts, stsName);
  baseLoad(true);
}

const hadnleAdd = () => {
  props.showDetails({});
}
const handleDbClick = (row) => {
  let isOnlyQueryAprvRec = true;
  if (props.dbClickRow) {
    let dbClickRslt = props.dbClickRow(row);
    if (typeof dbClickRslt?.isOnlyQueryAprvRec === 'boolean') {
      isOnlyQueryAprvRec = dbClickRslt?.isOnlyQueryAprvRec;
    }else if(typeof dbClickRslt === 'boolean'){
      if(!dbClickRslt){
        return;
      }
    }
  }
  if (isOnlyQueryAprvRec && row?.mkckAction === 'C') {
    ElMessageBox.alert('Record is not yet taken effective', 'Warning', {
      confirmButtonText: 'OK',
      type: 'warning',
    });
    return;
  }
  props.showDetails(row, true);
}

const handleClick = (row: any, column: any, event: Event) => {
  selectedRow("oid", selectedRecord, tableRef, lastSelected, row, column, event, props.selectable, props.isMultiple);
  if (props.clickRow) {
    props.clickRow(row);
  }
}

const selectAll = (selection) => {
  if(selection&&selection.length>0) {
    selectedAllRows("oid", selection, selectedRecord, tableRef, lastSelected, props.selectable);
  } else {
    clearSelectedCache(selectedRecord, tableRef, lastSelected);
  }

}

const setHeaderClass = (params: any) => {
  params.column.order = sortField.value[params.column.property];
}

const changeSort = (sts, stsName) => {
  if (Object.keys(sts).length == 0) {
    orderBy.value = "";
    orderByDesc.value = [];
  } else {
    let obv = "";
    let obvNames = [];
    for (let key in sts) {
      obvNames.push({
        name: stsName[key],
        order: sts[key],
        code: key
      });
      if (props.sortProp && props.sortProp[key]) {
        let o = sts[key].charAt(0).toUpperCase();
        let d = "";
        for (let i = 0; i < props.sortProp[key].length; i++) {
          let ele = props.sortProp[key][i];
          d += ";" + ele + "-" + o;
        }
        obv += ";" + d.substring(1);
      } else {
        if("recordStatus" === key){
          obv += ";recordStatus-" + sts[key].charAt(0).toUpperCase();
          obv += ";mkckAction-" + sts[key].charAt(0).toUpperCase();
        } else {
          obv += ";" + key + "-" + sts[key]?.charAt(0)?.toUpperCase();
        }
      }
    }
    orderBy.value = obv.substring(1);
    orderByDesc.value = obvNames;
  }
}

const deleteOrder = (tag) => {
  let sts = {
    ...sortField.value
  };
  let stsName = {
    ...sortFieldName.value
  };
  delete sts[tag.code];
  delete stsName[tag.code];
  sortField.value = sts;
  sortFieldName.value = stsName;
  changeSort(sts, stsName);
  baseLoad(true);
}

const cacheUserSearchParam = () => {
  OKLoading.value = true
  proxy.$axios.get("/auth/api/v1/user/cache/byfuncid?funcId=" + router.currentRoute._value.name).then((body) => {
    OKLoading.value = false
    if (body.success && body.data.length > 0) {
      paramData.value = body.data;
      nextTick(()=>{
        parameterTableRef.value!.setCurrentRow(body.data[0])
      })
    }
  }).catch((e)=>{
    OKLoading.value = false
  });
  showParam.value = true;
  paramListCurrentRow.value = {};
  param.value = formInline;
}

const saveParam = async () => {
  if (!parameter.value) {
    ElMessage({
      message: "Please input Parameter Name",
      type: 'error',
    })
  } else {
    if (await validInputedValue()) {
      paramList.sortShow = "";
      paramList.queryShow = "";
      if (orderByDesc.value != null) {
        for (var i in orderByDesc.value) {
          paramList.sortShow += orderByDesc.value[i].name + " " + orderByDesc.value[i].order + ".";
        }
      }
      let lableArray = [];
      for (let index in formInline) {
        //部分页面参数带clientAccountOid、clientMasterOid，要排除，如果还有其他页面也带oid，也要排除
        if (index != 'clientAccountOid' && index != 'clientMasterOid') {
          // lableArray.push({ code: index, name: paramListData.index || formInline[index] });
          if (index == 'status') {
            lableArray.push({ code: index, name: paramListData.status });
          } else if (index == 'recordStatus') {
            lableArray.push({ code: index, name: paramListData.recordStatus });
          } else if (index == 'multipleRecordStatus') {
            lableArray.push({ code: index, name: paramListData.multipleRecordStatus });
          } else if (index == 'codeType') {
            lableArray.push({ code: index, name: paramListData.codeType });
          } else if (index == 'sourceSys') {
            lableArray.push({ code: index, name: paramListData.sourceSys });
          } else if (index == 'identifierType') {
            lableArray.push({ code: index, name: paramListData.identifierType });
          } else if (index == 'clientType') {
            lableArray.push({ code: index, name: paramListData.clientType });
          } else if (index == 'bankCode') {
            lableArray.push({ code: index, name: paramListData.bankCode });
          } else if (index == 'aeCode') {
            lableArray.push({ code: index, name: paramListData.aeCode });
          } else if (index == 'fileType') {
            lableArray.push({ code: index, name: paramListData.fileType });
          } else if (index == 'channel') {
            lableArray.push({ code: index, name: paramListData.channel });
          } else if (index == 'processingStatus') {
            lableArray.push({ code: index, name: paramListData.processingStatus });
          } else if (index == 'processStatus') {
            lableArray.push({ code: index, name: paramListData.processStatus });
          } else if (index == 'reconStatus') {
            lableArray.push({ code: index, name: paramListData.reconStatus });
          } else if (index == 'isLock') {
            lableArray.push({ code: index, name: paramListData.isLock });
          } else if (index == 'type') {
            lableArray.push({ code: index, name: paramListData.type });
          } else if (index == 'frequency') {
            lableArray.push({ code: index, name: paramListData.frequency });
          } else if (index == 'searchLevel') {
            lableArray.push({ code: index, name: paramListData.searchLevel });
          } else if (index == 'accountStatus') {
            lableArray.push({ code: index, name: paramListData.accountStatus });
          }else {
            lableArray.push({ code: index, name: formInline[index] });
          }
        }
      }
      if (formInline != null) {
        for (let i = 0; i < lableArray?.length; i++) {
          lableArray[i].code = labelVal[i];
          if (lableArray[i].name != null && lableArray[i].name != "" && lableArray[i].code) {
            paramList.queryShow += lableArray[i].code + "=" + lableArray[i].name + ".";
          }
        }
      }
      OKLoading.value = true
      proxy.$axios.post("/auth/api/v1/user/cache", {
        userId: cookies.get('username'),
        funcId: router.currentRoute._value.name,
        queryCriteria: JSON.stringify({
          param: {
            ...param.value,
          },
          orderByDesc: orderByDesc.value,
          sortFieldName: sortFieldName.value,
          sortField: sortField.value,
          pageSize: pageSize.value,
        }),
        sortOrder: orderBy.value,
        sortShow: paramList.sortShow,
        paraName: parameter.value,
        queryShow: paramList.queryShow,
        pageShow: pageSize.value,
        defaultInd: paramList.defaultInd,
      }).then((body) => {
        if (body.success) {
          parameter.value = "";
          proxy.$axios.get("/auth/api/v1/user/cache/byfuncid?funcId=" + router.currentRoute._value.name).then((body) => {
            OKLoading.value = false
            if (body.success && body.data.length > 0) {
              paramData.value = body.data;
              nextTick(()=>{
                parameterTableRef.value!.setCurrentRow(body.data[body.data.length-1])
              })
            }
          }).catch((e)=>{
            OKLoading.value = false
          });
        }
      }).catch((e)=>{
        OKLoading.value = false
      });
    }
  }

}

const saveDefault = async () => {
  let obj = "";

  if (currentRow.value != null) {
    formInline.value = null;
    for (var i in formInline) {
      formInline[i] = null;
    }
    obj = JSON.parse(currentRow.value.queryCriteria);
    for (let key in obj.param) {
      formInline[key] = obj.param[key];
    }
    orderByDesc.value = obj.orderByDesc;
    pageSize.value = obj.pageSize;
    sortFieldName.value = obj.sortFieldName;
    sortField.value = obj.sortField;
    props.changePageSize&&props.changePageSize(obj.pageSize);
    onSearch();
  }
  showParam.value = false;
}
function exit() {
  showParam.value = false;
}

const showBtn = (row) => {
  if (props.hideEditBtn && props.hideEditBtn(row)) {
    return false;
  }
  return true;
}

function changeDefaultInd(row) {
  favoriteOid = row.userFavoritesSettingOid;
  for (var i in paramData.value) {
    if (paramData.value[i].userFavoritesSettingOid != row.userFavoritesSettingOid) {
      paramData.value[i].defaultInd = 'N';
    }
  }
  proxy.$axios.post("/auth/api/v1/user/cache/saveDefault?funcId=" + router.currentRoute._value.name + "&oid=" + favoriteOid);
}
const paramListCurrentRow = ref({});
function deleteRow() {
  favoriteOid = paramListCurrentRow.value.userFavoritesSettingOid;
  if(!favoriteOid) {
    return;
  }
  if(paramListCurrentRow.value.paraName == 'default'){
    ElMessage.closeAll();
    ElMessage({
      message:"cannot delete default record!",
      type: 'error',
      duration: 10000,
      offset: 100,
      showClose: true,
    });
    return;
  }
  OKLoading.value = true
  proxy.$axios.delete("/auth/api/v1/user/cache?funcId=" + router.currentRoute._value.name + "&oid=" + favoriteOid).then((body) => {
    if (body.success) {
      proxy.$axios.get("/auth/api/v1/user/cache/byfuncid?funcId=" + router.currentRoute._value.name).then((body) => {
        OKLoading.value = false
        if (body.success && body.data) {
          paramData.value = body.data;
          nextTick(()=>{
            parameterTableRef.value!.setCurrentRow(body.data[0])
          })
        }
      }).catch((e)=>{
        OKLoading.value = false
      });;
    }
  }).catch((e)=>{
    OKLoading.value = false
  });;
}

function handleSelectionChange(val) {
  paramListCurrentRow.value = val;
  if(val!=null &&  val.length){
    selectValue.value = val.length;
    totalTitle.value = 'Select ' + selectValue.value+ ' out of ' + total.value + ' records';
  }
  currentRow.value = val;
  if (props.handleSelectionChange) {
    props.handleSelectionChange(val);
  }
}

const cLeftWidth = ref(props.contentLeftWidth ? props.contentLeftWidth : '0px');
const isCollect = ref(false);

const collectContentLeft = () => {
  let width = props.contentLeftWidth.replace("px", "");
  isCollect.value = true;
  cLeftWidth.value = width * 0 + "px";
}
const refreshDate =ref();
const shrinkContentLeft = () => {
  let width = props.contentLeftWidth.replace("px", "");
  isCollect.value = false;
  cLeftWidth.value = width + "px";
}
const tableRowClassName = ({ row }) => {
  let selectedClass="";
  let oid = row["oid"];
  if (selectedRecord.value[oid]) {
    selectedClass= ' selected-row';
  }
  if ('/' === route.path) {
    if ('PA' === row.status||'PA1' === row.status||'PA2' === row.status) {
      if (!isToday(row.sysUpdateDate)) {
        return 'pending-row' + selectedClass;
      }
    }
    if ('R' === row.status || 'E' === row.status) {
      if (!isToday(row.sysUpdateDate)) {
        return 're-yel-row' + selectedClass;
      } else {
        return 're-row' + selectedClass;
      }
    }
    if ('A' === row.status) {
      if (!isToday(row.sysUpdateDate)) {
        return 'approve-yel-row' + selectedClass;
      } else {
        return 'approve-row' + selectedClass;
      }
    }
  }
  return selectedClass;
}


refreshDate.value= getDateAndTime();

const refreshTime = () => {
  refreshDate.value= getDateAndTime();
}
const refreshPage = () => {
  window.location.reload();
}
const isToday = (dateString) => {
  const date = new Date(dateString)

  const now = new Date()

  // 只比较年、月、日
  return date.getFullYear() === now.getFullYear() &&
      date.getMonth() === now.getMonth() &&
      date.getDate() === now.getDate()
}

const fieldsLabels = {fields:{}};
let labelVal = [];
const init = () => {
  setInputLabel(null,rules,true, ".search-container ");
  let titles = [];
  for (let idx in formInline) {
    titles.push(idx);
  }
  let btns = document.querySelectorAll("button");
  for (let i = 0; i < btns?.length; i++) {
    let e = btns[i];
    e.setAttribute("alt", e.textContent);
  }
  let lableBtn = document.querySelectorAll("label, div.ep-form-item__label");
  for (let i = 0; i < lableBtn?.length; i++) {
    let lbl = lableBtn[i].textContent;
    let hideLabel = lableBtn[i].closest("div[hideLabel]");
    if (hideLabel) {
      lbl = hideLabel.getAttribute("hideLabel");
    }
    if (lbl) {
      labelVal.push(lbl)
      fieldsLabels.fields[titles[i]] = lbl;
    }
  }
}


const goHome = () => {
  proxy.$axios.post('/datamgmt/api/v1/makerchecker/unlock', {
    logout: true,
  });
  proxy.$axios.post('/datamgmt/api/v1/handler/unlock', {
    flag: true,
  });
  proxy.$router.push("/");
}

setTimeout(() => {
  init();
  initLoad();
}, 800);

watch(formInline, (val)=>{
  // for(let key in formInline) {
  // if (key == 'multipleRecordStatus') {
  //   continue;
  // }
  // let rule = rules[key];
  // if (!rule) {
  //   rules[key] = [];
  // }
  // if (!rules[key].includes(commonRules.name)) {
  //   rules[key].push(commonRules.name);
  // }
  // }
});

const resizeTableScroll = () => {
  let scroll = document.querySelector("#mainTable .ep-scrollbar__bar.is-horizontal");
  if (scroll) {
    if (document.getElementById("mainTableBottomPage").getBoundingClientRect().top > window.innerHeight - 50
        && document.getElementById("mainTable").getBoundingClientRect().top <= window.innerHeight - 60) {
      scroll.style.position = 'fixed';
      scroll.style.bottom = '50px';
      scroll.style.left = document.getElementById("mainTableBottomPage").getBoundingClientRect().left + 'px';
    } else {
      scroll.style.position = '';
      scroll.style.bottom = '';
      scroll.style.left = '';
    }
  }
}

window.addEventListener('scroll', () => {
  resizeTableScroll();
});

window.addEventListener('resize', () => {
  resizeTableScroll();
});

defineExpose({
  load,
  formRef,
  formInline,
  onSearch,
  selectedRecord,
  handleDbClick,
  refreshTime
})
</script>

<style>
.demo-form-inline .el-input {
  --el-input-width: 220px;
}

.demo-form-inline {
  position: relative;
}

.demo-form-inline .el-select {
  --el-select-width: 220px;
}

.parameter-list {
  padding: 0px;
}

.parameter-list .ep-dialog__header {
  background-color: var(--ep-color-primary);
;
  padding: 0px 0px 0px 10px;
}

.parameter-list .ep-dialog__header .ep-dialog__title,
.parameter-list .ep-dialog__header .ep-dialog__close {
  color: white;
}

.parameter-list .ep-dialog__header button {
  height: 32px;
  width: 32px;
}

.aside-button {
  padding: 0px;
  border: 0px;
}

tr.ep-table__row.approve-row {
  color: green;
}

tr.ep-table__row.approve-yel-row {
  color: green;
}

tr.ep-table__row.re-row {
  color: red;
}

tr.ep-table__row.re-yel-row {
  color: red;
  background: lightyellow !important;
}

tr.ep-table__row.pending-row {
  background: lightyellow !important;
}
</style>
<style scoped></style>