<template>
  <BasePanel :searchParams="searchParams" :paramListData='paramListData' url="/cashmgmt/api/v1/projection/adjustment/list" :params="{ modeEdit: 'Y' }"
    :beforeSearch="beforeSearch" :showDetails="showDetails" :rules="rules" :deleteRow="deleteRow" :editRow="editRow" ref="tableRef">
    <template v-slot:searchPanel="slotProps">
      <FormRow>
        <ElFormItemProxy :label="$t('csscl.project.custodyAccNum')" prop="custAccNo" label-width="175px">
          
          <GeneralSearchInput v-model="slotProps.form.custAccNo" showDesc="false" searchType="custodyAcct" />

        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.project.custodyShortName')" prop="custAccShortName" label-width="155px">
          <el-input v-model="slotProps.form.custAccShortName" maxlength="50" class="text-none" style="width:300px"/>
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.project.cashAccNum')" prop="cashAccNo" label-width="165px">
          <el-input v-model="slotProps.form.cashAccNo" maxlength="18" />
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy :label="$t('csscl.project.functionType')" prop="funcType" label-width="175px">
          <Select v-model="slotProps.form.funcType" type='FUNCTION_TYPE' />
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.project.transactionRef')" prop="txnRef" label-width="155px">
          <el-input v-model="slotProps.form.txnRef" maxlength="50" style="width:300px"/>
        </ElFormItemProxy>
        <ElFormItemProxy></ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy>
          <ElFormItemProxy :label="$t('csscl.project.instructionDateFrom')" prop="instrDateFrom" label-width="175px">
            <DateItem v-model="slotProps.form.instrDateFrom"
                      :title="$t('message.earlier.equal.dateto', [$t('csscl.project.instructionDateFrom'), $t('csscl.project.instructionDateTo')] )"
                      type="date" />
          </ElFormItemProxy>
          <ElFormItemProxy :label="$t('common.title.date.to')" :hideLabel="$t('csscl.project.instructionDateTo')" prop="instrDateTo"  label-width="45px">
            <DateItem v-model="slotProps.form.instrDateTo" type="date"/>
          </ElFormItemProxy>
        </ElFormItemProxy>
        <ElFormItemProxy>
          <ElFormItemProxy :label="$t('csscl.project.valueDateFrom')" prop="valueDateFrom" label-width="155px">
            <DateItem v-model="slotProps.form.valueDateFrom"
                      :title="$t('message.earlier.equal.dateto', [$t('csscl.project.valueDateFrom'), $t('csscl.project.valueDateTo')] )"
                      type="date"/>
          </ElFormItemProxy>
          <ElFormItemProxy :label="$t('common.title.date.to')" :hideLabel="$t('csscl.project.valueDateTo')" prop="valueDateTo" label-width="45px">
            <DateItem v-model="slotProps.form.valueDateTo" type="date"/>
          </ElFormItemProxy>
        </ElFormItemProxy>
        <ElFormItemProxy>
          <ElFormItemProxy :label="$t('csscl.project.projectValueDateFrom')" prop="projectValueDateFrom" label-width="165px">
            <DateItem v-model="slotProps.form.projectValueDateFrom"
                      :title="$t('message.earlier.equal.dateto', [$t('csscl.project.projectValueDateFrom'), $t('csscl.project.projectValueDateTo')] )"
                      type="date"/>
          </ElFormItemProxy>
          <ElFormItemProxy :label="$t('common.title.date.to')" :hideLabel="$t('csscl.project.projectValueDateTo')"  prop="projectValueDateTo" label-width="45px">
            <DateItem v-model="slotProps.form.projectValueDateTo" type="date"/>
          </ElFormItemProxy>
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy label-width="175px" :label="$t('common.title.recordStatus')" prop="multipleRecordStatus">
          <MultipleSelect v-model="slotProps.form.multipleRecordStatus" v-model:desc="paramListData.multipleRecordStatus"  />
        </ElFormItemProxy>
       <ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.project.lastModifyDateFrom')" prop="lastModifyDateFrom" label-width="155px">
            <DateItem v-model="slotProps.form.lastModifyDateFrom"
                      :title="$t('message.earlier.equal.dateto', [$t('csscl.project.lastModifyDateFrom'), $t('csscl.project.lastModifyDateTo')] )"
                      type="date"/>
          </ElFormItemProxy>
          <ElFormItemProxy :label="$t('common.title.date.to')" :hideLabel="$t('csscl.project.lastModifyDateTo')"  prop="lastModifyDateTo" label-width="45px">
            <DateItem v-model="slotProps.form.lastModifyDateTo" type="date"/>
          </ElFormItemProxy> 
       </ElFormItemProxy>
        <ElFormItemProxy label-width="165" :label="$t('csscl.project.lastModifyBy')" prop="sysUpdater">
            <GeneralSearchInput v-model="slotProps.form.sysUpdater" maxlength="10" showDesc="false"
                codeTitle="csscl.project.lastModifyBy" searchType="user" />
        </ElFormItemProxy>
      </FormRow>

    </template>
    <template v-slot:tableColumn>
      <el-table-column sortable="custom" prop="custAccNo" :label="$t('csscl.project.custodyAccNum')" header-align="center"  />
      <el-table-column sortable="custom" prop="custAccShortName" :label="$t('csscl.project.custodyShortName')" header-align="center"  />
      <el-table-column sortable="custom" prop="cashAccNo" :label="$t('csscl.project.cashAccNum')"  header-align="center"/>
      <el-table-column sortable="custom" prop="funcType" :label="$t('csscl.project.functionType')" header-align="center" >
        <template #default="scope">
            {{ getCommonDesc('FUNCTION_TYPE', scope.row.funcType) }}
          </template>
      </el-table-column>
      <el-table-column sortable="custom" prop="txnRef" :label="$t('csscl.project.transactionRef')" header-align="center"  />
      <el-table-column sortable="custom" prop="instrDt" :label="$t('csscl.project.instructionDate')" header-align="center"  />
      <el-table-column sortable="custom" prop="valueDate" :label="$t('csscl.project.valueDate')" header-align="center"  />
      <el-table-column sortable="custom" prop="adjProjDate" :label="$t('csscl.project.projectValueDate')" header-align="center"/>
      <el-table-column sortable="custom" prop="drCrInd" :label="$t('csscl.project.drcr')" header-align="center" width="40" >
        <template #default="scope">
            {{ getCommonDesc('DR_CR', scope.row.drCrInd) }}
          </template>
      </el-table-column>
      <el-table-column sortable="custom" prop="currencyCode" :label="$t('csscl.project.currency')" header-align="center"  />
      <el-table-column sortable="custom" prop="amount"  :label="$t('csscl.project.amount')" header-align="center" >
            <template #default="scope">
              {{ gridTxnAmtFmt(scope.row) }}
            </template>
      </el-table-column>
      <el-table-column sortable="custom" prop="sysUpdateDate" :label="$t('csscl.project.lastModifyDate')" header-align="center"/>
      <el-table-column sortable="custom" prop="sysUpdater" :label="$t('csscl.project.lastModifyBy')" header-align="center" align="right" />
      <el-table-column sortable="custom" prop="recordStatus" :label="$t('common.title.recordStatus')" header-align="center" >
        <template #default="scope">
          {{ getRecordStatusDesc(scope.row) }}
        </template>
      </el-table-column>
    </template>
  </BasePanel>
  <Details ref="detailsRef" :reload="reload"/>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance, watchEffect } from 'vue';
import {
  Search
} from '@element-plus/icons-vue'
import BasePanel from '~/pages/base/index.vue'
import Details from './Details.vue'
import { getCommonDesc, getRecordStatusDesc, checkBeforeCurDt, checkDateFromTo, checkDateBetween, showErrorMsg,getDrcr,thousFormat } from '~/util/Function.js';
import { commonRules } from '~/util/Validators.js';
import { formatCashAccountNumber} from '~/util/AccountUtils.js'; 

const tableRef = ref();
const { proxy } = getCurrentInstance()
const paramListData = ref({});
const searchParams = {
  //顺序和上面绑定参数一致
  custAccNo:"",
  custAccShortName:"",
  cashAccNo:"",
  funcType:"",
  txnRef:"",
  instrDateFrom:"",
  instrDateTo:"",
  valueDateFrom:"",
  valueDateTo:"",
  projectValueDateFrom:"",
  projectValueDateTo:"",
  recordStatus:"",
  lastModifyDateFrom:"",
  lastModifyDateTo:"",
  multipleRecordStatus:[],
};

const formatAmount = (row, column, cellValue, index) => {
  if (typeof cellValue === 'string') {
    cellValue = parseFloat(cellValue);
  }
  console.log(cellValue)
  if(cellValue !== null){
    return cellValue.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
  }
}
function gridTxnAmtFmt(row){
    if(row.drCrInd === "D" && row.amount !=0 &&row.amount !=null ){
        return "(" + thousFormat(Math.abs(row.amount), 2) + ")";
    }
    return thousFormat(row.amount, 2);
}

const beforeSearch = () =>{
  // Start SK-COMMON-0267 LiShaoyi 2024/10/31
  if (!beforeSearch.flag) {
    beforeSearch.flag = true;
    return false;
  }
}

const rules = reactive({
  instrDateFrom:[
    commonRules.earlierEquDt(()=>{ return searchParams.instrDateTo }, proxy.$t('csscl.project.instructionDateTo')),
  ],
  valueDateFrom:[
    commonRules.earlierEquDt(()=>{ return searchParams.valueDateTo }, proxy.$t('csscl.project.valueDateTo')),
  ],
  projectValueDateFrom:[
    commonRules.earlierEquDt(()=>{ return searchParams.projectValueDateTo }, proxy.$t('csscl.project.projectValueDateTo')),
  ],
  lastModifyDateFrom:[
    commonRules.earlierEquDt(()=>{ return searchParams.lastModifyDateTo }, proxy.$t('csscl.project.lastModifyDateTo')),
  ],
});

const reload = () => {
  tableRef.value.load();
}
const detailsRef = ref();
const showDetails = (row, disabled) => {
  detailsRef.value.showDetails(row, disabled);
}
const deleteRow = (row) => {
  console.log("Delete ...");
}
const editRow = (row,disabled) => {
  detailsRef.value.editRow(row,disabled);
}
//paramList 参数显示用的
function recordStatusType(value){
  paramListData._value.recordStatus =  getCommonDesc('RECORD_STATUS', value);
}

</script>

<style></style>