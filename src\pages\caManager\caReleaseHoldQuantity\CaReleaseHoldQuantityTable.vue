<template>
    <EditGrid ref="editGridRef" :data="holdTableData" :formData="caHoldQuantityForm"
      :is-show-search="true" :showAddDeleteButtons="false"
      :formRules="caHoldQuantityFormRules" :isMultiple="true"
      :onSaveForm="handleFormSave" :beforeSearch="beforeSearch"
      :handleSelectionChange="handleSelectionChange"
      :handleChangePage="handleChangePage" :beforeChangePage="handleBeforeChangePage"
      :onQuery="handleQuery" :enableRecordStatusHighlight="true" :onReset="onSubSearchReset"
      :fieldDisabledRules="fieldDisabledRules">

      <template v-slot:searchPanel="slotProps">
        <FormRow>
          <ElFormItemProxy :label="$t('csscl.ca.common.custodyAccountNo')" label-width="150" prop="opCtryRegionCode">
            <GeneralSearchInput v-model="filterParams.tradingAccountCode" style="width:200px" searchType="custodyAcct"
              showDesc="false" codeTitle="csscl.acctCode.clientAccountOid"
              codeDescTitle="csscl.acctCode.accountShortName" />
          </ElFormItemProxy>
          <ElFormItemProxy :label="$t('csscl.ca.common.custodyAccountName')" label-width="180" prop="opCtryRegionCode">
            <InputText :disabled="false" v-model="filterParams.tradingAccountName" maxlength="100"
              style="width: 200px" />
          </ElFormItemProxy>
          <ElFormItemProxy :label="$t('csscl.ca.common.custodianAccount')" label-width="150" prop="opCtryRegionCode">
            <InputText :disabled="false" v-model="filterParams.custodianAccountNumber" maxlength="100"
              style="width: 200px" />
          </ElFormItemProxy>
          <ElFormItemProxy></ElFormItemProxy>
        </FormRow>
      </template>

      <template #tableColumnFront>
        <el-table-column prop="tradingAccountCode" :label="$t('csscl.ca.common.custodyAccountNo')" align="center" />
        <el-table-column prop="tradingAccountName" :label="$t('csscl.ca.common.custodyAccountName')" align="center"/>
        <el-table-column prop="custodianAccountNumber" :label="$t('csscl.ca.common.custodianAccount')" align="center"/>
        <el-table-column prop="caEventOptionSequenceNumber" :label="$t('csscl.ca.common.optionNo')" align="center"/>
        <el-table-column prop="optionPayDateForCash" :label="$t('csscl.ca.common.optionPayDateCash')" align="center"/>
        <el-table-column prop="optionPayDateForScrip" :label="$t('csscl.ca.common.optionPayDateScrip')" align="center"/>
        <el-table-column prop="selEntitleQuantity" :label="$t('csscl.ca.common.entitledQuantity')" align="center"/>
        <el-table-column prop="scripHoldQuantity" :label="$t('csscl.ca.common.holdQuantity')" align="center"/>
        <el-table-column prop="scripHoldStatus" :label="$t('csscl.ca.common.holdStatus')" align="center">
          <template #default="scope">
            {{ getCommonDesc('CA_HOLD_STATUS', scope.row.scripHoldStatus) }}
          </template>
        </el-table-column>
        <el-table-column prop="recordStatus" width="220" :label="$t('csscl.ca.common.recordStatus')" align="center">
          <template #default="scope">
            {{ getCommonDesc('RECORD_STATUS', scope.row.recordStatus) }}
            <span v-if="scope.row.recordStatus && scope.row.recordStatus !== 'A'">
              for {{ getCommonDesc('MKCK_ACTION', scope.row.mkckAction) }}
            </span>
          </template>
        </el-table-column>
      </template>
      <template #editForm="formSlot">
        <FormRow>
          <FormItemSign :detailsRef="formSlot.detailsRef" :label="$t('csscl.ca.common.eventRefNo')"
            prop="caEventReferenceNumber" label-width="150px" :disabled="isFieldDisabled('caEventReferenceNumber')">
            <InputText :disabled="true" v-model="formSlot.formData.form.caEventReferenceNumber" maxlength="100"
              style="width: 200px" />
          </FormItemSign>
          <FormItemSign :detailsRef="formSlot.detailsRef" :label="$t('csscl.ca.common.custodyAccountNo')"
            prop="tradingAccountCode" label-width="160px">
            <InputText :disabled="true" v-model="formSlot.formData.form.tradingAccountCode" maxlength="100"
              style="width: 200px" />
          </FormItemSign>
          <FormItemSign :detailsRef="formSlot.detailsRef" :label="$t('csscl.ca.common.custodyAccountName')"
            prop="tradingAccountName" label-width="170px" :disabled="isFieldDisabled('tradingAccountName')">
            <InputText :disabled="true" v-model="formSlot.formData.form.tradingAccountName" maxlength="100"
              style="width: 300px" />
          </FormItemSign>
          <FormItemSign />
        </FormRow>
        <FormRow>
          <FormItemSign :detailsRef="formSlot.detailsRef" :label="$t('csscl.ca.common.entitlementQuantity')"
            prop="selEntitleQuantity" label-width="150px" >
            <InputNumber v-model="formSlot.formData.form.selEntitleQuantity" scale="0"
              style="width: 200px" :disabled="isFieldDisabled('selEntitleQuantity')" />
          </FormItemSign>
          <FormItemSign :detailsRef="formSlot.detailsRef" :label="$t('Hold Quantity')"
            prop="scripHoldQuantity" label-width="160px" >
             <el-input v-model="formSlot.formData.form.scripHoldQuantity" :disabled="isFieldDisabled('scripHoldQuantity')"
             style="width: 200px" type="number"/>
          </FormItemSign>
          <FormItemSign label-width="470px" />
          <FormItemSign />
        </FormRow>
      </template>
      
    </EditGrid>
    <el-space>
      <el-button type="primary" @click="releaseClick">
        {{ $t('csscl.ca.common.release') }}
      </el-button>
    </el-space>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue';
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus';
import { getOid, saveMsgBox, getCommonDesc } from '~/util/Function.js';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import { commonRules } from '~/util/Validators.js';
import EditGrid from '~/components/Ca/CaEditGrid.vue';
import { getTimeZone } from "~/util/DateUtils";

const { proxy } = getCurrentInstance();
const props = defineProps(['reload']);
// 定义表格数据
const holdTableData = ref([]);
const editGridRef = ref();
const tableSelectRowsRef = ref([]);

const caEntitlementBatchUpdateUrl = "/bff/ca/api/v1/ca-event-client-entitlement/edit-batch-ca-event-client-entitlement";

const handleSelectionChange = (val) => {
  tableSelectRowsRef.value = val;
}

const caEventForm = reactive({
  form: {}
});
const caHoldQuantityForm = reactive({
  form: {}
});
const caHoldQuantityFormRules = reactive<FormRules<caHoldQuantityForm>>({
  scripHoldQuantity: [
    commonRules.required,
  ],
  tradingAccountCode: [
    commonRules.required,
  ],
  tradingAccountName: [
    commonRules.required,
  ],
  custodianAccountNumber: [
    commonRules.required,
  ],
  caEventReferenceNumber:[
    commonRules.required,
  ],
  selEntitleQuantity: [
    commonRules.required,
  ],
});
const filterParams = reactive({
  tradingAccountCode: '',
  tradingAccountName: '',
  custodianAccountNumber: ''
})

const generateRequestparam = (data) => {
  let params = {
    header: {timezone: getTimeZone(), lang:"en_US"},
    data: data
  };
  return params;
}

const releaseClick = () => {
  let tradingAccountCodes = tableSelectRowsRef.value.map(row => row.tradingAccountCode);
  console.log("releaseClick --> " + tradingAccountCodes);
}

const isResponseSuccess = (response) => {
  if (!response || !response.header) {
    return false;
  }
  return response.header.code === '000000';
}

const getErrorMessage = (errorMessage) => {
  return errorMessage || 'Unknown error';
}

const handleFormSave = (formData) => {
  formData.isChanged = true;
  // 查找是否已存在该记录
  const index = holdTableData.value.findIndex(item => item.oid === formData.oid);
  if (index !== -1) {
    // 如果找到匹配记录，更新现有记录
    Object.assign(holdTableData.value[index], formData);
    // 标记为更新状态
    // holdTableData.value[index].mkckAction = 'U';
  } else {
    holdTableData.value.push(formData);
  }
}

// 处理保存
const handleSave = async () => {
  // // 收集所有变更，包括新增、修改和删除的记录
  // const recordsToSave = holdTableData.value.filter(
  //   record => record.isChanged === true || record.isDelete === true
  // );

  // if (recordsToSave.length === 0) {
  //   ElMessage({
  //     message: proxy.$t('message.data.not.modify'),
  //     type: 'error',
  //     duration: 10000,
  //     offset: 100,
  //     showClose: true,
  //   });
  //   return;
  // }
  
  // let params = generateRequestparam({ 
  //       caEventReferenceNumber: caEventForm.form.caEventReferenceNumber,
  //       version: caEventForm.form.version,
  //       recordStatus: "PD",
  //       mkckAction: "U",
  //       caEventClientEntitlementDTOList: recordsToSave
  //     });

  // try {
  //   const response = await proxy.$axios.post(caEntitlementBatchUpdateUrl,
  //   params
  //   );


  //   if (isResponseSuccess(response)) {
  //     ElMessage.success('Save successfully');
  //     // 重新加载数据
  //     loadHoldData(caEventForm.form.caEventReferenceNumber);
  //   } else {
  //     const errorMsg = getErrorMessage(response?.header?.message);
  //     ElMessage.error('Save fail: ' + errorMsg);
  //   }
  // } catch (error) {
  //   ElMessage.error('Save fail: ' + getErrorMessage (error?.message));
  // }

};

const showDetails = async (row:any, isdoubleCheck:any) => {
  if (editGridRef.value) {
    if (row.caEventReferenceNumber) {
      loadHoldData(row.caEventReferenceNumber);
      caEventForm.form = row;
    }
  }
}

// 从后端获取数据的方法
const loadHoldData = async (eventReferenceNumber:any) => {
  if (!eventReferenceNumber) return;

  try {
    let params = generateRequestparam({
        caEventReferenceNumber: eventReferenceNumber,
        // pageNumber: currentPageNumber.value,
        // pageSize: currentPageSize.value,
        ...filterParams
      });

    const response = await proxy.$axios.post("/bff/ca/api/v1/ca-event-option-client-selection/get-client-selection-hold-page-list", params);

    if (isResponseSuccess(response)) {
      holdTableData.value = response.data.items;
    } else {
      const errorMsg = getErrorMessage(response?.header?.message);
      ElMessage.error('Load Ca Entitlement Details data fail: ' + errorMsg);
    }
  } catch (error) {
    const errorMsg = getErrorMessage(error?.message);
    ElMessage.error('Load Ca Entitlement Details data fail: ' + errorMsg);
  }
};
const handleQuery = async (param:any) => {
  await loadHoldData(caEventForm.form.caEventReferenceNumber);
};
const onSubSearchReset = () => {
  filterParams.tradingAccountCode = '';
  filterParams.tradingAccountName = '';
  filterParams.custodianAccountNumber = '';
};
const handleChangePage = async (newPage:any) => {
  await loadHoldData(caEventForm.form.caEventReferenceNumber);
};
const beforeSearch = async (search:any, params:any) => {
  return true;
};

const handleBeforeChangePage = async () => {
  return true;
}

// 定义字段禁用规则
const fieldDisabledRules = {
  // 始终禁用的字段
  caEventReferenceNumber: true,
  tradingAccountCode: true,
  tradingAccountName: true,
  selEntitleQuantity: true,
  // 根据编辑状态禁用的字段
  scripHoldQuantity: false,
};
const clearData = async() => {
  holdTableData.value = [];
  editGridRef.value.total = 0;
  editGridRef.value.cancelForm();
};
// 添加计算属性用于判断字段是否禁用
const isFieldDisabled = (fieldName) => {
  const rule = fieldDisabledRules[fieldName];
  if (typeof rule === 'boolean') return rule;
  return false;
};

defineExpose({
  clearData,
  // details,
  showDetails,
});

</script>

<style>

</style>