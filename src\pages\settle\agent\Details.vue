<template>
    <BaseDetails ref="details" :handleSave="handleSave" :reload="props.reload" :viewOriginalForm="viewOriginalForm">
        <el-form :validateOnRuleChange="false" :disabled="formDisabled" ref="agentFormRef" style="width: 100%" :model="agentForm.form" :rules="rules" status-icon>
            <FormRow>
                <FormItemSign label-width="380" :detailsRef="details" :label="$t('common.title.opCtryRegionCode')" prop="opCtryRegionCode" style="flex: 1.5;">
                    <CtryRegionSearchInput v-model="agentForm.form.opCtryRegionCode" 
                        style="width: 120px"
                        disabled
                        showDesc="false"
                        opCtryRegion />
                </FormItemSign>
                <FormItemSign label-width="280" :detailsRef="details" :label="$t('csscl.agent.clearingAgentCode')" prop="clearingAgentCode">                    
                    <GeneralSearchInput v-model="agentForm.form.clearingAgentCode" 
                        disabled
                        showDesc="false"
                        style="width: 160px"
                        searchType="clearingAgentCode" />
                </FormItemSign>
                <FormItemSign :detailsRef="details" :label="$t('common.title.status')" label-width="70" prop="status">
                    <el-col :span="8">  
                        <Select v-model="agentForm.form.status" style="width:160px" disabled type='STATUS' />
                    </el-col>
                </FormItemSign>
            </FormRow>
            <FormRow>
                <FormItemSign label-width="380" :detailsRef="details" :label="$t('csscl.agent.clientMasterOid')" prop="clientCode" style="flex: 1.5;">
                    <el-input disabled v-model="agentForm.form.clientCode" style="width:120px" />
                </FormItemSign>
            </FormRow>
            <FormRow>
                <FormItemSign label-width="380" :detailsRef="details" :label="$t('csscl.agent.shortName')" prop="agentShortName" style="flex: 1.5;">
                    <el-input disabled v-model="agentForm.form.agentShortName" style="width: 320px" input-style="text-transform:none"/>
                </FormItemSign>
                <FormItemSign label-width="280" :detailsRef="details" :label="$t('csscl.agent.ctryRegionCode')" prop="ctryRegionCode">

                    <CtryRegionSearchInput v-model="agentForm.form.ctryRegionCode" 
                        disabled 
                        style="width: 160px" 
                        showDesc="false" />
                                   
                </FormItemSign>
                <ElFormItemProxy></ElFormItemProxy>
            </FormRow>
            <FormRow>
                <FormItemSign :detailsRef="details" prop="fundHouseInd" style="flex: 1.5;">
                    <el-checkbox disabled v-model="agentForm.form.fundHouseInd" true-value="Y" false-value="N" >{{ $t('csscl.agent.fundHouseInd')  }}</el-checkbox>
                </FormItemSign>
                <FormItemSign label-width="280" :detailsRef="details" :label="$t('csscl.agent.settleInstrIncomingChannel')" prop="settleInstrIncomingChannel">
                    <Select v-model="agentForm.form.settleInstrIncomingChannel" style="width:160px" disabled type="SETTLE_INSTR_INCOMING_CHANNEL_CODE"/>
                </FormItemSign>
                <ElFormItemProxy></ElFormItemProxy>
            </FormRow>
            <el-tabs v-model="activeName" @tab-change="tabsClick" type="card" >
                <el-tab-pane :label="$t('csscl.agent.incomingChannel')" name="channel" active :lazy="true">
                    <channel  :details="details" :agentForm="agentForm" />
                </el-tab-pane>
                <el-tab-pane :label="$t('csscl.agent.custoianAccount')" :key="accKey" name="account" :lazy="true">
                    <custodianAccount @change-value="updateFlag" :details="details" :agentForm="agentForm" />
                </el-tab-pane>
            </el-tabs>
        </el-form>
    </BaseDetails>
   
</template>

<script lang="ts" setup>
import { ref, reactive, getCurrentInstance, nextTick } from 'vue';
import type { FormInstance, FormRules } from 'element-plus'
import BaseDetails from '~/pages/base/Details.vue';
import channel from './channel.vue'
import custodianAccount from './custodianAccount.vue'
import { getCommonDesc, getOid, saveMsgBox, updateListValue } from '~/util/Function.js';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import CtryRegionSearchInput from '~/pages/base/CtryRegionSearchInput.vue';
import { addEnterObj4List, addModifiedFlag } from "~/util/ModifiedValidate";

const { proxy } = getCurrentInstance();
const props = defineProps(['reload']);
const details = ref();
const formDisabled = ref(false);
const activeName = ref("channel");
let lightyellowTabs = [];
let pendingOid = null;
const accKey = ref(0);
//Start SIT-Cristin-172,amor ********
let changeFlag = false;
//End SIT-Cristin-172,amor ********

const editRow = (row,disabled,newId) => {
    if(row?.isApproveDetail && disabled){
        // Start R2411A-58175 LiShaoyi 2024/09/13
        row.afterImage.agentShortName = row.afterImage.shortName;
        row.afterImage.settleInstrIncomingChannel = row.afterImage.settleInstrInChannel;
        // End R2411A-58175 LiShaoyi 2024/09/13
        details.value.currentRow = row.afterImage;
        Object.assign(agentForm.form, row.afterImage);
        agentForm.form.clearingAgentOid = row.eventPkey;
        agentForm.form.isApproveDetail = true;
    } else {
        let oid = newId || (row?.currentOid && getOid(row, disabled, proxy));
        if(oid){
            proxy.$axios.get("/datamgmt/api/v1/clearingagent?objectId=" + oid).then((body) => {
                if(body.success) {
                    agentForm.form = body.data;
                    details.value.currentRow = body.data;
                    agentForm.form.exbDataList = [];
                    agentForm.form.clearingAgentOid = oid;
                    //Start SIT-Cristin-172,amor ********
                    changeFlag = true;
                    //End SIT-Cristin-172,amor ********
                    //Start B2411A-10371,amor,********
                    if (!disabled && body.data.recordStatus !='A' && (body.data.pendingOid || agentForm.form.clearingAgentOid)) {
                    //End B2411A-10371,amor,********
                        proxy.$axios.post("/datamgmt/api/v1/clearingagent/hightyellow?objectId="+(body.data.pendingOid || agentForm.form.clearingAgentOid)).then((body) => {
                            if(body.success) {
                                if (body.data.data.length > 0) {
                                    lightyellowTabs.push("tab-account");
                                    addEnterObj4List(body.data.data, "custodianAccExbOid")
                                    agentForm.form.exbDataList = body.data.data;
                                    tabsClick();
                                }
                            }
                        });
                    }

                }
            });
        }
    }
   
}
const updateFlag = (value)=>{
    changeFlag = value;
}
const viewOriginalForm = (pendingOid, isDisabled) => {
        formDisabled.value = isDisabled;
        viewOriginalForm.form  = agentForm.form;
        proxy.$axios.get("/datamgmt/api/v1/clearingagent?objectId="+pendingOid).then((body) => {
              if(body.success) {
                  agentForm.form = body.data;
                  details.value!.currentRow.beforeImage = body.data.beforeImage;
              }
          });

}
const handleSave = async (searchValid, isOnlyValidate, unPopping) => {
    // let result = await agentFormRef.value.validate((valid, fields) => {
    //     if (valid) {
            
    //     } else {
    //         console.log('error submit!', fields)
    //     }
    // });
    if (isOnlyValidate) {
        return true;
    }
    //Start SIT-Cristin-172,amor ********
    // if(changeFlag && !unPopping){
    //     ElMessage.closeAll();
    //     ElMessage({
    //       message: proxy.$t('message.data.not.modify'),
    //       type: 'error',
    //       duration: 10000,
    //       offset: 100,
    //       showClose: true,
    //     });
    //     return false;
    // }
    //End SIT-Cristin-172,amor ********
    if (searchValid && await saveMsgBox(unPopping)) {
        agentForm.form.exbVPO = agentForm.form.exbDataList;
        if (agentForm.form.clearingAgentOid) {
            const msg = await proxy.$axios.patch("/datamgmt/api/v1/clearingagent", {
                ...agentForm.form,
            });
            accKey.value += 1;
            agentForm.form.exbDataList=[];
            details.value.writebackId(msg.data);
            editRow(null,null,msg.data);
            return msg.success;
            
        } else {
            const msg = await proxy.$axios.post("/datamgmt/api/v1/clearingagent", {
                ...agentForm.form,
            });
            accKey.value ++;
            agentForm.form.exbDataList=[];
            details.value.writebackId(msg.data);
            editRow(null,null,msg.data);
            return msg.success;
        }
    }
    return false;
}
const showDetails = (row, isdoubleCheck) => {
    activeName.value = 'channel';
    lightyellowTabs = [];
    if(isdoubleCheck||row.recordStatus==='PA'){
        formDisabled.value = true;
    }else{
        formDisabled.value = false;
    }
    details.value.showDetails(row, formDisabled.value)
    editRow(row,isdoubleCheck);
    // hightyellow();
}

const hightyellow = () => {
    nextTick(()=>{

    });
}

defineExpose({
    details,
    editRow,
    showDetails,
    viewOriginalForm
});
const tabsClick = (tag) => {
    nextTick(()=>{
        for(let i = 0; i < lightyellowTabs.length; i++) {
            document.querySelector( "#"+lightyellowTabs[i] ).className += " lightyellow";
        }
    });
}
// --------------------------------------------

const agentFormRef = ref();
const agentForm = reactive({
    form: {
        opCtryRegionCode: "",
        ctryRegionCode: "",
        userId: "",
        userName: "",
        email: "",
        phoneExt: "",
        phoneNo: "",
        apprCurrency: "",
        apprLimitAmt: "",
        mclassCode: "",
        status: "",
        clrAgentInChannelOid:"",
        clearingAgentOid:"",
        custodianAccOid:"",
        custaccExbVPO:{},
        channelVPO:{},
        accVPO:{},
        exbVPO:[],
        cashVPO:{},
        clearingAgentCode:"",
        agentShortName:"",
        settleInstrIncomingChannel:"",
        fundHouseInd:"",
        clientCode:"",
        exbDataList:[],
        listData:[],
        isApproveDetail:false
    }
})

const rules = reactive({
    // opCtryRegionCode: [
    //     { required: true, message: 'Please input ', trigger: 'blur' },
    //     { min: 1, max: 13, message: 'Length should be 1 to 13', trigger: 'blur' },
    // ],
    // ctryRegionCode: [
    //     { required: true, message: 'Please input ', trigger: 'blur' },
    //     { min: 1, max: 50, message: 'Length should be 1 to 50', trigger: 'blur' },
    // ],
    // userId: [
    //     { required: true, message: 'Please input ', trigger: 'blur' },
    //     { min: 1, max: 12, message: 'Length should be 1 to 12', trigger: 'blur' },
    // ],
    // userName: [
    //     { required: true, message: 'Please input ', trigger: 'blur' },
    //     { min: 1, max: 50, message: 'Length should be 1 to 50', trigger: 'blur' },
    // ],
    // email: [
    //     { required: true, message: 'Please input ', trigger: 'blur' },
    //     { min: 1, max: 50, message: 'Length should be 1 to 50', trigger: 'blur' },
    // ],
    // phoneExt: [
    //     { required: true, message: 'Please input ', trigger: 'blur' },
    //     { min: 1, max: 5, message: 'Length should be 1 to 5', trigger: 'blur' },
    // ],
    // phoneNo: [
    //     { required: true, message: 'Please input ', trigger: 'blur' },
    //     { min: 1, max: 18, message: 'Length should be 1 to 18', trigger: 'blur' },
    // ],
    // apprCurrency: [
    //     { required: true, message: 'Please input ', trigger: 'blur' },
    //     { min: 1, max: 5, message: 'Length should be 1 to 5', trigger: 'blur' },
    // ],
    // apprLimitAmt: [
    //     { required: true, message: 'Please input ', trigger: 'blur' },
    // ],
    // mclassCode: [
    //     { required: true, message: 'Please input ', trigger: 'blur' },
    //     { min: 1, max: 10, message: 'Length should be 1 to 10', trigger: 'blur' },
    // ],
    // status: [
    //     { required: true, message: 'Please input ', trigger: 'blur' },
    // ],
})

</script>

<style></style>