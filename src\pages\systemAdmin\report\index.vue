<template>
  <BasePanel :searchParams="searchParams" :paramListData='paramListData' url="/rptsched/api/v1/report/scheduler/list" :params="{ modeEdit: 'Y' }"
    :showDetails="showDetails" :deleteRow="deleteRow" :editRow="editRow" :rules="rules" :hideEditBtn="hideEditBtn" :beforeSearch="beforeSearch" ref="tableRef">
    <template v-slot:searchPanel="slotProps">
      <FormRow>
        <ElFormItemProxy label-width="190px" :label="$t('csscl.reportScheduler.schedulerId')" prop="schedulerId">
          <el-input style="width: 240px" maxlength="10" v-model="slotProps.form.schedulerId" />
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.reportCenter.search.reportDesc')" prop="reportTemplateCode"  label-width="190px">
          <GeneralSearchInput 
            v-model="slotProps.form.reportTemplateCode"
            maxlength="8"
            inputStyle="width:180px"
            style="width: 400px"
            codeTitle="csscl.reportCenter.search.reportDesc"
            searchType="outDocFileType" />
        </ElFormItemProxy>

        <ElFormItemProxy label-width="140px" :label="$t('common.title.status')" prop="status">
          <Select v-model="slotProps.form.status" style="width: 180px" type='STATUS' :change="statusType(slotProps.form.status)"/>
        </ElFormItemProxy>
      </FormRow>

      <FormRow>
        <ElFormItemProxy label-width="190px" :label="$t('csscl.reportScheduler.deliveryChannel')" prop="channel">
          <Select v-model="slotProps.form.channel" style="width:180px"  type='DELIVERY_CHANNEL' :change="channelType(slotProps.form.channel)"/>
        </ElFormItemProxy>
        <ElFormItemProxy label-width="190px" :label="$t('csscl.jobScheduler.ftgidCode')" prop="ftgidCode">
          <GeneralSearchInput v-model="slotProps.form.ftgidCode"
            style="width: 400px"
            inputStyle="width:180px"
            searchType="ftgidCode" />
        </ElFormItemProxy>
        <ElFormItemProxy label-width="140px" :label="$t('common.title.recordStatus')" prop="multipleRecordStatus">
          <MultipleSelect v-model="slotProps.form.multipleRecordStatus" v-model:desc="paramListData.multipleRecordStatus"  />
        </ElFormItemProxy>
      </FormRow>

      <FormRow>
        <ElFormItemProxy :label="$t('csscl.acctCode.custodyAcctNumber')" prop="tradingAccountCode" label-width="190px" style="width:30%">
          <GeneralSearchInput v-model="slotProps.form.tradingAccountCode"
                searchType="custodyAcct"
                showDesc="false"
                :params="{var1:slotProps.form.clientMasterOid}"
                :change="(val)=>{
                  slotProps.form.clientAccountOid='';
                }"
                :dbClick="(row)=>{
                  slotProps.form.clientAccountOid=row.var2;
                }"
                />
          </ElFormItemProxy>        
        <ElFormItemProxy label-width="190px" :label="$t('csscl.reportScheduler.description')" prop="description" style="margin-left: -33.33%;">
          <el-input v-model="slotProps.form.description" style="width:800px"  maxlength="100" input-style="text-transform:none" />
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy label-width="190px" :label="$t('csscl.jobScheduler.frequency')" prop="frequency">
          <Select v-model="slotProps.form.frequency" style="width: 180px" type='FREQUENCY' :change="frequencyType(slotProps.form.frequency)"/>
        </ElFormItemProxy>
        <ElFormItemProxy >
            <ElFormItemProxy label-width="190px" :label="$t('csscl.reportScheduler.effectiveStartDateFrom')" prop="effectiveDateFrom" >
              <DateItem v-model="slotProps.form.effectiveDateFrom"
                        :title="$t('message.earlier.equal.dateto', [$t('csscl.reportScheduler.effectiveStartDateFrom'), $t('csscl.reportScheduler.effectiveStartDateTo')] ) + '\r' +
                        $t('message.date.range.error', [365] ) " />
            </ElFormItemProxy>
            <ElFormItemProxy label-width="40px" :label="$t('common.title.date.to')" :hideLabel="$t('csscl.reportScheduler.effectiveStartDateTo')" prop="effectiveDateTo" >
              <DateItem v-model="slotProps.form.effectiveDateTo"
                        :title="$t('message.date.range.error', [365] ) " />
            </ElFormItemProxy>
        </ElFormItemProxy>
      <ElFormItemProxy>
        <ElFormItemProxy label-width="140px" :label="$t('csscl.reportScheduler.scheduleTimeFrom')" prop="submitTimeFrom">
          <el-time-picker v-model="slotProps.form.submitTimeFrom"  style="width: 130px"  format="HH:mm" value-format="HH:mm"/>
        </ElFormItemProxy>
        <ElFormItemProxy label-width="40px" :label="$t('common.title.date.to')" prop="submitTimeTo" >
        <el-time-picker v-model="slotProps.form.submitTimeTo"  style="width:130px" format="HH:mm" value-format="HH:mm"/>
        </ElFormItemProxy>
      </ElFormItemProxy>

      </FormRow>
    </template>


    <template v-slot:tableColumn>
      <el-table-column align="left" header-align="center" sortable="custom" prop="schedulerId"
        :label="$t('csscl.reportScheduler.schedulerId')" width="250" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="reportTemplateCode"
        :label="$t('csscl.reportScheduler.reportId')" />
        <el-table-column align="left" header-align="center" sortable="custom" prop="channel"
        :label="$t('csscl.reportScheduler.deliveryChannel')" width="180">
        <template #default="scope">
          {{ getCommonDesc('DELIVERY_CHANNEL', scope.row.channel) }}
        </template>
        </el-table-column>
        <el-table-column align="left" header-align="center" sortable="custom" prop="ftgidCode"
        :label="$t('csscl.jobScheduler.ftgidCode')" width="180" />
        <el-table-column align="left" header-align="center" sortable="custom" prop="frequency"
        :label="$t('csscl.jobScheduler.frequency')" width="180" >
        <template #default="scope">
          {{ getCommonDesc('FREQUENCY', scope.row.frequency) }}
        </template>
      </el-table-column>
      <el-table-column align="left" header-align="center" sortable="custom" prop="description"
        :label="$t('csscl.reportScheduler.description')" width="180" />

      <el-table-column align="left" header-align="center" sortable="custom" prop="effectiveDateFrom"
        :label="$t('csscl.reportScheduler.effectiveStartDate')" width="180" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="effectiveDateTo"
        :label="$t('csscl.reportScheduler.effectiveEndDate')" width="180" />

      <el-table-column align="left" header-align="center" sortable="custom" prop="status"
        :label="$t('common.title.status')" width="90">
        <template #default="scope">
          {{ getCommonDesc('STATUS', scope.row.status) }}
        </template>
      </el-table-column>
      <el-table-column align="left" header-align="center" sortable="custom" prop="recordStatus"
        :label="$t('common.title.recordStatus')" width="190">
        <template #default="scope">
          {{ getRecordStatusDesc(scope.row) }}
          </template>
      </el-table-column>

    </template>
  </BasePanel>
  <Details ref="detailsRef" :reload="reload" />
</template>

<script lang="ts" setup>
import { ref, getCurrentInstance, watchEffect, reactive } from 'vue';
import BasePanel from '~/pages/base/index.vue'
import Details from './Details.vue'
import  { getCommonDesc, getRecordStatusDesc, checkDateFromTo, checkDateBetween } from '~/util/Function.js';
import  { commonRules } from '~/util/Validators.js';

const jobNameList = ref({});
const { proxy } = getCurrentInstance() as any
const paramListData = ref({});
const searchParams = {
  //顺序和上面绑定参数一致
  jobId:null,
  reportTemplateCode:null,
  status:null,
  channel:null,
  ftgidCode:null,
  multipleRecordStatus:[],
  cashAcctNumber:null,
  description:null,
  frequency:null,
  // Start R2411A-65007 LiShaoyi 2024/09/27
  effectiveDateFrom:'',
  effectiveDateTo:'',
  // End R2411A-65007 LiShaoyi 2024/09/27
  submitTimeFrom:null,
  submitTimeTo:null,
};
const tableRef = ref();
const reload = () => {
  tableRef.value.load();
}
const detailsRef = ref();
const showDetails = (row,disabled) => {
  detailsRef.value.showDetails(row,disabled);
}
const deleteRow = (row) => {
  proxy.$axios.delete("/rptsched/api/v1/report/scheduler?flowJobControlOid="+ getOid(row)).then((body) => {
    if (body.success) {
      tableRef.value.load();
    }
  });
}
const getOid = (row) => {
  return row?.flowJobControlOid;
}

const editRow = (row) => {
  detailsRef.value.editRow(row);
}

const hideEditBtn = (row) => {
	// if (row.channel == "CIB" || row.channel == "FTS") {
	// 	return true;
	// }
  if (row.reportTemplateCode == "R000101C" || row.reportTemplateCode == "R000201C") {
		return false;
	}
	return true;
}
// Start ISSUE 670, LiShaoyi, 2025/04/07
const rules = reactive({
  effectiveDateFrom:[
    commonRules.required,
    commonRules.earlierEquDt(()=>{ return searchParams.effectiveDateTo }, proxy.$t('csscl.reportScheduler.effectiveEndDate')),
    // Start R2411A-65007 LiShaoyi 2024/09/23
    commonRules.diffDate(365,()=>{ return searchParams.effectiveDateTo }, proxy.$t('csscl.reportScheduler.effectiveEndDate')),
    // End R2411A-65007 LiShaoyi 2024/09/23
  ],
  effectiveDateTo:[
    commonRules.required,
  ]
});
// End ISSUE 670, LiShaoyi, 2025/04/07
//-------------------------------
const beforeSearch = () =>{
// let dateFrom = tableRef.value.formInline.effectiveDateFrom;
// let dateTo = tableRef.value.formInline.effectiveDateTo;

// let msg = checkDateFromTo(proxy, dateFrom, dateTo, proxy.$t('csscl.reportScheduler.effectiveStartDateFrom'));
// if(msg){
//   return msg;
// }

// msg = checkDateBetween(proxy, dateFrom, dateTo, 365);
// return msg;  
}

watchEffect(() => {
  proxy.$axios.post("/rptsched/api/v1/inter/scheduler/job/config/list", {
    current: 1,
    pageSize: 1000,
    param: {
      opCtryRegionCode: 'HK'
    },
  }).then((body) => {
    if (body.success) {
      jobNameList.value = body.data.data;
    }
  });
});

//paramList 参数显示用的
function statusType(value){
  paramListData._value.status =  getCommonDesc('STATUS', value);
}
function channelType(value){
  paramListData._value.channel =  getCommonDesc('DELIVERY_CHANNEL', value);
}
function recordStatusType(value){
  paramListData._value.recordStatus =  getCommonDesc('RECORD_STATUS', value);
}
function frequencyType(value){
  paramListData._value.frequency =  getCommonDesc('FREQUENCY', value);
}
</script>

<style></style>