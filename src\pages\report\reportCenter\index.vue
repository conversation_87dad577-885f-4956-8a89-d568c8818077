<template>
  <BasePanel :searchParams="searchParams" :paramListData='paramListData' url="/rptsched/api/v1/report/center/list"
    :params="{ modeEdit: 'Y' }" :showDetails="showDetails" :editRow="editRow" hideOperation="true" :clickRow="clickRow"
    :selectFirstRecord="true" :changePageSize="(num:number) => subtableRef.setPageSize(num)" :showSummary="true" :hideFooterPaging="true"
    :beforeSearch="beforeSearch" :afterSearch="afterSearch" ref="tableRef" :rules="rules">
    <template v-slot:searchPanel="slotProps">
      <FormRow>
        <ElFormItemProxy label-width="200" :label="$t('csscl.reportCenter.search.reportDesc')" prop="reportTemplateCode" style="flex: 2;">
          <GeneralSearchInput 
            :params="{var1:proxy.$currentInfoStore.getUserInfo.userId}"
            v-model="slotProps.form.reportTemplateCode"
            maxlength="45"
            style="width:450px"
            inputStyle="width:130px"
            codeTitle="csscl.reportCenter.search.reportDesc"
            searchType="rptCenterReportId" />
        </ElFormItemProxy>
        <ElFormItemProxy label-width="150" :label="$t('csscl.reportCenter.sysCreator')" prop="sysCreator">
          <GeneralSearchInput style="width: 180px" showDesc="false"
            :params="{var1:proxy.$currentInfoStore.getUserInfo.userId}"
            searchType="user" />
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy>
          <FormRow>
            <ElFormItemProxy label-width="200" :label="fieldsDtl.fields.rptRequestDtFrom" prop="rptRequestDtFrom" style="flex: 2;" >
              <DateItem v-model="slotProps.form.rptRequestDtFrom"
                        :title="$t('message.earlier.equal.curdate', [fieldsDtl.fields.rptRequestDtFrom] ) + '\r' +
                        $t('message.earlier.equal.dateto', [fieldsDtl.fields.rptRequestDtFrom, fieldsDtl.fields.rptRequestDtTo] ) + '\r' +
                        $t('message.date.range.error', [7] ) "/>
            </ElFormItemProxy>
            <ElFormItemProxy label-width="30" :label="$t('common.title.date.to')" :hideLabel="fieldsDtl.fields.rptRequestDtTo" prop="rptRequestDtTo"
              style="width:210px" >
              <DateItem v-model="slotProps.form.rptRequestDtTo"
                        :title="$t('message.earlier.equal.curdate', [fieldsDtl.fields.rptRequestDtTo] ) + '\r' +
                        $t('message.date.range.error', [7] ) "/>
            </ElFormItemProxy>
          </FormRow>
        </ElFormItemProxy>
        <ElFormItemProxy>
          <FormRow>
            <ElFormItemProxy label-width="190" :label="$t('csscl.reportCenter.search.sysCreateDtTimeFrom')"
              prop="rptRequestTimeFrom" style="flex: 2;">
              <el-time-picker v-model="slotProps.form.rptRequestTimeFrom" style="width:100px" format="HH:mm"
                value-format="HH:mm" />
            </ElFormItemProxy>
            <ElFormItemProxy label-width="30" :label="$t('common.title.date.to')"
              prop="rptRequestTimeTo" style="width:250px">
              <el-time-picker v-model="slotProps.form.rptRequestTimeTo" style="width: 100px" format="HH:mm"
                value-format="HH:mm" />
            </ElFormItemProxy>
          </FormRow>
        </ElFormItemProxy>
        <ElFormItemProxy label-width="150" :label="$t('csscl.reportCenter.processingStatus')" prop="processingStatus">
          <Select v-model="slotProps.form.processingStatus" style="width: 150px" type="PROCESSING_STATUS"
            :change="processingStatusType(slotProps.form.processingStatus)" />
        </ElFormItemProxy>
      </FormRow>
    </template>
    <template v-slot:tableColumn>
      <el-table-column header-align="center" sortable="custom" prop="rptRequestDtFmt"
        :label="$t('csscl.reportCenter.rptRequestDate')" width="475">
        <template #default="scope">
          {{ dateFormat(scope.row.rptRequestDtFmt) }}
        </template>
      </el-table-column>
      <el-table-column header-align="center" sortable="custom" prop="reportTemplateCode"
        :label="$t('csscl.reportCenter.reportTemplateCode')" />
      <el-table-column header-align="center" sortable="custom" prop="reportName"
        :label="$t('csscl.reportCenter.reportDesc')" />
      <el-table-column header-align="center" :sortable="false" prop="procProgress"
        :label="$t('csscl.reportCenter.processingProgress')" width="475" />
    </template>

    <template #contentBottom>
      <br />
      <div style="margin: 0px; padding: 3px; border-bottom: 2px solid lightgrey;">
        <el-text style="color: #b31a25;font: 16px bold;" class="mx-1" size="large">Detail</el-text>
      </div>
      <DetailPanel url="/rptsched/api/v1/report/center/detail/list" :params='reqParams' :hideOperation="true"
        :isMultiple="true" :selectable="selectable" 
        ref="subtableRef">
        <template v-slot:tableColumn>
          <!-- <el-table-column header-align="center" sortable="custom" prop="rptRequestDtFmt"
            :label="$t('csscl.reportCenter.rptRequestDtTime')" width="300" /> -->
          <el-table-column header-align="center" sortable="custom" prop="reportReqDt"
            :label="$t('csscl.reportCenter.reportReqDt')" width="300" />
          <el-table-column header-align="center" sortable="custom" prop="sysCreator"
            :label="$t('csscl.reportCenter.sysCreator')" width="300" />
          <el-table-column header-align="center" sortable="custom" prop="processStatusDesc"
            :label="$t('common.title.status')" width="300" />
          <el-table-column header-align="center" sortable="custom" prop="rptFileName"
            :label="$t('csscl.reportCenter.rptFileName')"  />
          <el-table-column header-align="center" :label="$t('common.button.open')" width="80">
            <template #default="scope">
              <el-icon-folder-opened v-if="scope.row.processStatusDesc == 'Completed'"
                style="width:20px;height:20px;color:orange" @click="openClick(scope.row)" />
            </template>
          </el-table-column>
          <el-table-column header-align="center" :label="$t('common.button.download')" width="80">
            <template #default="scope">
              <el-icon-download v-if="scope.row.processStatusDesc == 'Completed'"
                style="width:20px;height:20px;color:darkorange" @click="downloadClick(scope.row)" />
            </template>
          </el-table-column>
        </template>
      </DetailPanel>

      <br />
      <el-space style="float:right;">
        <el-button type="primary" @click="downloadSelection">
          Download Selected
        </el-button>
      </el-space>
    </template>
  </BasePanel>
</template>

<script lang="ts" setup>
import { ref, reactive, getCurrentInstance, onMounted, watch } from 'vue';
import { ElMessageBox } from 'element-plus';
import type { FormRules } from 'element-plus';
import BasePanel from '~/pages/base/index.vue'
import DetailPanel from '~/pages/base/indexNoCondition.vue'
import { getCommonDesc, dateFormat, checkBeforeCurDt, checkDateFromTo, checkDateBetween, downloadFile, downloadOpenFile, downloadBatchFile } from '~/util/Function.js';
import { commonRules, showValidateMsg } from '~/util/Validators.js';

const { proxy } = getCurrentInstance()
const paramListData = ref({});
const searchParams = {
  //顺序和上面绑定参数一致
  reportTemplateCode: "",
  sysCreator: "",
  rptRequestDtFrom: "",
  rptRequestDtTo: "",
  rptRequestTimeFrom: "",
  rptRequestTimeTo: "",
  processingStatus: "",
};
interface RuleForm {
  rptRequestDtFrom: Date
  rptRequestDtTo: Date
}

const fieldsDtl = {
  fields:{
    rptRequestDtFrom: proxy.$t('csscl.reportCenter.search.sysCreateDtFrom'),
    rptRequestDtTo: proxy.$t('csscl.reportCenter.search.sysCreateDtTo'),
  }
}

const rules = reactive<FormRules<RuleForm>>({
  rptRequestDtFrom: [
    commonRules.required,
    commonRules.earlierEquCurDate,
    commonRules.earlierEquDt(()=>{ return searchParams.rptRequestDtTo }, fieldsDtl.fields.rptRequestDtTo),
    commonRules.diffDate(7, ()=>{ return searchParams.rptRequestDtTo }, fieldsDtl.fields.rptRequestDtTo),
  ],
  rptRequestDtTo: [
    commonRules.required,
    commonRules.earlierEquCurDate,
  ],
});

const isLoadTable = ref(true);

function openClick(row) {
  if(row?.processStatusDesc=="Completed"){
    let funcId = proxy.$currentInfoStore.getCurrentFuncId();
    let params = {
      funcId: funcId,
      rptFilePath: row.rptFilePath,
      rptFileName: row.rptFileName
    };
    downloadOpenFile("/rptsched/api/v1/report/center/download", params);
  } else {
    ElMessageBox.alert("File not exists, please contact Administrator.", 'Warning');
  }
} 
function downloadClick(row) {
  if(row?.processStatusDesc=="Completed"){
    let funcId = proxy.$currentInfoStore.getCurrentFuncId(); 
    let params = {
      funcId: funcId,
      rptFilePath: row.rptFilePath,
      rptFileName: row.rptFileName
    };
    downloadFile("/rptsched/api/v1/report/center/download", params);
  } else {
    ElMessageBox.alert("File not exists, please contact Administrator.", 'Warning');
  }
}

const reqParams = reactive({ 
  modeEdit: 'Y', 
  processingStatus: searchParams.processingStatus,
  rptGenLogOids:[]
});
const tableRef = ref();
const subtableRef = ref();
const detailsRef = ref();
const showDetails = (row, disabled) => {

}

const clickRow = (row) => {  
  if (row) {
    reqParams["rptGenLogOids"] = row?.rptGenLogOids;
    reqParams["processingStatus"] = searchParams.processingStatus;
    subtableRef.value.load();
  } else {
    subtableRef.value.resetTable();
  }
}
const afterSearch = (params, data) => {
  subtableRef.value.setTableData(null);
  // if(data && data.length !== 0){
  //   isLoadTable.value = true;
  //   let num = 0;
  //   for(let i=0; i<data.length; i++){
  //     let row = data[i];
  //     if(row?.procProgress){
  //       let procProgress = row?.procProgress;
  //       let ppList = procProgress.split("/");
  //       if(ppList[0] === ppList[1]){
  //         num++;
  //       }
  //     }
  //   }
  //   if(num === data.length){
  //     isLoadTable.value = false;
  //   } else {
  //     loadLazyData();
  //   }
  // }
}

// const loadLazyData = () => {
//   const remarkInterval = setInterval(()=>{
//     if(isLoadTable.value){
//       tableRef.value.load();
//     } else {
//       clearInterval(remarkInterval);
//     }
//   }, 30000);
// }

const editRow = (row) => {
  detailsRef.value.editRow(row);
}
const reload = () => {
  tableRef.value.load();
}
const beforeSearch = async() => {
  // let result = await tableRef.value.formRef.validate((valid, fields) => {
  //   if (!valid) {
  //     showValidateMsg(fieldsDtl, fields);
  //   }
  // });
  // if (!result) {
  //   return false;
  // }
  //
  // let msgs = [];
  // let msg1 = await checkBeforeCurDt(proxy, proxy.$t('csscl.reportCenter.search.sysCreateDtFrom'), tableRef.value.formInline.rptRequestDtFrom);
  // msgs.push(msg1);
  // let msg2 = await checkBeforeCurDt(proxy, proxy.$t('csscl.reportCenter.search.sysCreateDtTo'), tableRef.value.formInline.rptRequestDtTo);
  // msgs.push(msg2);
  // if (msg1 || msg2) {
  //   return msgs;
  // }
  //
  // let msg = checkDateFromTo(proxy, tableRef.value.formInline.rptRequestDtFrom, tableRef.value.formInline.rptRequestDtTo, proxy.$t('csscl.reportCenter.search.sysCreateDtFrom'));
  // if (msg) {
  //   return msg;
  // }
  // msg = checkDateBetween(proxy, tableRef.value.formInline.rptRequestDtFrom, tableRef.value.formInline.rptRequestDtTo, 7);
  // return msg;
}

const downloadSelection = async () => {
  let funcId = proxy.$currentInfoStore.getCurrentFuncId();

  var tableSelectRows = new Array();
  const rowsData = ref([]);
  rowsData.value = subtableRef.value.getRowsData().value;
  if (rowsData.value) {
    for (var index in rowsData.value) {
      tableSelectRows[index] = rowsData.value[index].rptFilePath + rowsData.value[index].rptFileName;
    }
  }
  let params = {
    funcId: funcId,
    rows: tableSelectRows,
  }
  if (tableSelectRows && tableSelectRows.length !== 0) {
    downloadBatchFile("/rptsched/api/v1/report/center/downloadBatch", params);
  } else {
    ElMessageBox.alert("Please select at least one row of records!", 'Warning');
  }
};

function selectable(row, index) {
  if (row?.processStatusDesc !== 'Completed') {
    return false;
  }
  return true;
}

//paramList 参数显示用的
function processingStatusType(value) {
  paramListData._value.processingStatus = getCommonDesc('PROCESSING_STATUS', value);
}
</script>

<style></style>