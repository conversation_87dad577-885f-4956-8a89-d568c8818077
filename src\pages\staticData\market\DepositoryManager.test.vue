<template>
  <div>
    <h2>DepositoryManager 组件测试</h2>
    <DepositoryManager 
      v-model="testData" 
      :disabled="false"
      :details-ref="null"
      @data-changed="onDataChanged"
      ref="depositoryManagerRef" 
    />
    
    <div style="margin-top: 20px;">
      <h3>测试数据:</h3>
      <pre>{{ JSON.stringify(testData, null, 2) }}</pre>
    </div>
    
    <div style="margin-top: 20px;">
      <el-button @click="addTestData">添加测试数据</el-button>
      <el-button @click="clearData">清空数据</el-button>
      <el-button @click="getData">获取数据</el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import DepositoryManager from './DepositoryManager.vue';

const testData = ref([]);
const depositoryManagerRef = ref();

const onDataChanged = (data: any[]) => {
  console.log('Data changed:', data);
  testData.value = data;
};

const addTestData = () => {
  const sampleData = [
    {
      depoCodeOid: 1,
      depoCode: 'TEST001',
      depoDesc: 'Test Depository 1',
      priSwiftBicCode: 'TESTBIC1',
      secSwiftBicCode: 'TESTBIC2',
      caPayMethodCode: 'A',
      setlPayMethodCode: 'A',
      genDepoIntfInd: 'Y',
      intfFormatCode: 'XML',
      depoIntfAddr: '<EMAIL>',
      settlePeriodCashRecDay: '1',
      settlePeriodCashDeliverDay: '2',
      settlePeriodStockRecDay: '3',
      settlePeriodStockDeliverDay: '4',
      marketDepoSubVPOList: [
        {
          currencyOid: 1,
          currency: 'USD',
          chargeSuspAcctNo: '123456789',
          incomeSuspAcctNo: '987654321',
          setlSuspAcctNo: '555666777',
          recordStatus: 'A',
          mkckAction: 'C'
        },
        {
          currencyOid: 2,
          currency: 'EUR',
          chargeSuspAcctNo: '111222333',
          incomeSuspAcctNo: '444555666',
          setlSuspAcctNo: '777888999',
          recordStatus: 'A',
          mkckAction: 'C'
        }
      ],
      recordStatus: 'A',
      mkckAction: 'C',
      status: 'A'
    }
  ];
  
  if (depositoryManagerRef.value) {
    depositoryManagerRef.value.setDepositoryData(sampleData);
  }
};

const clearData = () => {
  if (depositoryManagerRef.value) {
    depositoryManagerRef.value.clearAllData();
  }
};

const getData = () => {
  if (depositoryManagerRef.value) {
    const data = depositoryManagerRef.value.getDepositoryData();
    console.log('Current data:', data);
    alert(`当前数据条数: ${data.length}`);
  }
};
</script>

<style scoped>
pre {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
}
</style>
