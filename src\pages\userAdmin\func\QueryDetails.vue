<template>
    <el-dialog v-model="dialogFormVisible" title="" width="100%" :modal="false" :show-close="false"
        :close-on-click-modal="false" :close-on-press-escape="false" :destroy-on-close="true" class
        ref="queryDetailsRef"
        modal-class="details-dialog">
        <el-row :gutter="24" style="margin: 0px; padding: 3px; border-bottom: 2px solid lightgrey;">
            <el-col :span="10" style="padding:0px;">
                <el-button :icon="ArrowLeftBold" type="primary" @click="hideDetails" />
                <el-text style="color: #b31a25;" class="mx-1" size="large">{{ $t($currentInfoStore.getHeaderTitle)
                    }}</el-text>
            </el-col>
            <el-col :span="10" :offset="4" style="padding:0px;">
                <el-space style="float:right;">
                    <el-button :icon="CloseBold" type="primary" />
                </el-space>
            </el-col>
        </el-row>
        <div style="padding: 6px;overflow: auto;height: 100%;">
            <el-tabs v-model="activeName" class="demo-tabs">
                <el-tab-pane v-if="activeName == 'first'" :label="$t('csscl.useradmin.func.roleAssignForFunc')"
                    name="first">
                    <el-space>
                        <el-text class="mx-1" style="width: 200px;font-weight: bold;">
                            {{ $t('csscl.useradmin.func.funcId') }}
                        </el-text>
                        <SearchInput style="width: 500px" v-model="ruleForm.form.rightId"
                            url="/auth/api/v1/user/function/list" :desc="ruleForm.form.rightDesc" disabled
                            title="csscl.useradmin.func.function" :params="{}" :columns="[
                                {
                                    title: 'csscl.useradmin.func.funcId',
                                    colName: 'rightId',
                                },
                                {
                                    title: 'csscl.useradmin.func.funcName',
                                    colName: 'rightDesc',
                                }
                            ]" :pageSizes="[10, 20, 30]">
                        </SearchInput>
                    </el-space>
                    <br />
                    <br />
                    <el-table :data="rightsData" border style="width: 350px;">
                        <el-table-column :label="$t('csscl.useradmin.func.assignTo')">
                            <el-table-column prop="menuPath" :label="$t('csscl.useradmin.func.role')" width="350">
                                <template #default="scope">
                                    {{ formartRole(scope.row) }}
                                </template>
                            </el-table-column>
                        </el-table-column>
                    </el-table>
                </el-tab-pane>
                <el-tab-pane v-if="activeName == 'second'" :label="$t('csscl.useradmin.func.roleAssignForReport')"
                    name="second">
                    <el-space>
                        <el-text class="mx-1" style="width: 200px;font-weight: bold;">
                            {{ $t('csscl.useradmin.func.reportId') }}
                        </el-text>
                        <SearchInput style="width: 500px" v-model="ruleForm.form.reportTemplateCode"
                            url="/auth/api/v1/user/report/list" disabled :desc="ruleForm.form.reportDesc"
                            title="csscl.useradmin.func.report" :params="{}" :columns="[
                                {
                                    title: 'csscl.useradmin.func.reportId',
                                    colName: 'reportTemplateCode',
                                },
                                {
                                    title: 'csscl.useradmin.func.reportName',
                                    colName: 'reportDesc',
                                }
                            ]" :pageSizes="[10, 20, 30]">
                        </SearchInput>
                    </el-space>
                    <br />
                    <br />
                    <el-table :data="reportsData" border style="width: 350px;">
                        <el-table-column :label="$t('csscl.useradmin.func.assignTo')">
                            <el-table-column prop="menuPath" :label="$t('csscl.useradmin.func.role')" width="350">
                                <template #default="scope">
                                    {{ formartRole(scope.row) }}
                                </template>
                            </el-table-column>
                        </el-table-column>
                    </el-table>
                </el-tab-pane>
            </el-tabs>
        </div>
        <template #footer>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted, watch, getCurrentInstance } from 'vue'
import {
    CloseBold,
    ArrowLeftBold
} from '@element-plus/icons-vue'
import axios from 'axios';
const props = defineProps(['handleSave', 'reload', 'viewOriginalForm']);
const { proxy } = getCurrentInstance()
const dialogFormVisible = ref(false);
const currentRow = ref({});
const activeName = ref("first");
const rightsData = ref([]);
const reportsData = ref([]);
const queryDetailsRef = ref();
const ruleForm = reactive({
    form: {}
});

const showDialog = (isShow) => {
    let main = queryDetailsRef.value.$el.parentNode;
    if(main) {
        let panel = main.children[0];
        if (isShow) {
            panel.style.display = "none";
        } else {
            panel.style.display = "";
        }
    }
}

const showDetails = (row, disabled) => {
    dialogFormVisible.value = true;
    showDialog(true);
    ruleForm.form = row;
    if (row.rightId) {
        activeName.value = "first";
        proxy.$axios.post("/auth/api/v1/user/role/list", {
            param: {
                isApproved: "A",
                rightId: row.rightId,
            },
            current: 1,
            pageSize: 999,
        }).then((body) => {
            if (body.success) {
                let roles = body.data.data;
                rightsData.value = roles;
            }
        });
    } else {
        activeName.value = "second";
        proxy.$axios.post("/auth/api/v1/user/role/list", {
            param: {
                isApproved: "A",
                reportTemplateCode: row.reportTemplateCode,
            },
            current: 1,
            pageSize: 999,
        }).then((body) => {
            if (body.success) {
                let roles = body.data.data;
                reportsData.value = roles;
            }
        });
    }
}


const hideDetails = () => {
    dialogFormVisible.value = false;
    showDialog(false);
    props.reload && props.reload();
}

const handleCancel = () => {
    dialogFormVisible.value = false;
    showDialog(false);
    props.reload && props.reload();
}

const formartRole = (row) => {
    return row.roleId + " - " + row.roleName;
}

defineExpose({
    hideDetails,
    showDetails,
    currentRow,
})
</script>
<style>
.details-dialog {
    top: 60px;
    inset: unset !important;
}

.details-dialog .ep-overlay-dialog .ep-dialog {
    margin: 0px;
    height: 100%;
    padding: 0px;
}

.details-dialog .ep-overlay-dialog .ep-dialog .ep-dialog__body {
    height: calc(100% - 48px);
}

.details-dialog .ep-overlay-dialog {
    top: 60px;
}

.details-dialog .ep-overlay-dialog .ep-dialog>header {
    display: none;
}

.dialog-footer {
    float: right;
}

.button-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}
</style>