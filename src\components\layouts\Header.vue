<script lang="ts" setup>
import { reactive, ref, getCurrentInstance, onMounted, onUnmounted } from 'vue';
import { useCookies } from "vue3-cookies";
import { useRouter } from 'vue-router';
import { ElMessageBox } from 'element-plus';
import {
  HomeFilled,
  CloseBold,
  ArrowLeftBold,
  InfoFilled
} from '@element-plus/icons-vue'
import oidcClient from '~/util/oidcClient';
import { VITE_REDIRECTURL } from '~/util/env-config';
import { clearCookies, datetimeFormat } from "~/util/Function";
const {
  logoutHttp
} = oidcClient
const router = useRouter()
const { proxy } = getCurrentInstance();
const { cookies } = useCookies();

const systemName = import.meta.env.VITE_SYSTEM;

const handleClick = (itemI, itemII) => {
  // router.push({ name: itemII.name });
}
const handleLogout = () => {
  ElMessageBox.confirm('Are you sure to logout?')
    .then(() => {
      logout();
    })
    .catch(() => {
      // catch error
    })
}
const logout = async () => {
      await unlock();
      if(cookies.get("id_token")){
        await proxy.$axios.get('/auth/api/v1/user/logout');
        logoutHttp({redirectUrl: VITE_REDIRECTURL})
          .then((res) => {
            // Start SK-COMMON-0083, Tom.Li, 2024/08/22
            clearCookies(true, true);
            // End SK-COMMON-0083, Tom.Li, 2024/08/22
          });
      } else {
        await proxy.$axios.get('/auth/api/v1/user/logout');
        clearCookies(true);
      }
}
const goHome = () => {
  proxy.$axios.post('/datamgmt/api/v1/makerchecker/unlock', {
                 logout: true,
                 });
  proxy.$axios.post('/datamgmt/api/v1/handler/unlock', {
    flag: true,
  });
  router.push("/");
  if(router.currentRoute.value.path=="/") {
    window.location.reload();
  }
}
const version = import.meta.env.VITE_APP_VERSION;
const system = import.meta.env.VITE_SYSTEM;
const showAbout = ref(false);
const goAbout = async()=>{
  showAbout.value = true;
}
function closeAbout(){
  showAbout.value = false;
}

const unlock = async() => {
  console.log("unlock");
  await proxy.$axios.post('/datamgmt/api/v1/makerchecker/unlock', {
                 logout: true,
                 });
  await proxy.$axios.post('/datamgmt/api/v1/handler/unlock', {
      flag: true,
  });
}

// Start R2411A-38742, Cmm, 2024/08/23
// let sysDate = new Date();
const currentTime = ref();
const intervalId = ref();
const updateTime = () => {
  proxy.$currentInfoStore.currentTimeAddSec(1);
  currentTime.value = datetimeFormat(proxy.$currentInfoStore.getUserInfo.currentTime);
};
intervalId.value = setInterval(updateTime, 1000);
onUnmounted(() => {
  if(intervalId.value){
    clearInterval(intervalId.value);
  }
});
// End R2411A-38742, Cmm, 2024/08/23
</script>
<style scoped>
.el-menu-demo {
  height: 32px;
  background-color: #b31a25;
  padding-left: 6px;
}

.el-menu-demo>li {
  color: white;
  font-weight: bold;
}
.demo-poper-class li {
  font-weight: bold;
}
table {
      border-collapse: collapse;
      border: 0px;
}
th, td {
  border: 1px solid #c9c9c3;
  padding: 5px;
  text-align: left;
}
</style>
<template>
  <el-dialog v-model="showAbout" title="About" width="475px"  class="mkck-dialog"  append-to-body :show-close="false">
    <template #header>
        <div class="mkckTitle">
          <span class="title-name">About</span>
        </div>
      </template>  
    <table style="width:80%;margin: 30px 50px;background-color:#E4E4E4">
        <tr>
          <td style="font-weight: bold;border-top:0px;border-left:0px">System</td>
          <td style="border-right:0px;border-bottom:0px;border-top:0px;">{{system}}</td>
        </tr>
        <tr>
          <td style="font-weight: bold;border-left:0px">Version</td>
          <td style="border-right:0px;border-bottom:0px">{{version}}</td>
        </tr>
        <tr>
          <!-- Start R2411A-38742, Cmm, 2024/08/23 -->
          <!-- 
          <td style="font-weight: bold;border-left:0px">Calendar Date</td>
          <td style="border-right:0px;border-bottom:0px">{{ $currentInfoStore.getUserInfo.lastLoginTime.slice(0,10).replaceAll('-','/' ) }}</td>
          -->
          <td style="font-weight: bold;border-left:0px">Processing Date</td>
          <td style="border-right:0px;border-bottom:0px">{{ $currentInfoStore.getUserInfo.loginTime }}</td>
          <!-- End R2411A-38742, Cmm, 2024/08/23 -->
        </tr>
        <tr>
          <td style="font-weight: bold;border-left:0px;border-bottom:0px">Login User</td>
          <td style="border-right:0px;border-bottom:0px">{{ $currentInfoStore.getUserInfo.userId }}</td>
        </tr>
      </table>
      <!-- #c9c9c3 -->
      <el-button style="width:80px;height:25px; padding: 5px 20px;margin-left:184px;margin-bottom: 20px;background-color:#E4E4E4"   @click="closeAbout">OK</el-button>
    </el-dialog>
  <el-row :gutter="20" style="margin-inline: 10px;">
    <el-col :span="10" style="padding:0px;">
      <el-space style="float:left;font-weight: bold;">
        <img src="/logo.png" style="height: 30px;" />
        <el-text style="color: #b31a25;" class="mx-1" size="large">{{ systemName }}</el-text>
      </el-space>
    </el-col>
    <el-col :span="10" :offset="4" style="padding:0px;">
      <el-space style="float:right;height: 30px;">
        <el-text class="mx-1">Hi, <span style="color:var(--ep-color-primary);">{{ $currentInfoStore.getUserInfo.userId }}, {{ $currentInfoStore.getUserInfo.userName }}</span> 
          <!-- Start R2411A-38742, Cmm, 2024/08/23 -->
          <!-- {{ $currentInfoStore.getUserInfo.loginTime }} {{ $currentInfoStore.getUserInfo.timeZone }}</el-text> -->
          {{ currentTime }} {{ $currentInfoStore.getUserInfo.timeZone }}</el-text>
          <!-- End R2411A-38742, Cmm, 2024/08/23 -->
          <el-text style="color: blue;">Processing Date</el-text>
           <!-- Start SK-COMMON-0233,AMOR,2024/10/17 -->
          <el-text style="color: blue;">{{ $currentInfoStore.getUserInfo.loginTime }}</el-text>
          <!-- End SK-COMMON-0233,AMOR,2024/10/17 -->
        <el-button :icon="HomeFilled" link style="font-size: 18px;" @click="goHome"/>
        <el-button :icon="InfoFilled" link style="font-size: 18px;" @click="goAbout"/>
        <el-button type="primary" @click="handleLogout">Logout</el-button>
      </el-space>
    </el-col>
  </el-row>
  <el-menu :default-active="$route.path" :router="true" popper-class="demo-poper-class" menu-trigger="click" :close-on-click-outside="true" :unique-opened="true" class="el-menu-demo" mode="horizontal">
    <template v-for="itemI in proxy.$currentInfoStore.getMenus">
      <el-sub-menu :teleported="false" popper-class="header-popper-class" :index="itemI.path + ''">
        <template #title>{{ proxy.$t("menu." + itemI.name) }}</template>
        <template v-for="itemII in itemI.children">
          <el-menu-item :index='itemII.path' @click="handleClick(itemI, itemII)">{{
            proxy.$t("menu." + itemI.name + "." + itemII.name) }}
            <input type="password" hidden :id="itemII.name" :value="itemII.funcId" />
          </el-menu-item>
        </template>
      </el-sub-menu>
    </template>
  </el-menu>
  
</template>
