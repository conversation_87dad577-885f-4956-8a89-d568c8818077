import axios from '~/axios'

export interface RuleForm {
    opCtryRegionCode: String
    currencyCode: String
    restrictedCurrency: String
    decimalPoint: String
    calMethodCode: String
    status: String
    descpt:string
  }


/**
 * get Currency by primary key
 * @param id 
 * @returns 
 */
export const getCurrency = async (id: number) =>{
    return await axios.get('/datamgmt/api/v1/currency?currencyId='+id);
}

/**
 * Update currency
 * @param data 
 * @returns 
 */
export const updateCurrency = async(data: any) =>{
    return await axios.patch("/datamgmt/api/v1/currency", data);
}

/**
 * Add New Currency
 * @param data 
 * @returns 
 */
export const addCurrency = async(data: any) =>{
    return await axios.post("/datamgmt/api/v1/currency", data);
}

export const getCcyDecimalPoint = async (opCtryRegionCode: String, currencyCode: String) =>{
    return await axios.get('/datamgmt/api/v1/currency/decimal/point?opCtryRegionCode='+opCtryRegionCode+"&currencyCode="+currencyCode);
}
