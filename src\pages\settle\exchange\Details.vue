<template>
    <BaseDetails ref="details" :handleSave="handleSave" :reload="props.reload" :viewOriginalForm="viewOriginalForm">
        <el-form :validateOnRuleChange="false" ref="exchangeFormRef" :disabled="formDisabled" style="width: 100%" :model="exchangeForm.form"
            :rules="rules" status-icon>
            <FormRow>
                <FormItemSign :detailsRef="details" :label="$t('csscl.exchang.exchangeCode')" label-width="180" prop="exchangeCode">
                    <el-input v-model="exchangeForm.form.exchangeCode" style="width: 120px"/>
                </FormItemSign>
                <FormItemSign :detailsRef="details" :label="$t('csscl.exchang.exchangeShortName')" label-width="200" prop="exchangeShortName">
                    <el-input v-model="exchangeForm.form.exchangeShortName" style="width: 320px;" input-style="text-transform:none"/>
                </FormItemSign>
                <FormItemSign :detailsRef="details" :label="$t('common.title.ctryRegionCode')" label-width="210" prop="ctryRegionCode">
                    <CtryRegionSearchInput v-model="exchangeForm.form.ctryRegionCode" showDesc="false" style="width: 120px" />
                </FormItemSign>
            </FormRow>
            <FormRow>
                <FormItemSign :detailsRef="details" :label="$t('csscl.exchang.exchangeBoardCode')" prop="exBoardCode" label-width="180">
                    <el-input v-model="exchangeForm.form.exBoardCode" style="width: 120px"/>
                </FormItemSign>
                <FormItemSign :detailsRef="details" :label="$t('csscl.exchang.exchangeBoardShortName')" prop="exBoardShortName" label-width="200">
                    <el-input v-model="exchangeForm.form.exBoardShortName" style="width: 320px;" input-style="text-transform:none"/>
                </FormItemSign>
                <FormItemSign :detailsRef="details" :label="$t('common.title.opCtryRegionCode')" prop="opCtryRegionCode" label-width="210" >
                    <CtryRegionSearchInput v-model="exchangeForm.form.opCtryRegionCode" 
                        showDesc="false" 
                        style="width: 120px"
                        opCtryRegion />
                </FormItemSign>
            </FormRow>
            <FormRow>
                <FormItemSign :detailsRef="details" :label="$t('csscl.exchang.primaryBicCode')" prop="priSwiftCode" label-width="180">
                    <el-input v-model="exchangeForm.form.priSwiftCode" style="width: 140px"/>
                </FormItemSign>
                <FormItemSign :detailsRef="details" :label="$t('csscl.exchang.secondaryBicCode')" prop="secSwiftCode" label-width="200">
                    <el-input v-model="exchangeForm.form.secSwiftCode" style="width: 140px"/>
                </FormItemSign>
                <ElFormItemProxy> </ElFormItemProxy>
            </FormRow>
        </el-form>
        <!-- ---------------------------- -->
        <el-row>
            <span style="font-size: 16px; font-weight: bold;">Settlement</span>
            <div style="paddingBlock:5px;width: 100%;display: flex;flex-flow: wrap;border-top: 1px solid;color: rgb(216, 211, 211);"></div>
        </el-row>

        <el-form :validateOnRuleChange="false" style="width: 100%" :disabled="formDisabled"  label-position='left'>
            <el-row>
                <el-col :span="7">
                    <FormItemSign :detailsRef="details" :label="$t('csscl.exchang.settlement')" label-width="210" prop="settlement">
                        <Select v-model="exchangeForm.form.cashSettleMethodSi" style="width: 340px;" type='CASH_SETTLE_METHOD_SI_CODE' />
                    </FormItemSign>
                </el-col>

                <el-col :span="6" :offset="1">
                    <FormItemSign :detailsRef="details" label-width="160" :label="$t('csscl.exchang.settlementCutoffTime')" prop="siFundAvailableCutoffTime">
                        <el-input v-model="exchangeForm.form.siFundAvailableCutoffTime" style="width: 100px;"></el-input>
                    </FormItemSign>
                </el-col>
            </el-row>

            <el-row>
                <el-col :span="6">
                    <FormItemSign :detailsRef="details" prop="rdvpInd">
                        <el-checkbox disabled v-model="exchangeForm.form.rdvpInd" true-value="Y" false-value="N" >
                            {{  $t("csscl.exchang.rdvpIndicator") }}
                        </el-checkbox>
                    </FormItemSign>
                       
                    <FormItemSign :detailsRef="details" :label="$t('csscl.exchang.cashStockSettlementModel')" label-width="210" prop="cashStockSettleMethod">
                        <Select v-model="exchangeForm.form.cashStockSettleMethod" type='CASH_STOCK_SETTLE_METHOD_CODE' />
                    </FormItemSign>
                </el-col>
                <el-col :span="5" :offset="2">
                    <el-card>
                        <el-card>
                            <span style="text-decoration: underline;">On or Before /Without Settlement Cutoff Time</span>
                            <br><br>
                            <FormItemSign :detailsRef="details" :label="$t('csscl.exchang.fundAvailableDayForSi')" prop="siFundAvailableDay" label-width="180px">
                                <InputNumber v-model="exchangeForm.form.siFundAvailableDay" precision="2" scale="0" style="width: 50px;" />
                            </FormItemSign>
                            <FormItemSign :detailsRef="details" :label="$t('csscl.exchang.fundAvailableTimeForSi')" prop="siFundAvailableTime" label-width="180px">
                                <el-input v-model="exchangeForm.form.siFundAvailableTime" style="width: 100px;"/>
                            </FormItemSign>
                        </el-card>
                        <br><br>
                        <el-card>
                            <span style="text-decoration: underline; ">After Settlement Cutoff Time</span>
                            <br><br>
                            <FormItemSign :detailsRef="details" :label="$t('csscl.exchang.fundAvailableDayForSi')" prop="siFundAvailableDayAfter" label-width="180px"> 
                                <InputNumber v-model="exchangeForm.form.siFundAvailableDayAfter"  precision="2" scale="0" style="width: 50px;" />
                            </FormItemSign>
                            <FormItemSign :detailsRef="details" :label="$t('csscl.exchang.fundAvailableTimeForSi')" prop="siFundAvailableTimeAfter" label-width="180px"> 
                                <el-input v-model="exchangeForm.form.siFundAvailableTimeAfter" style="width: 100px;"/>
                            </FormItemSign>
                        </el-card>
                    </el-card>

                </el-col>

                <el-col :span="4" :offset="2">
                    <el-card>
                        <span style="text-decoration: underline;">Settlement Period</span>
                        <br><br>
                        <FormItemSign :detailsRef="details" :label="$t('csscl.exchang.cashReceive')" prop="settlePeriodCashRecDay" label-width="130px"> 
                            <InputNumber v-model="exchangeForm.form.settlePeriodCashRecDay" precision="2" scale="0" style="width: 50px;" />
                        </FormItemSign>
                        <FormItemSign :detailsRef="details" :label="$t('csscl.exchang.cashDeliver')" prop="settlePeriodCashDeliverDay" label-width="130px">
                            <InputNumber v-model="exchangeForm.form.settlePeriodCashDeliverDay" precision="2" scale="0" style="width: 50px;" />
                        </FormItemSign>
                        <FormItemSign :detailsRef="details" :label="$t('csscl.exchang.stockReceive')" prop="settlePeriodStockRecDay" label-width="130px">
                            <InputNumber v-model="exchangeForm.form.settlePeriodStockRecDay" precision="2" scale="0" style="width: 50px;" />
                        </FormItemSign>
                        <FormItemSign :detailsRef="details" :label="$t('csscl.exchang.stockDeliver')" prop="settlePeriodStockDeliverDay" label-width="130px">
                            <InputNumber v-model="exchangeForm.form.settlePeriodStockDeliverDay" precision="2" scale="0"  style="width: 50px;"/>
                        </FormItemSign>
                    </el-card>
                </el-col>
            </el-row>

            <el-row>
                <span style="font-size: 16px; font-weight: bold;">Corporate Action</span>
                <div style="paddingBlock:5px;width: 100%;display: flex;flex-flow: wrap;border-top: 1px solid;color: rgb(216, 211, 211);"></div>
            </el-row>
            <el-row>
                    <el-col :span="7">
                        <FormItemSign :detailsRef="details" :label="$t('csscl.exchang.caPaymentMethod')">
                        <Select v-model="exchangeForm.form.caPayMethod" style="width: 340px" type='CA_PAY_METHOD_CODE' />
                    </FormItemSign>
                    </el-col>
                    <el-col :span="5" :offset="1">
                        <el-card >
                            <span style="text-decoration: underline; ">After Settlement Cutoff Time</span>
                            <br><br>
                            <el-row>
                                <FormItemSign :detailsRef="details" :label="$t('csscl.exchang.fundAvailableDayForCa')" prop="caFundAvailableDay" label-width="180px">
                                <InputNumber v-model="exchangeForm.form.caFundAvailableDay" precision="2" scale="0"  style="width: 50px;"/>
                            </FormItemSign>
                            </el-row>
                            <el-row>
                                <FormItemSign :detailsRef="details" :label="$t('csscl.exchang.fundAvailableTimeForCa')" prop="caFundAvailableTime" label-width="180px">
                                <el-input v-model="exchangeForm.form.caFundAvailableTime"  style="width: 100px;"/>
                            </FormItemSign>
                            </el-row>
                         </el-card>
                    </el-col>


                </el-row>
        </el-form>

        <el-row>
            <el-col :label="$t('csscl.exchang.settlement')" prop="settlement"></el-col>
        </el-row>
    </BaseDetails>
</template>


<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue';
import type { FormInstance, FormRules } from 'element-plus'
import BaseDetails from '~/pages/base/Details.vue';
import CtryRegionSearchInput from '~/pages/base/CtryRegionSearchInput.vue';
const { proxy } = getCurrentInstance();
const details = ref();
const formDisabled = ref(false);
import FormItemSign from '~/pages/base/FormItemSign.vue';
import { getOid, saveMsgBox } from '~/util/Function.js';
import { clearEnterObjects } from '~/util/ModifiedValidate';
const props = defineProps(['reload']);
let siCutoffTime = "";
let caTime = "";
let siTime =  "";
let siAfterTime = "";
const editRow = (row,disabled) => {
    console.log("Edit ...");
    proxy.$axios.get("/datamgmt/api/v1/exboard?objectId=" +getOid(row, disabled)).then((body) => {
        if (body.success) {
            exchangeForm.form = body.data;
            details.value.currentRow = body.data;
            siCutoffTime =  body.data.siFundAvailableCutoffTime;
            caTime = body.data.caFundAvailableTime;
            siTime = body.data.siFundAvailableTime;
            siAfterTime = body.data.siFundAvailableTimeAfter;
            exchangeForm.form.siFundAvailableCutoffTime =( body.data.siFundAvailableCutoffTime==null)?'':body.data.siFundAvailableCutoffTime+':00';
            if(exchangeForm.form.siFundAvailableCutoffTime.length == 4){
                exchangeForm.form.siFundAvailableCutoffTime = '0'+exchangeForm.form.siFundAvailableCutoffTime
            }
            exchangeForm.form.caFundAvailableTime = (body.data.caFundAvailableTime==null)?'':body.data.caFundAvailableTime+':00';
            if(exchangeForm.form.caFundAvailableTime.length == 4){
                exchangeForm.form.caFundAvailableTime = '0'+exchangeForm.form.caFundAvailableTime
            }
            exchangeForm.form.siFundAvailableTime = (body.data.siFundAvailableTime==null)?'':body.data.siFundAvailableTime+':00';
            if(exchangeForm.form.siFundAvailableTime.length == 4){
                exchangeForm.form.siFundAvailableTime = '0'+exchangeForm.form.siFundAvailableTime
            }
            exchangeForm.form.siFundAvailableTimeAfter = (body.data.siFundAvailableTimeAfter==null )?'':body.data.siFundAvailableTimeAfter+':00';
            if(exchangeForm.form.siFundAvailableTimeAfter.length == 4){
                exchangeForm.form.siFundAvailableTimeAfter = '0'+exchangeForm.form.siFundAvailableTimeAfter
            }
        }
    });
}
const viewOriginalForm = (pendingOid, isDisabled) => {
    formDisabled.value = isDisabled;
  proxy.$axios.get("/datamgmt/api/v1/exboard?objectId="+pendingOid).then((body) => {
        if(body.success) {
            exchangeForm.form = body.data;
            // details.value.currentRow.value = body.data;
        }
    });
}
const handleSave = async (searchValid, isOnlyValidate, unPopping) => {
    let result = await exchangeFormRef.value.validate((valid, fields) => {
        if (valid) {
            console.log('submit!')
        } else {
            console.log('error submit!', fields)
        }
    });
    if (isOnlyValidate) {
        return result;
    }
    if (result && searchValid && await saveMsgBox(unPopping)) {
        if (exchangeForm.form.exBoardOid) {
            exchangeForm.form.siFundAvailableCutoffTime = siCutoffTime;
            exchangeForm.form.caFundAvailableTime = caTime;
            exchangeForm.form.siFundAvailableTime = siTime;
            exchangeForm.form.siFundAvailableTimeAfter = siAfterTime;
            const msg = await proxy.$axios.patch("/datamgmt/api/v1/exboard", {
                ...exchangeForm.form,
            });
            if (msg.success) {
              clearEnterObjects();
            }
            return msg.success;
        } else {
            const msg = await proxy.$axios.post("/datamgmt/api/v1/exboard/exboard", {
                ...exchangeForm.form,
            });
            return msg.success;
        }
    }
    return false;
}
const showDetails = (row, disabled) => {
    formDisabled.value = disabled;
    details.value.showDetails(row, disabled)
    editRow(row);
}
defineExpose({
    details,
    editRow,
    showDetails,
    viewOriginalForm
});
// --------------------------------------------

interface ExchangeForm {
    opCtryRegionCode: string
    ctryRegionCode: string
    exchangeCode: string
    userName: string
    email: string
    phoneExt: string
    phoneNo: string
    apprCurrency: string
    apprLimitAmt: number
    mClassCode: string
    status: string
}

const exchangeFormRef = ref<FormInstance>()
const exchangeForm = reactive({
    form: {
        opCtryRegionCode: "",
        ctryRegionCode: "",
        exchangeCode: "",
        exchangeShortName: "",
        exBoardCode: "",
        exBoardShortName: "",
        priSwiftCode: "",
        secSwiftCode: "",
        status: "",
        rdvpInd: "",
        cashStockSettleMethod: "",
        settlementPeriod: "",
        settlePeriodCashRecDay: "",
        settlePeriodCashDeliverDay: "",
        settlePeriodStockRecDay: "",
        settlePeriodStockDeliverDay: "",
        siFundAvailableDay: "",
        siFundAvailableTime: "",
        caFundAvailableDay: "",
        caFundAvailableTime: "",
        caPayMethod: "",
        siFundAvailableCutoffTime:"",
        siFundAvailableDayAfter:"",
        siFundAvailableTimeAfter:"",
        exBoardOid:"",
        cashSettleMethodSi:""

    }
})


// const rules = reactive<FormRules<ExchangeForm>>({
//     // opCtryRegionCode: [
//     //     { required: true, message: 'Please input ', trigger: 'blur' },
//     //     { min: 3, max: 5, message: 'Length should be 3 to 5', trigger: 'blur' },
//     // ],
//     // ctryRegionCode: [
//     //     { required: true, message: 'Please input ', trigger: 'blur' },
//     //     { min: 3, max: 5, message: 'Length should be 3 to 5', trigger: 'blur' },
//     // ],
//     userId: [
//         { required: true, message: 'Please input ', trigger: 'blur' },
//         { min: 3, max: 5, message: 'Length should be 3 to 5', trigger: 'blur' },
//     ],
//     userName: [
//         { required: true, message: 'Please input ', trigger: 'blur' },
//         { min: 3, max: 5, message: 'Length should be 3 to 5', trigger: 'blur' },
//     ],
//     email: [
//         { required: true, message: 'Please input ', trigger: 'blur' },
//         { min: 3, max: 5, message: 'Length should be 3 to 5', trigger: 'blur' },
//     ],
//     phoneExt: [
//         { required: true, message: 'Please input ', trigger: 'blur' },
//         { min: 3, max: 5, message: 'Length should be 3 to 5', trigger: 'blur' },
//     ],
//     phoneNo: [
//         { required: true, message: 'Please input ', trigger: 'blur' },
//         { min: 3, max: 5, message: 'Length should be 3 to 5', trigger: 'blur' },
//     ],
//     apprCurrency: [
//         { required: true, message: 'Please input ', trigger: 'blur' },
//         { min: 3, max: 5, message: 'Length should be 3 to 5', trigger: 'blur' },
//     ],
//     apprLimitAmt: [
//         { required: true, message: 'Please input ', trigger: 'blur' },
//         { min: 3, max: 5, message: 'Length should be 3 to 5', trigger: 'blur' },
//     ],
//     mClassCode: [
//         { required: true, message: 'Please input ', trigger: 'blur' },
//         { min: 3, max: 5, message: 'Length should be 3 to 5', trigger: 'blur' },
//     ],
//     status: [
//         { required: true, message: 'Please input ', trigger: 'blur' },
//         { min: 3, max: 5, message: 'Length should be 3 to 5', trigger: 'blur' },
//     ],
// })

const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate((valid, fields) => {
        if (valid) {
            console.log('submit!')
        } else {
            console.log('error submit!', fields)
        }
    })
}

const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.resetFields()
}

const options = Array.from({ length: 10000 }).map((_, idx) => ({
    value: `${idx + 1}`,
    label: `${idx + 1}`,
}))

</script>

<style>

</style>