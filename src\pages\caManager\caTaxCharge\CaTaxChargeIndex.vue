<template>
  <BasePanel :searchParams="searchParams" :paramListData="paramListData" url="/bff/ca/api/v1/ca-event/get-ca-event-page-list"
    selectFirstRecord="true" :clickRow="handleClick"
    ref="baseTableRef" :hideOperation="true" :isHideAdd="true" :params="{ modeEdit: 'Y' }" :beforeSearch= "beforeSearch">
    <template v-slot:searchPanel="slotProps">
      <FormRow>
        <ElFormItemProxy label-width="250" :label="$t('csscl.ca.common.eventRefNo')" prop="caEventReferenceNumber">
          <GeneralSearchInput v-model="slotProps.form.caEventReferenceNumber" style="width:280px" :title="$t('csscl.ca.common.eventRefNo')" searchType="caEventReferenceNumber" showDesc="false"/>
        </ElFormItemProxy>
        <ElFormItemProxy label-width="150" :label="$t('csscl.ca.common.eventCategoryCode')" prop="caEventCategory">
          <SearchInput style="width: 150px" v-model="slotProps.form.caEventCategory"
            url="/datamgmt/api/v1/searchinput" showDesc="false"
            :title="$t('csscl.ca.common.eventCategory')"
            :params="{searchType: 'caEventCategoryCode'}"
            :columns="[
              {
                  title: $t('csscl.ca.common.eventCategoryCode'),
                  colName: 'code',
              },
              {
                  title: $t('csscl.ca.common.eventCategoryDescription'),
                  colName: 'codeDesc',
              }
            ]"
            :pageSizes="[10, 20, 30]">
          </SearchInput>
        </ElFormItemProxy>
        <ElFormItemProxy label-width="130" :label="$t('csscl.ca.common.status')" prop="caEventStatus">
          <Select v-model="slotProps.form.caEventStatus" style="width: 150px;" type="STATUS"/>
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy label-width="250" :label="$t('csscl.ca.common.recordDate')" prop="recordDate" >
          <DateItem v-model="slotProps.form.recordDate"/>
        </ElFormItemProxy>
        <ElFormItemProxy label-width="150" :label="$t('csscl.ca.common.securityId')" prop="instrumentCode">
          <GeneralSearchInput v-model="slotProps.form.instrumentCode" inputStyle="width:150px" style="width:350px" searchType="instrumentCode" showDesc="true"/>
        </ElFormItemProxy>
        <ElFormItemProxy label-width="130" :label="$t('csscl.ca.common.swiftEventCode')" prop="swiftEventCode">
          <GeneralSearchInput v-model="slotProps.form.swiftEventCode" style="width:180px" searchType="swiftEventCode" showDesc="false"/>
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy label-width="250" :label="$t('csscl.ca.common.subCustodianClearingAgentCode')" prop="clearingAgentCode">
          <GeneralSearchInput v-model="slotProps.form.clearingAgentCode" style="width:270px" searchType="clearingAgentCode" showDesc="true"/>
        </ElFormItemProxy>
        <ElFormItemProxy label-width="150" :label="$t('csscl.ca.common.custodyAccountNo')" prop="tradingAccountCode">
          <GeneralSearchInput v-model="slotProps.form.tradingAccountCode" style="width:280px" searchType="custodyAcct" showDesc="false"/>
        </ElFormItemProxy>
        <ElFormItemProxy></ElFormItemProxy>
      </FormRow>
    </template>  
    <template v-slot:tableHeaderTitle>
      <div style="margin: 0px; padding: 3px; border-bottom: 2px solid #FFAB2D;">
        <el-text style="color: #FFAB2D; font: 14px bold;">
          {{ $t('csscl.ca.common.caEvent') }}
        </el-text>
      </div>
    </template>
    <template v-slot:tableColumn>
      <el-table-column sortable="custom" prop="caEventReferenceNumber" :label="$t('csscl.ca.common.eventRefNo')" header-align="center" align="center"/>
      <el-table-column sortable="custom" prop="caEventCategory" :label="$t('csscl.ca.common.eventCategoryCode')" header-align="center" align="center"/>
      <el-table-column sortable="custom" prop="swiftInstructionType" :label="$t('csscl.ca.common.camv')" header-align="center" align="center"/>
      <el-table-column sortable="custom" prop="swiftEventCode" :label="$t('csscl.ca.common.swiftEventType')" header-align="center" align="center"/>
      <el-table-column sortable="custom" prop="clearingAgentCode" :label="$t('csscl.ca.common.subCustodianClearingAgentCode')" header-align="center" align="center"/>
      <el-table-column sortable="custom" prop="announceDescription" :label="$t('csscl.ca.common.description')" header-align="center" align="center"/>
      <el-table-column sortable="custom" prop="instrumentCode" :label="$t('csscl.ca.common.securityId')" header-align="center" align="center"/>
      <el-table-column sortable="custom" prop="instrumentShortName" :label="$t('csscl.ca.common.securityName')" header-align="center" align="center"/>
      <el-table-column sortable="custom" prop="recordDate" :label="$t('csscl.ca.common.recordDate')" header-align="center" align="center"/>
      <el-table-column sortable="custom" prop="eventValueDate" :label="$t('csscl.ca.common.valueDate')" header-align="center" align="center"/>
      <el-table-column sortable="custom" prop="caEventStatus" :label="$t('csscl.ca.common.caEventStatus')" header-align="center" align="center"/>
    </template>

    <template v-slot:contentBottom>
      <br />
      <br />
      <!-- CA Tax And Charge ------------------------------>
      <div style="margin: 0px; padding: 3px; border-bottom: 2px solid #FFAB2D;">
        <el-text style="color: #FFAB2D; font: 14px bold;">
          {{ $t('csscl.ca.common.caTaxCharge') }}
        </el-text>
      </div>
      <TaxChargeTable ref="taxChargeTableRef" :handleHeaderClick="handleHeaderClick"/>
    </template>
  </BasePanel>
  <!-- Details页面 -->
  <Details ref="detailsRef" :reload="reload" />
</template>

<script lang="ts" setup>
import { ref, getCurrentInstance, h } from 'vue';
import { downloadFilePost, showWarningMsg, getCommonDesc } from '~/util/Function.js';
import BasePanel from '~/pages/base/CaIndex.vue'
import TaxChargeTable from "~/pages/caManager/caTaxCharge/CaTaxChargeTable.vue";
import Details from "./CaTaxChargeDetails.vue";
const { proxy } = getCurrentInstance()
const paramListData = {};
const baseTableRef = ref();
const taxChargeTableRef = ref();
const detailsRef = ref();
const searchParams = {
  caEventReferenceNumber: "",
  recordDate:"",
  clearingAgentCode: "",
  caEventCategory: "",
  instrumentCode: "",
  custodianAccountNumber: "",
  swiftEventCode:"",
  caEventStatus:"",
};
const handleHeaderClick = async (row:any) => {
  console.log("handleHeaderClick --> " + row); 
  await detailsRef.value.showDetails(row);
}
const handleClick = async (row:any) => { 
  // 清空数据
  clearHeaderData();
  // 渲染组件
  await taxChargeTableRef.value.showDetails(row);
}
const beforeSearch= () => {
  // 清空数据
  clearHeaderData();
}
const clearHeaderData = async() => {
  taxChargeTableRef.value.clearData?.();
  detailsRef.value.clearData?.();
}
const reload = () => {
  baseTableRef.value.load();
}
</script>

<style></style>