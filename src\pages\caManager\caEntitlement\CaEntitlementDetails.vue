<template>
  <BaseDetails ref="details" :handleSave="handleSave" :reload="reload" :viewOriginalForm="viewOriginalForm" 
  :form="dataState.currentData.caEventForm" 
  :beforeSaveValid="beforeSaveValid">
    <el-form :validateOnRuleChange="false" :disabled="true" ref="caEventFormRef" style="width: 85%"
       :model="dataState.currentData.caEventForm" 
       status-icon label-position="left" label-width="220px">
       <FormRow>
        <FormItemSign :detailsRef="details" :label="$t('csscl.ca.common.sequenceNo')"  label-width="150">
          <InputText :disabled="true" v-model="dataState.currentData.caEventForm.form.oid" maxlength="100" style="width: 80px" />
        </FormItemSign>
        <FormItemSign :detailsRef="details" :label="$t('csscl.ca.common.creationChannel')" label-width="150">
          <Select v-model="dataState.currentData.caEventForm.form.caEventSourceChannel" style="width: 200px" type='CA_CREATE_CHANNEL' />
        </FormItemSign>
        <FormItemSign :detailsRef="details" :label="$t('csscl.ca.common.caEventStatus')">
          <Select v-model="dataState.currentData.caEventForm.form.caEventStatus" style="width: 200px" type='STATUS' />
        </FormItemSign>
      </FormRow>
      <FormRow>
        <FormItemSign :detailsRef="details" :label="$t('csscl.ca.common.eventCategory')" label-width="150">
          <SearchInput style="width: 350px" v-model="dataState.currentData.caEventForm.form.caEventCategory" :disabled="true"
            url="/datamgmt/api/v1/searchinput" showDesc="true" :title="$t('csscl.ca.common.eventType')"
            :params="{ searchType: 'caEventCategoryCode' }" :columns="[
              {
                title: $t('csscl.ca.common.eventCategoryCode'),
                colName: 'code',
              },
              {
                title: $t('csscl.ca.common.eventCategoryDescription'),
                colName: 'codeDesc',
              }
            ]" :pageSizes="[10, 20, 30]">
          </SearchInput>
        </FormItemSign>
        <FormItemSign :detailsRef="details" :label="$t('csscl.ca.common.eventRefNo')" label-width="150">
          <InputText :disabled="true" v-model="dataState.currentData.caEventForm.form.caEventReferenceNumber" maxlength="100"
            style="width: 350px" />
        </FormItemSign>
        <FormItemSign :detailsRef="details" :label="$t('csscl.ca.common.paymentMethod')">
            <Select v-model="dataState.currentData.caEventForm.form.caCashPaymentMethod" style="width: 200px" type='CA_PAY_METHOD_CODE' />
        </FormItemSign>
      </FormRow>
      <FormRow>
        <FormItemSign :detailsRef="details" :label="$t('csscl.ca.common.swiftEventType')" label-width="150">
          <SearchInput style="width: 350px" v-model="dataState.currentData.caEventForm.form.swiftEventCode" :disabled="true"
            url="/datamgmt/api/v1/searchinput" showDesc="true" :title="$t('csscl.ca.common.eventType')"
            :params="{ searchType: 'caEventType' }" :columns="[
              {
                title: $t('csscl.ca.common.eventTypeCode'),
                colName: 'code',
              },
              {
                title: $t('csscl.ca.common.eventTypeDescription'),
                colName: 'codeDesc',
              }
            ]" :pageSizes="[10, 20, 30]">
          </SearchInput>
        </FormItemSign>
        <FormItemSign :detailsRef="details" :label="$t('csscl.ca.common.camv')" label-width="150">
            <SearchInput style="width: 345px" v-model="dataState.currentData.caEventForm.form.swiftInstructionType"
                                url="/datamgmt/api/v1/searchinput" showDesc="true"
                                :title="$t('csscl.ca.common.camv')"
                                :params="{searchType: 'caCamv'}"
                                :columns="[
                                    {
                                        title: $t('csscl.ca.common.camvCode'),
                                        colName: 'code',
                                    },
                                    {
                                        title: $t('csscl.ca.common.camvDescription'),
                                        colName: 'codeDesc',
                                    }
                                ]"
                                :pageSizes="[10, 20, 30]">
                    </SearchInput>
        </FormItemSign>
        <FormItemSign :detailsRef="details" :label="$t('csscl.ca.common.nature')" label-width="220">
          <Select v-model="dataState.currentData.caEventForm.form.announcePayType" style="width: 200px" type='CA_PAY_METHOD_CODE' />
        </FormItemSign>
      </FormRow>
      <FormRow>
        <FormItemSign :detailsRef="details" :label="$t('csscl.ca.common.description')" label-width="150">
          <InputText :disabled="true" v-model="dataState.currentData.caEventForm.form.announceDescription" maxlength="100"
            style="width: 355px" />
        </FormItemSign>
        <FormItemSign :detailsRef="details" :label="$t('csscl.ca.common.issueCountry')" label-width="150">
          <!-- <InputText :disabled="true" v-model="caEventForm.form.countryRegionCode" maxlength="100"
            style="width: 200px" /> -->
            <GeneralSearchInput v-model="dataState.currentData.caEventForm.form.countryRegionCode" style="width:345px" searchType="ctryRegion" showDesc="true"/>
        </FormItemSign>
        <FormItemSign :detailsRef="details" :label="$t('csscl.ca.common.entitlementReconFlagRepe')">
          <InputText :disabled="true" v-model="dataState.currentData.caEventForm.form.custodianEntitleQuantityReconMatchIndicator"
            maxlength="100" style="width: 200px" />
        </FormItemSign>
      </FormRow>
      <FormRow>
        <FormItemSign :detailsRef="details" :label="$t('csscl.ca.common.securityId')" label-width="150">
          <GeneralSearchInput :disabled="true" v-model="dataState.currentData.caEventForm.form.instrumentCode" style="width:350px" searchType="instrumentCode" showDesc="true"/>
        </FormItemSign>
        <FormItemSign :detailsRef="details" :label="$t('csscl.ca.common.clearingAgent')"  label-width="150">
          <GeneralSearchInput :disabled="true" v-model="dataState.currentData.caEventForm.form.clearingAgentCode" style="width:345px" searchType="clearingAgentCode" showDesc="true"/>
        </FormItemSign>
        <FormItemSign :detailsRef="details" :label="$t('csscl.ca.common.paymentReconFlag')" label-width="220">
          <Select v-model="dataState.currentData.caEventForm.form.caEventCustodianPaymentReconMatchIndicator" style="width: 200px" type='CA_PAY_METHOD_CODE' />
        </FormItemSign>
      </FormRow>
      <FormRow>
        <FormItemSign :detailsRef="details" :label="$t('csscl.ca.common.marketReferenceNo')" label-width="150">
          <InputText :disabled="true" v-model="dataState.currentData.caEventForm.form.caEventMarketReferenceNumber" maxlength="100"
            style="width: 355px" />
        </FormItemSign>
        <FormItemSign :detailsRef="details" :label="$t('csscl.ca.common.recordDate')" label-width="150">
          <!-- <InputText :disabled="true" v-model="caEventForm.form.recordDate" maxlength="100" style="width: 200px" /> -->
          <DateItem v-model="dataState.currentData.caEventForm.form.recordDate" type="date" style="width: 200px;"/>
        </FormItemSign>
        <el-form-item-proxy />
      </FormRow>
    </el-form>

    <SectionTitle :title="$t('csscl.ca.common.caEventEntitlement')" />

    <EditGrid ref="editGridRef" :data="dataState.currentData.entitlementTableData" :formData="caEventClientEntitlementForm"
      :formRules="caEventClientEntitlementFormRules" :is-show-search="true" :showAddDeleteButtons="true"
      :onAdd="handleAddRecord" :onDelete="handleDeleteRecord" :onSaveForm="handleFormSave"
      :handleChangePage="handleChangePage" :beforeChangePage="handleBeforeChangePage"
      :onQuery="handleQuery" :enableRecordStatusHighlight="true" :onReset="onSubSearchReset"
      :isDeleteButtonDisabled="isDeleteButtonDisabled" :fieldDisabledRules="fieldDisabledRules"
      :beforeSearch="beforeSearch" :disabled="formDisabled">

      <template v-slot:searchPanel="slotProps">
        <FormRow>
          <ElFormItemProxy :label="$t('csscl.ca.common.custodyAccountNo')" label-width="150" prop="opCtryRegionCode">
            <GeneralSearchInput v-model="filterParams.tradingAccountCode" style="width:200px" searchType="custodyAcct"
              showDesc="false" codeTitle="csscl.acctCode.clientAccountOid"
              codeDescTitle="csscl.acctCode.accountShortName" />
          </ElFormItemProxy>
          <ElFormItemProxy :label="$t('csscl.ca.common.custodyAccountName')" label-width="150" prop="opCtryRegionCode">
            <InputText :disabled="false" v-model="filterParams.tradingAccountName" maxlength="100"
              style="width: 200px" />
          </ElFormItemProxy>
          <ElFormItemProxy :label="$t('csscl.ca.common.custodianAccount')" label-width="150" prop="opCtryRegionCode">
            <InputText :disabled="false" v-model="filterParams.custodianAccountNumber" maxlength="100"
              style="width: 200px" />
          </ElFormItemProxy>
        </FormRow>
      </template>

      <template #tableColumnFront>
        <el-table-column prop="tradingAccountCode" :label="$t('csscl.ca.common.custodyAccountNo')" />
        <el-table-column prop="tradingAccountName" :label="$t('csscl.ca.common.custodyAccountName')" />
        <el-table-column prop="custodianAccountNumber" :label="$t('csscl.ca.common.custodianAccount')" />
        <el-table-column prop="currentQuantity" :label="$t('csscl.ca.common.currentQuantityTradedOrSettled')" />
        <el-table-column prop="originalEntitleQuantity" :label="$t('csscl.ca.common.entitlementQuantity')" />
        <el-table-column prop="entitleSelectedQuantity" :label="$t('csscl.ca.common.selectedQuantity')" />
        <el-table-column prop="excessQuantity" :label="$t('csscl.ca.common.excessQuantity')" />
        <el-table-column prop="taxRatePercent" :label="$t('csscl.ca.common.taxRatePct')" />
        <el-table-column prop="caCashPaymentMethod" :label="$t('csscl.ca.common.clientPaymentMethod')" />
        <el-table-column prop="recordStatus" width="220" :label="$t('csscl.ca.common.recordStatus')">
          <template #default="scope">
            {{ getCommonDesc('RECORD_STATUS', scope.row.recordStatus) }}
            <span v-if="scope.row.recordStatus && scope.row.recordStatus !== 'A'">
              for {{ getCommonDesc('MKCK_ACTION', scope.row.mkckAction) }}
            </span>
          </template>
        </el-table-column>
      </template>
      <template #editForm="formSlot">
        <FormRow>
          <FormItemSign :detailsRef="formSlot.detailsRef" :label="$t('csscl.ca.common.custodyAccountNo')"
            prop="tradingAccountCode" label-width="150px">
            <GeneralSearchInput v-model="formSlot.formData.form.tradingAccountCode" style="width:200px"
              searchType="custodyAcct" showDesc="false" codeTitle="csscl.acctCode.clientAccountOid"
               :disabled="isFieldDisabled('tradingAccountCode')"
              codeDescTitle="csscl.acctCode.accountShortName"
              :dbClick="(row) => {
                formSlot.formData.form.tradingAccountName = row.codeDesc;
              }" />
          </FormItemSign>
          <FormItemSign :detailsRef="formSlot.detailsRef" :label="$t('csscl.ca.common.eventRefNo')"
            prop="caEventReferenceNumber" label-width="150px" :disabled="isFieldDisabled('caEventReferenceNumber')">
            <InputText :disabled="true" v-model="formSlot.formData.form.caEventReferenceNumber" maxlength="100"
              style="width: 200px" />
          </FormItemSign>
          <FormItemSign :detailsRef="formSlot.detailsRef" :label="$t('csscl.ca.common.securityId')" prop="instrumentCode"
            label-width="150px" :disabled="isFieldDisabled('instrumentCode')">
            <InputText :disabled="true" v-model="formSlot.formData.form.instrumentCode" maxlength="100"
              style="width: 200px" />
          </FormItemSign>
          <ElFormItemProxy />
        </FormRow>
        <FormRow>
          <FormItemSign :detailsRef="formSlot.detailsRef" :label="$t('csscl.ca.common.entitlementQuantity')"
            prop="originalEntitleQuantity" label-width="150px" >
            <InputNumber v-model="formSlot.formData.form.originalEntitleQuantity" scale="0"
              style="width: 200px" :disabled="isFieldDisabled('originalEntitleQuantity')" />
          </FormItemSign>
          <FormItemSign :detailsRef="formSlot.detailsRef" :label="$t('csscl.ca.common.selectedQuantity')"
            prop="entitleSelectedQuantity" label-width="150px" >
            <InputNumber :disabled="isFieldDisabled('entitleSelectedQuantity')" v-model="formSlot.formData.form.entitleSelectedQuantity" maxlength="100"
              style="width: 200px" />
          </FormItemSign>
          <FormItemSign :detailsRef="formSlot.detailsRef" :label="$t('csscl.ca.common.excessQuantity')"
            prop="excessQuantity" label-width="150px">
            <InputNumber  :disabled="isFieldDisabled('excessQuantity')" v-model="formSlot.formData.form.excessQuantity" maxlength="100"
              style="width: 200px" />
          </FormItemSign>
          <el-form-item-proxy />
        </FormRow>
      </template>

    </EditGrid>
  </BaseDetails>

</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue';
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus';
import { getOid, saveMsgBox, getCommonDesc } from '~/util/Function.js';
import BaseDetails from '~/pages/base/CaDetails.vue';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import { commonRules, showValidateMsg } from '~/util/Validators.js';
// import ClientEntitleDetails from './ClientEntitleDetails.vue';
import SectionTitle from '~/components/Ca/CaSectionTitle.vue';
import EditGrid from '~/components/Ca/CaEditGrid.vue';
import { getTimeZone } from "~/util/DateUtils";


// EditGrid 引用
const editGridRef = ref();



// 分页控制
const DEFAULT_PAGE_SIZE = 10;
const DEFAULT_PAGE_NUMBER = 1;
const currentPageNumber = ref(DEFAULT_PAGE_NUMBER);
const currentPageSize = ref(DEFAULT_PAGE_SIZE);

const caEventUrl = "/bff/ca/api/v1/ca-event/get-ca-event-join";
const caEntitlementBatchUpdateUrl = "/bff/ca/api/v1/ca-event-client-entitlement/edit-batch-ca-event-client-entitlement";
const caEntitlementRetrievePageUil = "/bff/ca/api/v1/ca-event-client-entitlement/get-ca-event-client-entitlement-join-page-list";

const { proxy } = getCurrentInstance();
const props = defineProps(['reload']);
const details = ref();
const formDisabled = ref(false);
const editDis = ref(false);
const showParent = ref(false);
const caEventFormRef = ref<FormInstance>();
// entitlement 数据是否已变更标记
const entitlementDataHasBeenChanged = ref(false);
// 是否查看原始数据的标记
const isViewOriginal = ref(false);


// ========框架页面方法 开始 ======

// 重置界面数据
const resetFormData = () => {
  // 重置当前数据
  dataState.value.currentData.caEventForm.form = { ...emptyCaEventTemplate };
  dataState.value.currentData.entitlementTableData = [];
  
  // 重置原始数据
  dataState.value.originalData.caEventForm.form = { ...emptyCaEventTemplate };
  dataState.value.originalData.entitlementTableData = [];
  
  // 重置最新数据
  dataState.value.latestData.caEventForm.form = { ... emptyCaEventTemplate };
  dataState.value.latestData.entitlementTableData = [];
  
  details.value.currentRow = {};
  editDis.value = false;
  entitlementDataHasBeenChanged.value = false;
  currentPageNumber.value = DEFAULT_PAGE_NUMBER;
  isViewOriginal.value = false;
  onSubSearchReset();
}

// 编辑行
const editRow = (row, disabled, newId) => {
  console.log("details editRow, enter");
  resetFormData();
  if(row?.isApproveDetail && disabled){
    dataState.value.currentData.caEventForm.form = row.afterImage;
    details.value.currentRow = dataState.value.currentData.caEventForm.form;
  } else {
    const oid = newId || (row?.currentOid && getOid(row, disabled, proxy));
    if (oid) {
      loadCaEventData(row.caEventReferenceNumber, false);
      loadEntitlementData(row.caEventReferenceNumber, false);
      editDis.value = true;
    }else{
      details.value.initWatch(dataState.value.currentData.caEventForm);
    }
  }
  console.log("details editRow, exit");
}

// 查看原始表单
const viewOriginalForm = async (pendingOid, isDisabled) => {
    console.log("details viewOriginalForm, enter");
  const refNumber = dataState.value.currentData.caEventForm.form.caEventReferenceNumber;
  currentPageNumber.value = DEFAULT_PAGE_NUMBER;

  if (!isViewOriginal.value) {
    // 查询Approved数据
    // 检查是否已加载过Approved数据
    if (!dataState.value.originalData.caEventForm.form.caEventReferenceNumber) {
      // 如果未加载过Approved数据，则加载
      await loadCaEventData(refNumber, true);
      await loadEntitlementData(refNumber, true);
    } else {
      // 如果已经加载过，直接切换
      dataState.value.currentData.caEventForm.form = { ...dataState.value.originalData.caEventForm.form };
      // dataState.value.currentData.entitlementTableData = [...dataState.value.originalData.entitlementTableData];
      await loadEntitlementData(refNumber, true);
    }
    isViewOriginal.value = true;
  } else {
    // 查看最新数据
    // 检查是否已加载过最新数据
    if (!dataState.value.latestData.caEventForm.form.caEventReferenceNumber) {
      // 如果未加载过，则加载
      await loadCaEventData(refNumber, false);
      await loadEntitlementData(refNumber, false);
    } else {
      // 如果已经加载过，直接切换
      dataState.value.currentData.caEventForm.form = { ...dataState.value.latestData.caEventForm.form };
      // dataState.value.currentData.entitlementTableData = [...dataState.value.latestData.entitlementTableData];
      await loadEntitlementData(refNumber, false);
    }
    isViewOriginal.value = false;
  }

  console.log("details viewOriginalForm, exit");
}

// 处理保存
const handleSave = async (searchValid, isOnlyValidate, unPopping) => {
  console.log("handleSave, enter");
  console.log(searchValid, isOnlyValidate, unPopping);

  if (isOnlyValidate) {
    return true;
  }

  // 收集所有变更，包括新增、修改和删除的记录
  const recordsToSave = [
    ...dataState.value.currentData.entitlementTableData
      .filter(record => record.isChanged === true || record.isDelete === true), 
    ...deletedRecords.value];

  if (recordsToSave.length === 0) {
    return true;
  }
  
  let params = generateRequestparam({ 
        caEventReferenceNumber: dataState.value.currentData.caEventForm.form.caEventReferenceNumber,
        version: dataState.value.currentData.caEventForm.form.version,
        recordStatus: "PD",
        mkckAction: "U",
        caEventClientEntitlementDTOList: recordsToSave
      });

  try {
    const response = await proxy.$axios.post(caEntitlementBatchUpdateUrl,
    params
    );


    if (isResponseSuccess(response)) {
      // 重新加载数据
      deletedRecords.value = [];
      loadCaEventData(dataState.value.currentData.caEventForm.form.caEventReferenceNumber, false);
      loadEntitlementData(dataState.value.currentData.caEventForm.form.caEventReferenceNumber, false);
      return true;
    } else {
      const errorMsg = getErrorMessage(response?.header?.message);
      showErrorMsg('Save fail: ' + errorMsg);
      console.log("handleSave, error: " + errorMsg);
      return false;
    }
  } catch (error) {
    showErrorMsg('Save fail: ' + getErrorMessage (error?.message));
    console.log("handleSave, error: " + error?.message);
    return false;
  }
};

// 显示详情
const showDetails = (row, isDisabled) => {
  console.log("details showDetails, enter");
  if (isDisabled || row.recordStatus === 'PA') {
    formDisabled.value = true;

    details.value.showDetails(row, true);
  } else {
    formDisabled.value = false;
    details.value.showDetails(row, false);
  }
  showParent.value = false;
  resetFormData();
  editRow(row, isDisabled);
  console.log("details showDetails, exit");
}

// 重写beforeSaveValid方法，submit时会调用到
const beforeSaveValid = (str) => {

if (str === 'submitted') {
  // 如果是直接按submit按钮， 只要Ca Event的recordStatus是PD，就可以提交
  if (dataState.value.currentData.caEventForm.form.recordStatus == 'PD') {
    return true;
  } 
  // 如果是直接按submit按钮， 只要Ca Event的recordStatus不是PD，就要看是否已经修改过Entitlement数据
  // 只有修改过Entitlement数据，才可以提交
  if (!entitlementDataHasBeenChanged.value) {
    ElMessage({
    message: proxy.$t('message.data.not.modify'),
    type: 'error',
    duration: 10000,
    offset: 100,
    showClose: true,
  });
    return entitlementDataHasBeenChanged.value; 
  }
  return true;
}

// 保存之前的检查
// 检查表格数据是否有变化
const hasChanges = dataState.value.currentData.entitlementTableData.some(
  record => record?.isChanged === true || record?.isDelete === true
) || deletedRecords.value.length > 0 || entitlementDataHasBeenChanged.value;

if (!hasChanges) {
  ElMessage({
    message: proxy.$t('message.data.not.modify'),
    type: 'error',
    duration: 10000,
    offset: 100,
    showClose: true,
  });
  return false;
}

return true;
}
// ========框架页面方法 结束 ======

// ========通用方法 开始======

// 生成请求参数
const generateRequestparam = (data) => {
  let params = {
    header: {timezone: getTimeZone(), lang:"en_US"},
    data: data
  };
  return params;
}

// 判断响应是否成功
const isResponseSuccess = (response) => {
  if (!response || !response.header) {
    return false;
  }
  return response.header.code === '000000';
}

// 获取错误信息
const getErrorMessage = (errorMessage) => {
  return errorMessage || 'Unknown error';
}

const showErrorMsg = (errorMsg) => {
  ElMessageBox.alert(errorMsg, 'Error', {
                    confirmButtonText: 'OK',
                    type: 'error',
                }); 
}

// ========通用方法 结束======


// ========页面数据 开始======

// 空白的 Ca Event 数据模板
const emptyCaEventTemplate = {
  oid: '',
    caEventReferenceNumber: '',
    caEventCategory: '',
    swiftEventCode: '',
    announceDescription: '',
    instrumentOid: '',
    instrumentCode: '',
    instrumentShortName: '',
    marketCaEventReference: '',
    caEventSourceChannel: '',
    swiftInstructionType: '',
    countryRegionCode: '',
    clearingAgentCode: '',
    recordDate: '',
    caEventStatus: '',
    caCashPaymentMethod: '',
    announcePayType: '',
    custodianEntitleQuantityReconMatchIndicator: '',
    caEventCustodianPaymentReconMatchIndicator: '',
    caEventMarketReferenceNumber: '',
    version: '',
    recordStatus: '',
    mkckAction: ''
}


// 删除的记录
const deletedRecords = ref([]);

// Entitlement 子表表单类型, 用于EditGrid中的输入
interface CaEventClientEntitlementForm {
  caEventOid: Number,
  tradingAccountCode: String,
  caEventReferenceNumber: String,
  originalEntitleQuantity: Number,
  entitleSelectedQuantity: Number,
  excessQuantity: Number,
  instrumentOid: Number,
  nameClassCode: String,
  dueOverdueQuantity: Number,
  entitleQuantity: Number,
  taxAmount: Number,
  taxCurrencyCode: String,
  taxPayTypeCode: String,
  chargeAmount: Number,
  chargeCurrencyCode: String,
  chargeTypeCode: String,
  adjustedIndicator: String,
  recordDate: Date,
  countryRegionCode: String,
  marketCode: String,
};

// Entitlement 子表表单验证规则
const caEventClientEntitlementFormRules = reactive<FormRules<CaEventClientEntitlementForm>>({
  tradingAccountCode: [
    commonRules.required,
  ],
  caEventReferenceNumber: [
    commonRules.required,
  ],
  originalEntitleQuantity: [
    commonRules.required,
  ],
  entitleSelectedQuantity: [
    commonRules.required,
  ],
  excessQuantity: [
    commonRules.required,
  ],
  instrumentOid: [
    commonRules.required,
  ],
  nameClassCode: [
    commonRules.required,
  ],
  dueOverdueQuantity: [
    commonRules.required,
  ],
  entitleQuantity: [
    commonRules.required,
  ],
  taxAmount: [
    commonRules.required,
  ],
  taxCurrencyCode: [
    commonRules.required,
  ],
  taxPayTypeCode: [
    commonRules.required,
  ],
  chargeAmount: [
    commonRules.required,
  ],
  chargeCurrencyCode: [
    commonRules.required,
  ],
  chargeTypeCode: [
    commonRules.required,
  ],
  adjustedIndicator: [
    commonRules.required,
  ],
  recordDate: [
    commonRules.required,
  ],
  countryRegionCode: [
    commonRules.required,
  ],
  marketCode: [
    commonRules.required,
  ],
});

// Entitlement 子表表单, 用于EditGrid中的输入
const caEventClientEntitlementForm = reactive({
  rules: () => { return [{ rules: caEventClientEntitlementFormRules }] },
  form: {
    oid: "",
    caEventOid: "",
    caEventClientEntitleOid: "",
    tradingAccountCode: "",
    caEventReferenceNumber: "",
    originalEntitleQuantity: null,
    entitleSelectedQuantity: "",
    excessQuantity: null,
    instrumentOid: "",
    instrumentCode: "",
    nameClassCode: "",
    dueOverdueQuantity: null,
    entitleQuantity: null,
    taxAmount: null,
    taxCurrencyCode: "",
    taxPayTypeCode: "",
    chargeAmount: null,
    chargeCurrencyCode: "",
    chargeTypeCode: "",
    adjustedIndicator: "",
    recordDate: null,
    countryRegionCode: "",
    marketCode: "",
  }
});

// 数据状态
const dataState = ref({
  // 当前显示的数据
  currentData: {
    caEventForm: {
      form: { ...emptyCaEventTemplate }
    },
    entitlementTableData: []
  },
  // 原始数据 (Approved)
  originalData: {
    caEventForm: {
      form: { ...emptyCaEventTemplate }
    },
    entitlementTableData: []
  },
  // 最新数据 (包含Pending 数据)
  latestData: {
    caEventForm: {
      form: { ...emptyCaEventTemplate }
    },
    entitlementTableData: []
  }
});


// 子查询参数
const filterParams = reactive({
  tradingAccountCode: '',
  tradingAccountName: '',
  custodianAccountNumber: ''

})

// ========页面数据 结束======


// ========entitlement 子表表格方法 开始======

// 添加记录
const handleAddRecord = (data) => {
  console.log("handleAddRecord, enter");
  console.log(data);
  data.caEventReferenceNumber = dataState.value.currentData.caEventForm.form.caEventReferenceNumber;
  data.instrumentOid = dataState.value.currentData.caEventForm.form.instrumentOid;
  data.instrumentCode = dataState.value.currentData.caEventForm.form.instrumentCode;
  data.custodianAccountNumber = dataState.value.currentData.caEventForm.form.custodianAccountNumber;
  data.systemIndicator = 'N';
  data.excessQuantity = 0;
  data.tradingAccountName = ''; 
  data.caCashPaymentMethod = 'Actual payment';
  console.log("handleAddRecord, exit");
};

const handleFormSave = (formData) => {
  console.log("handeFormSave, enter");

  formData.isChanged = true;

  // 查找是否已存在该记录
  const index = dataState.value.currentData.entitlementTableData.findIndex(item => item.oid === formData.oid);

  if (index !== -1) {
    // 如果找到匹配记录，更新现有记录
    Object.assign(dataState.value.currentData.entitlementTableData[index], formData);
    // 标记为更新状态
    console.log("update record:", formData.oid);
  } else {
    dataState.value.currentData.entitlementTableData.push(formData);
    console.log("updae record ( has oid):", formData.oid);
  }

  entitlementDataHasBeenChanged.value = true;

  console.log("handeFormSave, exit");
}

// 删除记录
const handleDeleteRecord = (record) => {
  // 保存要删除的，从后端加载的记录
  // 对于pending for create 的记录， 删除后马上从界面表格中删除，保存的时候再提交
  if (record?.mkckAction == 'C' && record?.recordStatus == 'PD') {
    if (record?.oid === record?.currentOid) {
      record.mkckAction = 'D';
      record.recordStatus = 'PD';
      record.isDelete = true;
      const index = deletedRecords.value.findIndex(item => item.oid === record.oid);
      if (index === -1) {
        deletedRecords.value.push(record);
      }
    }
    entitlementDataHasBeenChanged.value = true;
  }
};

// 决定Entitlement 子表表格的删除按钮是否禁用, 如果systemIndicator为Y, 则禁用
const isDeleteButtonDisabled = (record) => {
  console.log("isDeleteButtonDisabled:" + record?.systemIndicator);
  const rtn = record?.systemIndicator === 'Y' || formDisabled.value || details.value?.editing;
  return rtn;
}

// 从后端获取Ca Event 数据
const loadCaEventData = async (eventReferenceNumber, isOriginal) => {
  if (!eventReferenceNumber) return;

  try {

    let params = generateRequestparam({
      caEventReferenceNumber: eventReferenceNumber,
      onlyGetApprovedRecord: isOriginal ? 'Y' : 'N'
    });
    
    const response = await proxy.$axios.post(caEventUrl, params);

    if (isResponseSuccess(response)) {
      // 取得的是original数据还是latest数据
      if (isOriginal) {
        dataState.value.originalData.caEventForm.form = response.data;
      } else {
        dataState.value.latestData.caEventForm.form = response.data;
      }
      // 更新当前显示的数据
      dataState.value.currentData.caEventForm.form = {...response.data};
      // caEventForm.form = {...response.data};
      if (!isOriginal) {
        details.value.currentRow = {...response.data};
      }
    } else {
      const errorMsg = getErrorMessage(response?.header?.message);
      showErrorMsg('Load caEvent data fail: ' + errorMsg);
    }
    if (!isOriginal) {
      details.value.initWatch(details.value.caEventForm);
    }
  } catch (error) {
    const errorMsg = getErrorMessage(error?.message);
    showErrorMsg('Load caEvent data fail: ' + errorMsg);
    console.error("retrieve caEvent data failed:", error);
  }
}

// 重置分页控制参数
const ressetPageControlParam = () => {
  currentPageNumber.value = DEFAULT_PAGE_NUMBER;
  currentPageSize.value = DEFAULT_PAGE_SIZE;
}

// 从后端获取数据的方法
const loadEntitlementData = async (eventReferenceNumber, isOriginal) => {

  if (!eventReferenceNumber) return;

  try {
    let params = generateRequestparam({
        caEventReferenceNumber: eventReferenceNumber,
        onlyGetApprovedRecord: isOriginal ? 'Y' : 'N',
        pageNumber: currentPageNumber.value,
        pageSize: currentPageSize.value,
        ...filterParams
      });

    const response = await proxy.$axios.post(caEntitlementRetrievePageUil, params);

    if (isResponseSuccess(response)) {
      // 取得的是original数据还是latest数据
      if (isOriginal) {
        dataState.value.originalData.entitlementTableData = [...response.data.items];
      } else {
        dataState.value.latestData.entitlementTableData = [...response.data.items];
      }
      // 更新当前显示的数据
      dataState.value.currentData.entitlementTableData = [...response.data.items];


      // 更新分页控制参数
      currentPageNumber.value = response.data.pageNumber;
      currentPageSize.value = response.data.pageSize;
      editGridRef.value.total = response.data.totalCount;
      editGridRef.value.totalPage = response.data.totalPage;
      editGridRef.value.currentPage = response.data.pageNumber;
      editGridRef.value.pageSize = response.data.pageSize;
      console.log("CaEntitlementDetails update success:", dataState.value.currentData.entitlementTableData.length);
    } else {
      const errorMsg = getErrorMessage(response?.header?.message);
      showErrorMsg('Load Ca Entitlement Details data fail: ' + errorMsg);
    }
  } catch (error) {
    const errorMsg = getErrorMessage(error?.message);
    showErrorMsg('Load Ca Entitlement Details data fail: ' + errorMsg);
    console.error("retrieve CaEntitlementDetails data failed:", error);
  }
};

const handleQuery = async (param) => {
  if (dataState.value.currentData.caEventForm.form.caEventReferenceNumber) {
    currentPageNumber.value = 1;
    await loadEntitlementData(dataState.value.currentData.caEventForm.form.caEventReferenceNumber, isViewOriginal.value);
  }
}

const onSubSearchReset = () => {
  console.log("onSubSearchReset, enter");
  filterParams.tradingAccountCode = '';
  filterParams.tradingAccountName = '';
  filterParams.custodianAccountNumber = '';
  console.log("onSubSearchReset, exit");
}


const handleChangePage = async (newPage) => {
  console.log("handleChangePage, enter");
  console.log(newPage);
  if (dataState.value.currentData.caEventForm.form.caEventReferenceNumber) {
    currentPageNumber.value = newPage;
    await loadEntitlementData(dataState.value.currentData.caEventForm.form.caEventReferenceNumber, isViewOriginal.value);
  }
  console.log("handleChange, exit");
}

const beforeSearch = async (search, params) => {
  console.log("beforeSearch, enter");
  
  // 检查是否有未保存的修改
  const hasUnsavedChanges = dataState.value.currentData.entitlementTableData.some(item => item.isChanged);
  if (hasUnsavedChanges) {
    try {
      await ElMessageBox.confirm(
        'Save the changes before search?',
        'Warning',
        {
          confirmButtonText: 'Save',
          cancelButtonText: 'Cancel',
          type: 'warning',
        }
      );
      // 用户选择保存,调用保存方法
      await handleSave();
      return true;
    } catch (e) {
      // 用户取消保存,阻止切换页面
      return false;
    }
  }
  console.log("beforeSearch, exit");
  return true;
}

const handleBeforeChangePage = async () => {
  console.log("handleBeforeChangePage, enter");
  
  // 检查是否有未保存的修改
  const hasUnsavedChanges = dataState.value.currentData.entitlementTableData.some(
  record => record?.isChanged === true || record?.isDelete === true) || deletedRecords.value.length > 0 ;

  //dataState.value.currentData.entitlementTableData.some(item => item.isChanged);
  
  if (hasUnsavedChanges) {
    try {
      await ElMessageBox.confirm(
        'Save the changes before switch page?',
        'Warning',
        {
          confirmButtonText: 'Save',
          cancelButtonText: 'Cancel',
          type: 'warning',
        }
      );
      // 用户选择保存,调用保存方法
      await handleSave();
      return { isChangePage: true };
    } catch (e) {
      // 用户取消保存,阻止切换页面
      return { isChangePage: false };
    }
  }
  
  console.log("handleBeforeChangePage, exit");
  return true;
}

// 定义字段禁用规则
const fieldDisabledRules = {
  // 始终禁用的字段
  caEventReferenceNumber: true,
  instrumentCode: true,
  
  // 根据编辑状态禁用的字段
  originalEntitleQuantity: (isEdit) => isEdit,
  entitleSelectedQuantity: false,
  excessQuantity: false,
  
  // 其他字段默认可用
  tradingAccountCode: (isEdit) => isEdit,
};

// 添加计算属性用于判断字段是否禁用
const isFieldDisabled = (fieldName) => {
  
  const rule = fieldDisabledRules[fieldName];
  if (typeof rule === 'boolean') return rule;
  if (typeof rule === 'function') return rule(editGridRef.value.isEditMode);
  return false;
};


// ========entitlement 子表表格方法 结束======

defineExpose({
  details,
  editRow,
  showDetails,
  // beforeSaveValid, 
});

</script>
<style scoped></style>
