<template>
  <BasePanel :searchParams="searchParams" :paramListData='paramListData' url="/cashmgmt/api/v1/cash/operation/baltxn/list"
    :params="{ modeEdit: 'Y' }" :showDetails="showDetails" :deleteRow="deleteRow" :editRow="editRow" hide-operation="true"
    :rules="rules" :sortProp="{}" :beforeSearch="beforeSearch" >
    <template v-slot:searchPanel="slotProps">
      <FormRow>
        <ElFormItemProxy :label="$t('csscl.acctCode.searchLevel')" prop="searchLevel" label-width="290">
          <Select v-model="searchParams.searchLevel" type="SEARCH_LEVEL"  :change="searchLevelType(searchParams.searchLevel)" />
        </ElFormItemProxy>
        <ElFormItemProxy></ElFormItemProxy>
        <ElFormItemProxy></ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy :label="$t('csscl.acctCode.clientMasterOid')" prop="clientCode" label-width="290">
          <GeneralSearchInput v-model="searchParams.clientCode"  :disabled = "clientCodeDisabled" setTitle="true"
                style="width:200px"
                maxlength="11"
                searchType="clientCode"
                showDesc="false"
                :change="(val)=>{
                  searchParams.clientMasterOid='';
                  searchParams.clientAccountOid = '';
                  searchParams.tradingAccountCode = '';
                }"
                :dbClick="(row)=>{ 
                  searchParams.clientMasterOid=row.var1;
                  searchParams,clientAccountOid = '';
                  searchParams.tradingAccountCode = '';
                }"
                />

        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.acctCode.clientGroupCode')" prop="clientGroupCode">
          <CommonSearchInput v-model="slotProps.form.clientGroupCode" 
            :disabled="clientGroupDisabled"
            codeTitle="csscl.acctCode.clientGroupCode"
            maxlength="50" 
            commType="CLIENT_GROUP"  
            setTitle="true"
            showDesc="false" />
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.acctCode.custodyAcctNumber')" prop="custodyAccNo">
          <GeneralSearchInput v-model="slotProps.form.custodyAccNo"  
            :disabled = "custodyAccDisabled"
            searchType="custodyAcct"
            showDesc="false"
            setTitle="true"
            :params="{var1:slotProps.form.clientMasterOid}"
            :change="(val)=>{
              slotProps.form.clientAccountOid='';
            }"
            :dbClick="(row)=>{
              slotProps.form.clientAccountOid=row.var2;
            }"
            />
        </ElFormItemProxy>
     
      </FormRow>
      <FormRow>
        <ElFormItemProxy :label="$t('csscl.acctCode.fundMgrCode')" prop="fundMgrCode" label-width="290">
          <CommonSearchInput v-model="slotProps.form.fundMgrCode" 
            :disabled="fundMgrDisabled"
            codeTitle="csscl.acctCode.fundMgrCode"
            maxlength="50" 
            style="width:300px" 
            setTitle="true"
            commType="FUND_MANAGER" 
            showDesc="false" />
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.acctCode.clientFundId')" prop="clientFundId">
          <GeneralSearchInput v-model="slotProps.form.clientFundId"  :disabled = "clientFundDisabled"
            codeTitle="csscl.acctCode.clientFundId"
            searchType="clientFundID"
            setTitle="true"
            maxlength="50"
            showDesc="false" />
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.acctCode.servicePlan')" prop="servicePlan">
          <CommonSearchInput v-model="slotProps.form.servicePlan" 
            :disabled="servicePlanDisabled"
            codeTitle="csscl.acctCode.servicePlan"
            setTitle="true"
            maxlength="10" 
            commType="SERVICE_PLAN_CODE"  
            showDesc="false" />
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy label-width="290"  :label="$t('csscl.agent.clearingAgentCode')" prop="clearingAgentCode">
          <GeneralSearchInput  v-model="slotProps.form.clearingAgentCode" 
            :disabled="clearingAgentDisabled"
            setTitle="true"
            showDesc="false"
            searchType="clearingAgentCode" />
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.acctCode.custdodianAccNo')" prop="custodianAccNo">
          <GeneralSearchInput v-model="slotProps.form.custodianAccNo" 
            :disabled="custodianAccDisabled"
            codeTitle="csscl.acctCode.custdodianAccNo"
            setTitle="true"
            searchType="custodianAccNum"
            showDesc="false" />
        </ElFormItemProxy>
      <ElFormItemProxy></ElFormItemProxy> 
      </FormRow>
      <FormRow>
        <ElFormItemProxy :label="$t('csscl.acctCode.currency')" prop="currency" disabled label-width="290">
          <CurrencySearchInput v-model="slotProps.form.currency" :disabled = "currencyDisabled"
            codeTitle='csscl.acctCode.currency'
            setTitle="true"
            showDesc="false"
            input-style="width:110px"
          />
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.acctCode.bankAccountNo')" prop="cashAccountNo">
          <el-input v-model="slotProps.form.cashAccountNo" maxlength="18" style="width:300px;"  input-style="text-transform:none" setTitle="true" :disabled="cashAccountDisabled"/>
        </ElFormItemProxy>
        <ElFormItemProxy></ElFormItemProxy>
      </FormRow>
    </template>

    <template v-slot:tableColumn>
      <el-table-column sortable="custom" prop="clientCode" :label="$t('csscl.acctCode.clientMasterOid')"
        header-align="center" width="200" />
      <el-table-column sortable="custom" prop="clientShortName" :label="$t('csscl.acctCode.clientShortName')"
        header-align="center" />
      <el-table-column sortable="custom" prop="custodyAccNo" :label="$t('csscl.acctCode.clientAccountOid')"
        header-align="center" width="250" >
        <template #default="scope">
          {{ formatCustodyAccountNumber(scope.row.custodyAccNo) }}
        </template>  
      </el-table-column>
      <el-table-column sortable="custom" prop="accountShortName" :label="$t('csscl.acctCode.accountShortName')"
        header-align="center" width="450" />
      <el-table-column sortable="custom" prop="clearingAgentCode" :label="$t('csscl.agent.clearingAgentCode')"
        header-align="center" width="250" />
        <el-table-column sortable="custom" prop="custodianAccNo" :label="$t('csscl.acctCode.custdodianAccNo')"
        header-align="center" width="250" />
      <el-table-column sortable="custom" prop="currency" :label="$t('csscl.currencyManagement.currencyCode')"
        header-align="center" width="150">
      </el-table-column>
      <el-table-column sortable="custom" prop="cashAccountNo" :label="$t('csscl.acctCode.bankAccountNo')" width="150">
      </el-table-column>
    </template>
  </BasePanel>
  <Details ref="detailsRef" />
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance, watchEffect } from 'vue';
import { Search } from '@element-plus/icons-vue'
import BasePanel from '~/pages/base/index.vue'
import Details from './Details.vue'
import { getCommonDesc } from '~/util/Function.js';
import CtryRegionSearchInput from '~/pages/base/CtryRegionSearchInput.vue';
import CommonSearchInput from '~/pages/base/CommonSearchInput.vue';
import CurrencySearchInput from '~/pages/base/CurrencySearchInput.vue';
import { formatCustodyAccountNumber} from '~/util/AccountUtils.js';
import {commonRules} from "~/util/Validators";


const { proxy } = getCurrentInstance();
const paramListData = ref({});
const searchParams = ref({
  //顺序和上面绑定参数一致
  searchLevel:"",
  clientCode: "",
  clientGroupCode: "",
  custodyAccNo:"",
  fundMgrCode: "",
  clientFundId: "",
  servicePlan: "",
  clearingAgentCode:"",
  custodianAccNo:"",
  currency:"",
  cashAccountNo:""

});

onMounted(()=>{
  // let searchParamsData = searchParams.value;
  // for(let i=0;i<searchParamsData.length;i++){
  //   if(searchParamsData[i]!=''){
  //     seachLevelDisabled(searchParamsData[i]);
  //   }
  // }

  // let lableBtn = document.querySelectorAll("label, div.ep-form-item__label");
  // let lable1 = lableBtn[0];
  // let lablevalue = document.querySelectorAll("label");
  // console.log(lableBtn)
  // console.log(lable1)
  // console.log(lablevalue)
  // let searchInputs = document.querySelectorAll("section.search-container input[searchtype]");
});



const detailsRef = ref();
const showDetails = (row, disabled) => {
  disabled = false;
  detailsRef.value.showDetails(row, disabled);
}
const deleteRow = (row) => {
  console.log("Delete ...");
}
const editRow = (row) => {
  detailsRef.value.editRow(row);
}

const clientCodeDisabled = ref(true);
const clientGroupDisabled = ref(true);
const custodyAccDisabled = ref(true);
const fundMgrDisabled = ref(true);
const clientFundDisabled = ref(true);
const servicePlanDisabled = ref(true);
const clearingAgentDisabled = ref(true);
const custodianAccDisabled = ref(true);
const currencyDisabled = ref(true);
const cashAccountDisabled = ref(true);


const seachLevelDisabled = (value)=>{
  
  if(value==''){
    // If the field is blank, all the other fields must be disable and clear. 
    clientCodeDisabled.value=true;
    clientGroupDisabled.value=true;
    custodyAccDisabled.value=true;
    fundMgrDisabled.value=true;
    clientFundDisabled.value=true;
    servicePlanDisabled.value=true;
    clearingAgentDisabled.value=true;
    custodianAccDisabled.value=true;
    currencyDisabled.value=true;
    cashAccountDisabled.value=true;
    searchParams._value.clientCode = "";
    searchParams._value.clientGroupCode = "";
    searchParams._value.custodyAccNo = "";
    searchParams._value.fundMgrCode = "";
    searchParams._value.clientFundId = "";
    searchParams._value.servicePlan = "";
    searchParams._value.clearingAgentCode = "";
    searchParams._value.custodianAccNo = "";
    searchParams._value.currency = "";
    searchParams._value.cashAccountNo = "";
  }else if(value=='clientGroup'){
    //If Client Group is selected, only the Client Group and currency field is enabled for input, and all other fields must be disable and clear. 
    clientCodeDisabled.value=true;
    clientGroupDisabled.value=false;
    custodyAccDisabled.value=true;
    fundMgrDisabled.value=true;
    clientFundDisabled.value=true;
    servicePlanDisabled.value=true;
    clearingAgentDisabled.value=true;
    custodianAccDisabled.value=true;
    currencyDisabled.value=false;
    cashAccountDisabled.value=true;
    searchParams._value.clientCode = "";
    searchParams._value.custodyAccNo = "";
    searchParams._value.fundMgrCode = "";
    searchParams._value.clientFundId = "";
    searchParams._value.servicePlan = "";
    searchParams._value.clearingAgentCode = "";
    searchParams._value.custodianAccNo = "";
    searchParams._value.cashAccountNo = "";

  }else if(value=='clientFundId'){
    //If Client Fund ID is selected, only the Client Fund ID and currency field is enabled for input, and all other fields must be disable and clear. 
    clientCodeDisabled.value=true;
    clientGroupDisabled.value=true;
    custodyAccDisabled.value=true;
    fundMgrDisabled.value=true;
    clientFundDisabled.value=false;
    servicePlanDisabled.value=true;
    clearingAgentDisabled.value=true;
    custodianAccDisabled.value=true;
    currencyDisabled.value=false;
    cashAccountDisabled.value=true;
    searchParams._value.clientCode = "";
    searchParams._value.clientGroupCode = "";
    searchParams._value.custodyAccNo = "";
    searchParams._value.fundMgrCode = "";
    searchParams._value.servicePlan = "";
    searchParams._value.clearingAgentCode = "";
    searchParams._value.custodianAccNo = "";
    searchParams._value.cashAccountNo = "";

  }else if(value=='fundManager'){
    //If Fund Manager is selected, only the Fund Manager and currency field is enabled for input, and all other fields must be disable and clear. 
    clientCodeDisabled.value=true;
    clientGroupDisabled.value=true;
    custodyAccDisabled.value=true;
    fundMgrDisabled.value=false;
    clientFundDisabled.value=true;
    servicePlanDisabled.value=true;
    clearingAgentDisabled.value=true;
    custodianAccDisabled.value=true;
    currencyDisabled.value=false;
    cashAccountDisabled.value=true;
    searchParams._value.clientCode = "";
    searchParams._value.clientGroupCode = "";
    searchParams._value.custodyAccNo = "";
    searchParams._value.clientFundId = "";
    searchParams._value.servicePlan = "";
    searchParams._value.clearingAgentCode = "";
    searchParams._value.custodianAccNo = "";
    searchParams._value.cashAccountNo = "";

  }else if(value=='nominee'){
    //If Nominee is selected, only two fields (Currency, Cash Account Number) are enabled for input, and all other fields must be disable and clear. 
    clientCodeDisabled.value=true;
    clientGroupDisabled.value=true;
    custodyAccDisabled.value=true;
    fundMgrDisabled.value=true;
    clientFundDisabled.value=true;
    servicePlanDisabled.value=true;
    clearingAgentDisabled.value=true;
    custodianAccDisabled.value=true;
    currencyDisabled.value=false;
    cashAccountDisabled.value=false;
    searchParams._value.clientCode = "";
    searchParams._value.clientGroupCode = "";
    searchParams._value.custodyAccNo = "";
    searchParams._value.fundMgrCode = "";
    searchParams._value.clientFundId = "";
    searchParams._value.servicePlan = "";
    searchParams._value.clearingAgentCode = "";
    searchParams._value.custodianAccNo = "";

  }else if(value=='clearingAgent'){
    // If Sub-custodian / Local Clearing Agent is selected, only two fields (Sub-custodian / Local Clearing Agent Code, 
    //Custodian Account Number and currency) are enabled for input, and all other fields must be disable and clear. 
    clientCodeDisabled.value=true;
    clientGroupDisabled.value=true;
    custodyAccDisabled.value=true;
    fundMgrDisabled.value=true;
    clientFundDisabled.value=true;
    servicePlanDisabled.value=true;
    clearingAgentDisabled.value=false;
    custodianAccDisabled.value=false;
    currencyDisabled.value=false;
    cashAccountDisabled.value=true;
    searchParams._value.clientCode = "";
    searchParams._value.clientGroupCode = "";
    searchParams._value.custodyAccNo = "";
    searchParams._value.fundMgrCode = "";
    searchParams._value.clientFundId = "";
    searchParams._value.servicePlan = "";
    searchParams._value.cashAccountNo = "";

  }else if(value=='servicePlan'){
    // If Service Plan is selected, only the Service Plan field is enabled for input, and all other fields must be disable and clear.
    clientCodeDisabled.value=true;
    clientGroupDisabled.value=true;
    custodyAccDisabled.value=true;
    fundMgrDisabled.value=true;
    clientFundDisabled.value=true;
    servicePlanDisabled.value=false;
    clearingAgentDisabled.value=true;
    custodianAccDisabled.value=true;
    currencyDisabled.value=true;
    cashAccountDisabled.value=true;
    searchParams._value.clientCode = "";
    searchParams._value.clientGroupCode = "";
    searchParams._value.custodyAccNo = "";
    searchParams._value.fundMgrCode = "";
    searchParams._value.clientFundId = "";
    searchParams._value.clearingAgentCode = "";
    searchParams._value.custodianAccNo = "";
    searchParams._value.currency = "";
    searchParams._value.cashAccountNo = "";
  }else if(value=='clientNumber'){
    // If Client Number CIN is selected, only the Client Number CIN and currency field is enabled for input, and all other fields must be disable and clear. 
    clientCodeDisabled.value=false;
    clientGroupDisabled.value=true;
    custodyAccDisabled.value=true;
    fundMgrDisabled.value=true;
    clientFundDisabled.value=true;
    servicePlanDisabled.value=true;
    clearingAgentDisabled.value=true;
    custodianAccDisabled.value=true;
    currencyDisabled.value=false;
    cashAccountDisabled.value=true;
    searchParams._value.clientGroupCode = "";
    searchParams._value.custodyAccNo = "";
    searchParams._value.fundMgrCode = "";
    searchParams._value.clientFundId = "";
    searchParams._value.servicePlan = "";
    searchParams._value.clearingAgentCode = "";
    searchParams._value.custodianAccNo = "";
    searchParams._value.cashAccountNo = "";
  }else if(value=='custodyAccount'){
    //If Custody Account is selected, only the Custody Account field is enabled for input, and all other fields must be disable and clear.
    clientCodeDisabled.value=true;
    clientGroupDisabled.value=true;
    custodyAccDisabled.value=false;
    fundMgrDisabled.value=true;
    clientFundDisabled.value=true;
    servicePlanDisabled.value=true;
    clearingAgentDisabled.value=true;
    custodianAccDisabled.value=true;
    currencyDisabled.value=true;
    cashAccountDisabled.value=true;
    searchParams._value.clientCode = "";
    searchParams._value.clientGroupCode = "";
    searchParams._value.fundMgrCode = "";
    searchParams._value.clientFundId = "";
    searchParams._value.servicePlan = "";
    searchParams._value.clearingAgentCode = "";
    searchParams._value.custodianAccNo = "";
    searchParams._value.currency = "";
    searchParams._value.cashAccountNo = "";
  }
};

const rules = reactive({
  searchLevel:[
      commonRules.selectRequired,
  ]
})

//paramList 参数显示用的
function searchLevelType(value) {
  seachLevelDisabled(value);

  // let arr = Object.keys(searchParams.value);
  // console.log(searchParams._value);
  // for(let i =0; i< arr.length; i++) {
  //   console.log(arr[i]);
  //   searchParams._value[arr[i]] = '';
    
  // }
  // console.log(searchParams._value);


  paramListData._value.searchLevel = getCommonDesc('SEARCH_LEVEL', value);
}
function accountStatusType(value) {
  paramListData._value.accountStatus = getCommonDesc('ACCOUNT_STATUS', value);
}


const beforeSearch = () => {
  if (!beforeSearch.first) {
    beforeSearch.first = true;
    return false;
  }
}

</script>

<style></style>