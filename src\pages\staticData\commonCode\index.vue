<template>
  <BasePanel :searchParams="searchParams" :paramListData='paramListData' url="/datamgmt/api/v1/comcode/list" :params="{ modeEdit: 'Y' }"
    :showDetails="showDetails" :deleteRow="deleteRow" :editRow="editRow" ref="tableRef" :sortProp="{}">
    <template v-slot:searchPanel="slotProps">
      <FormRow>
        <ElFormItemProxy :label="$t('common.title.opCtryRegionCode')" prop="opCtryRegionCode">
          <CtryRegionSearchInput v-model="slotProps.form.opCtryRegionCode" showDesc="false"
            opCtryRegion />
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.commonCode.commonCodeType')" label-width="150" prop="codeType">
          <Select v-model="slotProps.form.codeType" :source="commType" type="CODE_TYPE" :change="codeType(slotProps.form.codeType)"/>
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy :label="$t('csscl.commonCode.code')" prop="code">
          <SearchInput v-model="slotProps.form.code"
            style="width: 650px" 
            showDesc="false" 
            maxlength="100"
            url="/datamgmt/api/v1/comcode/list" 
            :title="$t('csscl.commonCode.code')" 
            :params="{}" 
            :columns="[
              {
                title: $t('csscl.commonCode.code'),
                colName: 'code',
              },
              {
                title: $t('csscl.commonCode.description'),
                colName: 'codeDesc',
              }
            ]">
          </SearchInput>
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.commonCode.description')" label-width="150" prop="codeDesc">
          <el-input v-model="slotProps.form.codeDesc" maxlength="150" style="width: 600px;" input-style="text-transform:none" />
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy :label="$t('common.title.status')" prop="status">
          <Select  v-model="slotProps.form.status" type="STATUS" v-model:desc="paramListData.status" />
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('common.title.recordStatus')" label-width="150" prop="multipleRecordStatus">
          <MultipleSelect v-model="slotProps.form.multipleRecordStatus" v-model:desc="paramListData.multipleRecordStatus"  />
        </ElFormItemProxy>
      </FormRow>
      <el-button @click="clearCache" v-show="false">loadCache</el-button>
    </template>
    <template v-slot:tableColumn>
      <el-table-column align="left" header-align="center" sortable="custom" prop="opCtryRegionCode"
        :label="$t('common.title.opCtryRegionCode')" width="300" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="codeName"
        :label="$t('csscl.commonCode.commonCodeType')" width="280" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="code"
        :label="$t('csscl.commonCode.code')" width="280" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="codeDesc"
        :label="$t('csscl.commonCode.description')" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="status"
        :label="$t('common.title.status')" width="130">
        <template #default="scope">
          {{ getCommonDesc('STATUS', scope.row.status) }}
        </template>
      </el-table-column>
      <el-table-column align="left" header-align="center" sortable="custom" prop="recordStsMkckAction"
        :label="$t('common.title.recordStatus')" width="210">
        <template #default="scope">
          {{ getRecordStatusDesc(scope.row) }}
        </template>
      </el-table-column>
    </template>
  </BasePanel>
  <Details ref="detailsRef" :reload="reload" />
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance, watch } from 'vue';
import { Search } from '@element-plus/icons-vue'
import { getCommonDesc } from '~/util/Function.js';
import BasePanel from '~/pages/base/index.vue'
import Details from './Details.vue'
import CtryRegionSearchInput from '~/pages/base/CtryRegionSearchInput.vue';
import { getOid, getRecordStatusDesc } from '~/util/Function.js';

const { proxy } = getCurrentInstance()
const paramListData = {};
const searchParams = ref({
  opCtryRegionCode:"",
  codeType:"",
  code:"",
  codeDesc:"",
  status:"",
  multipleRecordStatus:[]
});

const tableRef = ref();
const detailsRef = ref();
const commType = ref();
const showDetails = (row, disabled) => {
  detailsRef.value.showDetails(row, disabled);
}

const deleteRow = (row) => {
  proxy.$axios.delete("/datamgmt/api/v1/comcode?commonCodeId=" + getOid(row, false, null, true)).then((body) => {
    if (body.success) {
      tableRef.value.load();
    }
  });
}
const editRow = (row) => {
  detailsRef.value.editRow(row);
}
const reload = () => {
  tableRef.value.load();
}
//paramList 参数显示用的
function recordType(value){
  paramListData.recordStatus =  getCommonDesc('RECORD_STATUS', value);
}
function codeType(value){
  let desc = "";
  if(commType.value!=null){
    for(let i =0;i<commType.value.length;i++){
      if(value==commType.value[i].code){
        desc = commType.value[i].codeDesc;
        paramListData.codeType =  desc;
      }
    }
  }
}
function clearCache() {
  proxy.$axios.post("/datamgmt/api/v1/comcode/clear").then((body) => {
    if (body.success) {
      tableRef.value.load();
      // location.reload()
    }
  });
}
//-------------------------------
proxy.$axios.get("/datamgmt/api/v1/comcode/typelist").then((body) => {
  if (body.success) {
    commType.value = body.data;
  }
});

</script>

<style></style>