VITE_APP_VERSION = v0.13.0
VITE_SYSTEM = Custody and Clearing Platform(USMK)
VITE_BASEPATH=/
VITE_FRONTEND_HOME=https://csscl-usmk-web.apps.bochkuatcloud.com
VITE_FRONTEND=https://csscl-usmk-web.apps.bochkuatcloud.com
VITE_OIDCURL= https://uat-int-apigw.bochkuatcloud.com/eaphk/bapi/v1/csscl-usmk
VITE_REDIRECTURL=https://csscl-usmk-web.apps.bochkuatcloud.com
VITE_SERVICE= https://uat-int-apigw.bochkuatclout.com
# 接口请求地址，会设置到 axios 的 baseURL 参数上
VITE_API_BASE_URL = https://uat-int-apigw.bochkuatclout.com/eaphk/bapi/v1/csscl-usmk/protected
# message 请求的网关地址
VITE_API_DEV_URL = https://csscl-usmk.apps.bochkuatcloud.com/csscl-gateway
VITE_WS_PROTOCOL = wss