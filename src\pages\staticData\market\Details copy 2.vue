<template>
  <BaseDetails ref="details" :handleSave="handleSave" :reload="reload" :viewOriginalForm="viewOriginalForm"
    :form="ruleForm">
    <el-form :validateOnRuleChange="false" :disabled="formDisabled" ref="ruleFormRef" style="width: 100%"
      :model="ruleForm.form" :rules="rules" status-icon>
      <FormRow>
        <FormItemSign :detailsRef="details" :label="$t('common.title.opCtryRegionCode')" label-width="210"
          prop="opCtryRegionCode">
          <CtryRegionSearchInput v-model="ruleForm.form.opCtryRegionCode" showDesc="false" opCtryRegion />
        </FormItemSign>
        <FormItemSign :detailsRef="details" :label="$t('common.title.status')" label-width="270" prop="status">
          <Select v-model="ruleForm.form.status" type='STATUS' style="width: 80px" />
        </FormItemSign>
      </FormRow>
      <FormRow>
        <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.market.marketCode')" label-width="210"
          prop="marketCode">
          <InputText v-model="ruleForm.form.marketCode" maxlength="3" style="width: 80px" />
        </FormItemSign>
        <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.marketDesc')" label-width="270"
          prop="marketDesc">
          <InputText v-model="ruleForm.form.marketDesc" maxlength="50" style="width: 280px" />
        </FormItemSign>
      </FormRow>
      <FormRow>
        <FormItemSign :detailsRef="details" label-width="210" :label="$t('csscl.ctryRegionManagement.ctryRegionCode')"
          prop="ctryRegionCode">
          <CommonSearchInput v-model="ruleForm.form.ctryRegionCode" commType="CUSTODY_MARKET"
            url="/datamgmt/api/v1/searchinput" params='{ "searchType": "ctryRegion", "status": null }'
            codeTitle="csscl.ctryRegionManagement.ctryRegionCode" style="width:550px" />
        </FormItemSign>

        <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.market.operationInd')" label-width="270"
          prop="operationInd">
          <el-checkbox v-model="ruleForm.form.operationInd" true-value="Y" false-value="N" style="width:180px" />
        </FormItemSign>
      </FormRow>

      <FormRow>
        <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.daylightInd')" label-width="210"
          prop="daylightInd">
          <el-checkbox v-model="ruleForm.form.daylightInd" true-value="Y" false-value="N" style="width:180px" />
        </FormItemSign>
        <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.gmtOffset')" label-width="270" prop="gmtOffset">
          <!-- <CurrencySearchInput v-model="ruleForm.form.currencyCode" showDesc="false" /> -->
          <InputNumber v-model="ruleForm.form.gmtOffset" precision="5" scale="2" isNegative="true"
            style="width: 60px" />
        </FormItemSign>
      </FormRow>

      <!--      <FormRow>-->
      <!--        <el-col>-->
      <!--          <div style="width: 700px; border: 1px solid lightgray; padding: 10px 5px 10px 5px">-->
      <!--            <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.cashInstrctnDlDayOffset')" label-width="210" prop="cashInstrctnDlDayOffset">-->
      <!--                <el-space>-->
      <!--                  <InputNumber v-model="ruleForm.form.cashInstrctnDlDayOffset" style="width: 50px;" scale="0" maxlength="2"/>-->
      <!--                  <span style="background-color: lightgray; display: inline-block; padding: 4px 10px;">GMT+Market</span>-->
      <!--                    <el-time-picker v-model="ruleForm.form.cashInstrctnDlTime" format="HH:mm" :disabled="editDis" value-format="HH:mm" style="width: 80px;"></el-time-picker>-->
      <!--                </el-space>-->
      <!--          </FormItemSign>-->
      <!--          <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.tradeInstrctnDlDayOffset')" label-width="210" style="margin-top: 5px;" prop="tradeInstrctnDlDayOffset">-->
      <!--          <el-space>-->
      <!--                  <InputNumber v-model="ruleForm.form.tradeInstrctnDlDayOffset" style="width: 50px;"  scale="0" maxlength="2"/>-->
      <!--                  <span style="background-color: lightgray; display: inline-block; padding: 4px 10px;">GMT+Market</span>-->
      <!--                  <el-time-picker v-model="ruleForm.form.tradeInstrctnDlTime" format="HH:mm" :disabled="editDis" value-format="HH:mm" style="width: 80px;"></el-time-picker>-->
      <!--          </el-space>-->
      <!--          </FormItemSign>-->
      <!--          </div>-->
      <!--        </el-col>-->
      <!--      <el-col>-->
      <!--          <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.dayOfDormant')" label-width="220" prop="dayOfDormant">-->
      <!--          <InputNumber scale="0" v-model="ruleForm.form.dayOfDormant" maxlength="6" :disabled="editDis" />-->
      <!--      </FormItemSign>-->
      <!--      <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.dayOfDormantUnclaim')" label-width="220" prop="dayOfDormantUnclaim" style="margin-top: 5px;">-->
      <!--          <InputNumber scale="0" v-model="ruleForm.form.dayOfDormantUnclaim" maxlength="6" :disabled="editDis" />-->
      <!--      </FormItemSign>-->
      <!--      </el-col>-->
      <!--      </FormRow>-->
      <FormRow>
        <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.startOfOpsOnline')" label-width="210"
          prop="startOfOpsOnline">
          <el-space>
            <span style="background-color: lightgray; display: inline-block; padding: 4px 10px;">GMT+Market</span>
            <el-time-picker v-model="ruleForm.form.startOfOpsOnline" format="HH:mm" value-format="HH:mm"
              style="width: 80px;" @change="timeChange('Online')"></el-time-picker>
          </el-space>
        </FormItemSign>

        <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.retentionTranHis')" label-width="270"
          prop="retentionTranHis">
          <InputNumber v-model="ruleForm.form.retentionTranHis" :disabled="true" scale="0" maxlength="6"
            style="width: 60px" />
        </FormItemSign>
        <!--      <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.market.primary.bic.code')" label-width="220" prop="primaryBicCode" style="margin-top: 5px;">-->
        <!--        <el-input  v-model="ruleForm.form.primaryBicCode" maxlength="11" :disabled="editDis" />-->
        <!--      </FormItemSign>-->
      </FormRow>
      <FormRow style="height: 25px">
        <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.endOfOpsOnline')" label-width="210"
          prop="endOfOpsOnline">
          <el-space>
            <span style="background-color: lightgray; display: inline-block; padding: 4px 10px;">GMT+Market</span>
            <el-time-picker v-model="ruleForm.form.endOfOpsOnline" format="HH:mm" value-format="HH:mm"
              style="width: 80px;" @change="timeChange('Online')"></el-time-picker>
          </el-space>
        </FormItemSign>
        <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.dayOfDormant')" label-width="270"
          prop="dayOfDormant">
          <InputNumber scale="0" v-model="ruleForm.form.dayOfDormant" maxlength="6" style="width: 60px" />
        </FormItemSign>
        <!--      <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.market.secondary.bic.code')" label-width="220" prop="secondaryBicCode" style="margin-top: 5px;">-->
        <!--        <el-input v-model="ruleForm.form.secondaryBicCode" maxlength="11" :disabled="editDis" />-->
        <!--      </FormItemSign>-->
      </FormRow>

      <FormRow>
        <FormItemSign>

        </FormItemSign>
        <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.dayOfDormantUnclaim')" label-width="270"
          prop="dayOfDormantUnclaim">
          <InputNumber scale="0" v-model="ruleForm.form.dayOfDormantUnclaim" maxlength="6" style="width: 60px" />
        </FormItemSign>
      </FormRow>
      <el-row>
        <span style="font-size: 16px; font-weight: bold; color: #3fb2b2; margin-bottom: 5px;">Trade Instruction
          Deadline</span>
      </el-row>
      <el-container>
        <el-aside width="30%">
          <EditGrid v-model="tradeInstructionVpos" oid="marketTradeInstrOid" ref="tradeInstructionRef"
            :form="tradeInstructionForm" :rules="tradeInstructionRules" :details="details" :disabled="formDisabled"
            tableStyle="overflow: auto; height: 200px;">
            <template #columns>
              <el-table-column prop="transactionType" width="150" :label="$t('csscl.si.common.transactionType')" />
              <el-table-column prop="cutoffTime" :label="$t('csscl.si.common.cutoffTime')" />
              <el-table-column prop="recordStatus" :label="$t('common.title.recordStatus')">
                <template #default="scope">
                  {{ getCommonDesc('RECORD_STATUS', scope.row.recordStatus) }}
                  <span v-if="scope.row.recordStatus && scope.row.recordStatus !== 'A'">
                    for {{ getCommonDesc('MKCK_ACTION', scope.row.mkckAction) }}
                  </span>
                </template>
              </el-table-column>
            </template>
            <template #form>
              <FormItemSign :detailsRef="details" label-width="150px" :label="$t('csscl.si.common.transactionType')"
                prop="transactionType">
                <div style="position:relative">
                  <Select v-model="tradeInstructionForm.transactionType" type='TRANSACTION_TYPE' style="width: 150px" />
                </div>
              </FormItemSign>
              <FormItemSign :detailsRef="details" label-width="150px" :label="$t('csscl.si.common.cutoffTime')"
                prop="cutoffTime">
                <el-time-picker v-model="tradeInstructionForm.cutoffTime" format="HH:mm" value-format="HH:mm"
                  style="width: 60px;"></el-time-picker>
              </FormItemSign>
            </template>
          </EditGrid>
        </el-aside>

        
        <el-aside width="45%">
          <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.taxRelief')" label-width="445"
            prop="taxRelief">
            <Select v-model="ruleForm.form.taxRelief" type='TAX_RELIEF' style="margin-left: 190px; width: 125px" />
          </FormItemSign>
          <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.marketSanctioned')" label-width="500"
            prop="marketSanctioned">
            <Select v-model="ruleForm.form.marketSanctioned" type='MARKET_SANCTIONED'
              style="margin-left: 135px; width: 125px" />
          </FormItemSign>
          <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.supportBroadridgeExtraction')"
            label-width="565" prop="supBroadridgeExtraction">
            <Select v-model="ruleForm.form.supBroadridgeExtraction" type='SUP_BROADRIDGE_EXTRACTION'
              style="margin-left: 70px; width: 125px" />
          </FormItemSign>
          <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.omnibusSegregate')" label-width="505"
            prop="omnibusSegregate">
            <Select v-model="ruleForm.form.omnibusSegregate" type='OMNIBUS_SEGREGATE'
              style="margin-left: 130px; width: 125px" />
          </FormItemSign>
        </el-aside>
      </el-container>
      <br>
      <el-row>
        <span style="font-size: 16px; font-weight: bold; color: #3fb2b2; margin-bottom: 5px;">Cash Cutoff Time</span>
      </el-row>

      <el-container>
        <el-aside width="40%">
          <FormRow>
            <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.cutoffType')" label-width="100"
              prop="cutoffType" :disabled="false">
              <Select v-model="cashCutoffTimeFormSearch.cutoffType" type='CUTOFF_TYPE' :disabled="false" />
            </FormItemSign>
            <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.currency')" label-width="100"
              prop="currency">
              <SearchInput v-model="cashCutoffTimeFormSearch.currency" style="width:110px" showDesc="false"
                maxlength="3" onlyLetters searchField url="/datamgmt/api/v1/market/cash-cutoff/list"
                :title="$t('csscl.si.common.currency')" :params="{ marketOid: ruleForm.form.marketOid }" :columns="[
                  {
                    title: $t('csscl.si.common.currency'),
                    colName: 'currency',
                  },
                  {
                    title: $t('csscl.currencyManagement.descpt'),
                    colName: 'currencyDesc',
                  }
                ]">
              </SearchInput>
            </FormItemSign>
          </FormRow>
          <div style="text-align: left; margin: 5px 0 5px 0 ">
            <el-button @click="onReset">{{ $t('csscl.common.btn.clear')
            }}</el-button>
            <el-button type="primary" @click="onSearch">{{ $t('csscl.common.btn.search')
            }}</el-button>
          </div>
          <div style="color: lightgray; font-weight: bold;display: table-cell;width: 80px; align-content: center;">
            Order By: </div>
          <div
            style="margin: 0px; margin-bottom: 10px; padding: 3px 0; border-bottom: 2px solid lightgrey; width: 500px">
          </div>
          <EditGrid v-model="cashCutoffTimeVpos" oid="marketCashCutoffOid" ref="cashCutoffTimeRef"
            :form="cashCutoffTimeForm" :rules="cashCutoffTimeRules" :details="details" :disabled="formDisabled"
            tableStyle="overflow: auto; height: 200px;">
            <template #columns>
              <el-table-column prop="cutoffType" width="150" :label="$t('csscl.si.common.cutoffType')" />
              <el-table-column prop="currency" :label="$t('csscl.si.common.currency')" />
              <el-table-column prop="cutoffTime" :label="$t('csscl.si.common.cutoffTime')" />
              <el-table-column prop="recordStatus" :label="$t('common.title.recordStatus')">
                <template #default="scope">
                  {{ getCommonDesc('RECORD_STATUS', scope.row.recordStatus) }}
                  <span v-if="scope.row.recordStatus && scope.row.recordStatus !== 'A'">
                    for {{ getCommonDesc('MKCK_ACTION', scope.row.mkckAction) }}
                  </span>
                </template>
              </el-table-column>
            </template>
            <template #form>
              <FormItemSign :detailsRef="details" label-width="150px" :label="$t('csscl.si.common.cutoffType')"
                prop="cutoffType">
                <div style="position:relative">
                  <Select v-model="cashCutoffTimeForm.cutoffType" type='CUTOFF_TYPE' />
                </div>
              </FormItemSign>
              <FormItemSign :detailsRef="details" label-width="150px" :label="$t('csscl.si.common.currency')"
                prop="currency">
                <div style="position:relative">
                  <SearchInput v-model="cashCutoffTimeForm.currency" maxlength="3" onlyLetters searchField
                    showDesc="false" style="width: 500px" input-style="width:110px" url="/datamgmt/api/v1/currency/list"
                    :title="$t('csscl.currencyManagement.currencyCode')" :params="{ status: '', recordStatus: '' }"
                    :columns="[
                      {
                        title: $t('csscl.currencyManagement.currencyCode'),
                        colName: 'currencyCode',
                      },
                      {
                        title: $t('csscl.currencyManagement.descpt'),
                        colName: 'descpt',
                      }
                    ]" :dbClick="(row) => {
                      cashCutoffTimeForm.currencyDesc = row.descpt
                    }">
                  </SearchInput>
                </div>
              </FormItemSign>
              <FormItemSign :detailsRef="details" label-width="150px" :label="$t('csscl.si.common.cutoffTime')"
                prop="cutoffTime">
                <el-time-picker v-model="cashCutoffTimeForm.cutoffTime" format="HH:mm" value-format="HH:mm"
                  style="width: 60px;"></el-time-picker>
              </FormItemSign>
            </template>
          </EditGrid>
        </el-aside>
      </el-container>


      <el-row>
        <span style="font-size: 16px; font-weight: bold; color: #3fb2b2;">Settlement</span>
        <div
          style="paddingBlock:5px;width: 100%;display: flex;flex-flow: wrap;border-top: 2px solid; border-color: #9ad7d7;">
        </div>
      </el-row>

      <div style="height: 190px;">
        <FormRow>
          <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.siPayMethod')" label-width="220"
            prop="siPayMethod">
            <Select v-model="ruleForm.form.siPayMethod" type='CASH_SETTLE_METHOD_SI_CODE' style="width: 330px" />
          </FormItemSign>
        </FormRow>
        <FormRow>
          <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.caPayMethod')" label-width="220"
            prop="caPayMethod">
            <Select v-model="ruleForm.form.caPayMethod" type='CASH_SETTLE_METHOD_SI_CODE' style="width: 330px" />
          </FormItemSign>
        </FormRow>
        <FormRow>
          <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.cashStockSettleMethod')" label-width="220"
            prop="cashStockSettleMethod">
            <Select v-model="ruleForm.form.cashStockSettleMethod" type='CASH_STOCK_SETTLE_METHOD_CODE' />
          </FormItemSign>
        </FormRow>
        <!--      <FormRow> -->

        <!--        <FormItemSign :detailsRef="details" prop="rdvpInd">-->
        <!--          <el-checkbox v-model="ruleForm.form.rdvpInd" true-value="Y" false-value="N" style="width:18px">-->
        <!--            {{  $t("csscl.si.common.rdvpInd") }}-->
        <!--          </el-checkbox>-->
        <!--      </FormItemSign>-->
        <!--      </FormRow>-->

        <FormRow>
          <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.contractIncomeStart')" label-width="220"
            prop="contractIncomeStart">
            <!-- <InputText v-model="ruleForm.form.startContractualIncome" maxlength="3" uppercase :disabled="editDis" /> -->
            <el-space>
              <span style="background-color: lightgray; display: inline-block; padding: 4px 10px;">GMT+Market</span>
              <el-time-picker v-model="ruleForm.form.contractIncomeStart" format="HH:mm" value-format="HH:mm"
                style="width: 80px;" @change="timeChange('Income')"></el-time-picker>
            </el-space>
          </FormItemSign>
          <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.contractIncomeNotifyDays')" label-width="300"
            prop="contractIncomeNotifyDays">
            <InputNumber scale="0" v-model="ruleForm.form.contractIncomeNotifyDays" maxlength="2" uppercase />
          </FormItemSign>
        </FormRow>
        <FormRow>
          <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.endContractualIncome')" label-width="220"
            prop="contractIncomeEnd">
            <!-- <InputText v-model="ruleForm.form.endContractualIncome" maxlength="3" uppercase :disabled="editDis" /> -->
            <el-space>
              <span style="background-color: lightgray; display: inline-block; padding: 4px 10px;">GMT+Market</span>
              <el-time-picker v-model="ruleForm.form.contractIncomeEnd" format="HH:mm" value-format="HH:mm"
                style="width: 80px;" @change="timeChange('Income')"></el-time-picker>
            </el-space>
          </FormItemSign>
          <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.contractSetteNotifyDays')" label-width="300"
            prop="contractSetteNotifyDays">
            <InputNumber scale="0" v-model="ruleForm.form.contractSetteNotifyDays" maxlength="2" />
          </FormItemSign>
        </FormRow>
        <FormRow>
          <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.contractSetteStart')" label-width="220"
            prop="contractSetteStart">
            <!-- <InputText v-model="ruleForm.form.startContractualSettlement" maxlength="3" uppercase :disabled="editDis" /> -->
            <el-space>
              <span style="background-color: lightgray; display: inline-block; padding: 4px 10px;">GMT+Market</span>
              <el-time-picker v-model="ruleForm.form.contractSetteStart" format="HH:mm" value-format="HH:mm"
                style="width: 80px;" @change="timeChange('Settlement')"></el-time-picker>
            </el-space>
          </FormItemSign>
          <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.contractIncomeReverseDays')" label-width="300"
            prop="contractIncomeReverseDays">
            <InputNumber scale="0" v-model="ruleForm.form.contractIncomeReverseDays" maxlength="2" />
          </FormItemSign>
        </FormRow>

        <FormRow>
          <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.contractSetteEnd')" label-width="220"
            prop="contractSetteEnd">
            <!-- <InputText v-model="ruleForm.form.endContractualSettlement" maxlength="3" uppercase :disabled="editDis" /> -->
            <el-space>
              <span style="background-color: lightgray; display: inline-block; padding: 4px 10px;">GMT+Market</span>
              <el-time-picker v-model="ruleForm.form.contractSetteEnd" format="HH:mm" value-format="HH:mm"
                style="width: 80px;" @change="timeChange('Settlement')"></el-time-picker>
            </el-space>
          </FormItemSign>
          <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.contractSetteReverseDays')" label-width="300"
            prop="contractSetteReverseDays">
            <InputNumber scale="0" v-model="ruleForm.form.contractSetteReverseDays" maxlength="2" />
          </FormItemSign>
        </FormRow>

        <!--      <FormRow>-->
        <!--        <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.market.settlement.custoff.time')" label-width="220" prop="settlementCutoffTime">-->
        <!--          &lt;!&ndash; <InputText v-model="ruleForm.form.endContractualSettlement" maxlength="3" uppercase :disabled="editDis" /> &ndash;&gt;-->
        <!--          <el-space>-->
        <!--            <span style="background-color: lightgray; display: inline-block; padding: 4px 10px;">GMT+Market</span>-->
        <!--            <el-time-picker v-model="ruleForm.form.settlementCutoffTime" format="HH:mm" :disabled="editDis" value-format="HH:mm" style="width: 80px;"  @change="timeChange('Settlement')"></el-time-picker>-->
        <!--          </el-space>-->
        <!--        </FormItemSign>-->
        <!--      </FormRow>-->
      </div>

      <el-row>
        <span style="font-size: 16px; font-weight: bold; color: #3fb2b2;">Daylight Savings</span>
        <div
          style="paddingBlock:5px;width: 100%;display: flex;flex-flow: wrap;border-top: 2px solid; border-color: #9ad7d7;">
        </div>
      </el-row>

      <el-container>
        <el-aside width="40%">
          <EditGrid v-model="dayNightSavingVpos" oid="marketDaylightOid" ref="dayNightSavingRef" uniqueKey="gmtOffset"
            :form="dayNightForm" :rules="holidayRules" :details="details" :disabled="formDisabled"
            tableStyle="overflow: auto; height: 200px;" :readonly="ruleForm.form.daylightInd != 'Y'">
            <template #columns>
              <el-table-column prop="effectiveDate" width="150" :label="$t('csscl.si.common.effectiveDate')" />
              <el-table-column prop="gmtOffset" :label="$t('csscl.si.common.gmtOffset')" />
              <!--              <el-table-column prop="status" width="220" :label="$t('common.title.status')">-->
              <!--                <template #default="scope">-->
              <!--                  {{ getCommonDesc('STATUS', scope.row.status) }}-->
              <!--                </template>-->
              <!--              </el-table-column>-->
              <el-table-column prop="recordStatus" :label="$t('common.title.recordStatus')">
                <template #default="scope">
                  {{ getCommonDesc('RECORD_STATUS', scope.row.recordStatus) }}
                  <span v-if="scope.row.recordStatus && scope.row.recordStatus !== 'A'">
                    for {{ getCommonDesc('MKCK_ACTION', scope.row.mkckAction) }}
                  </span>
                </template>
              </el-table-column>
            </template>
            <template #form>
              <FormItemSign :detailsRef="details" label-width="150px" :label="$t('csscl.si.common.effectiveDate')"
                prop="effectiveDate">
                <div style="position:relative">
                  <DateItem v-model="dayNightForm.effectiveDate" style="width: 150px" />
                </div>
              </FormItemSign>
              <FormItemSign :detailsRef="details" label-width="150px" :label="$t('csscl.si.common.gmtOffset')"
                prop="gmtOffset">
                <InputNumber v-model="dayNightForm.gmtOffset" style="width: 450px" class="text-none" maxlength="6"
                  scale="2" isNegative="true" />
              </FormItemSign>
              <!--              <FormItemSign :detailsRef="details" label-width="150px" :label="$t('common.title.status')"-->
              <!--                prop="status">-->
              <!--                &lt;!&ndash; <el-input v-model="dayNightForm.status" maxlength="70" style="width: 450px" class="text-none" /> &ndash;&gt;-->
              <!--                <Select v-model="dayNightForm.status" style="width: 100px" type='MARKET_DAYNIGHT_STATUS' />-->
              <!--              </FormItemSign>-->
            </template>
          </EditGrid>
        </el-aside>
      </el-container>

      <br><br>
      <el-row>
        <span style="font-size: 16px; font-weight: bold;">Location/Depository</span>
        <div style="paddingBlock:5px;width: 100%;display: flex;flex-flow: wrap;border-top: 1px solid;"></div>
      </el-row>
      <el-row>


<InLineEditGrid v-model="tradeInstructionVpos" oid="marketTradeInstrOid" ref="tradeInstructionRef2"
            :form="tradeInstructionForm" :rules="tradeInstructionRules" :details="details" :disabled="formDisabled"
            tableStyle="overflow: auto; height: 200px;">
            <template #columns>
              <el-table-column prop="transactionType" width="150" :label="$t('csscl.si.common.transactionType')" />
              <el-table-column prop="cutoffTime" :label="$t('csscl.si.common.cutoffTime')" />
              <el-table-column prop="recordStatus" :label="$t('common.title.recordStatus')">
                <template #default="scope">
                  {{ getCommonDesc('RECORD_STATUS', scope.row.recordStatus) }}
                  <span v-if="scope.row.recordStatus && scope.row.recordStatus !== 'A'">
                    for {{ getCommonDesc('MKCK_ACTION', scope.row.mkckAction) }}
                  </span>
                </template>
              </el-table-column>
            </template>
            <template #form>
              <FormItemSign :detailsRef="details" label-width="150px" :label="$t('csscl.si.common.transactionType')"
                prop="transactionType">
                <div style="position:relative">
                  <Select v-model="tradeInstructionForm.transactionType" type='TRANSACTION_TYPE' style="width: 150px" />
                </div>
              </FormItemSign>
              <FormItemSign :detailsRef="details" label-width="150px" :label="$t('csscl.si.common.cutoffTime')"
                prop="cutoffTime">
                <el-time-picker v-model="tradeInstructionForm.cutoffTime" format="HH:mm" value-format="HH:mm"
                  style="width: 60px;"></el-time-picker>
              </FormItemSign>
            </template>
          </InLineEditGrid>
        
      </el-row>

      <el-row style="width: 100%; margin: 0; padding: 0;">
        <el-table id="depository-table" :data="depositoryTableList" row-key="depoCodeOid"
          :row-class-name="tableRowClassName" default-expand-all @row-click="selectedRow" table-layout="auto"
          ref="gridRef" :border="true" scrollbar-always-on class-name="multiple-table"
          style="width: calc(100% - 80px); margin: 0; padding: 0; float: left;height: 200px;">
        
          <el-table-column prop="depoCode" :label="$t('csscl.si.common.depoCode')" width="250">
            <template #default="scope">
              <template v-if="editingRow && highlightRow === scope.row">


                <SearchInput v-model="editingRow.depoCode" url="/datamgmt/api/v1/searchinput" title="Sub-Custodian Code"
                  :showDesc="false" :dbClick="(row, code, desc) => { depoCodeDbClickCheck(scope.row, code, desc); }"
                  :params="{ searchType: 'clearingAgentCode' }" :columns="[
                    {
                      title: $t('csscl.agent.clearingAgentCode'),
                      colName: 'code',
                    },
                    {
                      title: $t('csscl.agent.shortName'),
                      colName: 'codeDesc',
                    }
                  ]" :pageSizes="[10, 20, 30]">
                </SearchInput>

              </template>
              <template v-else>
                {{ scope.row.depoCode }}
              </template>
            </template>
          </el-table-column>
       
          <el-table-column prop="depoDesc" :label="$t('csscl.si.common.depoDesc')" width="250">
            
            <template #default="scope">
              <template v-if="editingRow && highlightRow === scope.row">
                {{ editingRow.depoDesc }}
              </template>
              <template v-else>
                {{ scope.row.depoDesc }}
              </template>
            </template>
          </el-table-column>
          <el-table-column prop="priSwiftBicCode" :label="$t('csscl.si.common.priSwiftBicCode')" width="300">
            
            <template #default="scope">
              <template v-if="editingRow && highlightRow === scope.row">
                <el-input v-model="editingRow.priSwiftBicCode" />
              </template>
              <template v-else>
                {{ scope.row.priSwiftBicCode }}
              </template>
            </template>
          </el-table-column>
          <el-table-column prop="secSwiftBicCode" :label="$t('csscl.si.common.secSwiftBicCode')" width="300">
            <template #default="scope">
              <template v-if="editingRow && highlightRow === scope.row">
                <el-input v-model="editingRow.secSwiftBicCode" />
              </template>
              <template v-else>
                {{ scope.row.secSwiftBicCode }}
              </template>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="status" width="250">
            <template #default="scope">
              
                {{ getCommonDesc('STATUS', scope.row.status) }}
             
            </template>
          </el-table-column>
          <el-table-column prop="recordStatus" label="recordStatus" width="200">
            <template #default="scope">
              <template v-if="editingRow && highlightRow === scope.row">
                {{ getRecordStatusDesc(scope.row) }}
              </template>
              <template v-else>

                {{ getRecordStatusDesc(scope.row) }}

              </template>
            </template>
          </el-table-column>
          <el-table-column fixed="right" :label="$t('common.table.operation')">
            <template #default="scope">
              <template v-if="editingRow && highlightRow === scope.row">
                <el-button type="primary" size="small" @click="saveEdit(scope.row, scope.$index)">Save</el-button>
                <el-button size="small" @click="cancelEdit">Cancel</el-button>
              </template>
              <template v-else>
                <el-button :icon="Edit" link type="primary" :disabled="isEditing"
                  @click.stop="startEdit(scope.row)"></el-button>
                <el-button :icon="Delete" link type="primary" :disabled="isEditing"
                  @click.stop="removeRow(scope.row)"></el-button>
              </template>
            </template>
          </el-table-column>
        </el-table>
        <el-space style="margin-left: 10px; float: left;" direction="vertical">
          <el-button :disabled="isEditing" :icon="Plus" @click="addRow" />
        </el-space>
      </el-row>

      <el-row style="width: 100%; margin: 0; padding: 0;margin-top: 10px;">
        <el-table id="depository-currency-table" :data="currencyTableList" row-key="currencyOid"
          :row-class-name="currencyTableRowClassName" @row-click="currencySelectedRow" default-expand-all
          table-layout="auto" ref="currencyGrid" :border="true" scrollbar-always-on class-name="multiple-table"
          style="width: calc(100% - 80px); margin: 0; padding: 0; float: left;height: 200px;">
          <el-table-column prop="currency" :label="$t('csscl.si.common.currency')" width="250">
            <template #default="scope">
              <template v-if="currencyEditingRow && currencyHighlightRow === scope.row">

                <el-input v-model="currencyEditingRow.currency" :disabled="currencyTableDisabled" />
              </template>
              <template v-else>
                {{ scope.row.currency }}
              </template>
            </template>
          </el-table-column>
          <el-table-column prop="chargeSuspAcctNo" :label="$t('csscl.si.common.chargeSuspAcctNo')" width="250">
            <template #default="scope">
              <template v-if="currencyEditingRow && currencyHighlightRow === scope.row">

                <el-input v-model="currencyEditingRow.chargeSuspAcctNo" :disabled="currencyTableDisabled" />
              </template>
              <template v-else>
                {{ scope.row.chargeSuspAcctNo }}
              </template>
            </template>
          </el-table-column>

          <el-table-column prop="incomeSuspAcctNo" :label="$t('csscl.si.common.incomeSuspAcctNo')" width="250">
            <template #default="scope">
              <template v-if="currencyEditingRow && currencyHighlightRow === scope.row">
                <el-input v-model="currencyEditingRow.incomeSuspAcctNo" :disabled="currencyTableDisabled" />
              </template>
              <template v-else>
                {{ scope.row.incomeSuspAcctNo }}
              </template>
            </template>
          </el-table-column>

          <el-table-column prop="setlSuspAcctNo" :label="$t('csscl.si.common.setlSuspAcctNo')" width="300">
            <template #default="scope">
              <template v-if="currencyEditingRow && currencyHighlightRow === scope.row">
                <el-input v-model="currencyEditingRow.setlSuspAcctNo" :disabled="currencyTableDisabled" />
              </template>
              <template v-else>
                {{ scope.row.setlSuspAcctNo }}
              </template>
            </template>
          </el-table-column>

          <el-table-column prop="recordStatus" label="recordStatus" width="300">
            <template #default="scope">
              <template v-if="currencyEditingRow && currencyHighlightRow === scope.row">
                {{ getRecordStatusDesc(scope.row) }}
              </template>
              <template v-else>

                {{ getRecordStatusDesc(scope.row) }}

              </template>
            </template>
          </el-table-column>
          <el-table-column fixed="right" :label="$t('common.table.operation')">
            <template #default="scope">
              <template v-if="currencyEditingRow && currencyHighlightRow === scope.row">
                <el-button type="primary" size="small"
                  @click="currencySaveEdit(scope.row, scope.$index)">Save</el-button>
                <el-button size="small" @click="currencyCancelEdit">Cancel</el-button>
              </template>
              <template v-else>
                <el-button :icon="Edit" link type="primary" :disabled="currencyIsEditing || currencyTableDisabled"
                  @click.stop="currencyStartEdit(scope.row)"></el-button>
                <el-button :icon="Delete" link type="primary" :disabled="currencyIsEditing || currencyTableDisabled"
                  @click.stop="currencyRemoveRow(scope.row)"></el-button>
              </template>
            </template>
          </el-table-column>

        </el-table>
        <el-space style="margin-left: 10px; float: left;" direction="vertical">
          <el-button :disabled="currencyIsEditing || currencyTableDisabled" :icon="Plus" @click="currencyAddRow" />
        </el-space>
      </el-row>

      <el-container style="margin-top:10px;" id="depository-info">
        <el-aside width="100%">


          <div>
            <FormRow>
              <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.caPayMethod')" label-width="350px"
                prop="caPayMethodCode">
                <Select v-model="depostitoryForm.caPayMethodCode" type='CASH_SETTLE_METHOD_SI_CODE' style="width: 330px"
                  :disabled="depositoryInfoDisabled" />
              </FormItemSign>

              <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.siPayMethod')" label-width="350px"
                prop="setlPayMethodCode">
                <Select v-model="depostitoryForm.setlPayMethodCode" type='CASH_SETTLE_METHOD_SI_CODE'
                  style="width: 330px" :disabled="depositoryInfoDisabled" />
              </FormItemSign>
            </FormRow>
            <FormRow>
              <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.genDepoIntfInd')" label-width="350px"
                prop="genDepoIntfInd">
                <Select v-model="depostitoryForm.genDepoIntfInd" type='GEN_DEPO_INTERFACE' style="width: 250px"
                  :disabled="depositoryInfoDisabled" />
              </FormItemSign>
              <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.intfFormatCode')" label-width="350px"
                prop="intfFormatCode">
                <Select v-model="depostitoryForm.intfFormatCode" type='INTERFACE_FORMAT' style="width: 250px"
                  :disabled="depositoryInfoDisabled" />
              </FormItemSign>
            </FormRow>
            <FormRow>
              <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.depoIntfAddr')" label-width="350px"
                prop="depoIntfAddr">
                <InputText maxlength="40" v-model="depostitoryForm.depoIntfAddr" style="width: 250px"
                  :disabled="depositoryInfoDisabled" />
              </FormItemSign>
            </FormRow>
          </div>
          <div>
            <el-card style="max-width: 440px">
              <span style="text-decoration: underline;">Days of Settlement Cycle</span>
              <br><br>
              <FormRow>
                <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.settlePeriodCashRecDay')"
                  label-width="120" prop="settlePeriodCashRecDay">
                  <InputNumber scale="0" maxlength="2" v-model="depostitoryForm.settlePeriodCashRecDay"
                    :disabled="depositoryInfoDisabled" />
                </FormItemSign>
              </FormRow>
              <FormRow>
                <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.settlePeriodCashDeliverDay')"
                  label-width="120" prop="settlePeriodCashDeliverDay">
                  <InputNumber scale="0" maxlength="2" v-model="depostitoryForm.settlePeriodCashDeliverDay"
                    :disabled="depositoryInfoDisabled" />
                </FormItemSign>
              </FormRow>
              <FormRow>
                <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.settlePeriodStockRecDay')"
                  label-width="120" prop="settlePeriodStockRecDay">
                  <InputNumber scale="0" maxlength="2" v-model="depostitoryForm.settlePeriodStockRecDay"
                    :disabled="depositoryInfoDisabled" />
                </FormItemSign>
              </FormRow>
              <FormRow>
                <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.settlePeriodStockDeliverDay')"
                  label-width="120" prop="settlePeriodStockDeliverDay">
                  <InputNumber scale="0" maxlength="2" v-model="depostitoryForm.settlePeriodStockDeliverDay"
                    :disabled="depositoryInfoDisabled" />
                </FormItemSign>
              </FormRow>
            </el-card>
          </div>

        </el-aside>
      </el-container>



      <br><br>
      <el-row>
        <el-icon style="color: #0099cc; width: 1em; height: 1em; margin-right: 4px ;font-size: 150%;">
          <Tickets />
        </el-icon>
        <span style="font-size: 16px; font-weight: bold; color: #3fb2b2;">Processing Time (File Generation Time)</span>
        <div
          style="paddingBlock:5px;width: 100%;display: flex;flex-flow: wrap;border-top: 2px solid; border-color: #9ad7d7;">
        </div>
      </el-row>

      <el-container>
        <el-aside width="40%">
          <EditGrid v-model="processingVpos" oid="marketProcessTimeOid" ref="processingRef" :form="processingForm"
            :rules="holidayRules" :details="details" :disabled="formDisabled"
            tableStyle="overflow: auto; height: 200px;">
            <template #columns>
              <el-table-column prop="cutoffNo" width="150" :label="$t('csscl.si.common.cutoffNo')" />
              <el-table-column prop="cutoffTime" :label="$t('csscl.si.common.cutoffTime')" />
              <el-table-column prop="recordStatus" :label="$t('common.title.recordStatus')">
                <template #default="scope">
                  {{ getCommonDesc('RECORD_STATUS', scope.row.recordStatus) }}
                  <span v-if="scope.row.recordStatus && scope.row.recordStatus !== 'A'">
                    for {{ getCommonDesc('MKCK_ACTION', scope.row.mkckAction) }}
                  </span>
                </template>
              </el-table-column>
            </template>
            <template #form>
              <FormItemSign :detailsRef="details" label-width="150px" :label="$t('csscl.si.common.cutoffNo')"
                prop="cutoffNo">
                <div style="position:relative">
                  <InputNumber maxlength="2" v-model="processingForm.cutoffNo" style="width: 150px" scale="0" />
                </div>
              </FormItemSign>
              <FormItemSign :detailsRef="details" label-width="150px" :label="$t('csscl.si.common.cutoffTime')"
                prop="cutoffTime">
                <!-- <DateItem v-model="processingForm.cutoffTime" style="width: 150px" class="cutoffTime" /> -->
                <el-time-picker v-model="processingForm.cutoffTime" format="HH:mm" value-format="HH:mm"
                  style="width: 60px;"></el-time-picker>
              </FormItemSign>
            </template>
          </EditGrid>
        </el-aside>
      </el-container>

      <br><br>
      <el-row>
        <el-icon style="color: #0099cc; width: 1em; height: 1em; margin-right: 4px ;font-size: 150%;">
          <Tickets />
        </el-icon>
        <span style="font-size: 16px; font-weight: bold; color: #3fb2b2;">Depository Account</span>
        <div
          style="paddingBlock:5px;width: 100%;display: flex;flex-flow: wrap;border-top: 2px solid; border-color: #9ad7d7;">
        </div>
      </el-row>

      <el-container>
        <el-aside width="60%">
          <EditGrid v-model="depositoryAccountVpos" oid="marketDepoCustodianAcctOid" ref="depositoryAccountRef"
            :form="depositoryAccountForm" :rules="holidayRules" :details="details" :disabled="formDisabled"
            tableStyle="overflow: auto; height: 200px;">
            <template #columns>
              <el-table-column prop="custodianAccountName" width="220"
                :label="$t('csscl.si.common.custodianAccountName')" />
              <el-table-column prop="subAccountName" width="220" :label="$t('csscl.si.common.subAccountName')" />
              <el-table-column prop="accountType" width="150" :label="$t('csscl.si.common.accountType')">
                <template #default="scope">
                  {{ getCommonDesc('ACCOUNT_TYPE', scope.row.accountType) }}
                </template>
              </el-table-column>
              <el-table-column prop="custodianAccountType" width="220"
                :label="$t('csscl.si.common.custodianAccountType')">
                <template #default="scope">
                  {{ getCommonDesc('CUSTODIAN_ACCOUNT_TYPE', scope.row.custodianAccountType) }}
                </template>
              </el-table-column>
              <el-table-column prop="status" :label="$t('common.title.status')">
                <template #default="scope">
                  {{ getCommonDesc('STATUS', scope.row.status) }}
                </template>
              </el-table-column>
              <el-table-column prop="recordStatus" width="220" :label="$t('common.title.recordStatus')">
                <template #default="scope">
                  {{ getCommonDesc('RECORD_STATUS', scope.row.recordStatus) }}
                  <span v-if="scope.row.recordStatus && scope.row.recordStatus !== 'A'">
                    for {{ getCommonDesc('MKCK_ACTION', scope.row.mkckAction) }}
                  </span>
                </template>
              </el-table-column>
            </template>
            <template #form>
              <FormItemSign :detailsRef="details" label-width="250px"
                :label="$t('csscl.si.common.custodianAccountName')" prop="custodianAccountName">
                <div style="position:relative">
                  <!-- <el-input v-model="depositoryAccountForm.custodianAccountName" style="width: 150px"/> -->
                  <GeneralSearchInput v-model="depositoryAccountForm.custodianAccountName" showDesc="false"
                    style="width: 160px" searchType="clearingAgentCodeAndAcc" :dbClick="(row) => {
                      depositoryAccountForm.custodianAccOid = row.var2,
                        depositoryAccountForm.clearingAgentOid = row.var1,
                        depositoryAccountForm.subAccountName = row.var4
                    }" />
                </div>
              </FormItemSign>
              <!-- <FormItemSign :detailsRef="details" label-width="250px" :label="$t('Sub-Account Name (external)')"
                prop="subAccountName">
                <el-input v-model="depositoryAccountForm.subAccountName" maxlength="70" style="width: 250px" class="subAccountName" />
              </FormItemSign> -->
              <FormItemSign :detailsRef="details" label-width="250px" :label="$t('csscl.si.common.accountType')"
                prop="accountType">
                <Select v-model="depositoryAccountForm.accountType" style="width: 100px" type='ACCOUNT_TYPE' />
              </FormItemSign>
              <FormItemSign :detailsRef="details" label-width="250px"
                :label="$t('csscl.si.common.custodianAccountType')" prop="custodianAccountType">
                <Select v-model="depositoryAccountForm.custodianAccountType" style="width: 100px"
                  type='CUSTODIAN_ACCOUNT_TYPE' />
              </FormItemSign>
              <FormItemSign :detailsRef="details" label-width="250px" :label="$t('common.title.status')" prop="status">
                <Select v-model="depositoryAccountForm.status" style="width: 100px" type='STATUS' />
              </FormItemSign>
            </template>
          </EditGrid>
        </el-aside>
      </el-container>

      <br><br>
      <!--      <el-row>-->
      <!--        <span style="font-size: 16px; font-weight: bold;">Holiday</span>-->
      <!--        <div style="paddingBlock:5px;width: 100%;display: flex;flex-flow: wrap;border-top: 1px solid;" ></div>-->
      <!--      </el-row>-->
      <!--      <el-container>-->
      <!--        <el-aside width="40%">-->
      <!--          <EditGrid v-model="holidayVpos"-->
      <!--            oid="marketHolidayOid" -->
      <!--            ref="holidayGridRef"-->
      <!--            uniqueKey="holidayDate"-->
      <!--            :form="holidayForm" -->
      <!--            :rules="holidayRules" -->
      <!--            :details="details" -->
      <!--            :disabled="formDisabled"-->
      <!--            tableStyle="overflow: auto; height: 200px;"-->
      <!--             >-->
      <!--            <template #columns>-->
      <!--              <el-table-column prop="holidayDate" width="150" :label="$t('csscl.ctryRegionManagement.holiday')" />-->
      <!--              <el-table-column prop="holidayDesc" :label="$t('csscl.ctryRegionManagement.description')"/>-->
      <!--              <el-table-column prop="recordStatus" width="220" :label="$t('common.title.recordStatus')">-->
      <!--                  <template #default="scope">-->
      <!--                      {{ getCommonDesc('RECORD_STATUS', scope.row.recordStatus) }}-->
      <!--                      <span v-if="scope.row.recordStatus && scope.row.recordStatus !== 'A'">-->
      <!--                          for {{ getCommonDesc('MKCK_ACTION', scope.row.mkckAction) }}-->
      <!--                      </span>-->
      <!--                  </template>-->
      <!--              </el-table-column>-->
      <!--            </template>-->
      <!--            <template #form>-->
      <!--              <FormItemSign :detailsRef="details" label-width="150px" :label="$t('csscl.ctryRegionManagement.holiday')" prop="holidayDate">-->
      <!--                <div style="position:relative">-->
      <!--                  <DateItem v-model="holidayForm.holidayDate" style="width: 150px"/>-->
      <!--                </div>-->
      <!--              </FormItemSign>-->
      <!--              <FormItemSign :detailsRef="details" label-width="150px" :label="$t('csscl.ctryRegionManagement.description')"-->
      <!--                prop="holidayDesc">-->
      <!--                <el-input v-model="holidayForm.holidayDesc" maxlength="70" style="width: 450px" class="text-none" />-->
      <!--              </FormItemSign>-->
      <!--            </template>-->
      <!--          </EditGrid>-->
      <!--        </el-aside>-->
      <!--        <el-aside width="15%"></el-aside>-->
      <!--        <el-main >-->
      <!--          <ElFormItemProxy></ElFormItemProxy>-->
      <!--          <el-text tag="P" class="form-item-sign">{{  $t("csscl.ctryRegionManagement.uploadHolidayFile") }}</el-text>-->
      <!--          <FormRow>-->
      <!--            <ElFormItemProxy label=" ">-->
      <!--              <UploadItem :show-file-list="false" class="upload-demo" drag :file-list="ruleForm.form.fileList" :auto-upload="false"-->
      <!--                accept=".xlsx" :on-change="handleUpload" style="width: 800px">-->
      <!--                <el-icon><Upload /></el-icon>-->
      <!--                <div class="el-upload__text">Browse or drop file</div>-->
      <!--              </UploadItem>-->
      <!--            </ElFormItemProxy>-->
      <!--          </FormRow>-->
      <!--          <FormRow>-->
      <!--            <FormItemSign :detailsRef="details" :label="$t('csscl.ctryRegionManagement.fileName')" prop="fileName">-->
      <!--              <el-space>-->
      <!--              <el-input v-model="ruleForm.form.fileName" :disabled="true" style="width: 400px" class="text-none">-->
      <!--                <template #append>-->
      <!--                  <el-button type="primary" @click="handleDownload" :icon="Download" v-if="ruleForm.form.fileName"/>-->
      <!--                </template>-->
      <!--              </el-input>-->
      <!--                <ElFormItemProxy>-->
      <!--                    <el-button type="primary" @click="downloadTemplate">{{$t('csscl.cashinstr.upload.downloadTemplate')}}</el-button>-->
      <!--                </ElFormItemProxy>-->
      <!--              </el-space>              -->
      <!--            </FormItemSign>-->
      <!--          </FormRow>-->
      <!--          <FormRow>-->
      <!--            <FormItemSign :detailsRef="details" :label="$t('csscl.ctryRegionManagement.totalRecord')" prop="uploadRcCnt">-->
      <!--              <el-input v-model="ruleForm.form.uploadRcCnt" disabled style="width: 100px" />-->
      <!--            </FormItemSign>-->
      <!--          </FormRow>-->
      <!--          <FormRow>-->
      <!--            <FormItemSign :detailsRef="details" :label="$t('csscl.ctryRegionManagement.logSatus')" prop="processStatus">-->
      <!--              <el-input v-model="ruleForm.form.processStatusDesc" disabled style="width: 150px" />-->
      <!--            </FormItemSign>-->
      <!--          </FormRow>-->
      <!--          <FormRow>-->
      <!--            <FormItemSign :detailsRef="details" :label="$t('csscl.ctryRegionManagement.logDetails')" prop="errorLog">-->
      <!--              <el-input v-model="ruleForm.form.errorLog" type="textarea" disabled style="width: 550px" :rows="6" />-->
      <!--            </FormItemSign>-->
      <!--          </FormRow>-->
      <!--        </el-main>-->
      <!--      </el-container>-->


    </el-form>
  </BaseDetails>
</template>

<script lang="ts" setup>
import { ref, reactive, getCurrentInstance, watch, onMounted, nextTick } from 'vue';
import type { FormInstance, } from 'element-plus'
import { ElMessageBox } from 'element-plus'
import { Upload, Download, Plus, Minus, Delete, Edit } from '@element-plus/icons-vue';
import BaseDetails from '~/pages/base/Details.vue';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import CtryRegionSearchInput from '~/pages/base/CtryRegionSearchInput.vue'
import CurrencySearchInput from '~/pages/base/CurrencySearchInput.vue'
import CommonSearchInput from '~/pages/base/CommonSearchInput.vue';
import { getCommonDesc, showErrorMsg, randomHashCode, getRecordStatusDesc } from '~/util/Function.js';
import { getOid, downloadFile, saveMsgBox } from '~/util/Function.js';
import { commonRules, showValidateMsg } from '~/util/Validators.js';
import { addCustValid, focusType } from '~/util/ModifiedValidate.js';
import { Row } from 'element-plus/es/components/table-v2/src/components';
import { fa } from "element-plus/es/locale";
import { debug } from 'console';


const { proxy } = getCurrentInstance();
const props = defineProps(['reload']);
const details = ref();
const formDisabled = ref(false);
const editDis = ref(false);
const isRead = ref(false);
const ruleFormRef = ref()
const ruleForm = reactive({
  rules: () => { return [{ rules: rules }] },
  form: {
    fileList: [],
  }
});
// const handleGridClick = (row) => {
//   depostitoryForm = { ...row }; // 同步到详情表单
// }

const reqParams = reactive({
  marketOid: ruleForm.form.marketOid,
  pendingOid: ruleForm.form.pendingOid,
  isApproveDetail: false,
  approveNumber: -1,

});

const holidayVpos = ref([]);
const dayNightSavingVpos = ref([]);
const processingVpos = ref([]);
const depositoryAccountVpos = ref([]);
const depositorySubVpos = ref([]);
const tradeInstructionVpos = ref([]);
const cashCutoffTimeVpos = ref([]);
const holidayGridRef = ref();
const dayNightSavingRef = ref();
const tradeInstructionRef = ref();
const cashCutoffTimeRef = ref();
// const depositoryRef = ref();
const processingRef = ref();
const depositoryAccountRef = ref();
const depositorySubRef = ref();

const loadGrid = async () => {
  const msg = await proxy.$axios.post("/datamgmt/api/v1/market/holiday/list", {
    param: {
      ...reqParams,
      marketCode: ruleForm.form.marketCode
    },
    current: 1,
    pageSize: 999999,
    // orderBy: ,
  });
  if (msg?.success) {
    holidayVpos.value = msg.data.data;
  }
}

const loadDaylightGrid = async () => {
  const msg = await proxy.$axios.post("/datamgmt/api/v1/market/daylight/list", {
    param: {
      ...reqParams,
      marketCode: ruleForm.form.marketCode
    },
    current: 1,
    pageSize: 999999,
    // orderBy: ,
  });
  if (msg?.success) {
    dayNightSavingVpos.value = msg.data.data;
  }
}
const loadProcessGrid = async () => {
  const msg = await proxy.$axios.post("/datamgmt/api/v1/market/process/list", {
    param: {
      ...reqParams,
      marketCode: ruleForm.form.marketCode
    },
    current: 1,
    pageSize: 999999,
    // orderBy: ,
  });
  if (msg?.success) {
    processingVpos.value = msg.data.data;
  }
}

const loadDepositoryAccountGrid = async () => {
  const msg = await proxy.$axios.post("/datamgmt/api/v1/market/depository-cust-acc/list", {
    param: {
      ...reqParams,
      marketCode: ruleForm.form.marketCode
    },
    current: 1,
    pageSize: 999999,
    // orderBy: ,
  });
  if (msg?.success) {
    depositoryAccountVpos.value = msg.data.data;
  }
}
const loadLocationDepositoryGrid = async () => {
  const msg = await proxy.$axios.post("/datamgmt/api/v1/market/location-depository/list", {
    param: {
      ...reqParams,
      marketCode: ruleForm.form.marketCode
    },
    current: 1,
    pageSize: 999999,
    // orderBy: ,
  });
  if (msg?.success) {

    const depositoryData = msg.data.data || [];
    // 同时更新 depositoryTableList
    depositoryTableList.value = [...depositoryData];
    // 默认选中第一行
    if (depositoryData.length > 0) {
      // 使用 nextTick 确保 DOM 更新后再选中第一行
      await nextTick();
      selectedRow(depositoryData[0]);
    } else {
      // 当没有数据时，清空相关状态，防止显示上一个页面的数据
      highlightRow.value = null;
      clearDepositoryInfoData();
      clearCurrencyTable();
    }
  }
}
const loadTradeInstrGrid = async () => {
  const msg = await proxy.$axios.post("/datamgmt/api/v1/market/trade-instr/list", {
    param: {
      ...reqParams,
      marketCode: ruleForm.form.marketCode
    },
    current: 1,
    pageSize: 999999,
    // orderBy: ,
  });
  if (msg?.success) {
    tradeInstructionVpos.value = msg.data.data;
  }
}

const loadCashCutoffGrid = async () => {
  const msg = await proxy.$axios.post("/datamgmt/api/v1/market/cash-cutoff/list", {
    param: {
      ...reqParams,
      marketCode: ruleForm.form.marketCode
    },
    current: 1,
    pageSize: 999999,
    // orderBy: ,
  });
  if (msg?.success) {
    cashCutoffTimeVpos.value = msg.data.data;
  }
}


const editRow = async (row, disabled, newId) => {
  if (row?.isApproveDetail && disabled) {
    ruleForm.form = row.afterImage;
    reqParams.isApproveDetail = true;
    reqParams.approveNumber = row?.approveNumber;
    reqParams.marketOid = row?.eventPkey;
    details.value.currentRow = ruleForm.form;
    loadGrid();
    loadDaylightGrid();
    loadProcessGrid();
    loadDepositoryAccountGrid();
    loadLocationDepositoryGrid();
    loadTradeInstrGrid();
    loadCashCutoffGrid();
  } else {
    const oid = newId || (row?.currentOid && getOid(row, disabled, proxy));
    if (oid) {
      const body = await proxy.$axios.get("/datamgmt/api/v1/market?objectId=" + oid);
      if (body.success) {
        ruleForm.form = body.data;
        reqParams.marketOid = body.data?.marketOid;
        reqParams.pendingOid = body.data?.pendingOid;
        details.value.currentRow = body.data;
        // holidayGridRef.value.clearModifyData();
        dayNightSavingRef.value.clearModifyData();
        depositoryAccountRef.value.clearModifyData();
        //depositoryRef.value.clearModifyData()
        await loadGrid();
        await loadDaylightGrid();
        await loadProcessGrid();
        await loadDepositoryAccountGrid();
        await loadLocationDepositoryGrid();
        await loadTradeInstrGrid();
        await loadCashCutoffGrid();
        addCustValid(ruleForm.form, () => {
          // let leg = holidayGridRef.value.getModifyRecords()?.length;
          // leg = leg > 0 ? true : false;
          // if (!leg){
          //     let datas = holidayGridRef.value.showData;
          //     for (let i = 0; i < datas.length; i++) {
          //         let data = datas[i];
          //         if (data.recordStatus != 'A') {
          //             focusType.type = focusType.EnterObj;
          //             break;
          //         }
          //     }
          // }
          let legNighgt = dayNightSavingRef.value.getModifyRecords()?.length;
          legNighgt = legNighgt > 0 ? true : false;
          if (!legNighgt) {
            let datas = dayNightSavingRef.value.showData;
            for (let i = 0; i < datas.length; i++) {
              let data = datas[i];
              if (data.recordStatus != 'A') {
                focusType.type = focusType.EnterObj;
                break;
              }
            }
          }
          // let legDepository = depositoryRef.value.getModifyRecords()?.length;
          // legDepository = legDepository > 0 ? true : false;
          // if (!legDepository) {
          //   let datas = depositoryRef.value.showData;
          //   for (let i = 0; i < datas.length; i++) {
          //     let data = datas[i];
          //     if (data.recordStatus != 'A') {
          //       focusType.type = focusType.EnterObj;
          //       break;
          //     }
          //   }
          // }
          let legProcess = processingRef.value.getModifyRecords()?.length;
          legProcess = legProcess > 0 ? true : false;
          if (!legProcess) {
            let datas = processingRef.value.showData;
            for (let i = 0; i < datas.length; i++) {
              let data = datas[i];
              if (data.recordStatus != 'A') {
                focusType.type = focusType.EnterObj;
                break;
              }
            }
          }
          let legDesitoryAccount = depositoryAccountRef.value.getModifyRecords()?.length;
          legDesitoryAccount = legDesitoryAccount > 0 ? true : false;
          if (!legDesitoryAccount) {
            let datas = depositoryAccountRef.value.showData;
            for (let i = 0; i < datas.length; i++) {
              let data = datas[i];
              if (data.recordStatus != 'A') {
                focusType.type = focusType.EnterObj;
                break;
              }
            }
          }
          let tradeInstruction = tradeInstructionRef.value.getModifyRecords()?.length;
          tradeInstruction = tradeInstruction > 0;
          if (!tradeInstruction) {
            let datas = tradeInstructionRef.value.showData;
            for (let i = 0; i < datas.length; i++) {
              let data = datas[i];
              if (data.recordStatus != 'A') {
                focusType.type = focusType.EnterObj;
                break;
              }
            }
          }
          let cashCutoffTime = cashCutoffTimeRef.value.getModifyRecords()?.length;
          cashCutoffTime = cashCutoffTime > 0;
          if (!cashCutoffTime) {
            let datas = cashCutoffTimeRef.value.showData;
            for (let i = 0; i < datas.length; i++) {
              let data = datas[i];
              if (data.recordStatus != 'A') {
                focusType.type = focusType.EnterObj;
                break;
              }
            }
          }
          // let depoSub = depositorySubRef.value.getModifyRecords()?.length;
          // depoSub = depoSub > 0;
          // if (!cashCutoffTime) {
          //   let datas = depositorySubRef.value.showData;
          //   for (let i = 0; i < datas.length; i++) {
          //     let data = datas[i];
          //     if (data.recordStatus != 'A') {
          //       focusType.type = focusType.EnterObj;
          //       break;
          //     }
          //   }
          // }
          return legNighgt || legProcess || legDesitoryAccount || tradeInstruction || cashCutoffTime;
        });

        details.value.initWatch({ w1: ruleForm, w2: holidayGridRef, w3: holidayForm, w4: dayNightSavingRef, w6: processingRef, w7: depositoryAccountRef, w8: tradeInstructionRef, w9: cashCutoffTimeRef }, ruleForm);
      };
      editDis.value = true;
    } else {
      details.value.initWatch({ w1: ruleForm, w2: holidayGridRef, w3: holidayForm, w4: dayNightSavingRef, w6: processingRef, w7: depositoryAccountRef, w8: tradeInstructionRef, w9: cashCutoffTimeRef }, ruleForm);
    }
  }
}


const viewOriginalForm = async (pendingOid, isDisabled) => {
  formDisabled.value = isDisabled;
  const body = await proxy.$axios.get("/datamgmt/api/v1/market?objectId=" + pendingOid);
  if (body.success) {
    ruleForm.form = body.data;
    details.value.currentRow.value = body.data;
    reqParams.marketOid = body.data?.marketOid;
    reqParams.pendingOid = body.data?.pendingOid;
    await loadGrid();
    await loadDaylightGrid();
    await loadProcessGrid();
    await loadDepositoryAccountGrid();
    await loadLocationDepositoryGrid();
    await loadTradeInstrGrid();
    await loadCashCutoffGrid();
  }
}

const handleSave = async (searchValid, isOnlyValidate, unPopping) => {



  // if (holidayGridRef.value.isEditing()) {
  //   showErrorMsg("The sub table data has not been saved. Please confirm if you want to leave.");
  //   return false;
  // }
  const time1 = timeChange('Online')
  const time2 = timeChange('Income')
  const time3 = timeChange('Settlement')
  let result = await ruleFormRef.value.validate((valid, fields) => {
    if (valid) {
    } else {
      showValidateMsg(details, fields);
    }
  });

  result = result && time1 && time2 && time3;
  if (isOnlyValidate) {
    return result;
  }
  if (result && searchValid && await saveMsgBox(unPopping)) {
    if (ruleForm.form.marketOid) {
      const msg = await proxy.$axios.patch("/datamgmt/api/v1/market", {
        ...ruleForm.form,
        // marketHolidayVPOList: holidayGridRef.value.getModifyRecords(),
        marketDaylightVPOList: dayNightSavingRef.value.getModifyRecords(),
        marketProcessTimeVPOList: processingRef.value.getModifyRecords(),
        marketDepositoryCustaccVPOList: depositoryAccountRef.value.getModifyRecords(),
        marketExBoardVPOList: depositoryTableList.value,
        marketTradeInstrVPOList: tradeInstructionRef.value.getModifyRecords(),
        marketCashCutoffVPOList: cashCutoffTimeRef.value.getModifyRecords(),
      });
      if (msg.success) {
        details.value.writebackId(msg.data);
        editRow(null, null, msg.data);
        // holidayGridRef.value.clearModifyData();
        dayNightSavingRef.value.clearModifyData();
        processingRef.value.clearModifyData()
        depositoryAccountRef.value.clearModifyData()

        loadGrid();
        loadDaylightGrid();
        loadProcessGrid();
        loadDepositoryAccountGrid();
        loadLocationDepositoryGrid();
        loadTradeInstrGrid();
        loadCashCutoffGrid();
      }
      return msg.success;
    } else {
      const msg = await proxy.$axios.post("/datamgmt/api/v1/market", {
        ...ruleForm.form,
        // marketHolidayVPOList: holidayGridRef.value.getModifyRecords(),
        marketDaylightVPOList: dayNightSavingRef.value.getModifyRecords(),
        marketProcessTimeVPOList: processingRef.value.getModifyRecords(),
        marketDepositoryCustaccVPOList: depositoryAccountRef.value.getModifyRecords(),
        marketExBoardVPOList: depositoryTableList.value,
        marketTradeInstrVPOList: tradeInstructionRef.value.getModifyRecords(),
        marketCashCutoffVPOList: cashCutoffTimeRef.value.getModifyRecords(),

      });
      if (msg.success) {
        details.value.writebackId(msg.data);
        editRow(null, null, msg.data);
        // holidayGridRef.value.clearModifyData()
        dayNightSavingRef.value.clearModifyData()
        processingRef.value.clearModifyData()
        depositoryAccountRef.value.clearModifyData()
        //depositoryRef.value.clearModifyData()

        // 保存当前的currency数据，避免重新加载时丢失用户编辑的数据
        const currentCurrencyData = [...currencyTableList.value];

        // await loadGrid();
        // await loadDaylightGrid();
        // await loadProcessGrid();
        // await loadDepositoryAccountGrid();
        // await loadLocationDepositoryGrid();
        // await loadTradeInstrGrid();
        // await loadCashCutoffGrid();

        // 恢复用户编辑的currency数据
        if (currentCurrencyData.length > 0) {
          currencyTableList.value = currentCurrencyData;
        }
      }
      return msg.success;
    }
  }
  return false;
}

const onReset = () => {
  cashCutoffTimeFormSearch.cutoffType = '',
    cashCutoffTimeFormSearch.currency = ''
}
const onSearch = async () => {
  const msg = await proxy.$axios.post("/datamgmt/api/v1/market/cash-cutoff/list", {
    param: {
      marketOid: ruleForm.form.marketOid,
      ...cashCutoffTimeFormSearch,
    },
    current: 1,
    pageSize: 999999,
    // orderBy: ,
  });
  if (msg?.success) {
    cashCutoffTimeVpos.value = msg.data.data;
  }
}

const showDetails = (row, isdoubleCheck) => {
  if (isdoubleCheck || row.recordStatus === 'PA') {
    formDisabled.value = true;
    details.value.showDetails(row, true)
  } else {
    formDisabled.value = false;
    details.value.showDetails(row, false)
  }
  if (isdoubleCheck) {
    isRead.value = true
  } else {
    isRead.value = false
  }
  ruleForm.form = {};
  details.value.currentRow = {};
  editDis.value = false;
  editRow(row, isdoubleCheck);
}

defineExpose({
  details,
  editRow,
  showDetails,
});
// --------------------------------------------
interface RuleForm {
  currencyCode: String
  status: String
}

const requiredFields = [
  'retentionTranHis',
  'cashInstrctnDlDayOffset',
  'tradeInstrctnDlDayOffset',
  'dayOfDormant',
  'dayOfDormantUnclaim',
  'startOfOpsOnline',
  'endOfOpsOnline',
  'siPayMethod',
  'caPayMethod',
  'cashStockSettleMethod',
  'contractIncomeStart',
  'contractIncomeNotifyDays',
  'contractIncomeEnd',
  'contractSetteNotifyDays',
  'contractSetteStart',
  'contractIncomeReverseDays',
  'contractSetteEnd',
  'contractSetteReverseDays',
  'taxRelief',
  'marketSanctioned',
  'supBroadridgeExtraction',
  'omnibusSegregate',
]
const rules = reactive({
  opCtryRegionCode: [
    commonRules.required,
  ],
  ctryRegionCode: [
    commonRules.required,
    commonRules.name,
  ],
  ctryRegionName: [
    commonRules.name,
  ],
  isoCode: [
    commonRules.required,
    commonRules.name,
  ],
  currencyCode: [
    commonRules.required,
  ],
  status: [
    commonRules.selectRequired,
  ],
  marketCode: [
    commonRules.required,
  ],
  marketDesc: [
    commonRules.required,
  ],
  gmtOffset: [
    commonRules.required,
  ],
  ...Object.fromEntries(
    requiredFields.map(field => [
      field,
      commonRules.required,
    ]),

  )
})

const value = ref('')

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      console.log('submit!')
    } else {
      console.log('error submit!', fields)
    }
  })
}

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
}

const downloadTemplate = (e) => {
  downloadFile("/datamgmt/api/v1/market/holiday/template", {});
}

const handleDownload = () => {
  var row = ruleForm.form;
  var params = {
    filePath: row.filePath,
    fileName: row.fileName,
  }
  downloadFile("/datamgmt/api/v1/market/holiday/download", params);
}

const handleUpload = async (file) => {
  let result = await ruleFormRef.value.validate((valid, fields) => {
    if (valid) {
    } else {
      showValidateMsg(details, fields);
    }
  });
  if (result) {
    ruleForm.form.fileList = [];
    ruleForm.form.fileList.push(file);

    if (file.size === 0) {
      ElMessageBox.alert("Please upload a file.", 'Warning');
    }

    let formData = new FormData();
    formData.append('channel', 'MANUAL');
    formData.append('fileType', 'CTRYHOLDAY');
    formData.append('file', file.raw);
    formData.append('ctryRegionCode', ruleForm.form?.ctryRegionCode);
    const resp = await proxy.$axios.post("/datamgmt/api/v1/market/holidayUploadXlsx", formData);
    if (resp?.success) {
      if (resp.data) {
        ruleForm.form.filePath = resp.data?.filePath;
        ruleForm.form.fileName = resp.data?.fileName;
        ruleForm.form.uploadRcCnt = resp.data?.uploadRcCnt;
        ruleForm.form.errorLog = resp.data?.errorLog;
        ruleForm.form.processStatus = resp.data?.processStatus;
        ruleForm.form.processStatusDesc = resp.data?.processStatusDesc;
        let datas = resp.data?.listData;
        if (datas) {
          let rows = [];
          datas.forEach(elem => {
            rows.push({
              holidayDate: elem.holidayDate,
              holidayDesc: elem.holidayDesc,
            });
          });
          holidayGridRef.value.addBatch(rows);
        }
      }
    }
    return resp?.success;
  } else {
    return false;
  }
}
const holidayForm = reactive({
  holidayDate: null,
  holidayName: '',
  holidayDesc: ''
});
const tradeInstructionForm = reactive({
  transactionType: '',
  cutoffTime: ''
});

const cashCutoffTimeForm = reactive({
  cutoffType: '',
  currency: '',
  cutoffTime: '',
  currencyDesc: ''
});
const cashCutoffTimeFormSearch = reactive({
  cutoffType: '',
  currency: '',
});

const dayNightForm = reactive({
  effectiveDate: null,
  gmtOffset: '',
  status: ''
});
const depostitoryForm = reactive({
  depoCode: null as string | null,
  depoDesc: null as string | null,
  priSwiftBicCode: null as string | null,
  secSwiftBicCode: null as string | null,
  chargeSuspAcctNo: null as string | null,
  incomeSuspAcctNo: null as string | null,
  setlSuspAcctNo: null as string | null,
  status: null as string | null,
  caPayMethodCode: null as string | null,
  setlPayMethodCode: null as string | null,
  genDepoIntfInd: null as string | null,
  intfFormatCode: null as string | null,
  depoIntfAddr: null as string | null,
  settlePeriodCashRecDay: null as string | null,
  settlePeriodCashDeliverDay: null as string | null,
  settlePeriodStockRecDay: null as string | null,
  settlePeriodStockDeliverDay: null as string | null,
  marketDepoSubVPOList: [], // 子表数据列表
  subForm: { // 临时表单
    currency: null as string | null,
    chargeSuspAcctNo: null as string | null,
    incomeSuspAcctNo: null as string | null,
    setlSuspAcctNo: null as string | null,
  },
  editingSubIndex: undefined,
});
const subFormRules = {
  currency: [
    { required: true, message: 'Currency is required', trigger: 'blur' }
  ]
};
const handleMainDbClick = (row) => {
  // 假设子表数据在 row.marketDepoSubVPOList 中
  if (row.marketDepoSubVPOList && row.marketDepoSubVPOList.length > 0) {
    depositorySubVpos.value = [...row.marketDepoSubVPOList];
  }
};
const handleSubRowClick = (row, index) => {
  depostitoryForm.subForm = {
    currency: row.currency,
    chargeSuspAcctNo: row.chargeSuspAcctNo,
    incomeSuspAcctNo: row.incomeSuspAcctNo,
    setlSuspAcctNo: row.setlSuspAcctNo,
  };
  depostitoryForm.editingSubIndex = index;
};
const handleSaveSubForm = () => {
  // if (!depostitoryForm.subForm || !depostitoryForm.subForm.currency) {
  //   return;
  // }
  //
  // 如果 subFormList 不存在，初始化
  if (!depostitoryForm.marketDepoSubVPOList) {
    depostitoryForm.marketDepoSubVPOList = [];
  }
  //
  // // 判断是否是新增还是编辑
  // if (depostitoryForm.editingSubIndex) {
  //   // 编辑已有子表数据
  //   depostitoryForm.marketDepoSubVPOList[depostitoryForm.editingSubIndex] = {
  //     ...depostitoryForm.subForm
  //   };
  //   delete depostitoryForm.editingSubIndex;
  // } else {
  //
  // }
  // // 新增子表数据
  depostitoryForm.marketDepoSubVPOList.push({
    ...depostitoryForm.subForm
  });

  // 清空临时表单
  // depostitoryForm.subForm.currency = '';
  // depostitoryForm.subForm.chargeSuspAcctNo = '';
  // depostitoryForm.subForm.incomeSuspAcctNo = '';
  // depostitoryForm.subForm.setlSuspAcctNo = '';
};
const handleAddSubForm = () => {
  depostitoryForm.subForm = reactive({
    currency: '',
    chargeSuspAcctNo: '',
    incomeSuspAcctNo: '',
    setlSuspAcctNo: '',
  });

  if (!depostitoryForm.marketDepoSubVPOList) {
    depostitoryForm.marketDepoSubVPOList = [];
  }
};
const processingForm = reactive({
  cutoffNo: null,
  cutoffTime: ''
});
const depositoryAccountForm = reactive({
  custodianAccountName: '',
  subAccountName: '',
  custodianAccOid: '',
  clearingAgentOid: '',
  accountType: '',
  custodianAccountType: '',
  status: ''
});
const holidayRules = reactive({
  holidayDate: [
    commonRules.required,
  ],
  holidayName: [
    commonRules.required,
    commonRules.name,
  ],
  cutoffType: [
    commonRules.required,
  ]
});
const tradeInstructionRules = reactive({
  transactionType: [
    commonRules.required,
    {
      validator: (rule, value, callback) => {
        if (value && !tradeInstructionForm.cutoffTime) {
          callback(new Error('Please input the cutoff time for transaction type.'));
        } else {
          callback();
        }
      },
      trigger: 'change'
    }
  ],
  cutoffTime: [
    {
      validator: (rule, value, callback) => {
        if (value && !tradeInstructionForm.transactionType) {
          callback(new Error('Please input the transaction type for the cutoff time.'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ]
});
const cashCutoffTimeRules = reactive({
  cutoffType: [
    commonRules.required,
  ]
});
const depositoryRules = reactive({
  depoCode: [
    commonRules.required,
  ],
  depoDesc: [
    commonRules.required,
  ],
  priSwiftBicCode: [
    commonRules.required,
  ],
  status: [
    commonRules.required,
  ],
  caPayMethodCode: [
    commonRules.required,
  ],
  setlPayMethodCode: [
    commonRules.required,
  ],
  genDepoIntfInd: [
    commonRules.required,
  ],
  intfFormatCode: [
    commonRules.required,
  ],
  settlePeriodCashRecDay: [
    commonRules.required,
  ],
  settlePeriodCashDeliverDay: [
    commonRules.required,
  ],
  settlePeriodStockRecDay: [
    commonRules.required,
  ],
  settlePeriodStockDeliverDay: [
    commonRules.required,
  ],
});


onMounted(() => {
});
watch(
  () => ruleForm.form.retentionTranHis,
  (newVal) => {
    if (!isRead.value && !newVal) {
      ruleForm.form.retentionTranHis = '9999';
    }
  },
  { immediate: true }
);
watch(
  () => ruleForm.form.siPayMethod,
  (newVal) => {
    if (!isRead.value && !newVal) {
      ruleForm.form.siPayMethod = 'A';
    }
  },
  { immediate: true }
);
watch(
  () => ruleForm.form.caPayMethod,
  (newVal) => {
    if (!isRead.value && !newVal) {
      ruleForm.form.caPayMethod = 'A';
    }
  },
  { immediate: true }
);
watch(
  () => ruleForm.form.cashStockSettleMethod,
  (newVal) => {
    if (!isRead.value && !newVal) {
      ruleForm.form.cashStockSettleMethod = 'S';
    }
  },
  { immediate: true }
);

watch(
  () => ruleForm.form.marketSanctioned,
  (newVal) => {
    if (!isRead.value && !newVal) {
      ruleForm.form.marketSanctioned = 'N'
    }
  },
  { immediate: true, deep: true }

);

//结束时间必须晚于开始时间
const timeChange = (type) => {
  if (type === 'Online') {
    if (ruleForm.form.startOfOpsOnline && ruleForm.form.endOfOpsOnline) {
      if (ruleForm.form.startOfOpsOnline > ruleForm.form.endOfOpsOnline) {
        showErrorMsg('End of Ops Online must be equal to or later than Start of Ops Online.');
        return false;
      }
    }
  }

  if (type === 'Income') {
    if (ruleForm.form.contractIncomeStart && ruleForm.form.contractIncomeEnd) {
      if (ruleForm.form.contractIncomeStart > ruleForm.form.contractIncomeEnd) {
        showErrorMsg('End of Contractual Income must be equal to or later than Start of Contractual Income.');
        return false;
      }
    }

    if (ruleForm.form.startOfOpsOnline && ruleForm.form.contractIncomeStart) {
      if (ruleForm.form.startOfOpsOnline >= ruleForm.form.contractIncomeStart) {
        showErrorMsg('Start of Contractual Income must be later than the Start of Ops Online.');
        return false;
      }
    }

    if (ruleForm.form.endOfOpsOnline && ruleForm.form.contractIncomeEnd) {
      if (ruleForm.form.endOfOpsOnline <= ruleForm.form.contractIncomeEnd) {
        showErrorMsg('End of Contractual Income must be earlier than the End of Ops Online.');
        return false;
      }
    }
  }

  if (type === 'Settlement') {
    if (ruleForm.form.contractSetteStart && ruleForm.form.contractSetteEnd) {
      if (ruleForm.form.contractSetteStart > ruleForm.form.contractSetteEnd) {
        showErrorMsg('End of Contractual Settlement must be equal to or later than Start of Contractual Settlement.');
        return false;
      }
    }

    if (ruleForm.form.startOfOpsOnline && ruleForm.form.contractSetteStart) {
      if (ruleForm.form.startOfOpsOnline >= ruleForm.form.contractSetteStart) {
        showErrorMsg('Start of Contractual Settlement must be later than the Start of Ops Online.');
        return false;
      }
    }
    if (ruleForm.form.endOfOpsOnline && ruleForm.form.contractSetteEnd) {
      if (ruleForm.form.endOfOpsOnline <= ruleForm.form.contractSetteEnd) {
        showErrorMsg('End of Contractual Settlement must be earlier than the End of Ops Online.');
        return false;
      }
    }
  }


  return true;
};

const isEditing = ref<any | false>(false);
const highlightRow = ref<any | null>(null);
const depositoryTableList = ref<any[]>([]); // 存储表格数据
const editingRow = ref<any | null>(null); // 当前正在编辑的行
const newRow = ref<any | null>(null); // 新增行

// depository-info 联动控制变量
const depositoryInfoDisabled = ref(true); // 默认禁用
const currentSelectedDepository = ref(null); // 当前选中的 depository

// depository-currency-table 联动控制变量
const currencyTableDisabled = ref(true); // 控制 currency 表格的启用/禁用状态
const currentSelectedDepositoryForCurrency = ref(null); // 当前选中的 depository 数据（用于 currency 联动）

// depository-info 数据同步辅助函数
const loadDepositoryInfoData = (row) => {
  if (row) {
    // 显示实际数据值，不使用默认值
    depostitoryForm.caPayMethodCode = row.caPayMethodCode || null;
    depostitoryForm.setlPayMethodCode = row.setlPayMethodCode || null;
    depostitoryForm.genDepoIntfInd = row.genDepoIntfInd || null;
    depostitoryForm.intfFormatCode = row.intfFormatCode || null;
    depostitoryForm.depoIntfAddr = row.depoIntfAddr || null;
    depostitoryForm.settlePeriodCashRecDay = row.settlePeriodCashRecDay || null;
    depostitoryForm.settlePeriodCashDeliverDay = row.settlePeriodCashDeliverDay || null;
    depostitoryForm.settlePeriodStockRecDay = row.settlePeriodStockRecDay || null;
    depostitoryForm.settlePeriodStockDeliverDay = row.settlePeriodStockDeliverDay || null;
    currentSelectedDepository.value = row;
  }
};

const clearDepositoryInfoData = () => {
  depostitoryForm.caPayMethodCode = null;
  depostitoryForm.setlPayMethodCode = null;
  depostitoryForm.genDepoIntfInd = null;
  depostitoryForm.intfFormatCode = null;
  depostitoryForm.depoIntfAddr = null;
  depostitoryForm.settlePeriodCashRecDay = null;
  depostitoryForm.settlePeriodCashDeliverDay = null;
  depostitoryForm.settlePeriodStockRecDay = null;
  depostitoryForm.settlePeriodStockDeliverDay = null;
  currentSelectedDepository.value = null;
};

const saveDepositoryInfoToRow = (row) => {
  if (row) {
    row.caPayMethodCode = depostitoryForm.caPayMethodCode;
    row.setlPayMethodCode = depostitoryForm.setlPayMethodCode;
    row.genDepoIntfInd = depostitoryForm.genDepoIntfInd;
    row.intfFormatCode = depostitoryForm.intfFormatCode;
    row.depoIntfAddr = depostitoryForm.depoIntfAddr;
    row.settlePeriodCashRecDay = depostitoryForm.settlePeriodCashRecDay;
    row.settlePeriodCashDeliverDay = depostitoryForm.settlePeriodCashDeliverDay;
    row.settlePeriodStockRecDay = depostitoryForm.settlePeriodStockRecDay;
    row.settlePeriodStockDeliverDay = depostitoryForm.settlePeriodStockDeliverDay;
  }
};

// currency 表格数据同步辅助函数
const loadCurrencyDataForDepository = (depositoryRow) => {
  if (depositoryRow) {
    // 从 depository 对象的 marketDepoSubVPOList 中加载数据
    currencyTableList.value = depositoryRow.marketDepoSubVPOList || [];
    currentSelectedDepositoryForCurrency.value = depositoryRow;
    currencyTableDisabled.value = false;
  }
};

// 保存 currency 数据到当前选中的 depository 对象
const saveCurrencyDataToDepository = () => {
  if (currentSelectedDepositoryForCurrency.value) {
    currentSelectedDepositoryForCurrency.value.marketDepoSubVPOList = [...currencyTableList.value];
  }
};

const clearCurrencyTable = () => {
  currencyTableList.value = [];
  currentSelectedDepositoryForCurrency.value = null;
  currencyTableDisabled.value = true;
  // 如果正在编辑 currency，取消编辑
  if (currencyIsEditing.value) {
    currencyCancelEdit();
  }
};



function selectedRow(row: any) {
  if (isEditing.value) {
    return;
  };
  highlightRow.value = row;
  // 联动：选中行时，depository-info 显示对应数据（只读模式）
  loadDepositoryInfoData(row);
  depositoryInfoDisabled.value = true; // 选中时为只读状态
  // 联动：选中行时，currency 表格显示对应数据（只读模式）
  loadCurrencyDataForDepository(row);
  currencyTableDisabled.value = true; // 选中时 currency 表格为只读状态
}

function tableRowClassName({ row }: { row: any }) {
  return row === highlightRow.value ? 'selected-row' : '';
}

function startEdit(row: any) {
  if (isEditing.value) return;
  editingRow.value = { ...row };
  highlightRow.value = row;
  isEditing.value = true;
  // 编辑现有行时，清空新增行标记，避免取消时错误删除数据
  if (newRow.value && newRow.value !== row) {
    newRow.value = null;
  }
  // 联动：编辑时，depository-info 变为可编辑状态并加载数据
  loadDepositoryInfoData(row);
  depositoryInfoDisabled.value = false;
  // 联动：编辑时，currency 表格显示对应数据并启用编辑
  loadCurrencyDataForDepository(editingRow.value);
  currencyTableDisabled.value = false; // 确保 currency 表格在 depository 编辑时可编辑
}

function saveEdit(row: any, rowIndex: number) {

  if (!editingRow.value.depoCode) {
    showErrorMsg('depoCode is not null');
    return;
  }
  const exist = depositoryTableList.value.find(
    (item, idx) => item.depoCode === editingRow.value.depoCode && idx !== rowIndex
  );
  if (exist) {
    showErrorMsg('The same record already exists!');
    return;
  }
  // 联动：保存时，将 depository-info 数据同步到行数据
  saveDepositoryInfoToRow(editingRow.value);
  // 确保 currency 数据也同步到 depository 对象
  saveCurrencyDataToDepository();
  // 将编辑行的数据（包括currency数据）复制到原始行
  Object.assign(row, editingRow.value, { finishWrite: true });
  editingRow.value = null;
  highlightRow.value = row; // 保持选中状态
  isEditing.value = false;
  newRow.value = null;



  // 联动：保存后，depository-info 变为只读状态
  depositoryInfoDisabled.value = true;
  // 联动：保存后，currency 表格显示对应数据（只读模式）
  loadCurrencyDataForDepository(row);
  currencyTableDisabled.value = true; // 保存后 currency 表格变为只读

  //把结果回写到表单

}

function cancelEdit() {


  // 只有当取消的是新增行时，才从列表中删除
  const isNewRow = newRow.value && editingRow.value &&
    newRow.value.depoCodeOid === editingRow.value.depoCodeOid;

  const wasNewRow = isNewRow;
  const previousRow = highlightRow.value;
  editingRow.value = null;
  highlightRow.value = null;
  isEditing.value = false;

  if (isNewRow && newRow.value) {
    // 取消新增时移除新行
    const idx = depositoryTableList.value.indexOf(newRow.value);
    if (idx > -1) depositoryTableList.value.splice(idx, 1);
    newRow.value = null;

  }
  // 联动：取消编辑时，depository-info 清空并禁用
  if (wasNewRow) {
    clearDepositoryInfoData();
    depositoryInfoDisabled.value = true;
    // 联动：取消新增时，currency 表格清空并禁用
    clearCurrencyTable();
  } else {
    // 如果是编辑现有行，恢复到选中状态
    depositoryInfoDisabled.value = true;
    // 联动：取消编辑现有行时，currency 表格保持显示但禁用编辑
    if (previousRow) {
      highlightRow.value = previousRow; // 恢复选中状态
      loadDepositoryInfoData(previousRow); // 恢复depository-info数据显示
      loadCurrencyDataForDepository(previousRow);
      currencyTableDisabled.value = true; // 取消编辑后变为只读
    }
  }
}

function addRow() {
  if (isEditing.value) return;
  const row = {
    depoCodeOid: Date.now(),
    depoDesc: '',
    priSwiftBicCode: '',
    secSwiftBicCode: '',

    caPayMethodCode: '',
    setlPayMethodCode: '',
    genDepoIntfInd: '',
    intfFormatCode: '',
    depoIntfAddr: '',
    settlePeriodCashRecDay: '',
    settlePeriodCashDeliverDay: '',
    settlePeriodStockRecDay: '',
    settlePeriodStockDeliverDay: '',
    rdvpInd: '',

    // 添加 marketDepoSubVPOList 属性来存储关联的 currency 数据
    marketDepoSubVPOList: [],

    mkckAction: 'C',
    recordStatus: 'PD',
    sysCreateDate: null,
    finishWrite: false,
    status: 'A',
    level: 1,
  };
  depositoryTableList.value.push(row);
  newRow.value = row;
  editingRow.value = { ...row };
  highlightRow.value = row;
  isEditing.value = true;
  // 联动：添加新行时，depository-info 变为可编辑状态并设置新增默认值
  clearDepositoryInfoData();
  // 新增时设置默认值为'A'
  depostitoryForm.caPayMethodCode = 'A';
  depostitoryForm.setlPayMethodCode = 'A';
  depositoryInfoDisabled.value = false;
  // 联动：添加新行时，currency 表格清空并启用，准备为新 depository 添加 currency
  clearCurrencyTable();
  currencyTableDisabled.value = false;
  currentSelectedDepositoryForCurrency.value = editingRow.value;
}

const removeRow = async (row: any) => {

  selectedRow(row);
  return;

  if (row.recordStatus == 'A') {
    row.recordStatus = 'PD';
    row.mkckAction = 'D';
    depositoryTableList.value[row.oid] = row;
    debugger;
  } else if (row.recordStatus == 'PD' && row.mkckAction == "C") {
    if (row.sysCreateDate) {
      row.mkckAction = 'D';
      depositoryTableList.value[row.oid] = row;
    }

  }




  const idx = depositoryTableList.value.indexOf(row);
  if (idx > -1) depositoryTableList.value.splice(idx, 1);
  if (highlightRow.value === row) {
    highlightRow.value = null;
    // 联动：删除选中行时，depository-info 清空并禁用
    clearDepositoryInfoData();
    depositoryInfoDisabled.value = true;
    // 联动：删除选中行时，currency 表格清空并禁用
    clearCurrencyTable();
  }
  if (editingRow.value && row.depoCodeOid === editingRow.value.depoCodeOid) cancelEdit();
}

const depoCodeDbClickCheck = (row, code, desc) => {


  const exist = depositoryTableList.value.find(
    (item, idx) => item.depoCode === code
  );
  if (exist) {
    showErrorMsg('depoCode is exist');
    return;
  }
  editingRow.value.depoCode = code;
  editingRow.value.depoDesc = desc;

}

const depoInlineRules = reactive({
    opCtryRegionCode: [
        commonRules.required,
    ],
    ctryRegionCode: [
        commonRules.required,
        commonRules.name,
    ],
    ctryRegionName:[
        commonRules.name,
    ],
    isoCode: [
        commonRules.required,
        commonRules.name,
    ],
    currencyCode: [
        commonRules.required,
    ],
    status: [
        commonRules.selectRequired,
    ],
})

// Start  currency 表格编辑



const currencyIsEditing = ref<any | false>(false); //是否正在编辑
const currencyHighlightRow = ref<any | null>(null); //高亮行
const currencyTableList = ref<any[]>([]); // 表格数据
const currencyEditingRow = ref<any | null>(null); // 当前正在编辑的行
const currencyNewRow = ref<any | null>(null); // 新增行
const currencyForm = reactive({
  marketDepoSubVPOList: [],
  form: {
    currencyOid: null as number | null,
    currency: null as string | null,
    chargeSuspAcctNo: null as string | null,
    incomeSuspAcctNo: null as string | null,
    setlSuspAcctNo: null as string | null,
    mkckAction: "C",
    recordStatus: "PD",
    finishWrite: true,
    status: "A",
  }
})





function currencySelectedRow(row: any) {
  if (currencyIsEditing.value) {
    return;
  };
  currencyHighlightRow.value = row;
}

function currencyTableRowClassName({ row }: { row: any }) {
  return row === currencyHighlightRow.value ? 'selected-row' : '';
}

function currencyStartEdit(row: any) {
  if (currencyIsEditing.value) return;
  currencyEditingRow.value = { ...row };
  currencyHighlightRow.value = row;
  currencyIsEditing.value = true;
  // 编辑现有行时，清空新增行标记，避免取消时错误删除数据
  if (currencyNewRow.value && currencyNewRow.value !== row) {
    currencyNewRow.value = null;
  }
}

function currencySaveEdit(row: any, rowIndex: number) {

  if (!currencyEditingRow.value.currency) {
    showErrorMsg('currency is not null');
    return;
  }
  const exist = currencyTableList.value.find(
    (item, idx) => item.currency === currencyEditingRow.value.currency && idx !== rowIndex
  );
  if (exist) {
    showErrorMsg('currency is exist');
    return;
  }
  Object.assign(row, currencyEditingRow.value, { finishWrite: true });
  currencyEditingRow.value = null;
  currencyHighlightRow.value = null;
  currencyIsEditing.value = false;
  currencyNewRow.value = null;

  // 保存 currency 数据到当前选中的 depository 对象
  saveCurrencyDataToDepository();
}

function currencyCancelEdit() {
  // 只有当取消的是新增行时，才从列表中删除
  const isNewRow = currencyNewRow.value && currencyEditingRow.value &&
    currencyNewRow.value.currencyOid === currencyEditingRow.value.currencyOid;

  currencyEditingRow.value = null;
  currencyHighlightRow.value = null;
  currencyIsEditing.value = false;

  if (isNewRow && currencyNewRow.value) {
    // 取消新增时移除新行
    const idx = currencyTableList.value.indexOf(currencyNewRow.value);
    if (idx > -1) currencyTableList.value.splice(idx, 1);
    currencyNewRow.value = null;
  }
}

function currencyAddRow() {
  if (currencyIsEditing.value) {
    showErrorMsg('正在编辑');
    return;
  };
  // 确保有选中的 depository
  if (!currentSelectedDepositoryForCurrency.value) {
    showErrorMsg('Please select a depository first');
    return;
  }
  const row = {
    currencyOid: Date.now(),
    currency: '',
    chargeSuspAcctNo: '',
    incomeSuspAcctNo: '',
    setlSuspAcctNo: '',
    depositoryOid: currentSelectedDepositoryForCurrency.value.depoCodeOid, // 关联到当前选中的 depository
    mkckAction: "C",
    recordStatus: "PD",
    finishWrite: true,
    status: "A",
  };
  currencyTableList.value.push(row);
  currencyNewRow.value = row;
  currencyEditingRow.value = { ...row };
  currencyHighlightRow.value = row;
  currencyIsEditing.value = true;
}

const currencyRemoveRow = async (row: any) => {
  const idx = currencyTableList.value.indexOf(row);
  if (idx > -1) currencyTableList.value.splice(idx, 1);
  if (currencyHighlightRow.value === row) currencyHighlightRow.value = null;
  if (currencyEditingRow.value && row.currencyOid === currencyEditingRow.value.currencyOid) currencyCancelEdit();

  // 删除后同步数据到 depository 对象
  saveCurrencyDataToDepository();
}

const currencyDbClickCheck = (row, code, desc) => {


  const exist = currencyTableList.value.find(
    (item, idx) => item.currency === code
  );
  if (exist) {
    showErrorMsg('currency is exist');
    return;
  }
  currencyEditingRow.value.currency = code;

}



// End  currency 表格编辑

// 构造 depository 提交格式



// end 构造 depository 提交格式


</script>

<style scoped>
.form-row {
  margin-block: 3px;
}

.selected-row {
  background-color: #e0f7fa !important;
}

.depository-info-disabled {
  opacity: 0.6;
  pointer-events: none;
}

.currency-table-disabled {
  opacity: 0.6;
  pointer-events: none;
}
</style>