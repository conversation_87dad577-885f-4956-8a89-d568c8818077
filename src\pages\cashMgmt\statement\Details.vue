<template>
    <BaseDetails ref="details" :handleSave="handleSave" :beforeSubmit="beforeSubmit" :reload="props.reload" :viewOriginalForm="viewOriginalForm">
        <el-form :validateOnRuleChange="false" :disabled="formDisabled" ref="ruleFormRef" style="width: 100%" :model="ruleForm.form" :rules="rules" status-icon>
            <FormRow>
                <ElFormItemProxy :detailsRef="details" :label="$t('csscl.cash.statement.clientNumberCin')" prop="clientCode" :label-width="labelWidthL">
                    <el-input v-model="ruleForm.form.clientCode" :disabled="true" style="width: 150px" />
                </ElFormItemProxy>
                <ElFormItemProxy :detailsRef="details" :label="$t('csscl.cash.statement.clientShortName')" prop="clientShortName" label-width="190px">
                    <el-input v-model="ruleForm.form.clientShortName" :disabled="true" style="width: 400px" input-style="text-transform:none"/>
                </ElFormItemProxy>
                <ElFormItemProxy :detailsRef="details" :label="$t('csscl.cash.statement.stmtDate')" prop="stmtDate" label-width="150px">
                    <DateItem v-model="ruleForm.form.stmtDate" style="width: 120px;" disabled />
                </ElFormItemProxy>
            </FormRow>
            <FormRow>
                <ElFormItemProxy :detailsRef="details" :label="$t('csscl.cash.statement.custAccNo')" prop="custAccNo" :label-width="labelWidthL">
                    <el-input v-model="ruleForm.form.custAccNo" :disabled="true" style="width: 200px" />
                </ElFormItemProxy>
                <ElFormItemProxy :detailsRef="details" :label="$t('csscl.cash.statement.custAccShortName')" prop="custAccShortName" label-width="190px">
                    <el-input v-model="ruleForm.form.custAccShortName" :disabled="true" style="width: 400px" input-style="text-transform:none"/>
                </ElFormItemProxy>
                <ElFormItemProxy :detailsRef="details" :label="$t('csscl.cash.statement.cashAccNo')" prop="cashAccNo" label-width="150px">
                    <el-input v-model="ruleForm.form.cashAccNo" :disabled="true" style="width: 200px" />
                </ElFormItemProxy>
            </FormRow>
            <FormRow>
                <FormItemSign :detailsRef="details" :label="$t('csscl.cash.statement.uploadFile')" prop="upldFileName" :label-width="labelWidthL">
                    <el-space>
                        <el-input v-model="ruleForm.form.upldFileName" :disabled="true" style="width: 400px;" class="text-none" />
                        <el-icon-folder-opened  style="width:20px;height:20px;color:red" @click="previewReconFile" v-if="ruleForm.form.upldFileName"/>
                        <UploadItem v-model:file-list="ruleForm.form.fileList" :show-file-list="false" :auto-upload="false" accept=".csv" 
                            :on-change="uploadFile" :disabled="reconCompInd">
                            <el-button type="info" :disabled="reconCompInd">{{$t('csscl.cash.statement.upload')}}</el-button>
                        </UploadItem>
                        <el-button type="info" @click="downloadTemplate">{{$t('csscl.cash.statement.downloadTemplate')}}</el-button>
                    </el-space>
                </FormItemSign>
            </FormRow>
            <el-row>
                <el-col :span="11">
                    <el-card :body-style="{padding:'0px',margin:'3px'}">
                        <div slot="header" style="display:flex;justify-content:center;background-color: #daeaf5;">
                            <span style="font-weight: bold;">{{$t('csscl.cash.statement.bankBook')}}</span>
                        </div>
                        <div>
                            <FormRow>
                                <FormItemSign></FormItemSign>
                                <FormItemSign :detailsRef="details" :label="$t('csscl.cash.statement.openBalance')" prop="bankBkOpenBal" label-width="120px">
                                    <!-- <el-input-number v-model="ruleForm.form.bankBkOpenBal" :controls="false" :precision="2" :disabled="true" style="width: 170px"/> -->
                                     <!-- Start SK-COMMON-0146, Tom.Li, 2024/09/06 -->
                                    <InputNumber v-model="ruleForm.form.bankBkOpenBal" :controls="false" :disabled="true" style="width: 170px;" :scale="ruleForm.form.decimalPoint" />
                                    <!-- End SK-COMMON-0146, Tom.Li, 2024/09/06 -->
                                </FormItemSign>
                            </FormRow>
                            <div class="reconGrid">
                            <Grid url="/cashmgmt/api/v1/bankstmt/reconciliation/bank/book/list" ref="bankBookGrid"
                            :cell-style="cellStyleBank"
                            :isHideOrder="true"
                            :isManual="true"
                            :afterSearch="afterSearchBank"
                            :params="{ reconBankStmtOid: ruleForm.form.currentOid, reconBankStmtPendingOid: ruleForm.form.pendingOid }" 
                            :onClick="bankBookRowClick"
                            :isMultiple="true"
                            :isHideCheckBox="true"
                            :columns="[
                                {title:'',name:'seq',sorter:false,width:'20',align:'center'},
                                {title:'csscl.cash.statement.txnRef',name:'txnRef',sorter:false,},
                                {title:'csscl.cash.statement.mnCode',name:'mnCode',sorter:false,width:'80'},
                                {title:'csscl.cash.statement.journalNumber',name:'jnlNo',sorter:false,width:'130'},
                                {title:'csscl.cash.statement.valueDate',name:'valueDate',sorter:false,width:'80'},
                                {title:'csscl.cash.statement.drCr',name:'drCrInd',sorter:false,width:'40',align:'center'},
                                {title:'csscl.cash.statement.ccy',name:'currencyCode',sorter:false,width:'30'},
                                {title:'csscl.cash.statement.amount',name:'txnAmt',sorter:false,width:'150',dataType:'NUM',fn:gridTxnAmtFmt},
                                {title:'csscl.cash.statement.match',name:'reconFlag',sorter:false,width:'40',align:'center'},
                                ]"
                            />
                            </div>
                            <FormRow>
                                <FormItemSign></FormItemSign>
                                <FormItemSign :detailsRef="details" :label="$t('csscl.cash.statement.closeBalance')" prop="bankBkCloseBal" label-width="120px">
                                    <!-- <el-input-number v-model="ruleForm.form.bankBkCloseBal" :controls="false" :precision="2" :disabled="true" style="width: 170px"/> -->
                                     <!-- Start SK-COMMON-0146, Tom.Li, 2024/09/06 -->
                                    <InputNumber v-model="ruleForm.form.bankBkCloseBal" :controls="false" :disabled="true" style="width: 170px;" :scale="ruleForm.form.decimalPoint" />
                                    <!-- End SK-COMMON-0146, Tom.Li, 2024/09/06 -->
                                </FormItemSign>
                            </FormRow>
                            <FormRow>
                                <FormItemSign :detailsRef="details" :label="$t('csscl.cash.statement.funcType')"prop="bankBook.funcType" :label-width="labelWidthL" style="margin-top: 34px">
                                    <el-input :model-value="getCommonDesc('FUNCTION_TYPE_BK', ruleForm.form.bankBook.funcType)" :disabled="true" style="width: 250px" class="text-none"/>
                                </FormItemSign>
                            </FormRow>
                            <FormRow>
                                <FormItemSign :detailsRef="details" :label="$t('csscl.cash.statement.fxType')" prop="bankBook.fxType" :label-width="labelWidthL">
                                    <el-input v-model="ruleForm.form.bankBook.fxType" :disabled="true" style="width: 180px" />
                                </FormItemSign>
                            </FormRow>
                            <FormRow>
                                <FormItemSign :detailsRef="details" :label="$t('csscl.cash.statement.fxPair')" prop="bankBook.fxPair" :label-width="labelWidthL">
                                    <el-input v-model="ruleForm.form.bankBook.fxPair" :disabled="true" style="width: 100px" />
                                </FormItemSign>
                                <FormItemSign :detailsRef="details" :label="$t('csscl.cash.statement.fxRate')" prop="bankBook.fxRate" :label-width="labelWidthR">
                                    <!-- <el-input-number v-model="ruleForm.form.bankBook.fxRate" :controls="false" :precision="6" :disabled="true" style="width: 170px"/> -->
                                    <el-input class="input-number" v-model="ruleForm.form.bankBook.fxRate" :disabled="true" style="width: 110px;" :formatter="thousFormat" :parser="thousParser" />
                                </FormItemSign>
                            </FormRow>
                            <FormRow>
                                <FormItemSign :detailsRef="details" :label="$t('csscl.cash.statement.drCr')" prop="bankBook.drCrInd" :label-width="labelWidthL">
                                    <Select v-model="ruleForm.form.bankBook.drCrInd" style="width: 100px" disabled type='DR_CR' />
                                </FormItemSign>
                            </FormRow>
                            <FormRow>
                                <FormItemSign :detailsRef="details" :label="$t('csscl.cash.statement.currency')" prop="bankBook.currencyCode" :label-width="labelWidthL">
                                    <CurrencySearchInput v-model="ruleForm.form.bankBook.currencyCode" showDesc="false" :disabled="true" style="width: 120px"/>
                                </FormItemSign>
                                <FormItemSign :detailsRef="details" :label="$t('csscl.cash.statement.amount')" prop="bankBook.txnAmt" :label-width="labelWidthR">
                                    <!-- <el-input-number v-model="ruleForm.form.bankBook.txnAmt" :controls="false" :precision="2" :disabled="true" style="width: 170px"/> -->
                                     <!-- Start SK-COMMON-0146, Tom.Li, 2024/09/06 -->
                                    <InputNumber v-model="ruleForm.form.bankBook.txnAmt" :controls="false" :disabled="true" style="width: 170px;" :scale="ruleForm.form.bankBook.decimalPoint" />
                                    <!-- End SK-COMMON-0146, Tom.Li, 2024/09/06 -->
                                </FormItemSign>
                            </FormRow>
                            <FormRow>
                                <FormItemSign :detailsRef="details" :label="$t('csscl.cash.statement.instructionDate')"prop="bankBook.instrDt" :label-width="labelWidthL">
                                    <DateItem v-model="ruleForm.form.bankBook.instrDt" :disabled="true" style="width: 130px;"/>
                                </FormItemSign>
                                <FormItemSign :detailsRef="details" :label="$t('csscl.cash.statement.valueDate')"prop="bankBook.valueDate" :label-width="labelWidthR">
                                    <DateItem v-model="ruleForm.form.bankBook.valueDate" :disabled="true" style="width: 130px;"/>
                                </FormItemSign>
                            </FormRow>
                            <FormRow>
                                <FormItemSign :detailsRef="details" :label="$t('csscl.cash.statement.classCode')" prop="bankBook.classCode" :label-width="labelWidthL">
                                    <el-input v-model="ruleForm.form.bankBook.classCode" :disabled="true" style="width: 170px" />
                                </FormItemSign>
                            </FormRow>
                            <FormRow>
                                <FormItemSign :detailsRef="details" :label="$t('csscl.cash.statement.depositDate')"prop="bankBook.depositDate" :label-width="labelWidthL">
                                    <DateItem v-model="ruleForm.form.bankBook.depositDate" :disabled="true" style="width: 130px;"/>
                                </FormItemSign>
                                <FormItemSign :detailsRef="details" :label="$t('csscl.cash.statement.maturityDate')"prop="bankBook.maturityDate" :label-width="labelWidthR">
                                    <DateItem v-model="ruleForm.form.bankBook.maturityDate" :disabled="true" style="width: 130px;"/>
                                </FormItemSign>
                            </FormRow>
                            <FormRow>
                                <FormItemSign :detailsRef="details" :label="$t('csscl.cash.statement.timeDepositIntRate')" prop="bankBook.timeDepositIntRate" :label-width="labelWidthL">
                                    <!-- <el-input-number v-model="ruleForm.form.bankBook.timeDepositIntRate" :controls="false" :precision="6" :disabled="true" style="width: 170px"/> -->
                                    <el-input class="input-number" v-model="ruleForm.form.bankBook.timeDepositIntRate" :disabled="true" style="width: 110px;" :formatter="thousFormat" :parser="thousParser" />
                                </FormItemSign>
                                <FormItemSign :detailsRef="details" :label="$t('csscl.cash.statement.timeDepositIntAmt')" prop="bankBook.timeDepositIntAmt" :label-width="labelWidthR">
                                    <!-- <el-input-number v-model="ruleForm.form.bankBook.timeDepositIntAmt" :controls="false" :precision="2" :disabled="true" style="width: 170px"/> -->
                                    <el-input class="input-number" v-model="ruleForm.form.bankBook.timeDepositIntAmt" :disabled="true" style="width: 170px;" :formatter="thousFormat" :parser="thousParser" />
                                </FormItemSign>
                            </FormRow>
                            <FormRow>
                                <FormItemSign :detailsRef="details" :label="$t('csscl.cash.statement.transRef')" prop="bankBook.txnRef" :label-width="labelWidthL">
                                    <el-input v-model="ruleForm.form.bankBook.txnRef" type="textarea" rows="4" :disabled="true" style="width: 640px;"/>
                                </FormItemSign>
                            </FormRow>
                            <FormRow>
                                <FormItemSign :detailsRef="details" :label="$t('csscl.cash.statement.remark')" prop="bankBook.remark" :label-width="labelWidthL">
                                    <el-input v-model="ruleForm.form.bankBook.remark" type="textarea" :disabled="true" style="width: 640px;"/>
                                </FormItemSign>
                            </FormRow>
                        </div>
                    </el-card>
                </el-col>
                <el-col :span="1">
                    <div style="display:flex;justify-content:center;">
                        <el-button type="info" @click="handleCopy" :disabled="custDis || reconCompInd" style="margin-top: 150px">{{$t('csscl.cash.statement.copy')}}</el-button>
                    </div>
                </el-col>
                <el-col :span="12">
                    <el-card :body-style="{padding:'0px',margin:'3px'}">
                        <div slot="header" style="display:flex;justify-content:center;background-color:#daeaf5;">
                            <span style="font-weight: bold;">{{$t('csscl.cash.statement.custodianBook')}}</span>
                        </div>
                        <div>
                            <FormRow>
                                <FormItemSign></FormItemSign>
                                <FormItemSign :detailsRef="details" :label="$t('csscl.cash.statement.openBalance')" prop="custBkOpenBal" label-width="120px">
                                    <!-- <el-input-number v-model="ruleForm.form.custBkOpenBal" :controls="false" :precision="2" :disabled="true" style="width: 170px"/> -->
                                     <!-- Start SK-COMMON-0146, Tom.Li, 2024/09/06 -->
                                    <InputNumber :class="custBkOpenBalSty()" v-model="ruleForm.form.custBkOpenBal" :controls="false" :disabled="true" style="width: 170px;" :scale="ruleForm.form.decimalPoint" />
                                    <!-- End SK-COMMON-0146, Tom.Li, 2024/09/06 -->
                                </FormItemSign>
                            </FormRow>
                            <el-row style="width: 100%;">
                            <el-col :span="23" class="reconGrid">
                            <Grid url="/cashmgmt/api/v1/bankstmt/reconciliation/cust/book/list" ref="custBookGrid"
                            :cell-style="cellStyleCust"
                            :isHideOrder="true"
                            :afterSearch="afterSearchCust"
                            :params="{ reconBankStmtOid: ruleForm.form.currentOid, reconBankStmtPendingOid: ruleForm.form.pendingOid }" 
                            :onClick="custBookRowClick"
                            :beforeChangePage="beforeChangePage"
                            :columns="[
                                {title:'',name:'linkedSeq',sorter:false,width:'20',align:'center'},
                                {title:'csscl.cash.statement.txnRef',name:'txnRef',sorter:false,},
                                {title:'csscl.cash.statement.source',name:'sourceSys',sorter:false,width:'65',fn:commDesc('RECON_SOURCE_SYS')},
                                {title:'csscl.cash.statement.instrDt',name:'instrDt',sorter:false,width:'80'},
                                {title:'csscl.cash.statement.valueDate',name:'valueDate',sorter:false,width:'80'},
                                {title:'csscl.cash.statement.projValueDt',name:'projValueDate',sorter:false,width:'110'},
                                {title:'csscl.cash.statement.od',name:'odInd',sorter:false,width:'20',align:'center'},
                                {title:'csscl.cash.statement.drCr',name:'drCrInd',sorter:false,width:'40',align:'center'},
                                {title:'csscl.cash.statement.ccy',name:'currencyCode',sorter:false,width:'30'},
                                {title:'csscl.cash.statement.amount',name:'txnAmt',sorter:false,dataType:'NUM',width:'120',fn:gridTxnAmtFmt},
                                {title:'csscl.cash.statement.match',name:'reconFlag',sorter:false,width:'40',align:'center'},
                                ]"
                            />
                            </el-col>
                            <el-col :span="1">
                                <el-button type="info" @click="addCustBook()" :disabled="reconCompInd" style="width: 22px; height: 22px; margin-top: 68px;" :icon="Plus"></el-button>
                                <br>
                                <el-button type="info" @click="deleteCustBook()" :disabled="custDis || reconCompInd" style="width: 22px; height: 22px; margin-top: 2px;" :icon="Minus"></el-button>
                                <br>
                                <el-button type="info" @click="unlinkCustBook(ruleForm.form.custBook)" :disabled="unlinkDis || reconCompInd" style="width: 40px; height: 22px; margin-top: 2px;">Unlink</el-button>
                            </el-col>
                            </el-row>
                            <el-form :validateOnRuleChange="false" ref="custBookFormRef" :disabled="formDisabled" :rules="custBookRules" :model="ruleForm.form.custBook">
                            <FormRow>
                                <FormItemSign></FormItemSign>
                                <FormItemSign :detailsRef="details" :label="$t('csscl.cash.statement.closeBalance')" prop="custBkCloseBal" label-width="120px">
                                    <el-space>
                                    <InputNumber :class="custBkCloseBalSty()" v-model="ruleForm.form.custBkCloseBal" :controls="false" :disabled="true" style="width: 170px;" :scale="ruleForm.form.decimalPoint" />
                                    <el-text>(exclude Overdue)</el-text>
                                    </el-space>
                                </FormItemSign>
                            </FormRow>
                            <FormRow>
                                <FormItemSign></FormItemSign>
                                <FormItemSign :detailsRef="details" :label="$t('csscl.cash.statement.overdueAmount')" prop="overdueAmount" label-width="120px">
                                    <!-- <el-input-number v-model="ruleForm.form.overdueAmount" :controls="false" :precision="2" :disabled="true" style="width: 170px"/> -->
                                     <!-- Start SK-COMMON-0146, Tom.Li, 2024/09/06 -->
                                    <InputNumber :class="custBkCloseBalSty()" v-model="ruleForm.form.custBkOdAmt" :controls="false" :disabled="true" style="width: 170px;" :scale="ruleForm.form.decimalPoint" />
                                    <!-- End SK-COMMON-0146, Tom.Li, 2024/09/06 -->
                                </FormItemSign>
                            </FormRow>
                            <FormRow>
                                <FormItemSign :detailsRef="custDetails" :label="$t('csscl.cash.statement.funcType')"prop="funcType" :label-width="labelWidthL">
                                    <Select v-model="ruleForm.form.custBook.funcType" :disabled="custDis" style="width: 250px" type='FUNCTION_TYPE' />
                                </FormItemSign>
                            </FormRow>
                            <FormRow>
                                <FormItemSign :detailsRef="custDetails" :label="$t('csscl.cash.statement.fxType')" prop="fxType" :label-width="labelWidthL">
                                    <Select v-model="ruleForm.form.custBook.fxType" :disabled="custDis" style="width: 180px" type='FX_TYPE'/>
                                </FormItemSign>
                            </FormRow>
                            <FormRow>
                                <FormItemSign :detailsRef="custDetails" :label="$t('csscl.cash.statement.fxPair')" prop="fxPair" :label-width="labelWidthL">
                                    <el-input v-model="ruleForm.form.custBook.fxPair" maxlength="7" :disabled="custDis" style="width: 100px" />
                                </FormItemSign>
                                <FormItemSign :detailsRef="custDetails" :label="$t('csscl.cash.statement.fxRate')" prop="fxRate" :label-width="labelWidthR">
                                    <InputNumber v-model="ruleForm.form.custBook.fxRate" :disabled="custDis" style="width: 110px;" precision="8" scale="6"/>
                                </FormItemSign>
                            </FormRow>
                            <FormRow>
                                <FormItemSign :detailsRef="custDetails" :label="$t('csscl.cash.statement.drCr')" prop="drCrInd" :label-width="labelWidthL">
                                    <Select v-model="ruleForm.form.custBook.drCrInd" :disabled="custDis||reconCompInd" style="width: 100px" type='DR_CR' :change="matchCustBook"/>
                                </FormItemSign>
                            </FormRow>
                            <FormRow>
                                <FormItemSign :detailsRef="custDetails" :label="$t('csscl.cash.statement.currency')" prop="currencyCode" :label-width="labelWidthL">
                                    <!-- Start SK-COMMON-0146, Tom.Li, 2024/09/06 -->
                                    <!-- <CurrencySearchInput v-model="ruleForm.form.custBook.currencyCode" showDesc="false" :disabled="custDis||reconCompInd" style="width: 120px" :dbClick="(row)=>{ruleForm.form.custBook.decimalPoint = row.var1;ruleForm.form.custBook.txnAmt=null;}" @change="changeCustCcyCode"/> -->
                                        <CurrencySearchInput v-model="ruleForm.form.custBook.currencyCode" showDesc="false" :disabled="true" style="width: 120px"/>    
                                    <!-- End SK-COMMON-0146, Tom.Li, 2024/09/06 -->
                                </FormItemSign>
                                <FormItemSign :detailsRef="custDetails" :label="$t('csscl.cash.statement.amount')" prop="txnAmt" :label-width="labelWidthR">
                                    <!-- Start SK-COMMON-0146, Tom.Li, 2024/09/06 -->
                                    <InputNumber v-model="ruleForm.form.custBook.txnAmt" :disabled="custDis||reconCompInd" style="width: 170px;" precision="13" :scale="ruleForm.form.custBook.decimalPoint" @change="changeCustTxnAmt"/>
                                    <!-- End SK-COMMON-0146, Tom.Li, 2024/09/06 -->
                                </FormItemSign>
                            </FormRow>
                            <FormRow>
                                <FormItemSign :detailsRef="custDetails" :label="$t('csscl.cash.statement.instructionDate')"prop="instrDt" :label-width="labelWidthL">
                                    <DateItem v-model="ruleForm.form.custBook.instrDt" :disabled="custDis" style="width: 130px;"/>
                                </FormItemSign>
                                <FormItemSign :detailsRef="custDetails" :label="$t('csscl.cash.statement.valueDate')"prop="valueDate" :label-width="labelWidthR">
                                    <DateItem v-model="ruleForm.form.custBook.valueDate" :disabled="custDis" style="width: 130px;" @change="matchCustBook"/>
                                </FormItemSign>
                            </FormRow>
                            <FormRow>
                                <FormItemSign :detailsRef="custDetails" :label="$t('csscl.cash.statement.classCode')" prop="classCode" :label-width="labelWidthL">
                                    <el-input v-model="ruleForm.form.custBook.classCode" maxlength="15" :disabled="custDis" style="width: 170px" />
                                </FormItemSign>
                            </FormRow>
                            <FormRow>
                                <FormItemSign :detailsRef="custDetails" :label="$t('csscl.cash.statement.depositDate')"prop="depositDate" :label-width="labelWidthL">
                                    <DateItem v-model="ruleForm.form.custBook.depositDate" :disabled="custDis" style="width: 130px;"/>
                                </FormItemSign>
                                <FormItemSign :detailsRef="custDetails" :label="$t('csscl.cash.statement.maturityDate')"prop="maturityDate" :label-width="labelWidthR">
                                    <DateItem v-model="ruleForm.form.custBook.maturityDate" :disabled="custDis" style="width: 130px;"/>
                                </FormItemSign>
                            </FormRow>
                            <FormRow>
                                <FormItemSign :detailsRef="custDetails" :label="$t('csscl.cash.statement.timeDepositIntRate')" prop="timeDepositIntRate" :label-width="labelWidthL">
                                    <InputNumber v-model="ruleForm.form.custBook.timeDepositIntRate" :disabled="custDis" style="width: 110px;" precision="8" scale="6" />
                                </FormItemSign>
                                <FormItemSign :detailsRef="custDetails" :label="$t('csscl.cash.statement.timeDepositIntAmt')" prop="timeDepositIntAmt" :label-width="labelWidthR">
                                    <InputNumber v-model="ruleForm.form.custBook.timeDepositIntAmt" :disabled="custDis" style="width: 170px;" />
                                </FormItemSign>
                            </FormRow>
                            <FormRow>
                                <FormItemSign :detailsRef="custDetails" :label="$t('csscl.cash.statement.transRef')" prop="txnRef" :label-width="labelWidthL">
                                    <el-input v-model="ruleForm.form.custBook.txnRef" type="textarea" rows="4" maxlength="400" show-word-limit :disabled="custDis" style="width: 710px;" @change="matchCustBook"/>
                                </FormItemSign>
                            </FormRow>
                            <FormRow>
                                <FormItemSign :detailsRef="custDetails" :label="$t('csscl.cash.statement.remark')" prop="remark" :label-width="labelWidthL">
                                    <el-input v-model="ruleForm.form.custBook.remark" type="textarea" maxlength="200" show-word-limit :disabled="custDis" style="width: 710px;"/>
                                </FormItemSign>
                            </FormRow>
                            <FormRow>
                                <ElFormItemProxy :detailsRef="custDetails" :label="$t('csscl.cash.statement.actualPostingDate')" prop="postingDt" :label-width="labelWidthL">
                                    <el-input v-model="ruleForm.form.custBook.postingDt" :disabled="true" style="width: 110px" />
                                </ElFormItemProxy>
                            </FormRow>
                            <FormRow>
                                <ElFormItemProxy :detailsRef="custDetails" :label="$t('csscl.cash.statement.creationDateAndTime')" prop="sysCreateDate" :label-width="labelWidthL">
                                    <el-input v-model="ruleForm.form.custBook.sysCreateDate" :disabled="true" style="width: 170px" />
                                </ElFormItemProxy>
                                <ElFormItemProxy :detailsRef="custDetails" :label="$t('csscl.cash.statement.lastModDateAndTime')" prop="sysUpdateDate" :label-width="labelWidthR">
                                    <el-input v-model="ruleForm.form.custBook.sysUpdateDate" :disabled="true" style="width: 170px" />
                                </ElFormItemProxy>
                            </FormRow>
                            </el-form>
                        </div>
                    </el-card>
                </el-col>
            </el-row>
        </el-form>
    </BaseDetails>
</template>

<script lang="ts" setup>
import { ref, reactive, getCurrentInstance, watch, nextTick } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessageBox } from 'element-plus';
import { Plus, Minus } from '@element-plus/icons-vue';
import BaseDetails from '~/pages/base/Details.vue';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import CurrencySearchInput from '~/pages/base/CurrencySearchInput.vue';
import Grid from '~/pages/base/Grid.vue';
import { getOid, downloadFile, thousFormat, thousParser, randomHashCode, validSearchInputValue, getCommonDesc, saveMsgBox, commDesc } from '~/util/Function.js';
import { commonRules, showValidateMsg } from '~/util/Validators.js';
import  * as CurrencyApi from "~/api/staticData/currency";
import { addCustValid, addEndHandle, addEnterObj4List } from "~/util/ModifiedValidate";

const { proxy } = getCurrentInstance();
const props = defineProps(['reload']);
const details = ref();
const formDisabled = ref(false);
const isMove = ref(false);
const editRow = (row, disabled, newId) => {
    const oid = newId || (row?.currentOid && getOid(row, disabled, proxy));
    if (oid) {
        proxy.$axios.get("/cashmgmt/api/v1/bankstmt/reconciliation/details?objectId=" + oid).then((body) => {
            if (body.success) {
                ruleForm.form = body.data;
                ruleForm.form.bankBook = {};
                ruleForm.form.custBook = {};
                ruleForm.form.fileList = [];
                details.value.currentRow = body.data;
                reconRsltData.value = null;
                if(ruleForm.form.processStatus !== "R" ){
                    formDisabled.value = true;
                }
                if(ruleForm.form.reconStatus === "C" && (!ruleForm.form.beforeImage || ruleForm.form.beforeImage.reconStatus === "C")){
                    reconCompInd.value = true;
                }

                if(newId){
                    bankBookGrid.value.load();
                    custBookGrid.value.load();
                }

                //
                let tbody = bankBookGrid.value.tableRef.$el.querySelector("tbody");
                let cbTbody = custBookGrid.value.tableRef.$el.querySelector("tbody");
                if(tbody){
                    let dragCache={
                        dragPlaceholder:null,draggedElement:null,initialX:null,initialY:null,isDragging:null
                    };
                    tbody.addEventListener('mouseup', function (e) {
                        if(!isMove.value){
                            bankBookGrid.value.mouseup({
                                target: e.target,
                                shiftKey: e.shiftKey,
                                ctrlKey: e.ctrlKey,
                            });
                        }
                    });
                    tbody.addEventListener('mousedown', function (e) {
                        isMove.value = false;
                        bankBookGrid.value.mousedown({
                            target: e.target,
                            shiftKey: e.shiftKey,
                            ctrlKey: e.ctrlKey,
                        });
                        if(disabled){
                            return;
                        }
                        setTimeout(()=>{
                            let trs = tbody.querySelectorAll("tr.selected-row");
                            if (trs&&trs.length>0) {
                                document.getElementById("app").style.userSelect="none";
                                dragCache.isDragging = true;  
                                dragCache.draggedElement = e.target;
                                dragCache.dragPlaceholder = document.createElement('table');
                                dragCache.dragPlaceholder.style.width=tbody.offsetWidth + "px";
                                dragCache.dragPlaceholder.classList.add('drag-panel');
                                dragCache.dragPlaceholder.classList.add('ep-table');
                                let tb = document.createElement('tbody');
                                for (let j = 0; j < trs.length; j++) {
                                    tb.appendChild(trs[j].cloneNode(1));
                                }
                                dragCache.dragPlaceholder.appendChild(tbody.parentElement.querySelector("colgroup").cloneNode(1));
                                dragCache.dragPlaceholder.appendChild(tb);
                                document.body.appendChild(dragCache.dragPlaceholder);
                                dragCache.initialX = e.clientX - dragCache.draggedElement.getBoundingClientRect().left;
                                dragCache.initialY = e.clientY - dragCache.draggedElement.getBoundingClientRect().top;
                            }
                        },20);
                    });
                    // Event listener for mousemove, also called for touchmove  
                    function handleDrag(event) {
                        isMove.value = true;
                        if (!dragCache.isDragging) return;
                        // Calculate new placeholder position  
                        const x = event.clientX - dragCache.initialX;
                        const y = event.clientY - dragCache.initialY + 20;

                        // Set placeholder position  
                        dragCache.dragPlaceholder.style.left = `${x}px`;
                        dragCache.dragPlaceholder.style.top = `${y}px`;

                        // Prevent default browser drag behavior  
                        event.preventDefault();
                    }

                    // Event listener for mouseup, also called for touchend  
                    function handleDragEnd() {
                        document.getElementById("app").style.userSelect="unset";
                        dragCache.isDragging = false;
                        // Remove the placeholder  
                        dragCache.dragPlaceholder&&document.body.removeChild(dragCache.dragPlaceholder);
                        dragCache.dragPlaceholder = null;
                        // Start SK-COMMON-0146, Tom.Li, 2024/09/06
                        let tabs = document.querySelectorAll("table.drag-panel");
                        if (tabs && tabs.length > 0) {
                            for (let i = 0; i < tabs.length; i++) {
                                document.body.removeChild(tabs[i]);
                            }
                        }
                        // End SK-COMMON-0146, Tom.Li, 2024/09/06
                    }
                    function handleDragTable(event) {
                        if (!dragCache.isDragging) return;
                        let rowIndex = event.target.closest("tr.ep-table__row").querySelector("td.data-grid-selection-index-cell>div>div").innerText;
                        console.log(rowIndex);
                        let dragRecord = custBookGrid.value.tableData[rowIndex-1];
                        let rows = Object.values(bankBookGrid.value.selectedRecord);
                        if(rows&&rows.length>0) {
                            // ElMessageBox.alert(
                            //     'leftSelect('+ rows.length +') ' + 
                            //     'rightRow: ' + JSON.stringify(dragRecord)
                            //     , 'Warning', {
                            //     confirmButtonText: 'OK',
                            //     type: 'warning',
                            // });
                            linkCustBook(rows, dragRecord);
                        }
                    }
                    // Add event listeners  
                    if(!disabled){
                        document.addEventListener('mousemove', handleDrag);
                        document.addEventListener('touchmove', handleDrag);
                        document.addEventListener('mouseup', handleDragEnd);
                        document.addEventListener('touchend', handleDragEnd);
                        cbTbody.addEventListener('mouseup', handleDragTable);
                        cbTbody.addEventListener('touchend', handleDragTable);
                    }
                    
                }
                addCustValid(ruleForm.form, ()=>{
                  ruleForm.form.custbookList = custBookData.value.filter(e => {
                    return e.sourceSys == BkSrcManual;
                  });
                  return false;
                });
                addEndHandle(ruleForm.form, ()=>{
                  delete ruleForm.form.custbookList;
                });
            }
            details.value.initWatch(ruleForm);
        });
    }else{
        details.value.initWatch(ruleForm);
    }
}
const viewOriginalForm = (pendingOid, isDisabled) => {
    formDisabled.value = isDisabled;
  proxy.$axios.get("/cashmgmt/api/v1/bankstmt/reconciliation/details?objectId="+pendingOid).then((body) => {
        if(body.success) {
            ruleForm.form = body.data; 
            ruleForm.form.bankBook = {};
            ruleForm.form.custBook = {};
        }
    });
}
const handleSave = async (searchValid, isOnlyValidate, unPopping) => {
    if(!await checkCustBookRow() || !searchValid){
        return false;
    }
    if (isOnlyValidate) {
        return true;
    }
    if(!await saveMsgBox(unPopping)){
        return false;
    }
    let params = requestParams();
    const msg = await proxy.$axios.patch("/cashmgmt/api/v1/bankstmt/reconciliation", params);
    if(msg.success){
        details.value.writebackId(msg.data);
        editRow(null,null,msg.data);
    }
    return msg.success; 
}
const beforeSubmit = async () => {
    let params = requestParams();
    const msg = await proxy.$axios.post("/cashmgmt/api/v1/bankstmt/reconciliation/submit/pre", params);
    return msg.success
}
const showDetails = (row, isdoubleCheck) => {
    reconCompInd.value = (row.reconStatus==="C");
    ccyDecimalPoints.value = [];
    if(isdoubleCheck||row.recordStatus==='PA'||row.recordStatus==='PA1'||row.recordStatus==='PA2'){
        formDisabled.value = true;
        details.value.showDetails(row, true)
    }else{
        formDisabled.value = false;
        details.value.showDetails(row, false)
    }
    ruleForm.form = {};
    ruleForm.form.bankBook = {},
    ruleForm.form.custBook = {};
    details.value.currentRow={};
    editRow(row,isdoubleCheck,null);
}


defineExpose({
    details,
    editRow,
    showDetails,
    viewOriginalForm,
});
// --------------------------------------------
const requestParams = () => {
    let params = {
        ...ruleForm.form,
        reconBankStmtOid: ruleForm.form.currentOid, 
        bankBookDtls: [],
        custBookDtls: [],
    };
    params.bankBookDtls.push(...bankBookData.value);
    params.custBookDtls.push(...custBookData.value);
    params.custBookDtls.push(...custBookDelRows.value);
    
    return params;
}

interface RuleForm {
    eventName: string
    custAccShortName: string
}
const labelWidthL="170px";
const labelWidthR="190px";

const ruleFormRef = ref<FormInstance>();
const custBookFormRef = ref<FormInstance>();
const ruleForm = reactive({
    form: {
        stmtDate: "",
        custAccShortName:"",
        bankBook:{},
        custBook:{},
        fileList:[],
    }
})

const rules = reactive<FormRules<RuleForm>>({
});
const custBookRules = reactive<FormRules<RuleForm>>({
    'funcType': [
        commonRules.required,
    ],
    'drCrInd': [
        commonRules.required,
    ],
    'fxRate': [
        commonRules.number(8, 6),
    ],
    // 'currencyCode': [
    //     commonRules.required,
    // ],
    'txnAmt': [
        commonRules.required,
        commonRules.number(13, 2),
    ],
    'timeDepositIntRate': [
        commonRules.number(8, 6),
    ],
    'timeDepositIntAmt': [
        commonRules.number(13, 2),
    ],
    'instrDt': [
        commonRules.required,
    ],
    'valueDate': [
        commonRules.required,
    ],
    "fxPair": [
       commonRules.name,
    ],
    "classCode": [
       commonRules.name,
    ],
    "remark": [
       commonRules.name,
    ],
    'txnRef': [
        commonRules.name,
        commonRules.required,
    ],
})

const bankBookGrid = ref();
const custBookGrid = ref();
const DeleteFlag = "D";
const BkSrcFtg = "FTG";
const BkSrcManual = "MANUAL";
const reconCompInd = ref(false);
const custDis = ref(true);
const unlinkDis = ref(true);
const bankBookData = ref([]);
const custBookData = ref([]);
const custBookDelRows = ref([]);
const ccyDecimalPoints = ref([]);
const isChangeCust = ref(false);
const reconRsltData = ref();
const custFields = ref({});
const custDetails = ref({
    fields: custFields,
    addField: (code, desc) => {
        custFields.value[code] = desc;
    }
});
const afterSearchCust = (params, data) => {
    if(data){
        custBookData.value = data;
        custBookDelRows.value = [];
        isChangeCust.value = false;
        addEnterObj4List(data);
    }
}
const afterSearchBank = (params, data) => {
    if(data){
        bankBookData.value = data;

        if(reconRsltData.value){
            //refresh cache data by reconciliation result data 
            for(let idx in reconRsltData.value?.bankBookDtls){
                refreshBankBook(reconRsltData.value.bankBookDtls[idx]);
            }
        }
    }
}
const bankBookRowClick = (row) => {
    ruleForm.form.bankBook = {};
    Object.assign(ruleForm.form.bankBook, row);
}
const custBookRowClick = async(row) => {
    if(row === custBookGrid.value.currentRow){
        //same row
        return false;
    }
    if(!await checkCustBookRow()){
        setCustCurrentRow();
        return false;
    }
    custBookRowChange(row);
    return true;
}
const custBookRowChange = (row) => {
    //change current row
    ruleForm.form.custBook = {};
    Object.assign(ruleForm.form.custBook, row);
    custDis.value = true;
    if (row?.sourceSys === BkSrcManual) {
        custDis.value = formDisabled.value;
    }
    setUnlinkDisVal(row);
    //highlight-current-row
    custBookGrid.value.currentRow = row;
    custBookGrid.value.tableRef.setCurrentRow(row);
    setCustCurrentRow();
}

const setUnlinkDisVal = (row) => {
    unlinkDis.value = true;
    if(!row?.linkedSeq){
        return;
    }
    if(row?.reconChange === true || row?.recordStatus != "A" || (row?.sourceSys === BkSrcManual || row?.sourceSys === BkSrcFtg)){
        unlinkDis.value = formDisabled.value;
    }
}

const setCustCurrentRow = () => {
    //Execute after table refresh
    nextTick(() => {
        let index = getGridRowIndex(custBookData.value, custBookGrid.value.currentRow);
        custBookGrid.value.setCurrentRow(index);

        resetCustFormDate();
        custDetails.value.currentRow = ref();
        if(ruleForm.form.custBook.sourceSys === BkSrcManual){
            custDetails.value.currentRow = ruleForm.form.custBook;
        }
    });
}

const addCustBook = async() => {
    if(!await checkCustBookRow()){
        return;
    }
    let row = newCustRow();
    //show first row
    //custBookData.value.push(row);
    custBookData.value.unshift(row); 
    custBookRowChange(row);
}

const deleteCustBook = () => {
    let row = ruleForm.form.custBook;
    let index = getGridRowIndex(custBookData.value, row);
    if (index !== -1) {
        let del = custBookData.value.splice(index,1);
        del[0].isDelete = true;
        ruleForm.form.deleteCustBookList = (ruleForm.form.deleteCustBookList || []).concat(del);

        let nextRow = getGridNextRow(custBookData.value, index);
        custBookRowChange(nextRow);
        custBookFormRef.value?.clearValidate();

        row.mkckAction = DeleteFlag;
        custBookDelRows.value.push(row);
        unlinkCustBook(row);
    }
}
const checkCustBookRow = async() => {
    if(custDis.value || !ruleForm.form.custBook?.oid){
        return true;
    }
    let result = await custBookFormRef.value.validate((valid, fields) => {
        if (!valid) {
            showValidateMsg(custDetails, fields);
        }
    });
    
    if(!await checkCustTxnAmtDecPoint()){
        result = false; 
    }

    let checkSrch = await validSearchInputValue();
    if(!checkSrch){
        result = false;
    }

    return result;
}
const resetCustFormDate = () => {
    let inputs = custBookFormRef.value?.$el.querySelectorAll("div.date-item-input input");
    for (let j = 0; j < inputs.length; j++) {
        if(inputs[j].alt == proxy.$t("csscl.cash.statement.instructionDate")){
            inputs[j].value = ruleForm.form.custBook.instrDt || null;
        }else if(inputs[j].alt == proxy.$t("csscl.cash.statement.valueDate")){
            inputs[j].value = ruleForm.form.custBook.valueDate || null;
        }else if(inputs[j].alt == proxy.$t("csscl.cash.statement.depositDate")){
            inputs[j].value = ruleForm.form.custBook.depositDate || null;
        }else if(inputs[j].alt == proxy.$t("csscl.cash.statement.maturityDate")){
            inputs[j].value = ruleForm.form.custBook.maturityDate || null;
        }
    }
}

watch(ruleForm,(newForm, oldForm)=>{
    let custForm = newForm.form.custBook;
    refreshCustBook(custForm);
})

const refreshCustBook = (custRow) => {
    let index = getGridRowIndex(custBookData.value, custRow);
    if (index !== -1) {
        let row = custBookData.value[index];
        for(let key in custRow){
            if(row[key] != custRow[key]){
                isChangeCust.value = true;
            }
            row[key] = custRow[key];
        }
    }
}
const refreshBankBook = (bankRow) => {
    let index = getGridRowIndex(bankBookData.value, bankRow);
    if (index !== -1) {
        let row = bankBookData.value[index];
        row.reconFlag = bankRow.reconFlag;
    }
}

const checkCustTxnAmtDecPoint = async() => {
    let row = ruleForm.form.custBook;
    let txnAmt = row?.txnAmt;
    if(!txnAmt){
        return true;
    }
    let txnAmts = String(txnAmt).split('.');
    if(txnAmts.length == 2){
        //remove the 0 at the end
        let decPart = String(txnAmts[1]).replace(/\.?0*$/, "");
        if(decPart == ""){
            return true;
        }
        let ccyDecPoint = await getCcyDecimalPoint(row.currencyCode);
        if(decPart.length > ccyDecPoint){
            let txnAmtRule = {
                field: "txnAmt", 
                fieldValue: ruleForm.form.custBook.txnAmt, 
                message: proxy.$t("message.common.decpoint.invalid", [proxy.$t('csscl.cash.statement.amount')]),
                showLabelInd: "N",
            };
            showValidateMsg(details, {txnAmt:[txnAmtRule]});
            return false;
        }
    }
    return true;
}
const changeCustCcyCode = async() => {
    if(await checkCustTxnAmtDecPoint()){
        matchCustBook();
    }
}
const changeCustTxnAmt = async() => {
    if(await checkCustTxnAmtDecPoint()){
        matchCustBook();
    }
}

const linkCustBook = async(bankRows, custRow) => {
    if(!bankRows || !custRow || reconCompInd.value) return true;
    
    let params = {
        reconBankStmtOid: ruleForm.form.currentOid, 
        pendingOid: ruleForm.form.pendingOid,
        cashAccNo: ruleForm.form.cashAccNo,
        custAccNo: ruleForm.form.custAccNo,
        stmtDate: ruleForm.form.stmtDate,
        reconStatus: ruleForm.form.reconStatus,
        custBkOpenBal: ruleForm.form.custBkOpenBal,
        linkBankBookDtls: bankRows,
        linkCustBookDtl: custRow,
        bankBookDtls: [],
        custBookDtls: [],
    };
    params.bankBookDtls.push(...bankBookData.value);
    params.custBookDtls.push(...custBookDelRows.value);
    for(let idx in custBookData?.value){
        if(isSameRecord(custRow, custBookData.value[idx])){
            //handle current form data not synchronized to table
            params.custBookDtls.push(custRow);
        }else{
            params.custBookDtls.push(custBookData.value[idx]);
        }
    }

    let isSuccess = false;
    await proxy.$axios.post("/cashmgmt/api/v1/bankstmt/reconciliation/link", params).then((body) => {
        if(body.success) {
            linkCustBookRslt(custRow, body.data);
            isChangeCust.value = true;
            isSuccess = true;
        }
    });
    return isSuccess;
}

const unlinkCustBook = (custBook) => {
    let params = {
        reconBankStmtOid: ruleForm.form.currentOid, 
        pendingOid: ruleForm.form.pendingOid,
        cashAccNo: ruleForm.form.cashAccNo,
        custAccNo: ruleForm.form.custAccNo,
        stmtDate: ruleForm.form.stmtDate,
        reconStatus: ruleForm.form.reconStatus,
        custBkOpenBal: ruleForm.form.custBkOpenBal,
        linkCustBookDtl: custBook,
        bankBookDtls: [],
        custBookDtls: [],
    };
    params.bankBookDtls.push(...bankBookData.value);
    params.custBookDtls.push(...custBookData.value);
    params.custBookDtls.push(...custBookDelRows.value);
    
    proxy.$axios.post("/cashmgmt/api/v1/bankstmt/reconciliation/unlink", params).then((body) => {
        if(body.success) {
            linkCustBookRslt(custBook, body.data);
            isChangeCust.value = true;
        }
    });
}

const matchCustBook = () => {
    let custRow = ruleForm.form.custBook;
    if(reconCompInd.value || !custRow || !custRow.linkedSeq || 
       !custRow.currencyCode || !custRow.txnAmt || !custRow.drCrInd || !custRow.txnRef || !custRow.valueDate){
        return;
    } 
    
    let params = {
        reconBankStmtOid: ruleForm.form.currentOid, 
        pendingOid: ruleForm.form.pendingOid,
        cashAccNo: ruleForm.form.cashAccNo,
        custAccNo: ruleForm.form.custAccNo,
        stmtDate: ruleForm.form.stmtDate,
        reconStatus: ruleForm.form.reconStatus,
        custBkOpenBal: ruleForm.form.custBkOpenBal,
        bankBookDtls: [],
        custBookDtls: [],
    };
    params.bankBookDtls.push(...bankBookData.value);
    params.custBookDtls.push(...custBookData.value);
    params.custBookDtls.push(...custBookDelRows.value);

    proxy.$axios.post("/cashmgmt/api/v1/bankstmt/reconciliation/match", params).then((body) => {
        if(body.success) {
            linkCustBookRslt(custRow, body.data);
        }
    });
}

const linkCustBookRslt = (custBook, data) => {
    reconRsltData.value = data;
    ruleForm.form.custBkCloseBal = data.custBkCloseBal;
    ruleForm.form.custBkOdAmt = data.custBkOdAmt;

    for (let idx in data.bankBookDtls) {
        refreshBankBook(data.bankBookDtls[idx]);
    }
    for (let idx in data.custBookDtls) {
        let row = data.custBookDtls[idx];
        if (isSameRecord(custBook, row)) {
            custBook.linkedSeq = row.linkedSeq;
            custBook.reconFlag = row.reconFlag;
        }
        if (isSameRecord(ruleForm.form.custBook, row)) {
            ruleForm.form.custBook.linkedSeq = row.linkedSeq;
            ruleForm.form.custBook.reconFlag = row.reconFlag;
            ruleForm.form.custBook.reconChange = row.reconChange;

            setUnlinkDisVal(row);
        }
        
        let index = getGridRowIndex(custBookData.value, row);
        if (index !== -1) {
            let gridRow = custBookData.value[index];
            gridRow.linkedSeq = row.linkedSeq;
            gridRow.reconFlag = row.reconFlag;
            gridRow.reconChange = row.reconChange;
        }
    }
}

const handleCopy = async() => {
    let bankRow = ruleForm.form.bankBook;
    let custRow = {...ruleForm.form.custBook};
    if(bankRow){
        custRow.txnRef = bankRow.txnRef || custRow.txnRef;
        custRow.funcType = bankRow.funcType || custRow.funcType;
        custRow.fxType = bankRow.fxType || custRow.fxType;
        custRow.fxPair = bankRow.fxPair || custRow.fxPair;
        custRow.fxRate = bankRow.fxRate || custRow.fxRate;
        custRow.instrDt = bankRow.instrDt || custRow.instrDt;
        custRow.valueDate = bankRow.valueDate || custRow.valueDate;
        custRow.drCrInd = bankRow.drCrInd || custRow.drCrInd;
        custRow.currencyCode = bankRow.currencyCode || custRow.currencyCode;
        custRow.txnAmt = bankRow.txnAmt || custRow.txnAmt;
        custRow.classCode = bankRow.classCode || custRow.classCode;
        custRow.depositDate = bankRow.depositDate || custRow.depositDate;
        custRow.maturityDate = bankRow.maturityDate || custRow.maturityDate;
        custRow.timeDepositIntRate = bankRow.timeDepositIntRate || custRow.timeDepositIntRate;
        custRow.timeDepositIntAmt = bankRow.timeDepositIntAmt || custRow.timeDepositIntAmt;
        custRow.remark = bankRow.remark || custRow.remark;

        
        custRow.instrDt = ruleForm.form.stmtDate;
        
        
        let isSuccess = await linkCustBook([bankRow], custRow);
        if(isSuccess){
            ruleForm.form.custBook = custRow;
        }
    }
}

const newCustRow = () => {
    let row = {
        oid: Math.trunc(randomHashCode()),
        reconDate: ruleForm.form.stmtDate,
        cashAccNo: ruleForm.form.cashAccNo,
        currencyCode: ruleForm.form.currencyCode,
        instrDt: ruleForm.form.stmtDate,
        sourceSys: BkSrcManual,
        reconFlag: "N",
    };
    return row;
}
const addCustUpldRow = (datas) => {
    if(!datas){
        return;
    }
    let custRows = [];
    for(let idx in datas){
        let data = datas[idx];
        let custRow = newCustRow();
        custRow.cashAccNo = data.cashAccNo;
        custRow.txnRef = data.txnRef;
        custRow.funcType = "CASH";//data.funcType;
        custRow.fxType = data.fxType;
        custRow.fxPair = data.fxPair;
        custRow.fxRate = data.fxRate;
        custRow.instrDt = data.tradeDate;
        custRow.valueDate = data.valueDate;
        custRow.currencyCode = data.ccy;
        custRow.txnAmt = Math.abs(data.amount);
        custRow.drCrInd = data.amount < 0?"D":"C";
        custRow.classCode = data.classCode;
        custRow.depositDate = data.depositDate;
        custRow.maturityDate = data.maturityDate;
        custRow.timeDepositIntRate = data.timeDepositIntRate;
        custRow.timeDepositIntAmt = data.timeDepositIntAmt;
        custRow.remark = data.remark;

        custRows.unshift(custRow);
    }
    for(let idx in custRows){
        custBookData.value.unshift(custRows[idx]); 
    }
    if(!custBookGrid.value.currentRow?.oid){
        custBookRowChange(custBookData.value[0]);
    }
}

const uploadFile = async (file) => {
    ruleForm.form.fileList = [];
    const formData = new FormData();
    formData.append('channel', 'MANUAL');
    formData.append('fileType', 'RECONUPLD');
    formData.append('file', file.raw);
    
    const msg = await proxy.$axios.post("/datamgmt/api/v1/document/in/upload", formData);
    if (msg.success) {
        ruleForm.form.fileList = [];
        if (msg.data.currentOid && msg.data.processStatus != "F") {
            ruleForm.form.upldFilePath = msg.data.filePath;
            ruleForm.form.upldFileName = msg.data.fileName;

            let params = "?cashAccNo="+ruleForm.form.cashAccNo;
            if(ruleForm.form.custAccNo){
                params = params+"&custAccNo="+ruleForm.form.custAccNo;
            }
            proxy.$axios.get("/cashmgmt/api/v1/bankstmt/reconciliation/upload/list" + params).then((body) => {
                if (body.success) {
                    addCustUpldRow(body.data);
                }
            });
        } else {
            let errorMsg = proxy.$t(msg.data.errorLog)?.replaceAll("\\n", "<br/>");
            ElMessageBox.alert(errorMsg, 'Warning', {
                confirmButtonText: 'OK',
                type: 'warning',
                dangerouslyUseHTMLString: true,
            });
            return;
        }
    }
    return msg.success;
}
const previewReconFile = () => {
    var params = {
        filePath: ruleForm.form.upldFilePath,
        fileName: ruleForm.form.upldFileName,
    }
    downloadFile("/datamgmt/api/v1/document/in/download", params);
}
const downloadTemplate = () => {
    downloadFile("/datamgmt/api/v1/document/in/template", {});
}

const getGridRowIndex = (datas, row) => {
    return datas.findIndex((item) =>{
        return isSameRecord(item, row);
    });
}
const isSameRecord = (item, row) => {
    if(!item || !row){
        return false;
    }
    if (item.currentOid) {
        return String(item.currentOid) === String(row.currentOid);
    } else if (item.oid) {
        return String(item.oid) === String(row.oid);
    } else {
        return false;
    }
}

const getGridNextRow = (rows, index) => {
    let nextRow = {};
    let len = rows?.length;
    if (len > 0) {
        if (index < len) {
            nextRow = rows[index];
        } else {
            nextRow = rows[index - 1];
        }
    }
    return nextRow;
}

const isDiffOpenBal = () => {
    let row = ruleForm.form;
    if(row?.bankBkOpenBal || row?.custBkOpenBal){
        if(row?.bankBkOpenBal !== row?.custBkOpenBal){
            return true;
        }
    }
    return false;
}
const isDiffCloseBal = () => {
    let row = ruleForm.form;
    if(row?.bankBkCloseBal || row?.custBkCloseBal){
        if(row?.bankBkCloseBal !== row?.custBkCloseBal){
            return true;
        }
    }
    return false;
}

const getCcyDecimalPoint = async(currencyCode) =>{
    let decimalPoint = 2;
    if(currencyCode){
        let row = ccyDecimalPoints.value.find((item) => { return item.currencyCode === currencyCode;});
        if(row){
            return row.decimalPoint;
        }

        await CurrencyApi.getCcyDecimalPoint(ruleForm.form.opCtryRegionCode, currencyCode).then((body) => {
            if (body.success && body.data >= 0) {
                decimalPoint = body.data;
                ccyDecimalPoints.value.push({currencyCode: currencyCode,decimalPoint: decimalPoint,});
            }
        });
    }
    return decimalPoint;
}

const beforeChangePage = async() => {
    if(isChangeCust.value == false || reconCompInd.value || formDisabled.value){
        return null;
    }
    
    let isOk = false;
    await ElMessageBox.confirm(
        proxy.$t('message.system.change.page.save'),
        'Warning',
        {
            confirmButtonText: 'OK',
            cancelButtonText: 'Cancel',
            type: 'warning',
        }
    ).then(() => {
        isOk = true;
    }).catch(() => {
    });
    
    if(isOk){
        isOk = await handleSave(true);
    }
    return { isChangePage: isOk, lsLoadData: false, };
}

const gridTxnAmtFmt = (row, txnAmt) =>{
    // Start SK-COMMON-0146, Tom.Li, 2024/09/06
    if(row.drCrInd === "D"){
        return "(" + thousFormat(Math.abs(txnAmt), row.decimalPoint) + ")";
    }
    return thousFormat(txnAmt, row.decimalPoint);
    // End SK-COMMON-0146, Tom.Li, 2024/09/06
}
const custBkOpenBalSty = () => {
    if(isDiffOpenBal()){
        return "input-number red-input";
    }
    return "input-number";
}
const custBkCloseBalSty = () => {
    if(isDiffCloseBal()){
        return "input-number red-input";
    }
    return "input-number";
}
const cellStyleBank = (data) => {
    let style;
    if(data.row.reconFlag){
        if (data.row.reconFlag == "Y") {
            style = {
                color: "green"
            };
        } else {
            style = {
                color: "red"
            };
        }
    }
    return style;
}
const cellStyleCust = (data) => {
    let style;
    if(data.row.sourceSys === BkSrcManual && !reconCompInd.value){
        style = {
            color: "blue"
        };
    }else if(data.row.reconFlag){
        if (data.row.reconFlag == "Y") {
            style = {
                color: "green"
            };
        } else {
            style = {
                color: "red"
            };
        }
    }
    if(!data.row.reconFlag){
        data.row.reconFlag = "--";
    }
    return style;
}

</script>

<style>
.red-input .ep-input__inner {
    -webkit-text-fill-color: red !important;
}
.dragging-hover{
    background-color: blue !important;
}
.drag-panel {
    table-layout: auto;
    position: fixed;
    background-color: white;
    z-index: 9999;
}
.reconGrid .ep-table .cell {
    padding: 0 6px !important;
}
.reconGrid .ep-pagination .btn-prev {
    margin-left: 15% !important;
}
</style>