<template>
  <BasePanel :searchParams="searchParams" :paramListData="paramListData" url="/datamgmt/api/v1/account/list" :params="{ modeEdit: 'Y' }"
    :showDetails="showDetails"  :deleteRow="deleteRow" :editRow="editRow" ref="tableRef" :sortProp="{}" >
    <template v-slot:searchPanel="slotProps">
      <FormRow>
        <ElFormItemProxy :label="$t('csscl.acctCode.clientMasterOid')" prop="clientCode">
            <GeneralSearchInput v-model="slotProps.form.clientCode"
                style="width:200px"
                maxlength="11"
                searchType="clientCode"
                showDesc="false"
                :change="(val)=>{
                  slotProps.form.clientMasterOid='';
                  slotProps.form.clientAccountOid = '';
                  slotProps.form.tradingAccountCode = '';
                }"
                :dbClick="(row)=>{ 
                  slotProps.form.clientMasterOid=row.var1;
                  slotProps.form.clientAccountOid = '';
                  slotProps.form.tradingAccountCode = '';
                }"
                />

        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.acctCode.clientShortName')" prop="clientShortName">
          <el-input v-model="slotProps.form.clientShortName" maxlength="50" tyle="width:300px" input-style="text-transform:none" />
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('common.title.recordStatus')" prop="multipleRecordStatus">
          <MultipleSelect v-model="slotProps.form.multipleRecordStatus" v-model:desc="paramListData.multipleRecordStatus" />
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy :label="$t('csscl.acctCode.custodyAcctNumber')" prop="tradingAccountCode">
          
          <GeneralSearchInput v-model="slotProps.form.tradingAccountCode"
                searchType="custodyAcct"
                showDesc="false"
                :params="{var1:slotProps.form.clientMasterOid}"
                :change="(val)=>{
                  slotProps.form.clientAccountOid='';
                }"
                :dbClick="(row)=>{
                  slotProps.form.clientAccountOid=row.var2;
                }"
                />
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.acctCode.accountShortName')" prop="accountShortName">
          <el-input v-model="slotProps.form.accountShortName" maxlength="50" style="width:300px" input-style="text-transform:none" /></ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.acctCode.accountStatus')" prop="accountStatus" >
          <Select v-model="slotProps.form.accountStatus" type="ACCOUNT_STATUS" v-model:desc="paramListData.accountStatus"  />
          </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy :label="$t('csscl.acctCode.bankAccountNo')" prop="bankAccountNo">
          <el-input v-model="slotProps.form.bankAccountNo" maxlength="18" style="width:300px;" input-style="text-transform:none" /></ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.acctCode.ctryRegionCode')" prop="opCtryRegionCode">
          <CtryRegionSearchInput v-model="slotProps.form.opCtryRegionCode" 
            showDesc="false"
            maxlength="2" />
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.acctCode.custodyLevel')"  prop="custodyLevel">
          <Select v-model="slotProps.form.custodyLevel" type="CUSTODY_LEVEL" style="width: 180px;" :change="() => slotProps.form.custodyMarket = ''" />
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy :label="$t('csscl.acctCode.clientGroupCode')" prop="clientGroupCode">  
          <CommonSearchInput v-model="slotProps.form.clientGroupCode"
            style="width:300px" 
            codeTitle="csscl.acctCode.clientGroupCode"
            commType="CLIENT_GROUP"
            maxlength="50"
            showDesc="false" />
        </ElFormItemProxy>
        <ElFormItemProxy  :label="$t('csscl.acctCode.fundMgrCode')" prop="fundMgrCode">
          <CommonSearchInput v-model="slotProps.form.fundMgrCode" 
            style="width:300px"
            codeTitle="csscl.acctCode.fundMgrCode"
            commType="FUND_MANAGER"
            maxlength="50"
            showDesc="false" />
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.acctCode.custodyMarket')"  prop="custodyMarket">
          <GeneralSearchInput v-model="slotProps.form.custodyMarket"
                              style="width: 210px"
                              showDesc="false"
                              maxlength="11"
                              searchType="custodyMarket"
                              :disabled="slotProps.form.custodyLevel === 'GC'" />
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy :label="$t('csscl.acctCode.clientFundId')" prop="clientFundId">
          <GeneralSearchInput v-model="slotProps.form.clientFundId" 
            codeTitle="csscl.acctCode.clientFundId"
            searchType="clientFundID"
            maxlength="50"
            showDesc="false" />
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.acctCode.servicePlan')" prop="servicePlan">
          <CommonSearchInput v-model="slotProps.form.servicePlan" 
            codeTitle="csscl.acctCode.servicePlan"
            commType="SERVICE_PLAN_CODE"
            maxlength="10"
            showDesc="false" />
        </ElFormItemProxy>
        <ElFormItemProxy></ElFormItemProxy>
      </FormRow>
    </template>

    <template v-slot:tableColumn>
      <el-table-column sortable="custom" prop="clientCode" :label="$t('csscl.acctCode.clientMasterOid')"  />
      <el-table-column sortable="custom" prop="clientShortName" :label="$t('csscl.acctCode.clientShortName')"  />
      <el-table-column sortable="custom" prop="tradingAccountCode" :label="$t('csscl.acctCode.clientAccountOid')"  />
      <el-table-column sortable="custom" prop="accountShortName" :label="$t('csscl.acctCode.accountShortName')" />
      <el-table-column sortable="custom" prop="opCtryRegionCode" :label="$t('csscl.acctCode.ctryRegionCode')"  />
      <el-table-column sortable="custom" prop="accountStatus" :label="$t('csscl.acctCode.accountStatus')"  >
        <template #default="scope">
          {{ getCommonDesc('ACCOUNT_STATUS', scope.row.accountStatus) }}
        </template>
      </el-table-column>
      <el-table-column sortable="custom" prop="recordStatus" :label="$t('common.title.recordStatus')"  >
        <template #default="scope">
          {{ getRecordStatusDesc(scope.row) }}  
        </template>
      </el-table-column>
    </template>
  </BasePanel>
  <Details ref="detailsRef" :reload="reload" />
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { Search } from '@element-plus/icons-vue'
import BasePanel from '~/pages/base/index.vue'
import Details from './Details.vue';
import  { getCommonDesc, getRecordStatusDesc } from '~/util/Function.js';
import CtryRegionSearchInput from '~/pages/base/CtryRegionSearchInput.vue';
import CommonSearchInput from '~/pages/base/CommonSearchInput.vue';


const { proxy } = getCurrentInstance();
const tableRef = ref();
const paramListData = {};
const searchParams = ref({
  //顺序和上面绑定参数一致
  clientCode:"",
  clientShortName:"",
  multipleRecordStatus:[],
  tradingAccountCode:"",
  accountShortName:"",
  accountStatus:"",
  bankAccountNo:"",
  opCtryRegionCode:"",
  custodyLevel:"",
  clientGroupCode:"",
  fundMgrCode:"",
  custodyMarket:"",
  clientFundId:"",
  servicePlan:""
});
const ruleFormRef = ref<FormInstance>()
const detailsRef = ref();

const showDetails = (row, disabled) => {
  detailsRef.value.showDetails(row, disabled);
}
const deleteRow = (row) => {
  console.log("Delete ...");
}
const editRow = (row) => {
  detailsRef.value.editRow(row);
}

const reload = () => {
  tableRef.value.load();
}


//-------------------------------
</script>

<style></style>