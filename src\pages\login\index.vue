<template>
  <!-- Start SK-COMMON-0118, Tom.Li, 2024/08/28 -->
  <div style="position: fixed;width: 100%;height: 100%;background-color: #909399;top: 0px;left: 0px;align-content: center;display:flex;z-index: 999;">
  <!-- End SK-COMMON-0118, Tom.Li, 2024/08/28 -->
    <el-form :validateOnRuleChange="false" ref="ruleFormRef" style="width: 300px; margin: auto;background-color: white;padding: 10px;"
      :model="ruleForm" status-icon :rules="rules" label-width="auto" class="login-form">
      <h1>Login</h1>
      <ElFormItemProxy label="User Id" prop="username"  class="login-form-item" >
        <InputText v-model="ruleForm.username" uppercase autocomplete="off" />
      </ElFormItemProxy>
      <ElFormItemProxy label="Password" prop="password" class="login-form-item" >
        <el-input v-model="ruleForm.password" type="password" autocomplete="off" />
      </ElFormItemProxy>
      <ElFormItemProxy>
        <el-button type="primary" @click="submitForm(ruleFormRef)">Submit</el-button>
        <el-button @click="resetForm(ruleFormRef)">Reset</el-button>
      </ElFormItemProxy>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, getCurrentInstance } from 'vue';
import {ElMessageBox, FormInstance, FormRules} from 'element-plus';
import { useCookies } from "vue3-cookies";
// Start SK-COMMON-0083, Tom.Li, 2024/08/19
// import { getBaseUrl } from '~/util/Function';
import {getBaseUrl, afterLoginOperat, clearCookies} from '~/util/Function';
// End SK-COMMON-0083, Tom.Li, 2024/08/19

const ruleFormRef = ref<FormInstance>()
const { proxy } = getCurrentInstance()
const { cookies } = useCookies();

const validatePass = (rule: any, value: any, callback: any) => {
  if (value === '') {
    callback(new Error('Please input the password'))
  } else {
    callback()
  }
}

const checkUsername = (rule: any, value: any, callback: any) => {
  if (value === '') {
    callback(new Error('Please input the User Name'))
  } else {
    callback()
  }
}

const ruleForm = reactive({
  password: '',
  username: '',
})

const rules = reactive<FormRules<typeof ruleForm>>({
  password: [{ validator: validatePass, trigger: 'blur' }],
  username: [{ validator: checkUsername, trigger: 'blur' }],
})

const submitForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.validate((valid) => {
    if (valid) {
      loginBeforeCheck().then((data) => {
        if(data){
          const alertMsg = data.alertMessage[0];
          ElMessageBox.confirm(alertMsg,'Warning', {
            confirmButtonText: 'OK',
            cancelButtonText: 'Cancel',
            type: 'warning',
          }).then(() => {
            login()
          }).catch(() => {});
        }
      }).catch(() => {
        login()
      });
    } else {
      console.log('error submit!')
      return false
    }
  })
}
const loginBeforeCheck = () => new Promise((resolve, reject) => {
  proxy.$axios.post('/auth/api/v1/user/login/b4check', {
    "userId": ruleForm.username,
  }).then((body) => {
    body.success && body.data ? resolve(body) : reject(false);
  }).catch(() => reject(false));
});
const login = () =>{
  proxy.$axios.post('/auth/api/v1/user/login', {
    "userId": ruleForm.username,
    "password": ruleForm.password,
    // Start SK-COMMON-0058, Tom.Li, 2024/08/14
    // }).then((body) => {
  }).then(async (body) => {
    // End SK-COMMON-0058, Tom.Li, 2024/08/14
    if (body.success) {
      // Start SK-COMMON-0083, Tom.Li, 2024/08/19
      afterLoginOperat(body);
      // End SK-COMMON-0083, Tom.Li, 2024/08/19
      localStorage.setItem('isDev', 'Y');
      // Start SK-COMMON-0059, Tom.Li, 2024/08/14
      localStorage.setItem('apiDecode',body.data);
      // End SK-COMMON-0059, Tom.Li, 2024/08/14
      cookies.set('loginTime', proxy.$moment().format('YYYY/MM/DD HH:mm:ss'));
      cookies.set('username', ruleForm.username);
      //This interface is designed to initialize default parameters after logging in
      // Start SK-COMMON-0058, Tom.Li, 2024/08/14
      // proxy.$axios.patch('/auth/api/v1/user/cache');
      await proxy.$axios.patch('/auth/api/v1/user/cache');
      // End SK-COMMON-0058, Tom.Li, 2024/08/14
      location.href = getBaseUrl();
      resolve(true);
    }else {
      reject(false);
    }
  }).catch((err)=>{reject(err)});
}
const resetForm = (formEl: FormInstance | undefined) => {
  console.log(0);
  if (!formEl) return
  formEl.resetFields()
}
</script>
<style scoped>
.login-form-item {
  margin-bottom: 20px !important;
}
</style>