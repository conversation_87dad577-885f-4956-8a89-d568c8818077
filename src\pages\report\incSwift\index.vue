<template>
  <BasePanel :searchParams="searchParams" :paramListData='paramListData' url="/datamgmt/api/v1/swiftmsg/list"
    :params="{ modeEdit: 'Y' }" :showDetails="showDetails" :editRow="editRow" :rules="rules"
    :isMultiple="true" :hideEditBtn="hideEditBtn"
    :beforeSearch="beforeSearch" ref="tableRef" :handleSelectionChange="handleSelectionChange">
    <template v-slot:searchPanel="slotProps">

      <FormRow>
        <ElFormItemProxy>
          <ElFormItemProxy :label="$t('csscl.incSwift.receivedDateFrom')" prop="receiveDtFrom" >
            <DateItem v-model="slotProps.form.receiveDtFrom"
                      :title="$t('message.earlier.equal.curdate', [$t('csscl.incSwift.receivedDateFrom')] ) + '\r' +
                        $t('message.earlier.equal.dateto', [$t('csscl.incSwift.receivedDateFrom'), $t('csscl.incSwift.receivedDateTo')] ) + '\r' +
                        $t('message.date.range.error', [7] ) " />
          </ElFormItemProxy>
          <ElFormItemProxy label-width="45" :label="$t('common.title.date.to')" :hideLabel="$t('csscl.incSwift.receivedDateTo')" prop="receiveDtTo" >
            <DateItem v-model="slotProps.form.receiveDtTo"
                      :title="$t('message.earlier.equal.curdate', [$t('csscl.incSwift.receivedDateTo')] ) + '\r' +
                      $t('message.date.range.error', [7] ) " />
          </ElFormItemProxy>
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.incSwift.messageType')" prop="messageType">
          <el-input v-model="slotProps.form.messageType" maxlength="30" style="width:280px" />
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.incSwift.pendingQueueStatus')" prop="status">
          <Select v-model="slotProps.form.status" type="PEND_QUE_STS" v-model:desc="paramListData.status" />
        </ElFormItemProxy>
      </FormRow>

      <FormRow>
        <ElFormItemProxy :label="$t('csscl.incSwift.senderBIC')" prop="senderBicCode">
          <el-input v-model="slotProps.form.senderBicCode" maxlength="20" />
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.incSwift.messageID')" prop="messageId">
          <el-input v-model="slotProps.form.messageId" maxlength="30" style="width:280px" />
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.incSwift.recordDate')" prop="recordDt">
          <DateItem v-model="slotProps.form.recordDt" type="date" />
        </ElFormItemProxy>
      </FormRow>

      <FormRow>
        <ElFormItemProxy :label="$t('csscl.incSwift.placeOfSettlement')" prop="placeOfSettl">
          <el-input v-model="slotProps.form.placeOfSettl" maxlength="11" />
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.incSwift.swiftEventCode')" prop="swiftEventCode">
          <Select v-model="slotProps.form.swiftEventCode" type="x" style="width:160px" v-model:desc="paramListData.swiftEventCode" />
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.incSwift.settlementDate')" prop="instSettlDt">
          <DateItem v-model="slotProps.form.instSettlDt" type="date"/>
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy :label="$t('csscl.incSwift.settlementType')" prop="settlType">
          <Select v-model="slotProps.form.settlType" type="x" style="width:130px" v-model:desc="paramListData.settlType" />
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.incSwift.accountNo')" prop="accountNo">
          <InputText v-model="slotProps.form.accountNo" maxlength="20" />
        </ElFormItemProxy>
        <ElFormItemProxy></ElFormItemProxy>
      </FormRow>
    </template>

    <template v-slot:tableColumn>
      <!-- Start R2411A-38517, Tom.Li, 2024/08/20 -->
      <el-table-column sortable="custom" prop="receiveDt" :label="$t('csscl.incSwift.receivedDateAndTime')" width="190" />
      <el-table-column sortable="custom" prop="messageId" :label="$t('csscl.incSwift.messageID')" width="160" />
      <el-table-column sortable="custom" prop="messageType" :label="$t('csscl.incSwift.messageType')" width="120" />
      <el-table-column sortable="custom" prop="accountNo" :label="$t('csscl.incSwift.accountNo')" width="120" />
      <el-table-column sortable="custom" prop="pageCode" :label="$t('csscl.incSwift.pageCode')" width="120" />
      <el-table-column sortable="custom" prop="senderBicCode" :label="$t('csscl.incSwift.senderBIC')" width="120" />
      <el-table-column sortable="custom" prop="swiftEventCode" :label="$t('csscl.incSwift.swiftEventCode')" width="180" />
      <el-table-column sortable="custom" prop="corpRef" :label="$t('csscl.incSwift.corpRef')" width="120" />
      <el-table-column sortable="custom" prop="refNo" :label="$t('csscl.incSwift.refNo')" width="160" />
      <el-table-column sortable="custom" prop="funcOfMsg" :label="$t('csscl.incSwift.funcitonOfMessage')" width="180" />
      <el-table-column sortable="custom" prop="instSettlDt" :label="$t('csscl.incSwift.instructionSettlementDate')" width="130" />
      <el-table-column sortable="custom" prop="placeOfSettl" :label="$t('csscl.incSwift.placeOfSettlement')" width="120" >
      </el-table-column>
      <el-table-column sortable="custom" prop="status" :label="$t('csscl.incSwift.pendingQueueStatus')" width="120">
        <template #default="scope">
          {{ getCommonDesc('PEND_QUE_STS', scope.row.status) }}
        </template>
      </el-table-column>
      <el-table-column sortable="custom" prop="recordStatus" :label="$t('common.title.recordStatus')"  >
        <template #default="scope">
          {{ getRecordStatusDesc(scope.row) }}
        </template>
      </el-table-column>
      <el-table-column sortable="custom" prop="recordDt" :label="$t('csscl.incSwift.recordDate')" width="120" />
      <el-table-column sortable="custom" prop="settlDt" :label="$t('csscl.incSwift.settlementDate')" width="130" />
      <el-table-column sortable="custom" prop="settlType" :label="$t('csscl.incSwift.settlementType')" width="150" />
      <el-table-column sortable="custom" prop="errorLog" :label="$t('csscl.incSwift.failReason')" width="200" />
      <el-table-column  prop="csv" :label="$t('csscl.incSwift.csv')" width="70" >
        <template #default="scope">
          <el-icon-folder-opened style="width:20px;height:20px;color:orange" @click="csvClick(scope.row)" />
        </template>
      </el-table-column>
      <!--  Start fix:BAU-70 2025-04-09   -->
      <el-table-column  prop="txt" :label="$t('csscl.incSwift.txt')" width="70" >
        <template #default="scope">
          <el-icon-folder-opened style="width:20px;height:20px;color:orange" @click="txtClick(scope.row)" />
        </template>
      </el-table-column>
      <!--  End fix:BAU-70  LiHaiBin 2025-04-09 -->
      <el-table-column prop="rawMessage" :label="$t('csscl.incSwift.rawMessage')" width="70" >
        <template #default="scope">
          <el-icon-folder-opened  style="width:20px;height:20px;color:orange" @click="rawClick(scope.row)" />
        </template>
      </el-table-column>
      <!-- End R2411A-38517, Tom.Li, 2024/08/20 -->
    </template>
    <template v-slot:contentBottom>
      <br />
      <el-space style="float:right;">
        <el-button @click="batchExportCsv">
          {{ $t('common.button.batchExportCsv') }}
        </el-button>
        <el-button @click="batchExportTxt">
          {{ $t('common.button.batchExportTxt') }}
        </el-button>
        <el-button v-if="$currentInfoStore.currentPermission && $currentInfoStore.currentPermission['Resume']"
          type="primary" @click="resumeSelection">
          {{ $t('csscl.incSwift.button.resume') }}
        </el-button>
        <el-button v-if="$currentInfoStore.currentPermission && $currentInfoStore.currentPermission['Marked as Complete']"
          type="primary" @click="openMarkDialog">
          {{ $t('csscl.incSwift.button.markedAsComplete') }}
        </el-button>
      </el-space>
    </template>
  </BasePanel>

  <Details ref="detailsRef" :reload="reload" />

  <el-dialog v-model="markedAsComp.showDialog" :title="$t('csscl.incSwift.button.markedAsComplete')"
    :close-on-click-modal="false" width="30%" class="mkck-dialog" append-to-body :show-close="false">
    <template #header>
      <div class="mkckTitle">
        <span class="title-name">{{ $t('csscl.incSwift.button.markedAsComplete') }}</span>
      </div>
    </template>
    <br>
    <el-form-item label="Remark" style="padding-right: 12px; padding-left: 12px;">
      <el-input type="textarea" v-model="markedAsComp.remark" :rows="6"></el-input>
    </el-form-item>
    <br>
    <div class="button-group" style="text-align:center;">
      <el-button @click="markedAsCompCancel" class="ep-button-custom">Cancel</el-button>
      <el-button type="primary" @click="markedAsCompOk" class="ep-button-custom">OK</el-button>
    </div>
    <br>
  </el-dialog>

  <SwiftMsgRawDetail ref="sMsgRawDetailRef"/>
</template>

<script lang="ts" setup>
import { ref, reactive, getCurrentInstance, toRaw } from 'vue';
import type { FormRules } from 'element-plus';
import { ElMessageBox } from 'element-plus';
import BasePanel from '~/pages/base/index.vue'
import Details from './Details.vue'
import SwiftMsgRawDetail from './swiftMsgRawDetail.vue'
import { getCommonDesc, getRecordStatusDesc, checkBeforeCurDt, checkInputDate, downloadBatchFile } from '~/util/Function.js';
import { commonRules } from '~/util/Validators.js';

const { proxy } = getCurrentInstance()
const paramListData = {};
const searchParams = {
  //顺序和上面绑定参数一致
  receiveDtFrom: null,
  receiveDtTo: null,
  messageType: "",
  status: "",
  senderBicCode: "",
  messageId: "",
  recordDt: "",
  placeOfSettl: "",
  swiftEventCode: "",
  instSettlDt: "",
  settlType: "",
};
function csvClick(row) {
  let funcId = proxy.$currentInfoStore.getCurrentFuncId();

  if(row?.recordStatus === 'PA'){
    ElMessageBox.alert("Cannot select pending approve records.", 'Warning');
    return;
  }

  var tableSelectRows = new Array();
  row.funcId = funcId;
  tableSelectRows.push(row);
  if(tableSelectRows && tableSelectRows.length !== 0){
    downloadBatchFile("/datamgmt/api/v1/swiftmsg/downloadBatchSwiftCSV", tableSelectRows);
  } else {
    ElMessageBox.alert("Please select at least one row of records!", 'Warning');
  }
}
//Start fix:BAU-70 添加导出txt By LiHaiBin 2025-04-09 14:13
const txtClick = (row:any)=>{
  let funcId = proxy.$currentInfoStore.getCurrentFuncId();

  if(row?.recordStatus === 'PA'){
    ElMessageBox.alert("Cannot select pending approve records.", 'Warning');
    return;
  }

  const tableSelectRows = new Array();
  row.funcId = funcId;
  tableSelectRows.push(row);
  if(tableSelectRows && tableSelectRows.length !== 0){
    downloadBatchFile("/datamgmt/api/v1/swiftmsg/downloadBatchSwiftTXT", tableSelectRows);
  } else {
    ElMessageBox.alert("Please select at least one row of records!", 'Warning');
  }
}
//Eed fix:BAU-70 添加导出txt By LiHaiBin 2025-04-09 14:13
const sMsgRawDetailRef = ref(); 
const rawClick = async (row) => {
  let swiftMsgRaw = "";
  const oid = row.pendingOid!=null?row.pendingOid:row.currentOid
  await proxy.$axios.get("/datamgmt/api/v1/swiftmsg?swiftMsgId=" + oid ).then((body) => {
      if (body.success) {
        swiftMsgRaw = body.data?.swiftMsgRaw;
        // Start fix:BAU-70  LiHaiBin 2025-04-09
        const parseSwiftMsgRaw = body.data?.parseSwiftMsgRaw;
        if (parseSwiftMsgRaw) {
          swiftMsgRaw = parseSwiftMsgRaw;
        }
        // End fix:BAU-70  LiHaiBin 2025-04-09
        sMsgRawDetailRef.value.showSMsgRawDetail(swiftMsgRaw, true);
      }
  });                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           
}

const tableRef = ref();
const detailsRef = ref();
const showDetails = (row, disabled) => {
  detailsRef.value.showDetails(row, disabled);
}

const editRow = (row) => {
  detailsRef.value.editRow(row,true);
}
const reload = () => {
  tableRef.value.load();
}
//-------------------------------
interface RuleForm {
  receiveDtFrom: Date
  receiveDtTo: Date
}
const rules = reactive<FormRules<RuleForm>>({
  receiveDtFrom: [
    commonRules.required,
    commonRules.earlierEquCurDate,
    commonRules.earlierEquDt(()=>{ return searchParams.receiveDtTo }, proxy.$t('csscl.incSwift.receivedDateTo')),
    commonRules.diffDate(7, ()=>{ return searchParams.receiveDtTo }, proxy.$t('csscl.incSwift.receivedDateTo')),
  ],
  receiveDtTo: [
    commonRules.required,
    commonRules.earlierEquCurDate,
  ],
})

const hideEditBtn = (row) => {
  if (row.recordStatus == 'PD' || row.recordStatus == 'R' || row.recordStatus == 'PA') {
    return false;
  }
  return true;
}

const beforeSearch = async() => {
  // let fieldsDtl = {
  //   fields:{
  //     receiveDtFrom: proxy.$t('csscl.incSwift.receivedDateFrom'),
  //     receiveDtTo: proxy.$t('csscl.incSwift.receivedDateTo'),
  //   }
  // }
  // let result = await tableRef.value.formRef.validate((valid, fields) => {
  //   if (!valid) {
  //     showValidateMsg(fieldsDtl, fields);
  //   }
  // });
  // if (!result) {
  //   return false;
  // }

  // let msgs = [];
  // let msg1 = await checkBeforeCurDt(proxy, proxy.$t('csscl.incSwift.receivedDateFrom'), tableRef.value.formInline.receiveDtFrom);
  // msgs.push(msg1);
  // let msg2 = await checkBeforeCurDt(proxy, proxy.$t('csscl.incSwift.receivedDateTo'), tableRef.value.formInline.receiveDtTo);
  // msgs.push(msg2);
  // if (msg1 || msg2) {
  //   return msgs;
  // }

  // let chkMsg = checkInputDate(proxy, tableRef.value.formInline.receiveDtFrom, tableRef.value.formInline.receiveDtTo);
  // return chkMsg;
}

const tableSelectRowsRef = ref([]);
const handleSelectionChange = (val) => {
  tableSelectRowsRef.value = val;
}
//Start fix:BAU-70 添加导出txt 修改名称 By LiHaiBin 2025-04-09 14:13
const batchExportCsv = async () => {
//Eed fix:BAU-70 添加导出txt 修改名称 By LiHaiBin 2025-04-09 14:13
  let funcId = proxy.$currentInfoStore.getCurrentFuncId();
  var tableRows = new Array();
  const isApprove = ref(false);
  if(tableSelectRowsRef.value){
    for(var index in tableSelectRowsRef.value){
      tableSelectRowsRef.value[index].funcId = funcId;
      let recordStatus = tableSelectRowsRef.value[index]?.recordStatus;
      if(recordStatus && ("PA" === recordStatus)){
        isApprove.value = true;
        break;
      }
      tableRows.push(tableSelectRowsRef.value[index]);
    }
  }

  if(isApprove.value){
    ElMessageBox.alert("Cannot select pending approve records.", 'Warning');
    return;
  }

  if(tableRows && tableRows.length !== 0){
    downloadBatchFile("/datamgmt/api/v1/swiftmsg/downloadBatchSwiftCSV", tableRows);
  } else {
    ElMessageBox.alert(proxy.$t('message.system.record.not.selected'), 'Warning');
  }
};
//Start fix:BAU-70 添加导出txt By LiHaiBin 2025-04-09 14:13
const batchExportTxt = async () => {
  let funcId = proxy.$currentInfoStore.getCurrentFuncId();
  var tableRows = new Array();
  const isApprove = ref(false);
  if(tableSelectRowsRef.value){
    for(var index in tableSelectRowsRef.value){
      tableSelectRowsRef.value[index].funcId = funcId;
      let recordStatus = tableSelectRowsRef.value[index]?.recordStatus;
      if(recordStatus && ("PA" === recordStatus)){
        isApprove.value = true;
        break;
      }
      tableRows.push(tableSelectRowsRef.value[index]);
    }
  }
//Eed fix:BAU-70 添加导出txt By LiHaiBin 2025-04-09 14:13
  if(isApprove.value){
    ElMessageBox.alert("Cannot select pending approve records.", 'Warning');
    return;
  }

  if(tableRows && tableRows.length !== 0){
    downloadBatchFile("/datamgmt/api/v1/swiftmsg/downloadBatchSwiftTXT", tableRows);
  } else {
    ElMessageBox.alert(proxy.$t('message.system.record.not.selected'), 'Warning');
  }
};

const resumeSelection = async () => {
  var tableSelectRows = new Array();
  if (tableSelectRowsRef.value) {
    for (var index in tableSelectRowsRef.value) {
      let row = toRaw(tableSelectRowsRef.value[index]);
      tableSelectRows.push(row);
    }
  }
  if (tableSelectRows && tableSelectRows.length !== 0) {
    proxy.$axios.post('/datamgmt/api/v1/swiftmsg/resume', tableSelectRows).then((obj)=>{
      if(obj.success){
        // Start R2411A-41696 LiShaoyi 2024/09/09
        if (obj.messageId.indexOf('message.swift.resumed.bypass.record') > -1) {
          ElMessageBox.alert(proxy.$t('message.swift.resumed.bypass.record'), 'Success');
        } else {
          ElMessageBox.alert("Resume Success!", 'Success');
        }
        // End R2411A-41696 LiShaoyi 2024/09/09
        tableRef.value.load();
      }
    });
  }else {
    ElMessageBox.alert(proxy.$t('message.system.record.not.selected'), 'Warning');
  }
};

const markedAsComp = ref({
  showDialog: false,
  remark: '',
});

const openMarkDialog = async () => {
  if(tableSelectRowsRef.value){
    const isComplate = ref(false);
    const isApprove = ref(false);
    const isNa = ref(false);
    const rows = new Array();
    for(var index in tableSelectRowsRef.value){
      let status = tableSelectRowsRef.value[index]?.status;
      if(status && ("C" === status || "MC" === status || "RC" === status)){
        isComplate.value = true;
        break;
      }
      if(status && ("NA" === status)){
        isNa.value = true;
        break;
      }
      let recordStatus = tableSelectRowsRef.value[index]?.recordStatus;
      if(recordStatus && ("PA" === recordStatus)){
        isApprove.value = true;
        break;
      }
      rows.push(tableSelectRowsRef.value[index]);
    }
    if(isComplate.value){
      ElMessageBox.alert("Cannot select completed records.", 'Warning');
      return;
    }
    if (isNa.value) {
      ElMessageBox.alert("Cannot select N/A records.", 'Warning');
      return;
    }
    if(isApprove.value){
      ElMessageBox.alert("Cannot select pending approve records.", 'Warning');
      return;
    }
    if(rows.length === 0){
      ElMessageBox.alert(proxy.$t('message.system.record.not.selected'), 'Warning');
      return;
    }
    let funcId = proxy.$currentInfoStore.getCurrentFuncId();
    if(!funcId||funcId=='ALL'){
      ElMessageBox.alert("funcId is null.", 'Warning');
      return;
    }
    markedAsComp.value.showDialog = true;
  }
}
const markedAsCompOk = async () => {
  if(tableSelectRowsRef.value){
    let funcId = proxy.$currentInfoStore.getCurrentFuncId();
    const rows = new Array();
    for(var index in tableSelectRowsRef.value){
      rows.push(tableSelectRowsRef.value[index]);
    }
    let alertMsg = "";
    for(let i=0; i<rows.length; i++){
      let row = rows[i];
      if(row?.currentOid){
        const params = {currentOid : row.currentOid};
        const msg = await proxy.$axios.post("/datamgmt/api/v1/swiftmsg/marked", params);
        if(msg && msg.success){
          if(msg.data){
            const response = await proxy.$axios.post('/datamgmt/api/v1/makerchecker/initial', {
              remark: markedAsComp.value.remark,
              status: "A",
              eventOid: msg.data,
              eventType: 'SD',
              funcId: funcId
            });
            if(response && !response?.success){
              alertMsg= alertMsg + row?.messageId + "  ";
            }
          }
        }
      }
    }
    if(alertMsg !== ""){
      ElMessageBox.alert(alertMsg + " is Marking failed.", 'Warning');
    }
    reload();
    markedAsCompCancel();
  }
};
const markedAsCompCancel = () => {
  markedAsComp.value.showDialog = false;
}


//paramList 参数显示用的
// function statusType(value) {
//   paramListData._value.status = getCommonDesc('PEND_QUE_STS', value);
// }

</script>

<style></style>