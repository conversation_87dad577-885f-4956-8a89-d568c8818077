<template> 
  <BaseDetails ref="details" :handleSave="handleSave" :reload="reload" :viewOriginalForm="viewOriginalForm" :form="ruleForm" :beforeSubmit="beforeSubmit" :beforeSave="beforeSave">
    <el-form :validateOnRuleChange="false" :disabled="formDisabled" ref="ruleFormRef" style="width: 100%" :model="ruleForm.form" :rules="rules" status-icon>
      <FormRow>
        <FormItemSign :detailsRef="details" label-width="120px" :label="$t('csscl.ftgManagement.clientFtgCode')" prop="clientFtgCode" style="width:420px">
          <el-input v-model="ruleForm.form.clientFtgCode" style="width: 250px" :disabled="editDis" maxlength="20"/>
        </FormItemSign>
        <FormItemSign :detailsRef="details"></FormItemSign>
        <FormItemSign :detailsRef="details"></FormItemSign>
      </FormRow>
      <FormRow>
        <FormItemSign :detailsRef="details" label-width="120px" :label="$t('csscl.ftgManagement.ftgidCode')" prop="ftgidCode" style="width:420px">
          <InputText v-model="ruleForm.form.ftgidCode" uppercase style="width: 250px" :disabled="editDis" maxlength="20"/>
        </FormItemSign>
        <FormItemSign :detailsRef="details" label-width="140px" :label="$t('csscl.ftgManagement.ftgidName')" prop="ftgidName">
          <el-input v-model="ruleForm.form.ftgidName" style="width: 470px" maxlength="50" input-style="text-transform: none;" />
        </FormItemSign>
        <FormItemSign :detailsRef="details" label-width="75px" :label="$t('common.title.status')" prop="status">
          <Select v-model="ruleForm.form.status" type='STATUS' />
        </FormItemSign>      
      </FormRow>
    </el-form>

    <div v-show="showJobList">
      <div class="splitLine">
        <span>{{  $t("csscl.ftgManagement.jobList") }}</span>
        <el-divider />
    </div>
    <Grid url="/rptsched/api/v1/inter/scheduler/job/list/ftgidcode"
            :params="reqParam"
            :afterSearch="afterSearch"
            :columns="[
                  {title:'csscl.jobScheduler.jobId',name:'jobId',width:'300'},
                  {title:'csscl.jobScheduler.jobName',name:'jobName',width:'300'},
                  {title:'csscl.jobScheduler.type',name:'jobType',width:'300',fn:commDesc('JOB_TYPE') },
                  {title:'csscl.jobScheduler.frequency',name:'frequency',width:'300',fn:commDesc('FREQUENCY') },
                  {title:'csscl.jobScheduler.proTimeCutoffTime',name:'processTimes',width:'500'},
                  {title:'common.title.status',name:'status',fn:commDesc('STATUS') },
            ]"
            >
    </Grid>
  </div>
    
  </BaseDetails>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue';
import type { FormInstance, FormRules } from 'element-plus'
import BaseDetails from '~/pages/base/Details.vue';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import Grid from '~/pages/base/Grid.vue';
import  { getCommonDesc, commDesc } from '~/util/Function.js';
import { getOid, saveMsgBox } from '~/util/Function.js';
import { commonRules, showValidateMsg } from '~/util/Validators.js';
import { ElMessageBox, ElMessage, ElLoading  } from 'element-plus';
const { proxy } = getCurrentInstance();
const props = defineProps(['reload']);
const details = ref();
const formDisabled = ref(false);
const editDis = ref(false);
const reqParam = ref({});
const showJobList = ref(false);
const editRow = async (row, disabled, newId) => {
  if(row?.isApproveDetail && disabled){
      ruleForm.form = row?.afterImage;
      details.value.currentRow = ruleForm.form;
  } else {
    const oid = newId || (row?.currentOid && getOid(row, disabled, proxy));
    if (oid) {
      const msg =  await proxy.$axios.get("/datamgmt/api/v1/ftg?ftgidId="+oid);
      if(msg.success) {
        ruleForm.form = msg.data;
        details.value.currentRow = msg.data;
      }
      editDis.value = true;
      reqParam.value = {ftgidCode:ruleForm.form.ftgidCode}
      details.value.initWatch(ruleForm);
    }
  }
}

const viewOriginalForm = (pendingOid, isDisabled) => {
  formDisabled.value = isDisabled;
  proxy.$axios.get("/datamgmt/api/v1/ftg?ftgidId="+pendingOid).then((body) => {
        if(body.success) {
            ruleForm.form = body.data;
        }
    });
    editDis.value = false;
}

const afterSearch = (params, data) => {
  if (data?.length > 0) {
    showJobList.value = true;
  }
}
const beforeSubmit = async () => {
  let msg = await proxy.$axios.post("/datamgmt/api/v1/ftg/verify", ruleForm.form);
  return msg.data;
}
const beforeSave = async () => {
    let msg = await proxy.$axios.post("/datamgmt/api/v1/ftg/verify", ruleForm.form);
    return msg.data;
}

const handleSave = async (searchValid, isOnlyValidate, unPopping) => {
  let result = await ruleFormRef.value.validate((valid, fields) => {
      if (valid) {

      } else {
        showValidateMsg(details, fields);
      }
  });
    if (isOnlyValidate) {
        return result;
    }
  if (result && searchValid && await saveMsgBox(unPopping)) {
    let msg = {};  
    if (ruleForm.form.ftgidOid) {
      msg = await proxy.$axios.patch("/datamgmt/api/v1/ftg", ruleForm.form);
      if(msg.data==null) {
        return false;
      }
    } else {
      msg = await proxy.$axios.post("/datamgmt/api/v1/ftg", ruleForm.form);
    }
    details.value.writebackId(msg.data);
    editRow(null,null,msg.data);
    return msg.success;
  }
  return false;
}
const showDetails = (row, isdoubleCheck) => {
  if(isdoubleCheck||row.recordStatus==='PA'){
        formDisabled.value = true;
        details.value.showDetails(row, true)
    }else{
        formDisabled.value = false;
        details.value.showDetails(row, false)
    }
    ruleForm.form = {};
    showJobList.value = false;
    reqParam.value = {};
    editDis.value = false;
    details.value.currentRow = {};
    if (row.currentOid) {
        editRow(row,isdoubleCheck);
    }else{
      details.value.initWatch(ruleForm);
    }
}

defineExpose({
  details,
  editRow,
  showDetails,
});
// --------------------------------------------
interface RuleForm {
  clientFtgCode: String
  ftgidCode: String
  ftgidName: String
  status: String
}

const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive({
  rules:()=>{ return [{ rules:rules }] },
  form: {
    clientFtgCode: "",
    ftgidCode: "",
    ftgidName: "",
    status: "",
  }
})

const rules = reactive<FormRules<RuleForm>>({
  clientFtgCode: [
      commonRules.required,
      commonRules.name,
  ],
  ftgidCode: [
      commonRules.required,
      commonRules.name,
  ],
  ftgidName: [
      commonRules.required,
      commonRules.name,
  ],
  status: [
      commonRules.selectRequired,
  ],
})

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
      if (valid) {
          console.log('submit!')
      } else {
          console.log('error submit!', fields)
      }
  })
}

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
}

const options = Array.from({ length: 10000 }).map((_, idx) => ({
  value: `${idx + 1}`,
  label: `${idx + 1}`,
}))

</script>

<style></style>