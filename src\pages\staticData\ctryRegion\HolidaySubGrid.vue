<template>
  <div style="margin:10px 0" class="grid-continer">
    <el-table :data="tableData" table-layout="auto" @row-click="handleClick"
      ref="tableRef" :cell-style="cellStyle" :border="true" class="grid-table"
      scrollbar-always-on class-name="multiple-table" :row-class-name="tableRowClassName"
      :header-cell-class-name="(params: any) => { setHeaderClass(params) }" 
      style="overflow: auto; height: 430px;">
      <slot name="tableColumnFront"></slot>
      <el-table-column v-for="item in columns" :key="item.name" :sortable="false" :prop="item.name" 
      :align="item.align" :label="$t(item.title)" header-align="center" :width="item.width?item.width:'*'">
        <template #default="scope" v-if="item.fn">
          {{ item.fn? item.fn(scope.row, scope.row[item.name]):"" }}
        </template>
      </el-table-column>
      <slot name="tableColumnAfter"></slot>
      <el-table-column type="index" class-name="data-grid-selection-index-cell" width="1px" />
    </el-table>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, getCurrentInstance } from 'vue'
import { highlight, selectedRow, validSearchInputValue, validDateItemValue } from '~/util/Function.js';

const { proxy } = getCurrentInstance();
const props = defineProps(['url', 'params', 'isSelectFirst', 'searchParams', 'onClick', 'columns', 'beforeSearch',
 'afterSearch', 'cellStyle', 'selectable', 'isMultiple', 'isManual']);
const currentPage = ref(1);
const pageSize = ref(9999);
const total = ref(0);
const currentRow = ref({});
const tableRef = ref();
const tableData = ref([]);
const param = ref({});
const orderBy = ref("");
const sortField = ref([]);
const columns = ref(props.columns);
const formInline = reactive(props.searchParams || {});
const oldParams = ref({});
const selectedRecord = ref([]);
const lastSelected = ref(0);

const loadData = async () => {
  let allParams = false;
  let keys = Object.keys(props.params || {});
  for(let i = 0; i < keys.length; i++) {
    let e = props.params[keys[i]];
    if (e) {
      allParams = true;
      break;
    }
  }
  if (!allParams) {
    return ;
  }
  oldParams.value = props.params;
  const msg = await proxy.$axios.post(props.url, {
    param: {
      ...param.value,
      ...props.params,
    },
    current: currentPage.value,
    pageSize: pageSize.value,
    orderBy: orderBy.value,
  });
  if (msg?.success) {
    total.value = msg.data.total;
    currentPage.value = msg.data.page;
    tableData.value = msg.data.data;
    setTimeout(() => {
      afterSearch(param.value, tableData.value);
    }, 300);
  }
}

const afterSearch = (params, data) => {
  if(props.afterSearch) {
    props.afterSearch(params, data);
  }
}

const load = async () => {
  await onSearch();
  }
  
const onSearch = async () => { 
  param.value = formInline;
  let res = await validSearchInputValue("div.grid-continer input[searchtype]")
  res = validDateItemValue("div.grid-continer .search-date-error input[alt]") == false ? false : res;
  if (res) {
    res = await beforeSearch(param.value, props.params);
    if(res){
      loadData();
    }
  }
}

const beforeSearch = async (search, params) => {
  let ret = true;
  if (props.beforeSearch) {
    ret = await props.beforeSearch(search, params);
    if (ret == false) {
      return false;
    } else {
      return true;
    }
  }
  return ret;
}

const handleClick = async(row: any, column: any, event: Event) => {
  !props.isManual&&event&&selectedRow("holidayDate", selectedRecord, tableRef, lastSelected, row, column, event, props.selectable, props.isMultiple);
  if (props.onClick) {
    let ret = await props.onClick(row);
    if(ret === false){
      //no change row when edit row validation failed
      tableRef.value!.setCurrentRow(currentRow.value);
      return;
    }
  }
  currentRow.value = row;
  tableRef.value!.setCurrentRow(row);
}

const tableRowClassName = ({ row }) => {
  let selectedClass="";
  let key = row["holidayDate"];
  if (selectedRecord.value[key]) {
    selectedClass= ' selected-row';
  }
  return selectedClass;
}

const setHeaderClass = (params: any) => {
  params.column.order = sortField.value[params.column.property];
}

const cellStyle = (row, column, rowIndex, columnIndex) => {
  if(props.cellStyle){
    let style = props.cellStyle(row, column, rowIndex, columnIndex);
    if(style){
      return style;
    }
  }
  return highlight(row);
}

const setCurrentRow = (idx, click) => {
  idx = idx || 0;
  tableRef.value.$el.querySelectorAll("tbody tr")[idx]?.click();
}

defineExpose({
  load,
  tableRef,
  tableData,
  total,
  currentPage,
  pageSize,
  orderBy,
  currentRow,
  selectedRecord,
})
</script>

<style>
.grid-table .ep-table__body {
  padding : 0;
}
</style>