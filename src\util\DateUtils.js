import moment from "moment"

/**
 * 时间戳格式化日期格式yyyy/MM/dd
 * 
 * @param {*} timestamp 
 * @returns 
 */
export const timestampToDate = (timestamp) => {
    if(timestamp && isNumber(timestamp)){
        var date = new Date(timestamp); // 将时间戳转换为Date对象
        var year = date.getFullYear(); // 获取年份
        var month = ('0' + (date.getMonth() + 1)).slice(-2); // 获取月份，并保证两位数字
        var day = ('0' + date.getDate()).slice(-2); // 获取日期，并保证两位数字
        return year + '/' + month + '/' + day; // 拼接字符串返回
    } else {
        return timestamp;
    }
}

function isNumber(str) {
    return /^\d+(\.\d+)?$/.test(str); // 检查字符串是否全数字
}

/**
 * 时间戳格式化日期格式yyyy/MM/dd HH:mm:ss
 * 
 * @param {*} timestamp 
 * @returns 
 */
export const timestampToDateTime = (timestamp) => {
    if(timestamp && isNumber(timestamp)){
        return moment(timestamp).format('YYYY/MM/DD HH:mm:ss');
    } else {
        return timestamp;
    }
}

export const formatDate = (dateString)=> {
    let date;
    if (typeof dateString === 'string') {
        date = new Date(dateString.replace(/-/g, '/'));
    } else if (typeof dateString === 'number') {
        date = new Date(dateString);
    } else if (dateString instanceof Date) {
        date = dateString;
    } else {
        throw new Error('Invalid date format');
    }
    if (isNaN(date.getTime())) {
        throw new Error('Invalid date');
    }
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}/${month}/${day}`;
}

/**
 * 获取时区
 * @returns {string}
 */
export const getTimeZone = () => {
    return new Intl.DateTimeFormat().resolvedOptions().timeZone;
}