<template> 
    <div>
        <!-- CA Grid -->
        <el-form :validateOnRuleChange="false" disabled>
            <div style="position: absolute;right: 60px; top: 198px;">
                <el-icon-download style="width:20px;height:20px;color:darkorange;padding-left: 192px;" @click="downloadClick" />
                <div></div>
                <el-text>Refreshed as at {{date}}</el-text>
            </div>
        </el-form>

        <el-table :data="txnTable" row-key="id" style="width: 100%" lazy default-expand-all :span-method="arraySpanMethod" >
            <el-table-column header-align="center" :label="$t('csscl.cashopt.enquiry.txn.postingDate')" >
                <el-table-column header-align="center" prop="postDate1" :label="$t('csscl.cashopt.enquiry.txn.branchCode')" width="150" />
            </el-table-column>
            <el-table-column header-align="center" :label="$t('csscl.cashopt.enquiry.txn.postingTime')" >
                <el-table-column header-align="center" prop="txTime" :label="$t('csscl.cashopt.enquiry.txn.terminal')" width="150" />
            </el-table-column>
            <el-table-column header-align="center" :label="$t('csscl.cashopt.enquiry.txn.txnType')">
                <el-table-column header-align="center" prop="txType" :label="$t('csscl.cashopt.enquiry.txn.tellerId')" width="160" />
            </el-table-column>
            <el-table-column header-align="center" :label="$t('csscl.cashopt.enquiry.txn.txnCode')" >
                <el-table-column header-align="center" prop="txCode" :label="$t('csscl.cashopt.enquiry.txn.reversalTxnSerialNum')" width="180" />
            </el-table-column>
            <el-table-column header-align="center" :label="$t('csscl.cashopt.enquiry.txn.txnDate')" >
                <el-table-column header-align="center" prop="txDate" :label="$t('csscl.cashopt.enquiry.txn.authorizedId')" width="180" />
            </el-table-column>
            <el-table-column header-align="center" :label="$t('csscl.cashopt.enquiry.txn.journalNum')" >
                <el-table-column header-align="center" label-class-name="txn-remark-col" prop="jrnNo" :label="$t('csscl.cashopt.enquiry.txn.remarks')" width="400" />
            </el-table-column>
            <el-table-column header-align="center" :label="$t('csscl.cashopt.enquiry.txn.currency')" >
                <el-table-column prop="cur" label=" " width="180" />
            </el-table-column>
            <el-table-column header-align="center" :label="$t('csscl.cashopt.enquiry.txn.amount')" width="180" >
                <el-table-column prop="txAmt" label=" " width="180" align="right">
                    <template #default="scope">{{ thousFormatK(scope.row.txAmt) }}</template>
                </el-table-column>
            </el-table-column>
            <el-table-column header-align="center" :label="$t('csscl.cashopt.enquiry.txn.ledgerBal')" width="180" >
                <el-table-column prop="bal" label=" " width="180" align="right">
                    <template #default="scope">{{ thousFormatK(scope.row.bal) }}</template>
                </el-table-column>
            </el-table-column>
            <el-table-column header-align="center" :label="$t('csscl.cashopt.enquiry.txn.promoptCode')" >
                <el-table-column prop="promotCode" label=" " />
            </el-table-column>
        </el-table>

        <el-pagination v-if="showTxnFlag" v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[17]"
            layout="sizes, |, prev, pager, next, ->, slot" v-model:total="total" @current-change="handleChange" 
            style="background-color: lightgrey;padding-inline: 10px;">
          </el-pagination>

    </div>
</template>

<script lang="ts" setup>
import { ref, watch, reactive, onMounted, getCurrentInstance, defineProps } from 'vue';
import { useRouter } from 'vue-router';
import { getDateAndTime, downloadBatchFile, thousFormatK } from '~/util/Function.js';
import { ElMessageBox } from 'element-plus'

const router = useRouter()
const props = defineProps([ "ruleForm", "details"]);
const { proxy } = getCurrentInstance();


const details = props.details;
const ruleForm = props.ruleForm;
let date = getDateAndTime();
const txnTable = reactive(ruleForm.form.txnForm.gp1.slice(0, 17));
const currentPage = ref(1);
const pageSize = ref(17);
const total = ruleForm.form.txnForm.gp1.length;
const showTxnFlag = ref(false);

onMounted(() => {
    loadLazyData();
    const remarkInterval = setInterval(()=>{
        const remarkCol = document.getElementsByClassName("txn-remark-col");
        if(remarkCol&&remarkCol.length>0) {
            remarkCol[0].colSpan=5;
            clearInterval(remarkInterval);
        }
    },100);
});

watch(() => ruleForm.form.txnForm.gp1, (newVal) => {
    if(txnTable && txnTable.length > 0){
        txnTable.splice(0, txnTable.length);
    }
    Object.assign(txnTable, ruleForm.form.txnForm.gp1.slice(0, 17));
    loadLazyData();
    currentPage.value = 1;
});

const handleChange = (index) => {
    if(txnTable && txnTable.length > 0){
        txnTable.splice(0, txnTable.length);
    }
    Object.assign(txnTable, ruleForm.form.txnForm.gp1.slice((index-1)*17, index*17));
    loadLazyData();
}

const loadLazyData = () => {
    if(txnTable && txnTable.length !== 0){
        showTxnFlag.value = true;
         let i = 0;
        txnTable.forEach(row => {
            if (row) {
                row.id = i++;
                row.hasChildren = true;
                row.children = [{
                    "id": row.id + "11",
                    "postDate1": row["branchNo"],
                    "txTime": row["termNo"],
                    "txType": row["tellerNo"],
                    "txCode": row["origJrnNo"],
                    "txDate": row["authTeller"],
                    "jrnNo": row["remarks"],
                }];
                row.accNo = ruleForm.form?.parentBankAccountNo;
                proxy.$axios.post("/eapmgmt/api/v1/boc/remark", row).then((rData) => {
                    if(rData && rData?.respBody){
                        row.children[0].jrnNo = rData.respBody?.summ;
                    }
                });
            }
        });
    } else {
        showTxnFlag.value = false;
    }
} 

function toFixedIntegerString(num) {
  return String(Math.floor(num)).padStart(4, '0');
}

const downloadClick = async()=>{
    ElMessageBox.confirm(
    'Are you sure you want to download it?',
    'Warning',
    {
      confirmButtonText: 'OK',
      cancelButtonText: 'Cancel',
      type: 'warning',
    })
    .then(() => {
      downloadBatchFile("/eapmgmt/api/v1/boc/txnDownload", ruleForm.form);
    })
    .catch(() => {})
}

const arraySpanMethod = ({
  row,
  column,
  rowIndex,
  columnIndex,
}) => {
  if (rowIndex % 2 === 1) {
    if (columnIndex === 5) {
      return [1, 5]
    }
  }
}

</script>

<style></style>