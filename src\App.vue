<template>
  <el-config-provider namespace="ep">
    <div class="common-layout" style="padding-bottom: 60px;">
      <el-container>
        <!-- Start SK-COMMON-0118, Tom.Li, 2024/08/28 -->
        <el-header id="main-header" style="position: fixed;width: 100%;background-color: white;z-index: 99;min-width: 1850px;">
        <!-- End SK-COMMON-0118, Tom.Li, 2024/08/28 -->
          <Header />
        </el-header>
        <!-- Start SK-COMMON-0118, Tom.Li, 2024/08/28 -->
        <el-main style="overflow: hidden;position: relative;margin: 62px 0px 0px 0px">
        <!-- End SK-COMMON-0118, Tom.Li, 2024/08/28 -->
          <router-view></router-view>
        </el-main>
      </el-container>
    </div>
    <!-- Start SK-COMMON-0118, Tom.Li, 2024/08/28 -->
    <div id="main-flooter" style="position: fixed; bottom: 0px; width: 100%;z-index: 99;min-width: 1850px;background-color: white;">
    <!-- End SK-COMMON-0118, Tom.Li, 2024/08/28 -->
      <Flooter />
    </div>
    <PreviewFile/>
    <div v-if="showContext" style="position: fixed;top: 0px; left: 0px; width: 100%; height: 100%; background-color: white;z-index: 100;"></div>
  </el-config-provider>
</template>
<script setup>
import { getCurrentInstance, onMounted, ref } from 'vue';
import { initialize } from '~/util/Function';
import { useCookies } from 'vue3-cookies';
const { proxy } = getCurrentInstance();
const { cookies } = useCookies();
const showContext = ref(false);
if(cookies.get("x-esession")&&!location.pathname.endsWith("/login")) {
    showContext.value = true;
    initialize(proxy).then((result)=>{
      if (result) {
        showContext.value = false;
      }
    });
}
// Start SK-COMMON-0083, Tom.Li, 2024/08/19
onMounted(()=> {
  window.addEventListener('beforeunload', function() {
    if ( sessionStorage.getItem("isLoggedIn") ) {
      proxy.$axios.post('/datamgmt/api/v1/handler/unlock',{
        flag:false
      });  
      proxy.$axios.post('/datamgmt/api/v1/makerchecker/unlock', {
        logout: true,
      });
    }
  });

  window.addEventListener('scroll', function(event) {
    document.getElementById("main-header").style.marginLeft = - event.target.scrollingElement.scrollLeft + "px";
    document.getElementById("main-flooter").style.marginLeft = - event.target.scrollingElement.scrollLeft + "px";
  });
});
// End SK-COMMON-0083, Tom.Li, 2024/08/19
</script>
<style>
#app {
  min-height: 100%;
  min-width: 1850px;
  position: relative;
}
</style>
