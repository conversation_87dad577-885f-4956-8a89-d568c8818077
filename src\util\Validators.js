import { watch } from 'vue';
import { ElMessage } from 'element-plus';
import { currentDate, serverCurrentDate } from './Function.js';

const earlierEquCurDate = async (rule, value, callback) => {
  if (value) {
    let dt = new Date(value);
    let d = await serverCurrentDate();
    d.setHours(0, 0, 0, 0);
    if (dt > d) {
      callback(new Error())
    }
  }
  callback()
}
// Start CAP1-326 heruiguang 2025-02-17
const earlierCurDate = async (rule, value, callback) => {
  if (value) {
    let dt = new Date(value);
    let d = await serverCurrentDate();
    d.setHours(0, 0, 0, 0);
    if (dt >= d) {
      callback(new Error())
    }
  }
  callback()
}
// End CAP1-326 heruiguang 2025-02-17
const earlierEquCurProcDate = async (rule, value, callback) => {
  if (value) {
    let dt = new Date(value);
    let d = await currentDate();
    if (dt > d) {
      callback(new Error())
    }
  }
  callback()
}

const earlierCurProcDate = async (rule, value, callback) => {
  if (value) {
    let dt = new Date(value);
    let d = await currentDate();
    if (dt >= d) {
      callback(new Error())
    }
  }
  callback()
}

const laterEquCurDate = (rule, value, callback) => {
  if (value) {
    let dt = new Date(value);
    let d = new Date();
    d.setHours(0, 0, 0, 0);
    if (dt < d) {
      callback(new Error())
    }
  }
  callback()
}

const laterEquCurProcDate = async (rule, value, callback) => {
  if (value) {
    let dt = new Date(value);
    let d = await currentDate();
    if (dt < d) {
      callback(new Error())
    }
  }
  callback()
}

const laterCurProcDate = async (rule, value, callback) => {
  if (value) {
    let dt = new Date(value);
    let d = await currentDate();
    if (dt > d) {
      callback()
    } else {
      callback(new Error());
    }
  }
}

const maxHoldingDate = async (rule,value, callback) => {
  if (value) {
    let dt = new Date(value);
    let d = await currentDate();
    if (Math.abs((dt - d)/(24*60*60*1000)) >= rule.diff) {
      callback(new Error())
    }
  }
  callback() 
}

// Start CAP1-413 修改获取服务器时间 rencan 2025-02-31.
const diffCurProcDate = async (rule,value, callback) => { // 该方法仅限于验证
  if (value) {
    let dt = new Date(value);
    let d = await currentDate();
    if (d < dt) { // 输入时间大于比较时间，则跳过
      callback() 
    } else if (Math.abs((dt - d)/(24*60*60*1000)) > rule.diff) {
      callback(new Error())
    }
  } else {
    callback() 
  }
}
// End CAP1-413 修改获取服务器时间 rencan 2025-02-31.

const noReportTemplete = async (rule,value, callback) => {
  if (value) {
    if ("Y" == value) {
      callback(new Error())
    }
  }
  callback() 
}


const laterEquDt = (rule, value, callback) => {
  let od = rule.otherDt && rule.otherDt()
  if (value && od) {
    let dt = new Date(value);
    let d = new Date(od);
    if (dt < d) {
      callback(new Error())
    }
  }
  callback()
}

const earlierEquDt = (rule, value, callback) => {
  let od = rule.otherDt && rule.otherDt();
  if (value && od) {
    let dt = new Date(value);
    let d = new Date(od);
    if (dt > d) {
      callback(new Error())
    }
  }
  callback()
}

const diffDate = (rule, value, callback) => {
  if (value && rule.otherDt) {
    let dt = new Date(value);
    let d = new Date(rule.otherDt());
    if (Math.abs((dt - d)/(24*60*60*1000)) > rule.diff) {
      callback(new Error())
    }
  }
  callback() 
}


const dateMustRange = (rule, value, callback) => {
  if (!value && rule.otherDt()) {
    callback(new Error())
  }
  callback() 
}

const caRefNoFileFormat = (rule, value, callback) => {  
  if (value && value == 'CA024') {
    if(!(rule.fileType() == 'PDF' && rule.fileLang() == 'EN')){
      callback(new Error())
    } 
  }
  callback() 
}
// Start CAP1-413 rencan 2025-02-21.
const entitleMustHasRecordDate = (rule, value, callback) => {
  if (!value) {
    // CAP1-516 LISY
    if(rule.caStatus() && (rule.caStatus() == 'Entitle' || rule.caStatus() == 'E')){
      callback(new Error())
    } 
  }
  callback() 
}
// End CAP1-413 rencan 2025-02-21.

// Start CAP1-413 rencan 2025-02-22.
const entitleRecordDateRange = (rule, value, callback) => {
  // CAP1-516 LISY 
  if (rule.caStatus() && (rule.caStatus() == 'Entitle'  || rule.caStatus() == 'E')) {
    if((value && rule.recordEnd())){
      let dt = new Date(value);
      let d = new Date(rule.recordEnd());
      if (Math.ceil(Math.abs((d - dt))/(24*60*60*1000)) >= rule.diff) {
        callback(new Error())
      }
    }
  }
  callback()
}
// End CAP1-413 rencan 2025-02-22.


const numberSize = (rule, value, callback) => {
  if (value) {
    if (rule.max || rule.max === 0) {
      if (Number(value) > Number(rule.max)) {
        callback(new Error())
      }
    }
    if (rule.min || rule.min === 0) {
      if (Number(value) < Number(rule.min)) {
        callback(new Error())
      }
    }
  }
  callback()
}

export const addCommonTxtRule = (formData) => {
  watch(formData, (n,o)=> {
    // if (formData) {
    //     let rulesArr = typeof(formData.rules) == 'function' ? formData.rules() : formData.rules;
    //     if (rulesArr) {
    //         for (let idx in rulesArr) {
    //             let ruleObj = rulesArr[idx];
    //             let form =  ruleObj.form ? ruleObj.form : formData.form;
    //             let rules = ruleObj.rules;
    //             for (let key in form) {
    //               if (key == "beforeImage") {
    //                 continue;
    //               }
    //               if (Array.isArray(form[key])) {
    //                 continue;
    //               }
    //               if ( !rules[key] ){
    //                   rules[key] = [];
    //               }
    //               if (!rules[key].includes(commonRules.name)) {
    //                   rules[key].push(commonRules.name);
    //               }
    //             }
    //         }
    //     }
    // }
  }, {deep: true});
} 

export const commonRules = {
  specialTxt: {
    pattern: /^[a-zA-Z0-9\s\\\/\|\-\?\:\(\)\.\,\'\+\!\#\&\%\*\=\^\_\{\}\~\"\;\@\[\]]*$/i,
    message: " Please enter the correct character.",
    trigger: 'blur',
  },
  onlyLetters: {
    pattern: /^[a-zA-Z]*$/i,
    message: " Only input the letter(s).",
    trigger: 'blur',
  },
  email: {
    pattern: /^[a-zA-Z0-9\\\/\|\-\?\:\(\)\.\,\'\+\!\#\&\%\*\=\^\_\{\}\~\"\;\$\[\]\<\>]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*(\.[a-zA-Z0-9]{2,6})?$/,
    message: ' Please enter the correct email',
    trigger: 'blur',
  },
  number: (num, scale) => {
    let reg;
    if (!scale || scale == 0) {
      reg = RegExp(`^(0|([1-9][0-9]{0,${num}}))$`);
    } else {
      reg = RegExp(`^(0|([1-9][0-9]{0,${num}}))(?:\\.[0-9]{0,${scale}})?$`);
    }
    return {
      pattern: reg,
      message: ' Please enter the correct data',
      trigger: 'blur'
    }
  },
  required: {
    required: true,
    message: ' Cannot be null',
    trigger: 'blur'
  },
  notRequired: {
    required: false,
  },
  selectRequired: {
    required: true,
    message: ' Please select one', 
    trigger: 'change'
  },
  earlierEquCurDate: {
    validator: earlierEquCurDate,
    message: ' Must be earlier than or equal to the current date',
    trigger: 'blur'
  },
  earlierCurDate: {
    validator: earlierCurDate,
    message: ' Must be earlier to the current date',
    trigger: 'blur'
  },
  earlierEquCurProcDate: {
    validator: earlierEquCurProcDate,
    message: ' Must be earlier than or equal to the current date',
    trigger: 'blur'
  },
  earlierCurProcDate: {
    validator: earlierCurProcDate,
    message: ' Must be earlier to the current date',
    trigger: 'blur'
  },
  laterEquCurDate: {
    validator: laterEquCurDate,
    message: ' Must be later than or equal to the current date',
    trigger: 'blur'
  },
  laterEquCurProcDate: {
    validator: laterEquCurProcDate,
    message: ' Must be later than or equal to the current date',
    trigger: 'blur'
  },
  laterCurProcDate: {
    validator: laterCurProcDate,
    message: ' Must be later than the current date',
    trigger: 'blur'
  },
  earlierEquDt: (otherDt, otherDtLabel) => {
    return {
      validator: earlierEquDt,
      otherDt: otherDt,
      message: ' Must be earlier than or equal to ' + (otherDtLabel || 'Date To'),
      trigger: 'blur'
    }
  },
  requiredEndDate: (getEnd, customMessage = 'Please include the end date') => ({
    validator: (rule, value, callback) => {
      if (value && !getEnd()) {
        callback(new Error(customMessage));
      } else {
        callback();
      }
    },
    trigger: 'blur'
  }),

  laterDt: (otherDt, otherDtLabel) => {
    return {
      validator: earlierEquDt,
      otherDt: otherDt,
      message: ' cannot be later than ' + (otherDtLabel || 'Date To'),
      trigger: 'blur'
    }
  },
  laterEquDt: (otherDt, otherDtLabel) => {
    return {
      validator: laterEquDt,
      otherDt: otherDt,
      message: ' Must be later than or equal to ' + (otherDtLabel || 'Date To'),
      trigger: 'blur'
    }
  },
  diffDate: (diff, otherDt, otherDtLabel) => {
    return {
      validator: diffDate,
      otherDt: otherDt,
      diff: diff,
      message: ' Maximum date range = ' + diff + ' processing days to ' + otherDtLabel,
      trigger: 'blur'
    }
  },
  diffCurProcDate: (diff, otherDtLabel) => {
    otherDtLabel = otherDtLabel || "Start";
    return {
      validator: diffCurProcDate,
      diff: diff,
      message: ' ' + otherDtLabel + ' date could not be earlier than ' + diff + ' processing days.' ,
      trigger: 'blur'
    }
  },
  noReportTemplete: (diff) => {
    return {
      validator: noReportTemplete,
      diff: diff,
      message: 'Report Templete not defind' ,
      trigger: 'blur'
    }
  },
  maxHoldingDate: (diff) => {
    return {
      validator: maxHoldingDate,
      diff: diff,
      message: ' Date Of Holding must be within past '+diff+' days' ,
      trigger: 'blur'
    }
  },
  numberSize: (max, min) => {
    return {
      validator: numberSize,
      max:max,
      min:min,
      message: ((max || max === 0) ? (' Max value is ' + max) : "") + ((min || min === 0) ? (' Min value is ' + min) : ""),
      trigger: 'blur'
    }
  },
  name: {
    pattern: /^[a-zA-Z0-9\-\s\/\?\:\(\)\,\'\+\.]*$/,
    message: ' Please enter the correct value',
    trigger: 'blur',
  },
  nameIncChinese:{
    pattern: /^[a-zA-Z0-9\-\s\/\?\:\(\)\,\'\+\.\u4e00-\u9fa5]*$/,
    message: 'Please enter the correct value',
    trigger: 'blur',
  },
  nameForSearch: {
    pattern: /^[a-zA-Z0-9\-\%\s\/\?\:\(\)\,\'\+\.]*$/,
    message: ' Please enter the correct value',
    trigger: 'blur',
  },
  nameIncChineseForSearch:{
    pattern: /^[a-zA-Z0-9\-\%\s\/\?\:\(\)\,\'\+\.\u4e00-\u9fa5]*$/,
    message: 'Please enter the correct value',
    trigger: 'blur',
  },
  length: (min, max) => {
    return {
      min: min,
      max: max,
      message: ' Length should be ' + min + ' to ' + max,
      trigger: 'blur'
    }
  },
  // Record End Date must be entered if Start Date is entered.
  dateMustRange: (otherDt, lbl1, lbl2) => {
    lbl1 = lbl1 || "Start date";
    lbl2 = lbl2 || "End date";
    return {
      validator: dateMustRange,
      otherDt: otherDt,
      message: ' ' + lbl1 + ' and '+ lbl2 +' must be inputted together',
      trigger: 'blur'
    }
  },

  // Start CAP1-413 rencan 2025-02-21.
  //Record Date Range must be entered if Status is Entitle
  entitleMustHasRecordDate: (caStatus) => {
    return {
      validator: entitleMustHasRecordDate,
      caStatus: caStatus,
      message: 'Record Date Range must be entered if Status is Entitle',
      trigger: 'blur'
    }
  },
  // End CAP1-413 rencan 2025-02-21.

  // Start CAP1-413 rencan 2025-02-22.
  // Record Date Range must be within 30 calendar days if Status is Entitle
  entitleRecordDateRange: (caStatus, recordEnd,diff) => {
    return {
      validator: entitleRecordDateRange,
      caStatus: caStatus,
      recordEnd: recordEnd,
      diff: diff,
      message: 'Date range must be within 30 processing days',
      trigger: 'blur'
    }
  },
  // End CAP1-413 rencan 2025-02-22.

  // File Format must be PDF and language EN if sorting by CA Event Ref No.is selected.
  caRefNoFileFormat: (fileType, fileLang) => {
    return {
      validator: caRefNoFileFormat,
      fileType: fileType,
      fileLang: fileLang,
      message: 'File Format must be PDF and language EN if sorting by CA Event Ref No.is selected.',
      trigger: 'blur'
    }
  },
}

export const showValidateMsg = (details, fields) => {
  for (let key in fields) {
    for (let j = 0; j < fields[key].length; j++) {
      let detailFields = details.fields||details.value.fields;
      let msg = detailFields[fields[key][j].field]+ ":" + fields[key][j].message;
      if(fields[key][j].showLabelInd === "N"){
        msg = fields[key][j].message;
      }
      setTimeout(()=>{
        ElMessage({
          message: msg,
          type: 'error',
          duration: 10000,
          offset: 100,
          showClose: true,
        });
      }, 50);
    }
  }
}

export const anyOneValid = (formInline, anyOneFieldsAndMsg) => {
  if(anyOneFieldsAndMsg.anyOneFields && Object.keys(anyOneFieldsAndMsg.anyOneFields).length !== 0){
      for (let key in anyOneFieldsAndMsg.anyOneFields) {
          let valList = [];
          let fields = key.split("\|");
          for (let j = 0; j < fields.length; j++) {
              if(fields[j].includes("&")){
                  let dates = fields[j].split("\&");
                  const dateFrom = dates[0];
                  const dateTo = dates[1];
                  const keyValF = formInline[dateFrom];
                  const keyValT = formInline[dateTo];
                  if(keyValF && keyValT){
                      valList.push(keyValF);
                      valList.push(keyValT);
                  } else {
                      valList.push("");
                  }
              } else if ("reportAccounts" === fields[j]){
                  const reportAccounts = formInline[fields[j]];
                  if(!reportAccounts || (reportAccounts && reportAccounts.length === 0)){
                      valList.push("");
                  } else {
                      valList.push(reportAccounts.length);
                  }
              } else {
                  const keyVal = formInline[fields[j]];
                  if(keyVal){
                      valList.push(keyVal);
                  } else {
                      valList.push("");
                  }
              }
          }
          if(valList.every(item => !item)){
              setTimeout(()=>{
                  ElMessage({
                      dangerouslyUseHTMLString: true,
                      message: anyOneFieldsAndMsg.anyOneFields[key],
                      type: 'error',
                      duration: 10000,
                      offset: 100,
                      showClose: true,
                  });
              }, 50);
              return false;
          }
      }
  }
  return true;
}