<template>
    <BaseDetails ref="details" :handleSave="handleSave" :reload="props.reload">
        <el-tabs v-model="activeName" class="demo-tabs" @tab-click="tabsClick" type="card" >
            <el-tab-pane :label="$t('csscl.useradmin.func.roleAssignForFunc')" name="first">
                <el-space>
                    <el-text class="mx-1" style="width: 200px;font-weight: bold;">
                        {{ $t('csscl.useradmin.func.funcId') }}
                    </el-text>
                    <SearchInput v-model="rightId" url="/auth/api/v1/user/function/list"
                        @changeDesc="(val) => rightName = val" 
                        :disabled="formDisabled" 
                        style="width: 500px" 
                        maxlength="150"
                        inputStyle="width:200px"
                        :title="$t('csscl.useradmin.func.function')" 
                        :alt="$t('csscl.useradmin.func.funcId')"
                        :params="{}"
                        :columns="[
                            {
                                title: $t('csscl.useradmin.func.funcId'),
                                colName: 'rightId',
                            },
                            {
                                title: $t('csscl.useradmin.func.funcName'),
                                colName: 'rightDesc',
                            }
                        ]" :pageSizes="[10, 20, 30]">
                    </SearchInput>
                </el-space>
                <br />
                <br />
                <el-space alignment="flex-start" >
                    <el-text class="mx-1" style="width: 200px;font-weight: bold;">
                    </el-text>
                    <el-table @row-click="selectedFunc" :data="roleAssignFuncData" border table-layout="auto"
                        header-row-class-name="func-detail-table-header" highlight-current-row ref="funcTableRef"
                        style="height: 200px;border: 1px;">
                        <el-table-column prop="rightId" :label="$t('csscl.useradmin.func.funcId')" width="180" />
                        <el-table-column prop="rightName" :label="$t('csscl.useradmin.func.funcName')" width="250" />
                        <!-- Start SIR-HLH-R97, Tom.Li, 2024/08/20 -->
                        <el-table-column prop="recordStatus" :label="$t('csscl.useradmin.usr.recordStatus')" width="200">
                        <!-- End SIR-HLH-R97, Tom.Li, 2024/08/20 -->
                            <template #default="scope">
                                {{ getRecordStatusDesc(scope.row) }}
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-space direction="vertical">
                        <el-button :icon="Plus" size="small" @click="addFunc" :disabled="formDisabled"/>
                        <el-button :icon="Minus" size="small" @click="removeFunc" :disabled="formDisabled"/>
                    </el-space>
                </el-space>
                <br />
                <br />

                <DataGrid :showName="formartRole" ref="rightGridRef" :disabled="formDisabled" leftOid="roleId" rightOid="roleId" :leftData="(v)=>{roleFuncLeftData=v}">
                    <template #name>
                        {{ $t('csscl.useradmin.func.roleAssign') }}
                    </template>
                    <template #leftCols>
                        <el-table-column :label="$t('csscl.useradmin.func.availRole')">
                            <el-table-column prop="roleName" :label="$t('csscl.useradmin.func.role')" width="600">
                                <template #default="scope">
                                    {{ formartRole(scope.row) }}
                                </template>
                            </el-table-column>
                        </el-table-column>
                    </template>
                    <template #rightCols>
                        <el-table-column :label="$t('csscl.useradmin.func.assignTo')">
                            <el-table-column prop="roleName" :label="$t('csscl.useradmin.func.role')" width="600">
                                <template #default="scope">
                                    {{ formartRole(scope.row) }}
                                </template>
                            </el-table-column>
                            <!-- Start SIR-HLH-R97, Tom.Li, 2024/08/20 -->
                            <el-table-column prop="recordStatus" :label="$t('csscl.useradmin.usr.recordStatus')"
                                width="200">
                            <!-- End SIR-HLH-R97, Tom.Li, 2024/08/20 -->
                                <template #default="scope">
                                    {{ getRecordStatusDesc(scope.row) }}
                                </template>
                            </el-table-column>
                        </el-table-column>
                    </template>
                </DataGrid>
            </el-tab-pane>
            <el-tab-pane :label="$t('csscl.useradmin.func.roleAssignForReport')" name="second">
                <el-space>
                    <el-text class="mx-1" style="width: 200px;font-weight: bold;">
                        {{ $t('csscl.useradmin.func.reportId') }}
                    </el-text>
                    <GeneralSearchInput :disabled="formDisabled" v-model="reportCode" v-model:inpVal="reportCodeInpVal"
                        @changeDesc="(val) => reportName = val"
                        :alt="$t('csscl.useradmin.func.reportId')"
                        :title="$t('csscl.useradmin.func.report')"
                        inputStyle="width:200px"
                        style="width:500px"
                        searchType="reportTemplateCode"  />
                </el-space>
                <br />
                <br />
                <el-space alignment="flex-start">
                    <el-text class="mx-1" style="width: 200px;font-weight: bold;">
                    </el-text>
                    <el-table @row-click="selectedReport" :data="roleAssignReportData" border table-layout="auto"
                        header-row-class-name="func-detail-table-header" highlight-current-row ref="reportTableRef"
                        style="height: 200px;border: 1px;">
                        <el-table-column prop="reportTemplateCodeTxt" :label="$t('csscl.useradmin.func.reportId')" width="180" />
                        <el-table-column prop="reportDesc" :label="$t('csscl.useradmin.func.reportName')" width="250" />
                        <!-- Start SIR-HLH-R97, Tom.Li, 2024/08/20 -->
                        <el-table-column prop="recordStatus" :label="$t('csscl.useradmin.usr.recordStatus')" width="200">
                        <!-- End SIR-HLH-R97, Tom.Li, 2024/08/20 -->
                            <template #default="scope">
                                {{ getRecordStatusDesc(scope.row) }}
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-space direction="vertical">
                        <el-button :icon="Plus" circle size="small" @click="addReport" :disabled="formDisabled"/>
                        <el-button :icon="Minus" circle size="small" @click="removeReport" :disabled="formDisabled"/>
                    </el-space>
                </el-space>
                <br />
                <br />

                <DataGrid :showName="formartRole" ref="reportGridRef" :disabled="formDisabled" leftOid="roleId" rightOid="roleId" :leftData="(v)=>{roleReportLeftData = v}">
                    <template #name>
                        {{ $t('csscl.useradmin.func.roleAssign') }}
                    </template>
                    <template #leftCols>
                        <el-table-column :label="$t('csscl.useradmin.func.availRole')">
                            <el-table-column prop="menuPath" :label="$t('csscl.useradmin.func.role')" width="600">
                                <template #default="scope">
                                    {{ formartRole(scope.row) }}
                                </template>
                            </el-table-column>
                        </el-table-column>
                    </template>
                    <template #rightCols>
                        <el-table-column :label="$t('csscl.useradmin.func.assignTo')">
                            <el-table-column prop="menuPath" :label="$t('csscl.useradmin.func.role')" width="600">
                                <template #default="scope">
                                    {{ formartRole(scope.row) }}
                                </template>
                            </el-table-column>
                            <!-- Start SIR-HLH-R97, Tom.Li, 2024/08/20 -->
                            <el-table-column prop="recordStatus" :label="$t('csscl.useradmin.usr.recordStatus')" width="200">
                            <!-- End SIR-HLH-R97, Tom.Li, 2024/08/20 -->
                                <template #default="scope">
                                    {{ getRecordStatusDesc(scope.row) }}
                                </template>
                            </el-table-column>
                        </el-table-column>
                    </template>
                </DataGrid>
            </el-tab-pane>
        </el-tabs>
    </BaseDetails>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance, nextTick } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { ElButton, ElMessageBox, ElMessage } from 'element-plus';
import BaseDetails from '~/pages/base/Details.vue';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import {
    Edit,
    Delete,
    Plus,
    Minus,
    Back,
} from '@element-plus/icons-vue';
// Start SIR-HLH-R97, Tom.Li, 2024/08/20
import { getOid, currentPageInfo, randomHashCode, getRecordStatusDesc, rowCompareWithBeforeImage, saveMsgBox } from '~/util/Function.js';
// End SIR-HLH-R97, Tom.Li, 2024/08/20
import DataGrid from '../role/DataGrid.vue';
import { addCustValid, compListInCustValid, focusType } from "~/util/ModifiedValidate";
const { proxy } = getCurrentInstance();
const props = defineProps(['reload']);
const details = ref();
const rightGridRef = ref();
const reportGridRef = ref();
const activeName = ref('first')
const rightId = ref("");
const rightName = ref("");
const reportCode = ref("");
const reportCodeInpVal = ref("");
const reportName = ref("");
const roleAssignFuncData = ref([]);
const roleAssignFuncDataFromDB = ref([]);
const roleAssignFuncDataModify = ref([]);
const roleAssignReportData = ref([]);
const roleAssignReportDataFromDB = ref([]);
const roleAssignReportDataModify = ref([]);

const roleFuncLeftData = ref();
const roleReportLeftData = ref();

const formDisabled = ref(false);
const funcTableRef = ref();
const reportTableRef = ref();
let lightyellowTabs = [];
const initFuncsRole = {};
const initReportRole = {};
const editRow = (row, disabled,newId) => {
    const oid = newId || row?.currentOid;
    proxy.$axios.get("/auth/api/v1/user/roleassignlog/profile?sysRoleAssignLogOid=" + oid).then((body) => {
        if (body.success) {
            details.value.currentRow = {
                ...body.data,
                currentOid: oid,
            };
            roleAssignFuncDataFromDB.value = [...body.data.functions];
            roleAssignFuncData.value = [...body.data.functions];
            // first
            if (body.data.functions?.length) {
                roleAssignFuncData.value[0].master = body.data.sysRoleAssignLogOid;
                rowCompareWithBeforeImage(roleAssignFuncData.value[0], "tab-first", lightyellowTabs, ['recordStatus']);
            }
            roleAssignReportDataFromDB.value = [...body.data.reports];
            roleAssignReportData.value = [...body.data.reports];
            // second
            if (body.data.reports?.length) {
                roleAssignReportData.value[0].master = body.data.sysRoleAssignLogOid;
                rowCompareWithBeforeImage(roleAssignReportData.value[0], "tab-second", lightyellowTabs, ['recordStatus']);
            }

            roleAssignReportDataModify.value= [];
            roleAssignFuncDataModify.value= [];
            if(roleAssignFuncDataFromDB.value){
                //selectedFunc(roleAssignFuncDataFromDB.value[0]);
                setTimeout(() => {
                    let firstTr = funcTableRef.value.$el.querySelector("tbody tr");
                    if(firstTr){
                        firstTr.click();
                    }
                }, 150);
            }
            if(roleAssignReportDataFromDB.value) {
                setTimeout(() => {
                    let firstTr = reportTableRef.value.$el.querySelector("tbody tr");
                    if(firstTr){
                        firstTr.click();
                    }
                }, 150);
            }
            addCustValid(details.value.currentRow, ()=>{
              focusType.type = focusType.EnterObj;
              beforeSaveInit();
              let result = compListInCustValid(
                  roleAssignFuncData.value, // 当前 Function 列表
                  roleAssignFuncDataFromDB.value, // 当前 Function 列表的进入时的值
                  'sysRoleAssignFuncOid', // 列表的主键
                  ['rightId', 'recordStatus', 'mkckAction'] // 对象的字段名
              ); // Role Function
              if (!result) {
                if (rights.value) {
                  for (let key in rights.value) {
                    if(Object.keys(rights.value[key].modifyRecords).length > 0) {
                      return true;
                    }
                  }
                }
              } // Function Role
              if (!result) {
                result = compListInCustValid(
                    roleAssignReportData.value,
                    roleAssignReportDataFromDB.value,
                    'sysRoleAssignReportOid',
                    ['reportTemplateCode', 'recordStatus', 'mkckAction']
                ); // Role Report
              }
              if (!result) {
                if (reports.value) {
                  for (let key in reports.value) {
                    if(Object.keys(reports.value[key].modifyRecords).length > 0) {
                      return true;
                    }
                  }
                }
              }
              return result;
            });
        }
        details.value.initWatch({w1:rightId,w2:rights,w3:reports,w4:reportCode,w5:roleFuncLeftData,w6:roleReportLeftData});
    });
}

const beforeSaveInit = () => {
  if (selectedRight.value) {
    rights.value[selectedRight.value.rightId] = rightGridRef.value.getData();
  }
  if (selectedRpt.value) {
    reports.value[selectedRpt.value.reportTemplateCode] = reportGridRef.value.getData();
  }
}
const handleSave = async (searchValid, isOnlyValidate, unPopping) => {
    if (roleAssignFuncData.value.length == 0 && roleAssignReportData.value.length == 0) {
      return false;
    }
    beforeSaveInit();
    let modifyFunc = [];
    let modifyReport = [];
    if (rights.value) {
        for (let key in rights.value) {
            if (rights.value[key].modifyRecords) {
                modifyFunc.push.apply(modifyFunc, Object.values(rights.value[key].modifyRecords));
            }
        }
    }
    if (reports.value) {
        for (let key in reports.value) {
            if (reports.value[key].modifyRecords) {
                modifyReport.push.apply(modifyReport, Object.values(reports.value[key].modifyRecords));
            }
        }
    }
    if (isOnlyValidate) {
        return true;
    }
    //  && (modifyFunc.length > 0 || modifyReport.length > 0 || roleAssignReportDataModify.value.length > 0 || roleAssignFuncDataModify.value.length > 0)
    if (searchValid && await saveMsgBox(unPopping)) {
        if (details.value.currentRow && details.value.currentRow.sysRoleAssignLogOid) {
            const msg = await proxy.$axios.patch("/auth/api/v1/user/roleassignlog", {
                ...details.value.currentRow,
                reports: roleAssignReportDataModify.value,
                functions: roleAssignFuncDataModify.value,
                roleRightRels: modifyFunc,
                reportTempRels: modifyReport,
            });
            details.value.writebackId(msg.data);
            clearCache();
            editRow(null,null,msg.data);
            return msg.success;
        } else {
            const msg = await proxy.$axios.post("/auth/api/v1/user/roleassignlog", {
                ...details.value.currentRow,
                reports: roleAssignReportDataModify.value,
                functions: roleAssignFuncDataModify.value,
                roleRightRels: modifyFunc,
                reportTempRels: modifyReport,
            });
            details.value.writebackId(msg.data);
            clearCache();
            editRow(null,null,msg.data);
            return msg.success;
        }
    }
    return false;
}
const clearCache = () => {
    rightId.value = "";
    rightName.value = "";
    reportCode.value = "";
    reportName.value = "";
    roleAssignFuncDataModify.value = [];
    roleAssignReportDataModify.value = [];
    rights.value = {};
    reports.value = {};

    selectedRight.value = {};
    selectedRpt.value = {};
    lightyellowTabs = []

    roleAssignFuncDataFromDB.value = [];
    roleAssignFuncData.value = [];
    roleAssignReportDataFromDB.value = [];
    roleAssignReportData.value = [];
}
const showDetails = (row, isdoubleCheck) => {
    if(isdoubleCheck||row.recordStatus==='PA'){
        formDisabled.value = true;
        details.value.showDetails(row, true)
    }else{
        formDisabled.value = false;
        details.value.showDetails(row, false)
    }
	// Start SIR-HLH-R97, Tom.Li, 2024/08/20
    currentPageInfo.setEditPage();
	// End SIR-HLH-R97, Tom.Li, 2024/08/20
    clearCache();
    if (row.currentOid) {
        editRow(row, isdoubleCheck);
    }else{
        details.value.initWatch({w1:rightId,w2:rights,w3:reports,w4:reportCode,w5:roleFuncLeftData,w6:roleReportLeftData});
    }
}
const tabsClick = (tag) => {
    setTimeout(()=>{
        for(let i = 0; i < lightyellowTabs.length; i++) {
            document.querySelector( "#"+lightyellowTabs[i] ).className += " lightyellow";
        }
    }, 200);
}
defineExpose({
    details,
    editRow,
    showDetails,
});
// --------------------------------------------
const rights = ref({});
const reports = ref({});
const selectedRight = ref();
const formartRole = (row) => {
    return row.roleId + " - " + row.roleName;
}
const addFunc = async () => {
    if (rightId.value) {
        let rows = roleAssignFuncData.value.filter(function (item) {
            return item.rightId == rightId.value;
        });
        if (rows && rows.length > 0) {
            ElMessage({
                message: "The same record already exists!",
                type: 'error',
                duration: 10000,
                offset: 100,
                showClose: true,
            });
            return;
        }
        let oid = details.value.currentRow?.sysRoleAssignLogOid ? details.value.currentRow?.sysRoleAssignLogOid : "";
        const msg = await proxy.$axios.get("/auth/api/v1/user/role/pending?" +
            "sysRoleAssignLogOid=" + oid +
            "&rightId=" + rightId.value,
        );
        if (msg.success) {
            if (msg.data.length == 0) {
                roleAssignFuncData.value.push({
                    rightId: rightId.value,
                    rightName: rightName.value,
                    recordStatus: "PD",
                    mkckAction: "C",
                    sysRoleAssignFuncOid: Math.trunc(randomHashCode())
                });
                roleAssignFuncDataModify.value = roleAssignFuncDataModify.value.filter(function (item) {
                    return item.rightId != rightId.value;
                });
                let recs = roleAssignFuncDataFromDB.value.filter(function (item) {
                    return item.rightId == rightId.value;
                });
                if (recs.length == 0) {
                    roleAssignFuncDataModify.value.push({
                        rightId: rightId.value,
                        rightName: rightName.value,
                        mkckAction: "C",
                        recordStatus: "PD",
                    });
                }
                roleAssignFuncData.value = [...roleAssignFuncData.value];
                rightId.value = "";
                rightName.value = "";
                roleAssignFuncDataModify.value = [...roleAssignFuncDataModify.value];
            } else {
                ElMessage({
                    message: "There are pending records present.",
                    type: 'error',
                    duration: 10000,
                    offset: 100,
                    showClose: true,
                })
            }
        }
    }
}
const removeFunc = () => {
    let row = selectedRight.value;
    let recs = roleAssignFuncDataFromDB.value.filter(function (item) {
        return item.rightId == row.rightId;
    });
    if (recs.length > 0) {
        roleAssignFuncDataModify.value = roleAssignFuncDataModify.value.filter(function (item) {
            return item.rightId != row.rightId;
        });
        roleAssignFuncDataModify.value.push({
            ...recs[0],
            recordStatus: "D",
            mkckAction: "D",
        });
        roleAssignFuncDataModify.value = [...roleAssignFuncDataModify.value];
    } else {
        roleAssignFuncDataModify.value = [
            ...roleAssignFuncDataModify.value.filter(function (item) {
                return item.rightId != row.rightId;
            })
        ]
    }
    delete rights.value[row.rightId];
    selectedRight.value=null;
    rights.value = { ...rights.value };
    roleAssignFuncData.value = [
        ...roleAssignFuncData.value.filter(function (item) {
            return item.rightId != row.rightId;
        })
    ]
    rightGridRef.value.loadData(1, []);
    rightGridRef.value.loadData(2, []);
}
const selectedFunc = (row) => {
    if (selectedRight.value?.rightId) {
        rights.value[selectedRight.value.rightId] = rightGridRef.value.getData();
        rights.value = { ...rights.value };
    }
    selectedRight.value = row;
    if (rights.value[row.rightId]) {
        rightGridRef.value.setData(rights.value[row.rightId]);
    } else {
        proxy.$axios.post("/auth/api/v1/user/role/list", {
            param: {
                isApproved: "A",
                unRightId: row.rightId,
            },
            orderBy: "roleId-A",
            current: 1,
            pageSize: 999,
        }).then((body) => {
            if (body.success) {
                for (let j = 0; j < body.data.data.length; j++) {
                    body.data.data[j].rightId = row.rightId;
                }
                rightGridRef.value.loadData(1, body.data.data);
            }
        });
        proxy.$axios.post("/auth/api/v1/user/role/function/list", {
            isApproved: "P",
            rightId: row.rightId,
        }).then((body) => {
            if (body.success) {
                let roles = body.data;
                rightGridRef.value.loadData(2, roles);
            }
        });
    }
}

// ************************************************

const selectedRpt = ref();
const addReport = async () => {
    if (reportCode.value) {
        let rows = roleAssignReportData.value.filter(function (item) {
            return item.reportTemplateCode == reportCode.value;
        });
        if (rows && rows.length > 0) {
            ElMessage({
                message: "The same record already exists!",
                type: 'error',
                duration: 10000,
                offset: 100,
                showClose: true,
            });
            return;
        }
        let oid = details.value.currentRow?.sysRoleAssignLogOid ? details.value.currentRow?.sysRoleAssignLogOid : "";
        const msg = await proxy.$axios.get("/auth/api/v1/user/role/pending?" +
            "sysRoleAssignLogOid=" + oid +
            "&reportTemplateCode=" + reportCode.value,
        );
        if (msg.success) {
            if (msg.data.length == 0) {
                roleAssignReportData.value.push({
                    reportTemplateCode: reportCode.value,
                    reportTemplateCodeTxt: reportCodeInpVal.value,
                    reportDesc: reportName.value,
                    recordStatus: "PD",
                    mkckAction: "C",
                    sysRoleAssignFuncOid: Math.trunc(randomHashCode())
                });
                roleAssignReportDataModify.value = roleAssignReportDataModify.value.filter(function (item) {
                    return item.reportTemplateCode != reportCode.value;
                });
                let recs = roleAssignReportDataFromDB.value.filter(function (item) {
                    return item.reportTemplateCode == reportCode.value;
                });
                if (recs.length == 0) {
                    roleAssignReportDataModify.value.push({
                        reportTemplateCode: reportCode.value,
                        reportTemplateCodeTxt: reportCodeInpVal.value,
                        reportDesc: reportName.value,
                        recordStatus: "PD",
                        mkckAction: "C",
                    });
                }
                roleAssignReportData.value = [...roleAssignReportData.value];
                reportCode.value = "";
                reportCodeInpVal.value = "";
                reportName.value = "";
                roleAssignReportDataModify.value = [...roleAssignReportDataModify.value];

            } else {
                ElMessage({
                    message: "There are pending records present.",
                    type: 'error',
                })
            }
        }
    }
}
const removeReport = () => {
    let row = selectedRpt.value;
    let recs = roleAssignReportDataFromDB.value.filter(function (item) {
        return item.reportTemplateCode == row.reportTemplateCode;
    });
    if (recs.length > 0) {
        roleAssignReportDataModify.value = roleAssignReportDataModify.value.filter(function (item) {
            return item.reportTemplateCode != row.reportTemplateCode;
        });
        roleAssignReportDataModify.value.push({
            ...recs[0],
            recordStatus: "D",
            mkckAction: "D",
        });
        roleAssignReportDataModify.value = [...roleAssignReportDataModify.value];
    } else {
        roleAssignReportDataModify.value = [
            ...roleAssignReportDataModify.value.filter(function (item) {
                return item.reportTemplateCode != row.reportTemplateCode;
            })
        ]
    }
    delete reports.value[row.reportTemplateCode];
    selectedRpt.value = null;
    reports.value = { ...reports.value };
    roleAssignReportData.value = [
        ...roleAssignReportData.value.filter(function (item) {
            return item.reportTemplateCode != row.reportTemplateCode;
        })
    ]
    reportGridRef.value.loadData(1, []);
    reportGridRef.value.loadData(2, []);
}
const selectedReport = (row) => {
    if (selectedRpt.value?.reportTemplateCode) {
        reports.value[selectedRpt.value.reportTemplateCode] = reportGridRef.value.getData();
        reports.value = { ...reports.value };
    }
    selectedRpt.value = row;
    if (reports.value[row.reportTemplateCode]) {
        reportGridRef.value.setData(reports.value[row.reportTemplateCode]);
    } else {
        proxy.$axios.post("/auth/api/v1/user/role/list", {
            param: {
                isApproved: "A",
                unReportTemplateCode: row.reportTemplateCode,
            },
            orderBy: "roleId-A",
            current: 1,
            pageSize: 999,
        }).then((body) => {
            if (body.success) {
                for (let j = 0; j < body.data.data.length; j++) {
                    const ele = body.data.data[j];
                    ele.reportTemplateCode = row.reportTemplateCode;
                }
                reportGridRef.value.loadData(1, body.data.data);
            }
        });
        proxy.$axios.post("/auth/api/v1/user/role/report/list", {
            isApproved: "P",
            reportTemplateCode: row.reportTemplateCode,
        }).then((body) => {
            if (body.success) {
                let roles = body.data;
                reportGridRef.value.loadData(2, roles);
            }
        });
    }
}

</script>

<style>
.custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 13px;
    padding-right: 8px;
}

.func-detail-table-header th.ep-table__cell {
    background-color: #f5f7fa !important;
}
</style>