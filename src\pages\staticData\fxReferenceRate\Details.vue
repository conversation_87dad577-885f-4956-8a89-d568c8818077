<template>
  <BaseDetails ref="details" :handleSave="handleSave" :reload="reload" :viewOriginalForm="viewOriginalForm" :form="ruleForm" >
    <el-form :validateOnRuleChange="false" :disabled="formDisabled" ref="ruleFormRef" style="width: 100%" :model="ruleForm.form" :rules="rules" status-icon>
      <FormRow>
        <FormItemSign :detailsRef="details" :label="$t('common.title.opCtryRegionCode')" label-width="220"  prop="opCtryRegionCode">
          <CtryRegionSearchInput v-model="ruleForm.form.opCtryRegionCode" 
            showDesc="false" 
            style="width: 120px" 
            opCtryRegion />
        </FormItemSign>
      </FormRow>
      <FormRow>  
        <FormItemSign :detailsRef="details" :label="$t('csscl.fxRate.fromCurrency')" prop="currency" label-width="220" >
          <CurrencySearchInput v-model="ruleForm.form.currency" showDesc="false" style="width:120px" codeTitle="csscl.fxRate.fromCurrency" :disabled="editDis" />
        </FormItemSign>
        <FormItemSign :detailsRef="details" :label="$t('csscl.fxRate.toCurrency')" label-width="100"  prop="baseCurrency" >
          <CurrencySearchInput v-model="ruleForm.form.baseCurrency" showDesc="false" style="width:120px" codeTitle="csscl.fxRate.toCurrency" :disabled="editDis" />
        </FormItemSign>
        <ElFormItemProxy></ElFormItemProxy>
      </FormRow>
      <FormRow>  
        <FormItemSign :detailsRef="details" :label="$t('csscl.fxRate.rate')" prop="rate" label-width="220" >
          <InputNumber v-model="ruleForm.form.rate" style="width: 120px;" precision="8" scale="6"  />
        </FormItemSign>
        <FormItemSign :detailsRef="details" :label="$t('csscl.fxRate.rateDate')" label-width="100" prop="rateDate">
          <div style="position:relative">
          <DateItem v-model="ruleForm.form.rateDate" 
            style="width: 150px"
            :clearable="false"
            :disabled="rateDateDisabled"
            />
          </div>
        </FormItemSign>
        <FormItemSign :detailsRef="details" :label="$t('csscl.fxRate.dataSource')" label-width="100" prop="sourceSys">
          <Select v-model="ruleForm.form.sourceSys" style="width: 120px" type="FX_DATA_SOURCE" />
        </FormItemSign>
      </FormRow>
      <FormRow>  
        <FormItemSign :detailsRef="details" :label="$t('common.title.status')" prop="status" label-width="220" >
          <Select v-model="ruleForm.form.status" style="width: 120px" type='STATUS' />
        </FormItemSign>
      </FormRow>
    </el-form>
  </BaseDetails>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue';
import type { FormInstance, FormRules,ElMessage  } from 'element-plus'
import BaseDetails from '~/pages/base/Details.vue';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import CtryRegionSearchInput from '~/pages/base/CtryRegionSearchInput.vue';
import CurrencySearchInput from '~/pages/base/CurrencySearchInput.vue';
import { getOid, checkBeforeCurDt, saveMsgBox } from '~/util/Function.js';
import { commonRules, showValidateMsg } from '~/util/Validators.js';
import { timestampToDate } from '~/util/DateUtils.js';


const { proxy } = getCurrentInstance();
const props = defineProps(['reload']);
const details = ref();
const formDisabled = ref(false);
const editDis = ref(false);
const rateDateDisabled = ref(true);
const editRow = (row, disabled,newId) => {
  if(row?.isApproveDetail && disabled){
    ruleForm.form = row.afterImage;
    if(row.afterImage.rateDate){
      ruleForm.form.rateDate=timestampToDate(row.afterImage.rateDate);
    }
    details.value.currentRow = ruleForm.form;
  } else {
    const oid = newId || (row?.currentOid && getOid(row, disabled, proxy));
    if (oid) {
      proxy.$axios.get("/datamgmt/api/v1/fxrate?fxRefRateId="+oid).then((body) => {
          if(body.success) {
            body.data['rate'] = Number(body.data['rate']).toFixed(6)
            ruleForm.form = body.data;
              details.value.currentRow = body.data;

          }
          details.value.initWatch(ruleForm);
      });
      editDis.value = true;
    }else{
      details.value.initWatch(ruleForm);
    }
  }
  //Rate date = Today - 1
  const isRateDate = row?.rateDate
  if(!isRateDate){
    proxy.$axios.post("/datamgmt/api/v1/sysctrl/list",{param: {},
      current: 0,
      pageSize: 15 }).then((body) => {
      if(body.success) {
        if(body.data||body.data.data){
            const data = body.data.data[0]
            ruleForm.form.rateDate=timestampToDate(data.lastPrcsDate);
            rateDateDisabled.value = true
        }
      }else {
        rateDateDisabled.value = false
      }
    }).catch((err) => {
      rateDateDisabled.value = false
    });
  }
}

const viewOriginalForm = (pendingOid, isDisabled) => {
  formDisabled.value = isDisabled;
  proxy.$axios.get("/datamgmt/api/v1/fxrate?fxRefRateId="+pendingOid).then((body) => {
        if(body.success) {
            ruleForm.form = body.data;
        }
    });
}

const handleSave = async (searchValid, isOnlyValidate, unPopping) => {
  let result = await ruleFormRef.value.validate((valid, fields) => {
      if (valid) {

      } else {
        showValidateMsg(details, fields);
      }
  });
    if (isOnlyValidate) {
        return result;
    }
    const sourceSys = ruleForm.form.sourceSys
    if("MANUAL"!==sourceSys){
      ElMessage({
        message: 'Data Source must be MANUAL',
        type: 'error',
        duration: 2500,
        offset: 100,
        showClose: true,
      });
      return false;
    }
  if (result && searchValid && await saveMsgBox(unPopping)) {
      if (ruleForm.form.fxRefRateOid) {
          const msg = await proxy.$axios.patch("/datamgmt/api/v1/fxrate", {
              ...ruleForm.form,
          });
          details.value.writebackId(msg.data);
          editRow(null,null,msg.data);
          return msg.success;
      } else {
          const msg = await proxy.$axios.post("/datamgmt/api/v1/fxrate", {
              ...ruleForm.form,
          });
          details.value.writebackId(msg.data);
          editRow(null,null,msg.data);
          return msg.success;
      }
  }
  return false;
}
const showDetails = (row, isdoubleCheck) => {
    if(isdoubleCheck||row.recordStatus==='PA'){
        formDisabled.value = true;
    }else{
        formDisabled.value = false;
    }

    details.value.showDetails(row, formDisabled.value)

    ruleForm.form = {};
    details.value.currentRow={};
    editDis.value = false;
    editRow(row, isdoubleCheck);
}
defineExpose({
  details,
  editRow,
  showDetails,
});
// --------------------------------------------
interface RuleForm {
  opCtryRegionCode: String
  rate: String
  rateDate: String
  sourceSys: String
  status: String
}
const ruleFormRef = ref()
const ruleForm = reactive({
  form: {
    opCtryRegionCode: "",
    rate: "",
    rateDate: "",
    sourceSys: "",
    status: "",
  }
})

const rules = reactive<FormRules<RuleForm>>({
  opCtryRegionCode: [
    commonRules.required,
  ],
  baseCurrency: [
    commonRules.required,
  ],
  currency: [
    commonRules.required,
  ],
  rate: [
    commonRules.required,
  ],
  rateDate: [
    commonRules.required,
  ],
  sourceSys: [
    commonRules.selectRequired,
  ],
  status: [
    commonRules.selectRequired,
  ],
})

</script>

<style></style>