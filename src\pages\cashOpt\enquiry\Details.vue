<template>
    <BaseDetails ref="details" :handleSave="handleSave" :showFooter ="false" :reload="props.reload">
        <el-form :validateOnRuleChange="false" :disabled="formDisabled" ref="ruleFormRef" style="width: 100%" :model="ruleForm.form" status-icon>
            <FormRow>
                <FormItemSign :detailsRef="details" :label="$t('csscl.cashopt.enquiry.tranDateFrom')" label-width="180px"
                    prop="tranDateFrom">
                    <DateItem v-model="ruleForm.form.tranDateFrom" type="date" disabled style="width: 150px" />
                </FormItemSign>
                <FormItemSign :detailsRef="details" :label="$t('csscl.cashopt.enquiry.tranDateTo')" label-width="50px"
                    prop="tranDateTo">
                    <DateItem v-model="ruleForm.form.tranDateTo" type="date" disabled style="width: 150px" />
                </FormItemSign>

                <FormItemSign :detailsRef="details" :label="$t('csscl.cashopt.enquiry.type')" prop="accountStatus"
                    label-width="80px">
                    <Select v-model="ruleForm.form.type" style="width: 240px" :hideEmpty="true" :source="typeOptions" />
                </FormItemSign>
                <FormItemSign></FormItemSign>
                <FormItemSign></FormItemSign>
            </FormRow>
            <FormRow>
                <div style="padding: 6px;overflow: auto;height: 100%;">
                    <el-col :span="9.99" :offset="14.01" style="padding:0px;">
                        <el-space style="float:right;">
                            <el-button type="primary" @click="handleSearch">
                                Search
                            </el-button>
                        </el-space>
                    </el-col>
                </div>
            </FormRow>
            
        </el-form>
        <el-text tag="B" style="font: 14px bold;">{{  $t("csscl.cashopt.enquiry.balTitle") }}</el-text>
        <el-divider />
        <el-form :validateOnRuleChange="false" disabled>
            <div style="background-color: #d9ecff;" disabled>
                <FormRow >
                    <FormItemSign :detailsRef="details" :label="$t('csscl.cashopt.enquiry.cashAccNum')" prop="ruleForm.form.cashAccountNo"><el-input v-model="ruleForm.form.cashAccountNo"/></FormItemSign>
                    <!-- <FormItemSign :detailsRef="details" :label="$t('csscl.cashopt.enquiry.cashAccName')" prop="ruleForm.form.cashAccountName"><el-input v-model="ruleForm.form.cashAccountName" style="width: 400px;"/></FormItemSign> -->
                    <FormItemSign :detailsRef="details" :label="$t('csscl.cashopt.enquiry.cashAccName')" prop="ruleForm.form.balForm.r2AcName"><el-input v-model="ruleForm.form.balForm.r2AcName" style="width: 400px;"/></FormItemSign>
                </FormRow>
                <FormRow diabled>
                    <FormItemSign :detailsRef="details" :label="$t('csscl.cashopt.enquiry.parentBankAcctNum')" prop="ruleForm.form.parentBankAccountNo"><el-input v-model="ruleForm.form.parentBankAccountNo" /></FormItemSign>
                    <FormItemSign :detailsRef="details" :label="$t('csscl.cashopt.enquiry.currency')" prop="ruleForm.form.currency"><el-input v-model="ruleForm.form.currency" /></FormItemSign>
                </FormRow>
            </div>
        </el-form>
        <balDetail v-if="showBal" :details="details" :ruleForm="ruleForm" />
        <txnDetail v-if="showTxn" :details="details" :ruleForm="ruleForm" />

    </BaseDetails>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance, defineProps } from 'vue';
import type { FormInstance } from 'element-plus'
import BaseDetails from '~/pages/base/Details.vue';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import { ElMessageBox } from 'element-plus';
import { getSysCtrlDate } from '~/util/Function.js';

import balDetail from "./balDetail.vue";
import txnDetail from "./txnDetail.vue";
import moment from 'moment';


const { proxy } = getCurrentInstance();
const props = defineProps(['reload']);
const details = ref();

const formDisabled = ref(false);
const showBal = ref(false);
const showTxn = ref(false);
const ruleFormRef = ref({});

const ruleForm = reactive({
    form: {
        balForm: {

        },
        txnForm: {

        },
        tranDateFrom: '',
        tranDateTo: '',
        type: "B",
        fromTxnNo: "0001",
    }
});



const typeOptions = [
    {
        code: 'B',
        codeDesc: 'Balance',
    },
    {
        code: 'T',
        codeDesc: 'Transaction',
    },
]




const editRow = (row) => {

    if (row?.clientAccountOid) {
        proxy.$axios.get("/datamgmt/api/v1/account?objectId=" + row.clientAccountOid + "&modeEdit=Y").then((body) => {
            if (body.success) {
                Object.assign(ruleForm.form, body.data);
            }
        });
    }
}
const handleSearch = async () => {
    let result = await ruleFormRef.value.validate((valid, fields) => {
        if (valid) {

        } else {
            console.log('error submit!', fields)
        }
    });
    
    let parentBankAccountNo = ruleForm.form.parentBankAccountNo;
    if(!parentBankAccountNo || parentBankAccountNo === undefined){
        ElMessageBox.alert("Parent Bank Account Number cannot be Empty.", 'Warning');
        return;
    }

    if (result) {
        showBal.value = false;
        showTxn.value = false;
        if (ruleForm.form.type === 'B') {
            const msg = await proxy.$axios.post("/eapmgmt/api/v1/boc/balance", {
                ...ruleForm.form,
            }).then((body) => {
                if(body && body.respBody){
                    Object.assign(ruleForm.form.balForm, body.respBody);
                    showBal.value = true;
                }

                if(body && body?.respHeader?.respStatus == '01' && body.respError){
                    let errorMsg = body.respError?.errAddMsg['>C0000001Msg'];
                    if (!errorMsg) {
                        errorMsg = body.respError?.errMsg;
                    }
                    ElMessageBox.alert(errorMsg, 'Warning');
                }
            });
            //return msg.success;
        } else if (ruleForm.form.type === 'T') {
            ruleForm.form.fromTxnNo = "0001";
            const msg = await proxy.$axios.post("/eapmgmt/api/v1/boc/txn", {
                ...ruleForm.form,
            }).then((body) => {
                if(body && body.respBody){
                    if(body.respBody.total && body.respBody.total > 68){
                        showTxn.value = false;
                        ElMessageBox.alert("If over 4 pages, prompt message to remind user to use report to view transactions.", 'Warning');
                        return;
                    }
                    Object.assign(ruleForm.form.txnForm, body.respBody);
                    showTxn.value = true;
                }
                if(body && body?.respHeader?.respStatus == '01' && body.respError){
                    let errorMsg = body.respError?.errAddMsg['>C0000001Msg'];
                    if (!errorMsg) {
                        errorMsg = body.respError?.errMsg;
                    }
                    ElMessageBox.alert(errorMsg, 'Warning');
                }
            });
            //return msg.success;
        } 
    }
    //return false;
}

const handleSave = async () => {

}


const showDetails =async (row, disabled) => {
    formDisabled.value = disabled;
    showBal.value = false;
    showTxn.value = false;
    details.value.showDetails(row, disabled)
    let msg = await getSysCtrlDate();
    ruleForm.form.tranDateFrom = msg;
    ruleForm.form.tranDateTo = msg;
    if (row?.parentBankAccountNo) {
        //ruleForm.form.accNo = row.cashAccountNo;
        ruleForm.form.accNo = row.parentBankAccountNo;
    }else {
        //tips : No such bank account in CBL
    }
    
    //each time inquiry need clear last info
    ruleForm.form.balForm={};
    ruleForm.form.txnForm={};
    // add default val
    // if(row?.accType == 'internal'){
    //     ruleForm.form.balForm.r2AcName = row?.cashAccountName;
    // }
    

    Object.assign(ruleForm.form, row);
    // editRow(row);
    proxy.$axios.post("/cashmgmt/api/v1/cash/operation/baltxn/recordEnquiryLog", row);
}

const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate((valid, fields) => {
        if (valid) {
            console.log('submit!')
        } else {
            console.log('error submit!', fields)
        }
    })
}

const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.resetFields()
}

const options = Array.from({ length: 10000 }).map((_, idx) => ({
    value: `${idx + 1}`,
    label: `${idx + 1}`,
}));

defineExpose({
    details,
    editRow,
    showDetails,
});
</script>

<style></style>