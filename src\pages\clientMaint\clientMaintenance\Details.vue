<template> 
    <BaseDetails ref="details" :handleSave="handleSave" :reload="props.reload" :viewOriginalForm="viewOriginalForm" :form="ruleForm">
        <el-form :validateOnRuleChange="false" :disabled="formDisabled" ref="ruleFormRef" style="width: 100%" :model="ruleForm.form" :rules="rules" status-icon>
            <FormRow>
                <FormItemSign :detailsRef="details" :label="$t('common.title.opCtryRegionCode')" prop="opCtryRegionCode">
                    <CtryRegionSearchInput v-model="ruleForm.form.opCtryRegionCode" 
                        showDesc="false"
                        opCtryRegion :disabled="true" :readonly="true" />
                </FormItemSign>
                <FormItemSign :detailsRef="details"></FormItemSign>
                <FormItemSign :detailsRef="details"></FormItemSign>
            </FormRow>
            <FormRow>
                <FormItemSign :detailsRef="details" :label="$t('csscl.clientManagement.clientCode')" prop="clientCode">
                    <el-input v-model="ruleForm.form.clientCode" disabled />
                </FormItemSign>
                <FormItemSign :detailsRef="details"></FormItemSign>
                <FormItemSign :detailsRef="details" :label="$t('csscl.clientManagement.accountStatus')" prop="status">
                    <Select v-model="ruleForm.form.status" type="ACCOUNT_STATUS" disabled />
                </FormItemSign>
            </FormRow>
            <FormRow>
                <FormItemSign :detailsRef="details" :label="$t('csscl.clientManagement.clientNameLine1')">
                    <el-input v-model="clientName" style="width:350px" class="text-none" disabled />
                </FormItemSign>
                <FormItemSign :detailsRef="details" :label="$t('csscl.clientManagement.clientType')" prop="clientType">
                    <Select v-model="ruleForm.form.clientType" style="width:240px" type="CLIENT_TYPE_CODE" disabled />
                </FormItemSign>
                <FormItemSign :detailsRef="details" :label="$t('csscl.clientManagement.accountCloseDate')" prop="closeDate">
                    <DateItem v-model="ruleForm.form.closeDate" disabled />
                </FormItemSign>
            </FormRow>
            <FormRow>
                <FormItemSign :detailsRef="details" :label="$t('csscl.clientManagement.clientShortName')" prop="clientShortName">
                    <el-input v-model="ruleForm.form.clientShortName" style="width:300px" class="text-none" disabled />
                </FormItemSign>
                <FormItemSign :detailsRef="details"></FormItemSign>
                <FormItemSign :detailsRef="details" :label="$t('csscl.ftgManagement.inheritTypeInd')" prop="sourceSys">
                    <el-input v-model="ruleForm.form.sourceSys" disabled />
                </FormItemSign>
            </FormRow>
            <FormRow>
                <FormItemSign :detailsRef="details" :label="$t('csscl.clientManagement.bankCode')" prop="bankCode">
                    <Select v-model="ruleForm.form.bankCode" vkEnqual type="BANK_CODE" disabled />
                </FormItemSign>
                <FormItemSign :detailsRef="details" :label="$t('csscl.clientManagement.AECode')" prop="aeCode">
                    <Select v-model="ruleForm.form.aeCode" vkEnqual type="AE_CODE" disabled />
                </FormItemSign>
                <FormItemSign :detailsRef="details" :label="$t('csscl.clientManagement.ctryRegionCode')" prop="ctryRegionCode">
                    <CtryRegionSearchInput v-model="ruleForm.form.ctryRegionCode" showDesc="false" disabled />
                </FormItemSign>
            </FormRow>
            <!-- <FormRow>
                <FormItemSign :detailsRef="details" :label="$t('csscl.clientManagement.investorTypeCode')" prop="investorTypeCode">
                    <GeneralSearchInput v-model="ruleForm.form.investorTypeCode" searchType="clientCode" showDesc="true" style="width: 295px;"/>
                </FormItemSign>
                <FormItemSign :detailsRef="details" :label="$t('csscl.clientManagement.taxCtryIsoCode')" prop="taxCtryIsoCode">
                    <GeneralSearchInput v-model="ruleForm.form.taxCtryIsoCode"  searchType="clientCode" showDesc="false" style="width: 110px;"/>
                </FormItemSign>
                <ElFormItemProxy></ElFormItemProxy>
            </FormRow> -->
            <FormRow>
              <FormItemSign :detailsRef="details" :label="$t('csscl.si.common.omnibusSegregate')" prop="omnibusSegregate">
                <Select v-model="ruleForm.form.omnibusSegregate" type='OMNIBUS_SEGREGATE' style="width: 125px"/>
              </FormItemSign>
                <FormItemSign :detailsRef="details" :label="$t('csscl.acctCode.custodyLevel')"  prop="custodyLevel">
                    <Select v-model="ruleForm.form.custodyLevel" type="CUSTODY_LEVEL" style="width: 170px;" @update:modelValue="changeCustodyLevel" />
                </FormItemSign>
                <FormItemSign :detailsRef="details" :label="$t('csscl.acctCode.custodyMarket')"  prop="custodyMarket">
                    <!-- <GeneralSearchInput v-model="ruleForm.form.custodyMarket" style="width: 280px"
                        showDesc="true"
                        maxlength="11"
                        searchType="custodyMarket"
                        :params="getCustodyMarketParams()"
                        :disabled="ruleForm.form.custodyLevel === 'GC'" /> -->
                    <CommonSearchInput v-model="ruleForm.form.custodyMarket"
                    commType="CUSTODY_MARKET"
                    url="/datamgmt/api/v1/searchinput"
                    :params="getCustodyMarketParams()"
                    codeTitle="csscl.acctCode.custodyMarket"
                    style="width:280px"
                    :disabled="ruleForm.form.custodyLevel === 'GC'" />
                </FormItemSign>
            </FormRow>
            <FormRow>
                <ElFormItemProxy></ElFormItemProxy>
                <ElFormItemProxy></ElFormItemProxy>
                <ElFormItemProxy></ElFormItemProxy>
            </FormRow>
        </el-form>

        <span style="font-size: 16px; font-weight: bold;padding-top:10px">{{  $t("csscl.clientManagement.custAcc") }}</span>
        <div style="paddingBlock:5px;width: 100%;display: flex;flex-flow: wrap;border-top: 1px solid;"></div> 

        <Grid url="/datamgmt/api/v1/account/client/list"
            :params="{ clientMasterOid: ruleForm.form.pendingOid ? ruleForm.form.pendingOid : ruleForm.form.clientMasterOid }" 
            :columns="[
                {title:'csscl.clientManagement.custAccNum',name:'tradingAccountCode', },
                {title:'csscl.clientManagement.custAccName',name:'accountName1', },
                {title:'csscl.clientManagement.custAccShortName',name:'accountShortName', },
                {title:'csscl.clientManagement.accOpenDt',name:'dateOpen', },
                {title:'csscl.clientManagement.accountCloseDate',name:'dateClose', },
                {title:'csscl.clientManagement.accountStatus',name:'accountStatus', fn:commDesc('ACCOUNT_STATUS') },
            ]"
            />
    </BaseDetails>
</template>

<script lang="ts" setup>
import { ref, reactive, getCurrentInstance, computed } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { commonRules, showValidateMsg } from '~/util/Validators.js';
import BaseDetails from '~/pages/base/Details.vue';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import CtryRegionSearchInput from '~/pages/base/CtryRegionSearchInput.vue';
import CommonSearchInput from '~/pages/base/CommonSearchInput.vue';
import Grid from '~/pages/base/Grid.vue';
import  { commDesc, saveMsgBox, getOid, showErrorMsg } from '~/util/Function.js';

const { proxy } = getCurrentInstance();
const props = defineProps(['reload']);
const details = ref();
const formDisabled = ref(false);
// Start, LiKunBiao, 2025/07/28
const clientName = computed(() => {
  return ruleForm.form.clientName1 + " " + ruleForm.form.clientName2
})
// End, LiKunBiao

const isViewOriginal = ref(false);

const editRow = (row, disabled, newId) => {
    if (row?.isApproveDetail && disabled) {
        ruleForm.form = row?.afterImage;
        details.value.currentRow = ruleForm.form;
    } else {
        const oid = newId || (row?.currentOid && getOid(row, disabled, proxy));
        if (oid) {
             proxy.$axios.get("/datamgmt/api/v1/client?objectId=" + oid).then((body) => {
                if(body.success) {
                    details.value.currentRow = body.data;
                    ruleForm.form = body.data;
                }
                details.value.initWatch(ruleForm);
            });
        } else {
            details.value.initWatch(ruleForm);
        }
    }
}
const handleSave = async (searchValid, isOnlyValidate, unPopping) => {
    let result = await ruleFormRef.value.validate((valid, fields) => {
        if (valid) {
        } else {
            showValidateMsg(details, fields);
        }
    });
    let customValidResult = true;
    if (ruleForm.form.custodyLevel === "GC") {
        if (ruleForm.form.custodyMarket) {
            showErrorMsg("Custody market is only for local custody");
            customValidResult = false;
        }
    } else {
        if (!ruleForm.form.custodyMarket) {
            showErrorMsg("Custody market must be selected for Local Custody accounts");
            customValidResult = false;
        }
    }
    result = result && customValidResult;
    if (isOnlyValidate) {
        return result;
    }
    if (result && searchValid && await saveMsgBox(unPopping)) {
        if (ruleForm.form.clientMasterOid) {
            const msg = await proxy.$axios.patch("/datamgmt/api/v1/client", {
                ...ruleForm.form,
            });
            details.value.writebackId(msg.data);
            editRow(null, false, msg.data);
            return msg.success;
        } else {
            const msg = await proxy.$axios.post("/datamgmt/api/v1/client", {
                ...ruleForm.form,
            });
            details.value.writebackId(msg.data);
            editRow(null, false, msg.data);
            return msg.success;
        }
    }
    return false;
}

const showDetails = (row, isdoubleCheck) => {

    isViewOriginal.value = false;

 if(isdoubleCheck||row.recordStatus==='PA'){
      formDisabled.value = true;
      details.value.showDetails(row, true)
  }else{
      formDisabled.value = false;
      details.value.showDetails(row, false)
  }

      ruleForm.form = {};
  details.value.currentRow={};
  editRow(row, isdoubleCheck,null);
}

const setIsViewOriginal = () => {
    isViewOriginal.value = true;
}

// 动态参数构造参数
const getCustodyMarketParams = () => {
    if (isViewOriginal.value) {
       return JSON.stringify({ "searchType": "custodyMarketAll", "status": null });
    }
    else{
        return JSON.stringify({ "searchType": "custodyMarket", "status": null });
    }
}

const changeCustodyLevel = (newVal: string) => {
    if (newVal === "GC" && ruleForm.form.custodyMarket) {
        ruleForm.form.custodyMarket = "";
    }
}

const viewOriginalForm = (pendingOid, isDisabled) => {

    // 设置为ViewOriginal,用于组件的参数标记
    setIsViewOriginal();
    
    formDisabled.value = isDisabled;
    proxy.$axios.get("/datamgmt/api/v1/client?objectId=" + pendingOid).then((body) => {
        if (body.success) {
            ruleForm.form = body.data;
            details.value.currentRow.value = body.data;
        }
    });
}

defineExpose({
    details,
    editRow,
    showDetails,

});
// --------------------------------------------

interface RuleForm {
    custodyLevel: string,
    omnibusSegregate: string
}

const ruleFormRef = ref()
const ruleForm = reactive({
    rules: () => { return [{ rules: rules }] },
    form: {
        clientName1: "",
        clientName2: "",
        custodyLevel: "",
        custodyMarket: "",
        omnibusSegregate: ""
    }
});

const rules = reactive<FormRules<RuleForm>>({
    custodyLevel: [
        commonRules.required
    ],
    omnibusSegregate: [
        commonRules.required
    ],
})

const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate((valid, fields) => {
        if (valid) {
            console.log('submit!')
        } else {
            console.log('error submit!', fields)
        }
    })
}

const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.resetFields()
}

const options = Array.from({ length: 10000 }).map((_, idx) => ({
    value: `${idx + 1}`,
    label: `${idx + 1}`,
}))

</script>

<style></style>