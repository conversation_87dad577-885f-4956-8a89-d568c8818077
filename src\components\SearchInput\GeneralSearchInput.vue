<template #default>

  <SearchInput 
        url="/datamgmt/api/v1/searchinput"
        ref="searchInput"
        :title="$t(title())"
        :style="style()"
        :showDesc="showDesc"
        text-none
        :params="Object.assign({searchType:searchType()}, params(), props.params)"
        :searchType="searchType()"
        :maxlength="maxlength()"
        :columns="[
          { title: $t(codeTitle()), width: codeWidth(), colName: codeColName(), },
          { title: $t(codeDescTitle()), width: codeDescWidth(), colName: codeDescColName(), }
        ]"
        />
        
</template>

<script lang="ts" setup>
import { ref, watch, computed, defineEmits } from 'vue';
import  { parseBool } from '~/util/Function.js';

const props = defineProps([ 'title', 'codeTitle', 'style', 'showDesc', 'codeDescTitle', 'searchType', 'params', 'maxlength' ]);
const searchInput = ref();

const style = () => {
  return props.style ||  titleDesc[props.searchType]?.style;
}

const title=()=>{
  return props.title || titleDesc[props.searchType]?.title || props.codeTitle || titleDesc[props.searchType]?.code;
}

const codeTitle=()=>{
  return props.codeTitle || titleDesc[props.searchType]?.code;
}

const codeDescTitle=()=>{
  return props.codeDescTitle || titleDesc[props.searchType]?.codeDesc;
}

const codeColName=()=>{
  return titleDesc[props.searchType]?.codeColName || 'code';
}

const codeDescColName=()=>{
  return titleDesc[props.searchType]?.codeDescColName || 'codeDesc';
}

const codeWidth=()=>{
  return titleDesc[props.searchType]?.codeWidth || 300;
}

const codeDescWidth=()=>{
  return titleDesc[props.searchType]?.codeDescWidth || 430;
}

const searchType=()=>{
  return titleDesc[props.searchType]?.searchType || props.searchType;
}

const maxlength = () => {
  return props.maxlength || titleDesc[props.searchType]?.maxlength;
}

const params = () => {
  return titleDesc[props.searchType]?.params;
}

const titleDesc = {
  "currency":{
    maxlength:3,
    code:"csscl.currencyManagement.currencyCode",
    codeDesc:"csscl.currencyManagement.descpt",
    style:"width:110px;"
  },
  "ctryRegion":{
    maxlength:3,
    code:"csscl.ctryRegionManagement.ctryRegionCode",
    codeDesc:"csscl.ctryRegionManagement.descpt",
    style:"width:110px;"
  },
  "opCtryRegion":{
    searchType:"ctryRegionALL",
    maxlength:3,
    code:"common.title.opCtryRegionCode",
    codeDesc:"csscl.ctryRegionManagement.descpt",
    style:"width:110px;"
  },
  "ctryRegionALL":{
    maxlength:10,
    code:"csscl.ctryRegionManagement.ctryRegionCode",
    codeDesc:"csscl.ctryRegionManagement.descpt",
    style:"width:110px;"
  },
  "custodyAcct":{
    maxlength:18,
    code:"csscl.acctCode.custodyAcctNumber",
    codeDesc:"csscl.acctCode.accountShortName",
    style:"width:300px;",
    params:{ status: null }
  },
  "clearingAgentCode":{
    maxlength:10,
    code:"csscl.agent.clearingAgentCode",
    codeDesc:"csscl.agent.shortName",
    style:null,
  },
  "clearingAgentCodeAndAcc":{
    maxlength:10,
    code:"csscl.si.common.custodianAccountName",
    codeDesc:"csscl.agent.custoianAccountShortName",
    style:null,
  },
  "clientCode":{
    maxlength:11,
    code:"csscl.clientManagement.clientCode",
    codeDesc:"csscl.clientManagement.clientNameLine1",
    style:null,
    params:{ status: null }
  },
  "custodyMarket":{
    maxlength:11,
    code:"csscl.si.common.marketCode",
    codeDesc:"csscl.si.common.marketDesc",
    style:null,
    params:{ status: null }
  },
  "custodyMarketP1":{
    maxlength:20,
    code:"csscl.si.common.custodyMarket",
    codeDesc:"csscl.commonCode.description",
    style:null,
    params:{ status: null }
  },
  "exboardCode":{
    maxlength:20,
    code:"csscl.exchang.exchangeBoardCode",
    codeDesc:"csscl.exchang.exchangeBoardShortName",
  },
  "ftgidCode":{
    maxlength:20,
    code:"csscl.ftgManagement.ftgidCode",
    codeDesc:"csscl.ftgManagement.ftgidName",
    style:null,
  },
  "jobId":{
    maxlength:12,
    code:"csscl.jobScheduler.jobId",
    codeDesc:"csscl.jobScheduler.jobName",
    style:null,
  },
  "jobStatusId":{
    maxlength:12,
    code:"csscl.jobScheduler.jobId",
    codeDesc:"csscl.jobExec.jobNames",
    style:null,
  },
  "user":{
    maxlength:7,
    code:"csscl.useradmin.usr.userId",
    codeDesc:"csscl.useradmin.usr.userName",
    style:"width:300px",
  },
  "allUser":{
    searchType:"user",
    maxlength:7,
    code:"csscl.useradmin.usr.userId",
    codeDesc:"csscl.useradmin.usr.userName",
    style:"width:300px",
    params:{ status: null }
  },
  "functionId":{
    maxlength:30,
    code:"csscl.useradmin.func.funcId",
    codeDesc:"csscl.home.table.function",
    style:"width:380px",
  },
  "roleId":{
    maxlength:32,
    code:"csscl.useradmin.role.roleId",
    codeDesc:"csscl.useradmin.role.roleName",
    style:null,
    params:{ status: null },
  },
  "custodianAccNo":{
    maxlength:15,
    code:"csscl.acctCode.clearingAgentOid",
    codeDesc:"csscl.agent.clearingAgentCode",
    style:null,
  },
  "custodianAccNum":{
    maxlength:15,
    code:"csscl.agent.custodianAccountCode",
    codeDesc:"csscl.agent.custoianAccountShortName",
    style:null,
  },
  "siAgentCode":{
    maxlength:15,
    code:"csscl.acctCode.clearingAgentCode",
    codeDesc:"csscl.acctCode.custAcctNo",
    style:null,
  },
  "siAgentAccNum":{
    maxlength:15,
    searchType: "siAgentCode",
    codeColName: "codeDesc",
    codeDescColName: "code",
    code:"csscl.acctCode.custAcctNo",
    codeDesc:"csscl.acctCode.clearingAgentCode",
    style:null,
  },
  "exchangeCode":{
    maxlength:10,
    code:"csscl.exchang.exchangeCode",
    codeDesc:"csscl.exchang.exchangeShortName",
    style:null,
  },
  "reportTemplateCode":{
    maxlength:40,
    code:"csscl.reportCenter.reportTemplateCode",
    codeDesc:"csscl.reportCenter.reportDesc",
    style:"width:350px",
  },
  "allReportTemplateCode":{
    maxlength:40,
    code:"csscl.reportCenter.reportTemplateCode",
    codeDesc:"csscl.reportCenter.reportDesc",
    style:"width:350px",
  },
  "outDocFileType":{
    maxlength:40,
    code:"csscl.reportCenter.reportTemplateCode",
    codeDesc:"csscl.reportCenter.reportDesc",
    style:"width:350px",
  },
  "outDocFileTypeP1":{
    maxlength:40,
    code:"csscl.reportCenter.reportTemplateCode",
    codeDesc:"csscl.reportCenter.reportDesc",
    style:"width:350px",
  },
  "generatorRptId":{
    maxlength:40,
    code:"csscl.reportCenter.reportTemplateCode",
    codeDesc:"csscl.reportCenter.reportDesc",
    style:"width:350px",
  },
  "l000301ENReportId":{
    maxlength:40,
    code:"csscl.reportCenter.reportTemplateCode",
    codeDesc:"csscl.reportCenter.reportDesc",
    style:"width:350px",
  },
  "rptCenterReportId":{
    maxlength:40,
    code:"csscl.reportCenter.reportTemplateCode",
    codeDesc:"csscl.reportCenter.reportDesc",
    style:"width:350px",
  },
  "clientFundID":{
    maxlength:50,
    code:"csscl.acctCode.clientFundId",
    codeDesc:"csscl.acctCode.clientFundId",
    style:null,
  },
  "schedulerId":{
    maxlength:50,
    code:"csscl.reportScheduler.schedulerId",
    codeDesc:"csscl.reportCenter.search.reportDesc",
    style:null,
  },
  "funByUserid":{
    maxlength:50,
    code:"csscl.home.table.function",
    codeDesc:"csscl.useradmin.func.funcId",
    style:null,
  },
  "L000201ENFunctionIdReportId":{
    maxlength:30,
    code:"csscl.useradmin.func.funcId",
    codeDesc:"csscl.home.table.function",
    style:"width:380px",
  },
  "custodianCashAcc":{
    maxlength:30,
    code:"csscl.agent.cashAccountNumber",
    codeDesc:"csscl.acctCode.custodianCashAccOid",
    style:null,
  },
  "parentComm":{
    maxlength:50,
    code:"csscl.commonCode.code",
    codeDesc:"csscl.commonCode.description",
    style:null,
  },
  "tradingAccGroup":{
    maxlength:50,
    code:"csscl.commonCode.code",
    codeDesc:"csscl.commonCode.description",
    style:null,
  },
  "clientGroup":{
    maxlength:50,
    code:"csscl.commonCode.code",
    codeDesc:"csscl.commonCode.description",
    style:null,
  },
  "fundManager":{
    maxlength:50,
    code:"csscl.commonCode.code",
    codeDesc:"csscl.commonCode.description",
    style:null,
  },
  "instrumentCode":{
    maxlength:50,
    code:"csscl.commonCode.code",
    codeDesc:"csscl.commonCode.description",
    style:null,
  },
  "swiftEventType":{
    maxlength:50,
    code:"csscl.commonCode.code",
    codeDesc:"csscl.commonCode.description",
    style:null,
  },
  "swiftEventCodeCIB002":{
      maxlength:50,
      code:"csscl.commonCode.code",
      codeDesc:"csscl.commonCode.description",
      style:null,
  },
  "swiftEventCodeCIB003":{
      maxlength:50,
      code:"csscl.commonCode.code",
      codeDesc:"csscl.commonCode.description",
      style:null,
  },
  "swiftEventCodeCIB004":{
      maxlength:50,
      code:"csscl.commonCode.code",
      codeDesc:"csscl.commonCode.description",
      style:null,
  },
  "swiftEventCodeCA017":{
      maxlength:50,
      code:"csscl.commonCode.code",
      codeDesc:"csscl.commonCode.description",
      style:null,
  },
  "swiftEventCodeCA018":{
      maxlength:50,
      code:"csscl.commonCode.code",
      codeDesc:"csscl.commonCode.description",
      style:null,
  },
  "swiftEventCode":{
    maxlength:50,
    code:"csscl.commonCode.code",
    codeDesc:"csscl.commonCode.description",
    style:null,
  },
  "caEventReferenceNumber":{
    maxlength:50,
    code:"csscl.commonCode.code",
    codeDesc:"csscl.commonCode.description",
    style:null,
  },
  "clientShortName":{
    maxlength:50,
    code:"csscl.commonCode.description",
    codeDesc:"csscl.commonCode.code",
    style:null,
    params:{ status: null }
  },
  "custodyAcctShortName":{
    maxlength:50,
    code:"csscl.commonCode.description",
    codeDesc:"csscl.commonCode.code",
    style:"width:300px;",
    params:{ status: null }
  },
  "externalInstrumentCode":{
    maxlength:50,
    code:"csscl.commonCode.code",
    codeDesc:"csscl.commonCode.description",
    style:null,
  },
  "instrumentName":{
    maxlength:50,
    code:"csscl.commonCode.description",
    codeDesc:"csscl.commonCode.code",
    style:null,
  },
  "":{
    maxlength:10,
    code:"",
    codeDesc:"",
    codeWidth:260,
    codeDescWidth:300,
    style:null,
  },
}

defineExpose({
  searchInput
})
</script>

<style>

</style>