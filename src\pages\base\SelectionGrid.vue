<template>
  <div style="margin:10px 0" class="grid-continer">
    <el-form>
      <el-pagination @current-change="handlePageChange" :current-page="currentPage"  @size-change="handlePageChange"
         v-model:current-page="currentPage" v-model:page-size="pageSize" class="selection-grid-pagination"
        :page-sizes="[10, 20, 30, 40]" layout="sizes, , jumper, prev, pager, next, ->, slot" v-model:total="total"
        style="background-color: lightgrey;padding-inline: 10px;" >
        {{ totalTitle }}
      </el-pagination>
    </el-form>

    <el-table :data="tableData"  
      table-layout="auto"
      @sort-change="handleSort" ref="tableRef" :cell-style="cellStyle" :border="true" class="grid-table"
      @selection-change="handleSelectionChange"
      :header-cell-class-name="(params: any) => { setHeaderClass(params) }">
      <slot name="tableColumnFront"></slot>
      <el-table-column v-for="item in columns" :sortable="item.sorter == false ? false : 'custom'" :prop="item.name"
        :align="item.dataType?.toUpperCase() == 'NUM' || item.dataType?.toUpperCase() == 'THOUS' ? 'right' : 'left'"
        :label="$t(item.title)" header-align="center" :width="item.width ? item.width : '*'">
        <template #default="scope" v-if="item.fn">
          {{ item.fn ? item.fn(scope.row, scope.row[item.name]) : "" }}
        </template>
        <template #default="scope" v-if="item.dataType?.toUpperCase() == 'THOUS'">
          {{ thousFormat(scope.row[item.name]) }}
        </template>
      </el-table-column>
      <slot name="tableColumnAfter"></slot>
    </el-table>

    <el-form>
    <el-pagination @current-change="handlePageChange" :current-page="currentPage"  @size-change="handlePageChange"
      v-model:current-page="currentPage" v-model:page-size="pageSize" class="selection-grid-pagination"
      :page-sizes="[10, 20, 30, 40]" layout="sizes, |, jumper, prev, pager, next, ->, slot" v-model:total="total"
      style="background-color: lightgrey;padding-inline: 10px;" >
      Total {{ total }} records
    </el-pagination>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, getCurrentInstance, watch, onMounted, nextTick } from 'vue'
import type { FormInstance } from 'element-plus'
import { useRouter } from 'vue-router';
import { useCookies } from "vue3-cookies";
import { highlight, thousFormat } from '~/util/Function.js';
import cloneDeep from 'lodash/cloneDeep';

const router = useRouter()
const { cookies } = useCookies()
const { proxy } = getCurrentInstance()
const formRef = ref<FormInstance>()
const props = defineProps(['url', 'params', 'editRow', 'deleteRow', 'sortProp', 'lazy', 'isSelectFirst',
  'onClick', 'onDbClick', 'hidePagination', 'columns', 'beforeSearch', 'afterSearch', 'onReset', 'cellStyle']);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableRef = ref();
const tableData = ref([]);
const param = ref({});
const orderBy = ref("");
const orderByDesc = ref([]);
const sortField = ref({});
const sortFieldName = ref({});
const columns = ref(props.columns);
const formInline = reactive(props.searchParams || {});
const lazy = ref(props.lazy);
const oldParams = ref({});
const selectedRows = ref([]);
const totalTitle = ref("");
const isChangePage = ref(false);


watch(() => props.params, (newVal) => {
  let isChange = true;
  if (Object.keys(oldParams.value || {}).length > 0) {
    isChange = false;
    let keys = Object.keys(props.params || {});
    for (let i = 0; i < keys.length; i++) {
      let key = keys[i];
      let p = props.params[key];
      let op = oldParams.value[key];
      if (p != op) {
        isChange = true;
        break;
      }
    }
    if (keys.length == 0) {
      isChange = true;
    }
  }
  if (isChange) {
    defalutSel();
  }
});

const defalutSel = () => {
  onSearch();
}

const loadData = async () => {
  oldParams.value = props.params;
  const msg = await proxy.$axios.post(props.url, {
    param: {
      ...param.value,
      ...props.params,
    },
    current: currentPage.value,
    pageSize: pageSize.value,
    orderBy: orderBy.value,
  });
  if (msg?.success) {
    total.value = msg.data.total;
    totalTitle.value = 'Total ' + total.value + ' records';
    tableData.value = {};
    tableData.value = msg.data.data;
    await updateSelection()
  }
}

const baseLoad = () => {
  if (lazy.value == 'false') {
    lazy.value = true;
  } else {
    loadData();
  }
}

const load = async () => {
  baseLoad();
}

const onSearch = () => {
  if (props.beforeSearch) {
    tableData.value = [];
    const res = props.beforeSearch();
    if (res) {
      param.value = formInline;
      load();
    }
  } else {
    param.value = formInline;
    load();
  }
}

const handleSort = (obj) => {
  let sts = {
    ...sortField.value
  };
  let stsName = {
    ...sortFieldName.value
  };
  if (sts[obj.prop] && !obj.order) {
    delete sts[obj.prop];
    delete stsName[obj.prop];
  } else {
    sts[obj.prop] = obj.order;
    stsName[obj.prop] = obj.column.label;
  }
  sortField.value = sts;
  sortFieldName.value = stsName;
  changeSort(sts, stsName);
  load();
}

const setHeaderClass = (params: any) => {
  params.column.order = sortField.value[params.column.property];
}

const changeSort = (sts, stsName) => {
  if (Object.keys(sts).length == 0) {
    orderBy.value = "";
    orderByDesc.value = [];
  } else {
    let obv = "";
    let obvNames = [];
    for (let key in sts) {
      obvNames.push({
        name: stsName[key],
        order: sts[key],
        code: key
      });
      if (props.sortProp && props.sortProp[key]) {
        let o = sts[key].charAt(0).toUpperCase();
        let d = "";
        for (let i = 0; i < props.sortProp[key].length; i++) {
          let ele = props.sortProp[key][i];
          d += ";" + ele + "-" + o;
        }
        obv += ";" + d.substring(1);
      } else {
        obv += ";" + key + "-" + sts[key].charAt(0).toUpperCase();
      }
    }
    orderBy.value = obv.substring(1);
    orderByDesc.value = obvNames;
  }
}

const cellStyle = (row, column, rowIndex, columnIndex) => {
  if (props.cellStyle) {
    let style = props.cellStyle(row, column, rowIndex, columnIndex);
    if (style) {
      return style;
    }
  }
  return highlight(row);
}

const initSelection = async (val) => {
  selectedRows.value = [...val]; 
  await nextTick();
  if (tableRef.value) {
    tableRef.value.clearSelection();
    val.forEach(row => {
      const rowData = tableData.value.find(item => item.tradingAccountCode === row.tradingAccountCode);
      if (rowData) {
        tableRef.value.toggleRowSelection(rowData, true);
      }
    });
  }
  await nextTick();
};
//Start CAP1-415, Tom.Li, 2025-02-19
const backUpdating = ref(false);
//End CAP1-415, Tom.Li, 2025-02-19
const updateSelection = async () => {
//Start CAP1-415, Tom.Li, 2025-02-19
  backUpdating.value = true;
//End CAP1-415, Tom.Li, 2025-02-19
  await nextTick();
  if (tableRef.value) {
    tableRef.value.clearSelection();
    selectedRows.value.forEach(row => {
      const rowData = tableData.value.find(item => item.tradingAccountCode === row.tradingAccountCode);
      if (rowData) {
        tableRef.value.toggleRowSelection(rowData, true);
      }
    });
  }
  await nextTick();
//Start CAP1-415, Tom.Li, 2025-02-19
  backUpdating.value = false;
//End CAP1-415, Tom.Li, 2025-02-19
};

const emptySelection = async () => {
  selectedRows.value = [];
  pageSize.value = 10;
};
const handlePageChange = async (page) => {
  // currentPage.value = page;
  isChangePage.value=true;
  await loadData();
};

const handleSelectionChange = async(val) => {
      //Start SIR-HLH-R27 HYC ********
      // Selected XX out of XXX Records
      if(val!=null &&  val.length){
        totalTitle.value = 'Select ' + val.length+ ' out of ' + total.value + ' records';
      }else{
        totalTitle.value = 'Total ' + total.value + ' records';
      }
      //End SIR-HLH-R27 HYC ********
      //Start CAP1-415, Tom.Li, 2025-02-19
      if (backUpdating.value) {
        return;
      }
      // Step 1: Remove current page data from selectedRows
      selectedRows.value = selectedRows.value.filter(row => 
        !tableData.value.some(item => item.tradingAccountCode === row.tradingAccountCode)
      );
      //End CAP1-415, Tom.Li, 2025-02-19
      
      // Step 2: Add current page selected data to selectedRows
      val.forEach(newRow => {
        if (!selectedRows.value.some(row => row.tradingAccountCode === newRow.tradingAccountCode)) {
          selectedRows.value.push(newRow);
        }
      });
    };
setTimeout(() => {
  onSearch();
}, 150);

defineExpose({
  load,
  getSelectedData: () => selectedRows.value,
  initSelection,
  emptySelection,
  getAllData: () => tableData.value
})
</script>

<style>
.demo-form-inline .el-input {
  --el-input-width: 220px;
}

.demo-form-inline {}

.demo-form-inline .el-select {
  --el-select-width: 220px;
}

.grid-table .ep-table__body {
  padding: 0;
}

::v-deep .seltAllbtnDis .cell {
  visibility: hidden;
}

/* Start CAP1-415, Tom.Li, 2025-02-19 */
.selection-grid-pagination .btn-prev {
    margin-left: 20% !important;
}
/* End CAP1-415, Tom.Li, 2025-02-19 */
</style>
