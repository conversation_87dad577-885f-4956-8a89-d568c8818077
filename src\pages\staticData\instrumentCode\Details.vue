<template>
    <BaseDetails ref="details" :handleSave="handleSave" :reload="props.reload" :form="instrumentForm" >
        <el-form :validateOnRuleChange="false" :disabled="formDisabled" ref="ruleFormRef" style="width: 100%" :model="instrumentForm.form" :rules="rules" status-icon>
            <FormRow>
                <FormItemSign :label="$t('csscl.instrument.instrumentShortName')" label-width="150" prop="instrumentShortName">
                   <InputText disabled v-model="instrumentForm.form.instrumentShortName" />
                </FormItemSign>
                <ElFormItemProxy></ElFormItemProxy>
                <ElFormItemProxy></ElFormItemProxy>
            </FormRow>
            <FormRow>
                <FormItemSign :label="$t('csscl.instrument.exBoardCode')" label-width="150" prop="exBoardCode">

                    <GeneralSearchInput  v-model="instrumentForm.form.exBoardCode" disabled 
                        style="width: 340px"
                        searchType="exboardCode" />

                </FormItemSign>
                <FormItemSign :label="$t('common.title.status')" label-width="50" prop="status">
                    <Select v-model="instrumentForm.form.status" type='STATUS' />
                </FormItemSign>
                <ElFormItemProxy></ElFormItemProxy>
            </FormRow>
            <el-row>
                <h3>Indentify</h3>
                <div style="paddingBlock:5px;width: 100%;display: flex;flex-flow: wrap;border-top: 1px solid;" ></div>
            </el-row>
            
        </el-form>
        <Grid url="/datamgmt/api/v1/instrument/identifier/list"
            :params="{instrumentOid: oid}" 
            :onClick="instrClick"
            isHideOrder
            :columns="[
                {title:'csscl.instrument.identifierType',name:'identifierType',sorter:false, fn:commDesc('IDENTIFIER_TYPE_CODE')},
                {title:'csscl.instrument.identifierCode',name:'identifierCode',sorter:false},
                {title:'csscl.instrument.defaultIndicator',name:'defaultInd',sorter:false},
                {title:'csscl.agent.recordStatus',name:'recordStatus',sorter:false,fn:getRecordStatusDesc },
            ]"
            >
        </Grid>
        <FormRow>
            <ElFormItemProxy :label="$t('csscl.instrument.identifierType')" label-width="100" prop="identifierType">
                <el-input disabled :value="getCommonDesc('IDENTIFIER_TYPE_CODE', instrumentForm.form.identifierType)" />
            </ElFormItemProxy> 
            <ElFormItemProxy :label="$t('csscl.instrument.identifierCode')" label-width="110" prop="defIdentifierCode">
                <el-input disabled v-model="instrumentForm.form.identifierCode" /></ElFormItemProxy> 
            <ElFormItemProxy :label="$t('csscl.instrument.defaultIndicator')" label-width="120" prop="defaultInd">
                <el-checkbox disabled v-model="instrumentForm.form.defaultInd" true-value="Y" false-value="N"/>
            </ElFormItemProxy> 
        </FormRow>
    </BaseDetails>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue';
import type { FormInstance, FormRules } from 'element-plus'
import BaseDetails from '~/pages/base/Details.vue';
import Grid from '~/pages/base/Grid.vue';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import  { getCommonDesc, getRecordStatusDesc, saveMsgBox, commDesc } from '~/util/Function.js';


const { proxy } = getCurrentInstance();
const props = defineProps(['reload']);
const details = ref();
const formDisabled = ref(false);
let oid = "";
const instrClick = (row) => {
    instrumentForm.form.identifierCode = row.identifierCode;
    instrumentForm.form.identifierType = row.identifierType;
    instrumentForm.form.defaultInd = row.defaultInd;
}
const rules = reactive({});
const editRow = (row) => {
    oid =  row.instrumentOid;
    proxy.$axios.post("/datamgmt/api/v1/instrument/list" , {
                param: {
                  instrumentOid: row.instrumentOid
                },
                current: 1,
                pageSize: 10,
                orderBy: null} ).then((body) => {
        if(body.success) {
            instrumentForm.form = body.data.data[0];
        }
    });
}
const handleSave = async (searchValid, isOnlyValidate, unPopping) => {
    let result = await ruleFormRef.value.validate((valid, fields) => {
        if (valid) {

        } else {
            console.log('error submit!', fields)
        }
    });
    if (isOnlyValidate) {
        return result;
    }
    if (result && searchValid && await saveMsgBox(unPopping)) {
        if (instrumentForm.form.txnOid) {
            const msg = await proxy.$axios.patch("/cashmgmt/api/v1/projection/adjustment", {
                ...instrumentForm.form,
            });
            return msg.success;
        } else {
            const msg = await proxy.$axios.post("/cashmgmt/api/v1/projection/adjustment", {
                ...instrumentForm.form,
            });
            return msg.success;
        }
    }
    return false;
}
const showDetails = (row, disabled) => {
    formDisabled.value = disabled;
    details.value.showDetails(row, disabled)
    editRow(row);
}
defineExpose({
    details,
    editRow,
    showDetails,
});
// --------------------------------------------

interface InstrumentForm {
    instrumentShortName: string
    exBoardCode:string
}

const ruleFormRef = ref<FormInstance>()
const instrumentForm = reactive({
    form: {
    exBoardCode:"",
    instrumentShortName:"",
    identifierType:"",
    identifierCode:"",
    defaultInd:"",
    recordStatus:"",
    }
})


const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate((valid, fields) => {
        if (valid) {
            console.log('submit!')
        } else {
            console.log('error submit!', fields)
        }
    })
}

const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.resetFields()
}

const options = Array.from({ length: 10000 }).map((_, idx) => ({
    value: `${idx + 1}`,
    label: `${idx + 1}`,
}))

</script>

<style></style>