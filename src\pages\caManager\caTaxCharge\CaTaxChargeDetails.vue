<template>
    <BaseDetails ref="baseDetailsRef" :handleSave="handleSave" :reload="props.reload">
        <el-form ref="baseDetailsFormRef" :disabled="false" style="width: 100%" :model="baseDetailsForm.form" status-icon :rules="caHoldQuantityFormRules">
            <FormRow>
                <FormItemSign :detailsRef="baseDetailsRef" :label="$t('csscl.ca.common.type')" label-width="250" class="blue-font" prop="taxChargeType">
                    <Select v-model="baseDetailsForm.form.taxChargeType" style="width: 200px" type='TAX_CHARGE_TYPE' />
                </FormItemSign>
                <FormItemSign :detailsRef="baseDetailsRef" :label="$t('csscl.ca.common.status')" label-width="150" prop="status">
                    <Select v-model="baseDetailsForm.form.status" style="width: 200px" type='STATUS' />
                </FormItemSign>
            </FormRow>
            <FormRow>
                <ElFormItemProxy label-width="250" :label="$t('csscl.ca.common.eventRefNo')" prop="caEventReferenceNumber">
                    <GeneralSearchInput v-model="baseDetailsForm.form.caEventReferenceNumber" inputStyle="width:200px" style="width:400px" :title="$t('csscl.ca.common.eventRefNo')" 
                    searchType="caEventReferenceNumber" showDesc="true"/>
                </ElFormItemProxy>
            </FormRow>
            <FormRow>
                <FormItemSign :detailsRef="baseDetailsRef" :label="$t('csscl.ca.common.securityId')" label-width="250" prop="instrumentCode">
                    <GeneralSearchInput v-model="baseDetailsForm.form.instrumentCode" inputStyle="width:200px" style="width:400px" searchType="instrumentCode" showDesc="true"/>
                </FormItemSign>
            </FormRow>
            <FormRow>
                <ElFormItemProxy :label="$t('csscl.ca.common.recordDate')" prop="recordDate" label-width="250">
                    <DateItem v-model="baseDetailsForm.form.recordDate" disabled="true"
                            type="date" style="width: 200px;"/>
                </ElFormItemProxy>
            </FormRow>
            <FormRow>
                <FormItemSign :detailsRef="baseDetailsRef" :label="$t('csscl.ca.common.eventCategory')" label-width="250" prop="caEventCategory">
                    <SearchInput style="width: 400px" v-model="baseDetailsForm.form.caEventCategory" disabled="true"
                        url="/datamgmt/api/v1/searchinput" showDesc="true"
                        :title="$t('csscl.ca.common.eventCategory')"
                        :params="{searchType: 'caEventCategoryCode'}"
                        :columns="[
                        {
                            title: $t('csscl.ca.common.eventCategoryCode'),
                            colName: 'code',
                        },
                        {
                            title: $t('csscl.ca.common.eventCategoryDescription'),
                            colName: 'codeDesc',
                        }
                        ]"
                        :pageSizes="[10, 20, 30]">
                    </SearchInput>
                </FormItemSign>
            </FormRow>
            <FormRow>
                <FormItemSign :detailsRef="baseDetailsRef" :label="$t('SWIFT Event Code')" label-width="250" prop="swiftEventCode">
                    <GeneralSearchInput v-model="baseDetailsForm.form.swiftEventCode" style="width:400px" searchType="swiftEventCode" showDesc="true" disabled="true"/>
                </FormItemSign>
            </FormRow>
            <FormRow>
                <FormItemSign :detailsRef="baseDetailsRef" :label="$t('csscl.ca.common.subCustodianClearingAgentCode')" label-width="250" prop="clearingAgentCode">
                    <GeneralSearchInput v-model="baseDetailsForm.form.clearingAgentCode" inputStyle="width:200px" style="width:400px" searchType="clearingAgentCode" showDesc="true"  disabled="true"/>
                </FormItemSign>
            </FormRow>
            <FormRow>
                <ElFormItemProxy :label="$t('csscl.ca.common.holdingDate')" prop="taxChargeHoldDate" label-width="250" class="blue-font">
                    <DateItem v-model="baseDetailsForm.form.taxChargeHoldDate"
                            type="date" style="width: 200px;"/>
                </ElFormItemProxy>
            </FormRow>
            <FormRow>
                <ElFormItemProxy label-width="250" :label="$t('csscl.ca.common.custodianAccount')" prop="custodianAccountNumber">
                   <GeneralSearchInput v-model="baseDetailsForm.form.custodianAccountNumber" style="width:230px" searchType="custodianAccNum" showDesc="false"/>
                </ElFormItemProxy>
            </FormRow>
            <FormRow>
                <FormItemSign :detailsRef="baseDetailsRef" :label="$t('csscl.ca.common.paymentType')" label-width="250" prop="taxChargePayTypeCode">
                    <Select v-model="baseDetailsForm.form.taxChargePayTypeCode" style="width: 200px" type='CA_TAX_CHARGE_PAY_TYPE_CODE' />
                </FormItemSign>
                <FormItemSign :detailsRef="baseDetailsRef" :label="$t('csscl.ca.common.actionType')" label-width="150" prop="drCrIndicator">
                    <Select v-model="baseDetailsForm.form.drCrIndicator" style="width: 200px" type='CA_ACTION_TYPE' />
                </FormItemSign>
            </FormRow>
            <FormRow>
                <FormItemSign :detailsRef="baseDetailsRef" :label="$t('csscl.ca.common.totalTransactionAmount')" label-width="250" prop="totalTaxChargeAmount1">
                    <el-input v-model="baseDetailsForm.form.totalTaxChargeAmount1" style="width: 200px" type="number"/>
                </FormItemSign>
                <FormItemSign :detailsRef="baseDetailsRef" :label="$t('csscl.ca.common.allocationType')" label-width="150" prop="allocationMethodCode">
                    <Select v-model="baseDetailsForm.form.allocationMethodCode" style="width: 200px" type='Test_Method' />
                </FormItemSign>
            </FormRow>
            <FormRow>
                <FormItemSign :detailsRef="baseDetailsRef" :label="$t('csscl.ca.common.DRCRCurrency')" label-width="250" prop="totalTaxChargeCcy1Code" disabled="true">
                    <el-input v-model="baseDetailsForm.form.totalTaxChargeCcy1Code" style="width: 200px"/>
                </FormItemSign>
                <FormItemSign :detailsRef="baseDetailsRef" :label="$t('csscl.ca.common.suppressFx')" label-width="150" prop="suppressFxIndicator" disabled="true">
                    <Select v-model="baseDetailsForm.form.suppressFxIndicator" style="width: 200px" type='YES_NO' />
                </FormItemSign>
            </FormRow>
            <FormRow>
                <ElFormItemProxy :label="$t('csscl.ca.common.originalPostingDate')" label-width="250" prop="postingDate">
                    <DateItem v-model="baseDetailsForm.form.postingDate"
                            type="date" style="width: 200px;"/>
                </ElFormItemProxy>
                <ElFormItemProxy :label="$t('csscl.ca.common.originalValueDate')" label-width="150" prop="valueDate">
                    <DateItem v-model="baseDetailsForm.form.valueDate"
                            type="date" style="width: 200px;"/>
                </ElFormItemProxy>
            </FormRow>
            <FormRow>
                <el-form-item :label="$t('csscl.ca.common.englishNarrative')" label-width="250" prop="narrative">
                    <el-input type="textarea" v-model="baseDetailsForm.form.narrative" input-style="text-transform: none;" style="width: 400px"/>
                </el-form-item>
            </FormRow>
        </el-form>

        <!-- Allocation To Clients ---------------------------- -->
         <div style="margin: 0px; padding: 3px; border-bottom: 2px solid black;">
            <el-text style="font: 14px bold;">
                {{ $t('csscl.ca.common.allocationToClients') }}
            </el-text>
        </div>
        <Allocation ref="allocationRef"/>

        <!-- Manal input for Exceptional Client ------------------------------->
        <br/>
        <div style="margin: 0px; padding: 3px; border-bottom: 2px solid black;">
            <el-text style="font: 14px bold;">
                {{ $t('csscl.ca.common.manalInputForClient') }}
            </el-text>
        </div>
        <Manal ref="manalRef" :allocationData="allocationData"/>
    </BaseDetails>
</template>

<script lang="ts" setup>
import { ref, reactive, getCurrentInstance, onMounted, nextTick, watch  } from 'vue';
import { ElMessage, ElMessageBox, type FormInstance, type FormRules, ElLoading } from 'element-plus';
import BaseDetails from '~/pages/base/CaDetails.vue';
import Allocation from './CaTaxChargeAllocation.vue';
import Manal from './CaTaxChargeManal.vue';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import { getTimeZone } from "~/util/DateUtils";
import { commonRules, showValidateMsg } from '~/util/Validators.js';

const { proxy } = getCurrentInstance();
const props = defineProps(['reload']);
const baseDetailsRef = ref();
const manalRef = ref();
const allocationRef = ref();
const allocationData = ref([]);
const baseDetailsFormRef = ref<FormInstance>()
const baseDetailsForm = reactive({
    form: {
        taxChargeType: "",
        status: "",
        caEventReferenceNumber: "",
        instrumentCode: "",
        recordDate: "",
        caEventCategory: "",
        swiftEventCode: "",
        clearingAgentCode: "",
        taxChargeHoldDate: "",
        custodianAccountNumber: "",
        taxChargePayTypeCode: "",
        totalTaxChargeAmount1: "",
        totalTaxChargeCcy1Code: "",
        valueDate: "",
        narrative: "",
        drCrIndicator: "",
        allocationMethodCode: "",
        postingDate: "",
        suppressFxIndicator: "",
        recordStatus:'',
    }
});
const caHoldQuantityFormRules = reactive<FormRules<baseDetailsForm>>({
  type: [
    commonRules.required,
  ],
  status: [
    commonRules.required,
  ],
  paymentType: [
    commonRules.required,
  ],
  totalTransactionAmount: [
    commonRules.required,
  ],
});
const showDetails = async (row:any, disabled:any) => {
    // 优先处理baseDetailsRef的showDetails逻辑
    await baseDetailsRef.value.showDetails(row, false);;
    // 暂时关闭后端接口调用，使用死数据
    loadTaxChargeHeaderData(row);
    baseDetailsRef.value.currentRow = row;
    baseDetailsRef.value.currentRow.oid = row.oid;
    baseDetailsRef.value.currentRow.currentOid = row.oid;
    baseDetailsRef.value.currentRow.mkckAction = 'D' // 避开beforeSaveValid
    // 渲染 组件
    await allocationRef.value.showDetails(row);
    await manalRef.value.showDetails(row);
}
const generateRequestparam = (data:any) => {
  let params = {
    header: {timezone: getTimeZone(), lang:"en_US"},
    data: data
  };
  return params;
}
const isResponseSuccess = (response:any) => {
  if (!response || !response.header) {
    return false;
  }
  return response.header.code === '000000';
}
const handleSave = async () => {
    // TODO: TBC
    return manalRef.value.handleSave();
}
const getErrorMessage = (errorMessage:any) => {
  return errorMessage || 'Unknown error';
}
// 从后端获取数据
const loadTaxChargeHeaderData = async (row:any) => {
    if (!row?.caEventReferenceNumber) return;
    let params = generateRequestparam({
        caEventReferenceNumber: row?.caEventReferenceNumber,
        caEventTaxChargeHdrOid: row?.caEventTaxChargeHdrOid,
    });
    const response = await proxy.$axios.post("/bff/ca/api/v1/ca-event-tax-charge/get-tax-charge-header-join", params);
    if (isResponseSuccess(response)) {
        baseDetailsForm.form = response.data;
    } else {
        const errorMsg = getErrorMessage(response?.header?.message);
        ElMessage.error(errorMsg);
    }
};
const handleSubmit = async () => {
    let flag = true;
    return flag;
}
const clearData = async() => {
  allocationRef.value = [];
  manalRef.value = [];
  baseDetailsForm.form = {};
}
defineExpose({
    baseDetailsRef,
    showDetails,
    handleSubmit,
    clearData,
});
</script>

<style scoped>
::v-deep .ep-textarea__inner {
  min-height: 100px !important;
}
::v-deep .blue-font .ep-form-item__label {
    color: #0000ff;
    font-weight: bold;
}
</style>