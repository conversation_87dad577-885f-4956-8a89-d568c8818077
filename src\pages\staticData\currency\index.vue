<template> 
  <BasePanel :searchParams='searchParams'  :paramListData='paramListData' url='/datamgmt/api/v1/currency/list' :params="{ modeEdit: 'Y' }"
    :showDetails="showDetails" :deleteRow="deleteRow" :editRow="editRow" ref="tableRef" :sortProp="{}">
    <template v-slot:searchPanel="slotProps">
      <FormRow>
        <ElFormItemProxy label-width="220" :label="$t('common.title.opCtryRegionCode')" prop="opCtryRegionCode">
          <CtryRegionSearchInput v-model="slotProps.form.opCtryRegionCode" 
            showDesc="false"
            opCtryRegion />
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('common.title.status')" label-width="110" prop="STATUS" >
          <Select v-model="slotProps.form.status" type="STATUS" v-model:desc="paramListData.status" />
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy label-width="220" :label="$t('csscl.currencyManagement.currencyCode')" prop="currencyCode">
          <SearchInput v-model="slotProps.form.currencyCode"
            maxlength="3"
            onlyLetters
            searchField
            style="width: 500px"
            input-style="width:110px"
            url="/datamgmt/api/v1/currency/list" 
            :title="$t('csscl.currencyManagement.currencyCode')"
            :params="{status: '',recordStatus:''}" 
            :columns="[
              {
                title: $t('csscl.currencyManagement.currencyCode'),
                colName: 'currencyCode',
              },
              {
                title: $t('csscl.currencyManagement.descpt'),
                colName: 'descpt',
              }
            ]"
          >
          </SearchInput>
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('common.title.recordStatus')" label-width="110" prop="multipleRecordStatus">
          <MultipleSelect v-model="slotProps.form.multipleRecordStatus" v-model:desc="paramListData.multipleRecordStatus"  />
        </ElFormItemProxy>
      </FormRow>
      <FormRow>    
        <ElFormItemProxy label-width="220" :label="$t('csscl.currencyManagement.descpt')" prop="descpt">
          <el-input v-model="slotProps.form.descpt" style="width: 500px" maxlength="50" class="text-none" />
        </ElFormItemProxy>
      </FormRow>
    </template>
    <template v-slot:tableColumn>
      <el-table-column align="left" header-align="center" sortable="custom" prop="opCtryRegionCode" :label="$t('common.title.opCtryRegionCode')" width="260" />
        <el-table-column align="left" header-align="center" sortable="custom" prop="currencyCode" :label="$t('csscl.currencyManagement.currencyCode')" width="260" />
        <el-table-column align="left" header-align="center" sortable="custom" prop="descpt" :label="$t('csscl.currencyManagement.descpt')" />
        <el-table-column align="left" header-align="center" sortable="custom" prop="restrictedCurrency" :label="$t('csscl.currencyManagement.restrictedCurrency')" width="180" />
        <el-table-column align="left" header-align="center" sortable="custom" prop="status" :label="$t('csscl.useradmin.usr.status')" width="150" >
          <template #default="scope">
            {{ getCommonDesc('STATUS', scope.row.status) }}
          </template>
        </el-table-column>
        <el-table-column align="left" header-align="center" sortable="custom" prop="recordStatus" :label="$t('csscl.useradmin.usr.recordStatus')" width="220" >
          <template #default="scope">
            {{ getRecordStatusDesc(scope.row) }}
          </template>
        </el-table-column>
    </template>
  </BasePanel>
  <Details ref="detailsRef" :reload="reload" />
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance, watch } from 'vue';
import { Search } from '@element-plus/icons-vue'
import BasePanel from '~/pages/base/index.vue'
import Details from './Details.vue'
import CtryRegionSearchInput from '~/pages/base/CtryRegionSearchInput.vue'
import CurrencySearchInput from '~/pages/base/CurrencySearchInput.vue'
import { getOid, getCommonDesc, getRecordStatusDesc } from '~/util/Function.js';


const mkckActionCode = ref({});
const { proxy } = getCurrentInstance()
const paramListData = {};
const searchParams = ref({
  opCtryRegionCode:"",
  status:"",
  currencyCode:"",
  multipleRecordStatus:[],
  descpt:""
});
const tableRef = ref();
const detailsRef = ref();
const showDetails = (row, disabled) => {
  detailsRef.value.showDetails(row, disabled);
}
const deleteRow = (row) => {
  proxy.$axios.delete("/datamgmt/api/v1/currency?currencyId="+getOid(row, false,null,true)).then((body) => {
    if (body.success) {
      tableRef.value.load();
    }
  });
}

const editRow = (row) => {
  detailsRef.value.editRow(row);
}
const reload = () => {
  tableRef.value.load();
}

//paramList 参数显示用的
function recordType(value){
  paramListData.recordStatus =  getCommonDesc('RECORD_STATUS', value);
}


// 获取包含查询参数的部分，如 '#/data/currency?id=1'
const hashWithParams = window.location.hash;

// 从 hash 中提取查询参数部分，如 '?id=1'
const queryString = hashWithParams.split('?')[1];

// 如果存在查询参数
if (queryString) {
  // 使用 URLSearchParams 解析查询参数
  const params = new URLSearchParams(queryString);
  // 获取参数值
  const id = params.get('id');
  if(id!=null){
    proxy.$axios.get("/datamgmt/api/v1/currency?currencyId="+id).then((body) => {
        if(body.success) {
          console.log(body.data);
            detailsRef.value.showDetails(body.data, false);
        }
    });
  }
}

</script>

<style>

</style>