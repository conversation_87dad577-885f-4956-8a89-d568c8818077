<template> 
  <BasePanel :searchParams="searchParams" url="/datamgmt/api/v1/bank/holiday/list" :params="{ modeEdit: 'Y' }"
    :showDetails="showDetails" :deleteRow="deleteRow" :editRow="editRow" ref="tableRef" :sortProp="{}">
    <template v-slot:searchPanel="slotProps">
      <FormRow>
        <ElFormItemProxy label-width="220px" :label="$t('common.title.opCtryRegionCode')" prop="opCtryRegionCode">
          <CtryRegionSearchInput v-model="slotProps.form.opCtryRegionCode" showDesc="false" style="width: 120px" opCtryRegion />
        </ElFormItemProxy>
        <ElFormItemProxy>
          <ElFormItemProxy label-width="130px" :label="$t('csscl.bankHolidayManagement.holidayDateFrom')" prop="lowestSearchHolidayDt">
            <DateItem :validate-event="false" v-model="slotProps.form.lowestSearchHolidayDt" style="width:130px"/>
          </ElFormItemProxy>
          <ElFormItemProxy label-width="45px" :label="$t('csscl.bankHolidayManagement.holidayDateTo')" prop="searchHolidayDt">
            <DateItem :validate-event="false" v-model="slotProps.form.searchHolidayDt" style="width:130px"/>
          </ElFormItemProxy>
          
        </ElFormItemProxy>
        <ElFormItemProxy></ElFormItemProxy>
      </FormRow>
    </template>
    <template v-slot:tableColumn>
      <el-table-column align="left" header-align="center" sortable="custom" prop="opCtryRegionCode" :label="$t('common.title.opCtryRegionCode')" width="450" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="holidayDate" :label="$t('csscl.bankHolidayManagement.holidayDate')" width="450" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="status" :label="$t('common.title.status')" width="400" >
        <template #default="scope">
          {{ getCommonDesc('STATUS', scope.row.status) }}
        </template>
      </el-table-column>
      <el-table-column align="left" header-align="center" sortable="custom" prop="recordStatus" :label="$t('common.title.recordStatus')" >
        <template #default="scope">
          <!-- scope.row -->
          {{ getCommonDesc('RECORD_STATUS', scope.row.recordStatus) }}
            <span v-if="scope.row.recordStatus&&scope.row.recordStatus!=='A' ">
              for  {{ getCommonDesc('MKCK_ACTION', scope.row.mkckAction) }}
            </span>   
        </template>
      </el-table-column>     
    </template>
  </BasePanel>
  <Details ref="detailsRef" :reload="reload" />
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance, watch } from 'vue';
import {
Search
} from '@element-plus/icons-vue'
import BasePanel from '~/pages/base/index.vue'
import Details from './Details.vue'
import CtryRegionSearchInput from '~/pages/base/CtryRegionSearchInput.vue'
import  { getCommonDesc } from '~/util/Function.js';

const { proxy } = getCurrentInstance()
const searchParams = ref({ });

const tableRef = ref();
const detailsRef = ref();
const showDetails = (row, disabled) => {
  detailsRef.value.showDetails(row, disabled);
}
const deleteRow = (row) => {
  proxy.$axios.delete("/datamgmt/api/v1/bank/holiday?bankHolidayId="+row.bankHolidayOid).then((body) => {
    if (body.success) {
      tableRef.value.load();
    }
  });
}
const editRow = (row) => {
  detailsRef.value.editRow(row);
}
const reload = () => {
  tableRef.value.load();
}
//-------------------------------

</script>

<style>

</style>