<template> 
    
    <div >
        <!-- identifier grid -->
        <el-text class="mx-1">Identifier Preference</el-text>
        <IdentifierListGrid :ruleForm="ruleForm" url="/datamgmt/api/v1/account/identifier/pref" :disabled="disabled" />
    </div>

    <div class="splitLine">
        <span>{{  $t("csscl.incomingCode.incomingChannel") }}</span>
        <el-divider />
    </div>
        
        <!-- incoming grid -->
        <Grid url="/datamgmt/api/v1/account/channel/in/list"
            :params="{ clientAccountOid: ruleForm.form.opearteOid, pendingOid: ruleForm.form.pendingOid }" 
            :onClick="incomingClick"
            :beforeSearch="()=>{ pageObj.incomingVPO = {}; }"
            :afterSearch="(p,d)=>{ d.forEach(e=>{ e.addrFtgId = e.addrFtgId || ruleForm.form.ftgid }); }"
            :columns="[
                {title:'csscl.acctCode.incomingChannel',name:'channel', fn:commDesc('CUST_ACC_INCOMING_CHANNEL_CODE') },
                {title:'csscl.acctCode.addrBicCode',name:'addrBicCode',},
                {title:'csscl.acctCode.ftgId',name:'addrFtgId',},
                {title:'csscl.acctCode.ca',name:'purposeCa',},
                {title:'csscl.acctCode.siReceiver',name:'purposeSiReceive',},
                {title:'csscl.acctCode.siDeliver',name:'purposeSiDeliver',},
                {title:'csscl.acctCode.cashTransaction',name:'purposeRecon',},
                {title:'common.title.status',name:'status',fn:commDesc('STATUS') },
                {title:'common.title.recordStatus',name:'recordStatus',fn:getRecordStatusDesc },
            ]"
            />
        <FormRow>
            <FormItemSign :detailsRef="details" label-width="150" :label="$t('csscl.acctCode.incomingChannel')" prop="incomingVPO.channel">
                <Select v-model="pageObj.incomingVPO.channel" style="width: 180px" type="CUST_ACC_INCOMING_CHANNEL_CODE" />
            </FormItemSign>
            <FormItemSign :detailsRef="details" label-width="150" :label="$t('common.title.status')" prop="incomingVPO.status">
                <Select v-model="pageObj.incomingVPO.status" style="width: 180px" type="STATUS" />
            </FormItemSign>
        </FormRow>
        <FormRow>
            <FormItemSign :detailsRef="details" label-width="150" :label="$t('csscl.acctCode.addrBicCode')" prop="incomingVPO.addrBicCode">
                <el-input v-model="pageObj.incomingVPO.addrBicCode" />
            </FormItemSign>
            <FormItemSign :detailsRef="details" label-width="150" :label="$t('csscl.acctCode.ftgId')" prop="incomingVPO.addrFtgId">
                 <GeneralSearchInput v-model="pageObj.incomingVPO.addrFtgId"
                    inputStyle="width: 180px"
                    style="width:650px"
                    title="csscl.acctCode.ftgId"
                    searchType="ftgidCode" />
            </FormItemSign>
        </FormRow>
        <FormRow>
            
            <FormItemSign :detailsRef="details" label-width="150" :label="$t('csscl.acctCode.purpose')" prop="incomingVPO.purposeList">
                <div>
                    <el-checkbox :label="$t('csscl.acctCode.ca')" v-model="pageObj.incomingVPO.purposeCa" true-value="Y" false-value="N"  />
                    <el-checkbox :label="$t('csscl.acctCode.siReceiver')" v-model="pageObj.incomingVPO.purposeSiReceive" true-value="Y" false-value="N"  />
                    <el-checkbox :label="$t('csscl.acctCode.siDeliver')" v-model="pageObj.incomingVPO.purposeSiDeliver" true-value="Y" false-value="N"  />
                    <el-checkbox :label="$t('csscl.acctCode.cashTransaction')" v-model="pageObj.incomingVPO.purposeRecon" true-value="Y" false-value="N"  />
                </div>
            </FormItemSign>
        </FormRow>

        <div class="splitLine">
            <span>{{  $t("csscl.outgoingCode.outgoingChannel") }}</span>
            <el-divider />
        </div>
        <!-- outgoing grid -->
        
        <Grid url="/datamgmt/api/v1/account/channel/out/list"
            :params="{ clientAccountOid: ruleForm.form.opearteOid, pendingOid: ruleForm.form.pendingOid }" 
            :onClick="outgoingClick"
            :beforeSearch="()=>{ pageObj.outgoingVPO = {}; }"
            :afterSearch="(p,d)=>{ d.forEach(e=>{ e.addrFtgId = e.addrFtgId || ruleForm.form.ftgid }); }"
            :columns="[
                {title:'csscl.acctCode.outgoingChannel',name:'channel', fn:commDesc('OUTGOING_CHANNEL_CODE') },
                {title:'csscl.acctCode.addrBicCode',name:'addrBicCode',},
                {title:'csscl.acctCode.ftgId',name:'addrFtgId',},
                {title:'csscl.acctCode.addrEmail',name:'addrEmail',},
                {title:'csscl.acctCode.mailAddrCode',name:'mailAddrCode'},
                {title:'common.title.status',name:'status',fn:commDesc('STATUS') },
                {title:'common.title.recordStatus',name:'recordStatus',fn:getRecordStatusDesc },
            ]"
            />
        <FormRow>
            <FormItemSign :detailsRef="details" label-width="150" :label="$t('csscl.acctCode.outgoingChannel')" prop="outgoingVPO.channel">
                <Select v-model="pageObj.outgoingVPO.channel" style="width: 180px" type="OUTGOING_CHANNEL_CODE" />
            </FormItemSign>
            <FormItemSign :detailsRef="details" label-width="150" :label="$t('csscl.acctCode.language')" prop="outgoingVPO.language" style="width:750px">
                <Select v-model="pageObj.outgoingVPO.language" style="width: 180px" type="LANGUAGE" />
            </FormItemSign>
            <FormItemSign :detailsRef="details" label-width="110" :label="$t('common.title.status')" prop="outgoingVPO.status">
                <Select v-model="pageObj.outgoingVPO.status" style="width: 180px" type="STATUS" />
            </FormItemSign>
        </FormRow>
        <FormRow>
            <FormItemSign :detailsRef="details" label-width="150" :label="$t('csscl.acctCode.addrBicCode')" prop="outgoingVPO.addrBicCode">
                <el-input v-model="pageObj.outgoingVPO.addrBicCode" />
            </FormItemSign>
            <FormItemSign :detailsRef="details" label-width="150" :label="$t('csscl.acctCode.ftgId')" prop="outgoingVPO.addrFtgId" style="width:750px">
                
                 <GeneralSearchInput v-model="pageObj.outgoingVPO.addrFtgId"
                    inputStyle="width: 180px"
                    style="width:500px"
                    title="csscl.acctCode.ftgId"
                    searchType="ftgidCode" />

            </FormItemSign>
            <FormItemSign :detailsRef="details" label-width="110" :label="$t('csscl.acctCode.addrEmail')" prop="outgoingVPO.addrEmail">
                <el-input v-model="pageObj.outgoingVPO.addrEmail" style="width:300px" />
            </FormItemSign>
        </FormRow>
        <FormRow>
            <FormItemSign :detailsRef="details" prop="outgoingVPO.internetEnqInd">
                <el-checkbox v-model="pageObj.outgoingVPO.internetEnqInd" true-value="Y" false-value="N" > 
                    <el-text class="form-item-sign">{{ $t('csscl.acctCode.internetEnqInd') }}</el-text>
                </el-checkbox>
            </FormItemSign>
        </FormRow>
        <FormRow>
            <FormItemSign :detailsRef="details" label-width="150" :label="$t('csscl.acctCode.purpose')" prop="outgoingVPO.purpose" class="check-box">
                
                <div>
                    <FormRow>
                        <ElFormItemProxy>
                            <el-checkbox :label="$t('csscl.acctCode.cashTrans')" v-model="pageObj.outgoingVPO.purposePects" true-value="Y" false-value="N" />
                        </ElFormItemProxy>
                        <ElFormItemProxy>
                            <el-checkbox :label="$t('csscl.acctCode.settlAdv')" v-model="pageObj.outgoingVPO.purposeSa" true-value="Y" false-value="N" />
                        </ElFormItemProxy>
                        <ElFormItemProxy></ElFormItemProxy>
                    </FormRow>
                    <FormRow>
                        <ElFormItemProxy>
                            <el-checkbox :label="$t('csscl.acctCode.protStat')" v-model="pageObj.outgoingVPO.purposePs" true-value="Y" false-value="N" />
                        </ElFormItemProxy>
                        <ElFormItemProxy>
                            <el-checkbox :label="$t('csscl.acctCode.portStatInTradPos')" v-model="pageObj.outgoingVPO.purposePstp" true-value="Y" false-value="N" />
                        </ElFormItemProxy>
                        <ElFormItemProxy></ElFormItemProxy>
                    </FormRow>
                    <FormRow>
                        <ElFormItemProxy>
                            <el-checkbox :label="$t('csscl.acctCode.transStatinTradPos')" v-model="pageObj.outgoingVPO.purposeTstp" true-value="Y" false-value="N" />
                        </ElFormItemProxy>
                        <ElFormItemProxy>
                            <el-checkbox :label="$t('csscl.acctCode.transStat')" v-model="pageObj.outgoingVPO.purposeTs" true-value="Y" false-value="N" />
                        </ElFormItemProxy>
                        <ElFormItemProxy></ElFormItemProxy>
                    </FormRow>
                    <FormRow>
                        <ElFormItemProxy>
                            <el-checkbox :label="$t('csscl.acctCode.compStat')" v-model="pageObj.outgoingVPO.purposeCs" true-value="Y" false-value="N" />
                        </ElFormItemProxy>
                        <ElFormItemProxy>
                            <el-checkbox :label="$t('csscl.acctCode.ca')" v-model="pageObj.outgoingVPO.purposeCa" true-value="Y" false-value="N" />
                        </ElFormItemProxy>
                        <ElFormItemProxy></ElFormItemProxy>
                    </FormRow>
                    <FormRow>
                        <ElFormItemProxy>
                            <el-checkbox :label="$t('csscl.acctCode.proxyVoting')" v-model="pageObj.outgoingVPO.purposePv" true-value="Y" false-value="N" />
                        </ElFormItemProxy>
                        <ElFormItemProxy>
                            <el-checkbox :label="$t('csscl.acctCode.income')" v-model="pageObj.outgoingVPO.purposeIncome" true-value="Y" false-value="N" />
                        </ElFormItemProxy>
                        <ElFormItemProxy></ElFormItemProxy>
                    </FormRow>
                    <FormRow>
                        <ElFormItemProxy>
                            <el-checkbox :label="$t('csscl.acctCode.transSumOfShadCashAcc')" v-model="pageObj.outgoingVPO.purposeTsosca" true-value="Y" false-value="N" />
                        </ElFormItemProxy>
                        <ElFormItemProxy>
                            <el-checkbox :label="$t('csscl.acctCode.statOfOutSICashPen')" v-model="pageObj.outgoingVPO.purposeSoosi" true-value="Y" false-value="N" />
                        </ElFormItemProxy>
                        <ElFormItemProxy></ElFormItemProxy>
                    </FormRow>
           
                </div>
              
                
            </FormItemSign>
        </FormRow>

        <div class="splitLine">
            <span>{{  $t("csscl.mailInformationCode.mailInformation") }}</span>
            <el-divider />
        </div>

        <FormRow>
            <FormItemSign :detailsRef="details" label-width="190" :label="$t('csscl.acctCode.mailAddrCode')" prop="outgoingVPO.mailAddrCode"><el-input v-model="pageObj.outgoingVPO.mailAddrCode" /></FormItemSign>
            <FormItemSign :detailsRef="details" label-width="180" :label="$t('csscl.acctCode.mailCtryRegion')" prop="outgoingVPO.mailCtryRegion">
                <CtryRegionSearchInput v-model="pageObj.outgoingVPO.mailCtryRegion" showDesc="false" />
            </FormItemSign>
        </FormRow>
        <FormRow>
            <FormItemSign :detailsRef="details" label-width="190" :label="$t('csscl.acctCode.mailAddrHdr1')" prop="outgoingVPO.mailAddrHdr1">
                <el-input v-model="pageObj.outgoingVPO.mailAddrHdr1" style="width:450px"/>
            </FormItemSign>
            <ElFormItemProxy></ElFormItemProxy>
        </FormRow>
        <FormRow>
            <FormItemSign :detailsRef="details" label-width="190" :label="$t('csscl.acctCode.mailAddrHdr2')" prop="outgoingVPO.mailAddrHdr2">
                <el-input v-model="pageObj.outgoingVPO.mailAddrHdr2" style="width:450px"/>
            </FormItemSign>
            <ElFormItemProxy></ElFormItemProxy>
        </FormRow>
        <FormRow>
            <FormItemSign :detailsRef="details" label-width="190" :label="$t('csscl.acctCode.mailAddr1')" prop="outgoingVPO.mailAddr1">
                <el-input v-model="pageObj.outgoingVPO.mailAddr1" style="width:450px"/>
            </FormItemSign>
            <ElFormItemProxy></ElFormItemProxy>
        </FormRow>
        <FormRow>
            <FormItemSign :detailsRef="details" label-width="190" :label="$t('csscl.acctCode.mailAddr2')" prop="outgoingVPO.mailAddr2">
                <el-input v-model="pageObj.outgoingVPO.mailAddr2" style="width:450px"/>
            </FormItemSign>
            <ElFormItemProxy></ElFormItemProxy>
        </FormRow>
        <FormRow>
            <FormItemSign :detailsRef="details" label-width="190" :label="$t('csscl.acctCode.mailAddr3')" prop="outgoingVPO.mailAddr3">
                <el-input v-model="pageObj.outgoingVPO.mailAddr3" style="width:450px"/>
            </FormItemSign>
            <ElFormItemProxy></ElFormItemProxy>
        </FormRow>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import Grid from '~/pages/base/Grid.vue';
import CtryRegionSearchInput from '~/pages/base/CtryRegionSearchInput.vue';
import  { getCommonDesc, getRecordStatusDesc, commDesc } from '~/util/Function.js';
import IdentifierListGrid from './IdentifierListGrid.vue';

const props = defineProps([ "ruleForm", "details", "disabled"]);
const details = props.details;
const ruleForm = props.ruleForm;
const pageObj = reactive ({
    incomingVPO:{},
    outgoingVPO:{},
});

const incomingClick = (row) => {
    pageObj.incomingVPO = row;
}
const outgoingClick = (row) => {
    pageObj.outgoingVPO = row;
}

</script>

<style scoped>
.check-box .ep-form-item__label {
    min-height: 190px ;
}
</style>