<template>
  <BaseDetails ref="details" :handleSave="handleSave" :reload="reload" :viewOriginalForm="viewOriginalForm" :form="ruleForm" >
    <el-form :validateOnRuleChange="false" :disabled="formDisabled" ref="ruleFormRef" style="width: 100%" :model="ruleForm.form" :rules="rules" status-icon>
      <FormRow>
        <FormItemSign :detailsRef="details" :label="$t('common.title.opCtryRegionCode')" prop="opCtryRegionCode" style="width:550px" >
          <CtryRegionSearchInput v-model="ruleForm.form.opCtryRegionCode" 
            :disabled="editDis" 
            showDesc="false"
            opCtryRegion />
        </FormItemSign>
        <FormItemSign :detailsRef="details" :label="$t('common.title.status')" label-width="110" prop="status">
          <Select v-model="ruleForm.form.status" style="width: 150px" type='STATUS' />
        </FormItemSign>
      </FormRow>
      <FormRow v-if="showParent">
        <FormItemSign  :detailsRef="details" :label="$t('csscl.commonCode.parentCodeType')" prop="parentCodeType" style="width:550px">
          <Select v-model="ruleForm.form.parentCodeType" style="width: 200px" :source="commParentType" disabled  />
        </FormItemSign>
        <FormItemSign  :detailsRef="details" :label="$t('csscl.commonCode.parentCode')" label-width="110" prop="parentCode">
          <Select v-model="ruleForm.form.parentCode" style="width: 200px" :source="commParentCode" vkEnqual :disabled="editDis"  />
        </FormItemSign>
      </FormRow>
      <FormRow>
        <FormItemSign :detailsRef="details" :label="$t('csscl.commonCode.commonCodeType')" prop="codeType">
          <Select v-model="ruleForm.form.codeType" :disabled="editDis" :change="commTypeChg" :source="commType" type="CODE_TYPE" />
        </FormItemSign>
        <FormItemSign :detailsRef="details"  prop="codeName" v-if="false">
          <el-input v-model="ruleForm.form.codeName"  />
        </FormItemSign>
      </FormRow>
      <FormRow>  
        <FormItemSign :detailsRef="details" :label="$t('csscl.commonCode.code')" prop="code">
          <InputText v-model="ruleForm.form.code" uppercase trim maxlength="100" :disabled="editDis" style="width: 900px" />
        </FormItemSign>
      </FormRow>
      <FormRow>  
        <FormItemSign :detailsRef="details" :label="$t('csscl.commonCode.description')" prop="codeDesc">
          <InputText v-model="ruleForm.form.codeDesc" maxlength="150" style="width: 1100px" />
        </FormItemSign>
      </FormRow>
    </el-form>
  </BaseDetails>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue';
import { ElMessage, ElMessageBox,type FormInstance, type FormRules } from 'element-plus';
import { getOid, saveMsgBox } from '~/util/Function.js';
import BaseDetails from '~/pages/base/Details.vue';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import CtryRegionSearchInput from '~/pages/base/CtryRegionSearchInput.vue'
import { commonRules, showValidateMsg } from '~/util/Validators.js';

const { proxy } = getCurrentInstance();
const props = defineProps(['reload']);
const details = ref();
const formDisabled = ref(false);
const editDis = ref(false);
const showParent = ref(false);
const commType = ref();
const commParentType = ref();
const commParentCode = ref();

const editRow = (row, disabled,newId) => {
  if(row?.isApproveDetail && disabled){
    ruleForm.form = row.afterImage;
    details.value.currentRow = ruleForm.form;
  } else {
    const oid = newId || (row?.currentOid && getOid(row, disabled, proxy));
    if (oid) {
      proxy.$axios.get("/datamgmt/api/v1/comcode?commonCodeId="+oid).then((body) => {
          if(body.success) {
              ruleForm.form = body.data;
              details.value.currentRow = body.data;
              commTypeChg(ruleForm.form.codeType, null, true);
          }
          details.value.initWatch(ruleForm);
      });
      editDis.value = true;
    }else{
      details.value.initWatch(ruleForm);
    }
  }
}

const viewOriginalForm = (pendingOid, isDisabled) => {
  formDisabled.value = isDisabled;
  proxy.$axios.get("/datamgmt/api/v1/comcode?commonCodeId="+pendingOid).then((body) => {
        if(body.success) {
            ruleForm.form = body.data;
            details.value.currentRow.value = body.data;
        }
    });
}

const handleSave = async (searchValid, isOnlyValidate, unPopping) => {
    let result = await ruleFormRef.value.validate((valid, fields) => {
        if (valid) {
        } else {
          showValidateMsg(details, fields);
        }
    });
    if (isOnlyValidate) {
        return result;
    }
    if (result && searchValid && await saveMsgBox(unPopping)) {
        if (ruleForm.form.commonCodeOid) {
            const msg = await proxy.$axios.patch("/datamgmt/api/v1/comcode", {
                ...ruleForm.form,
            });
            details.value.writebackId(msg.data);
            editRow(null,null,msg.data);
            return msg.success;
        } else {
            const msg = await proxy.$axios.post("/datamgmt/api/v1/comcode", {
                ...ruleForm.form,
            });
            details.value.writebackId(msg.data);
            editRow(null,null,msg.data);
            return msg.success;
        }
    }
    return false;
}

const showDetails = (row, isdoubleCheck) => {
    if(isdoubleCheck||row.recordStatus==='PA'){
        formDisabled.value = true;
        details.value.showDetails(row, true)
    }else{
        formDisabled.value = false;
        details.value.showDetails(row, false)
    }
    showParent.value = false;
    setCommType();
    ruleForm.form = {};
    details.value.currentRow={};
    editDis.value = false;
    editRow(row, isdoubleCheck);
}

const setCommType = () => {
  proxy.$axios.get("/datamgmt/api/v1/comcode/typelist").then((body) => {
    if (body.success) {
      commType.value = body.data;
      commTypeChg(ruleForm.form.codeType, null, true);
    }
  });
}

const commTypeChg = (val, obj, isAuto) => {
  if (!isAuto) {
    ruleForm.form.parentCode = null;
    ruleForm.form.parentCodeType = null;
  }
  showParent.value = false;
  rules.parentCode = [];
  commType.value.forEach(e=>{
    if (val == e.code) {
      ruleForm.form.codeName = e.codeDesc
      if (e.searchType) {
        proxy.$axios.get("/datamgmt/api/v1/comcode/comm/list?commonType="+e.searchType).then((body) => {
          if (body.success) {
            showParent.value = true;
            ruleForm.form.parentCodeType = e.searchType;
            commParentCode.value = body.data;
            rules.parentCode = [commonRules.selectRequired]
          }
        });
      }
    }
  });
}

defineExpose({
  details,
  editRow,
  showDetails,
});
// --------------------------------------------


interface RuleForm {
  opCtryRegionCode: String
  codeType: String
  code: String
  codeDesc: String
  status: String
}
const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive({
    rules:()=>{ return [{ rules:rules }] },
    form: {
      opCtryRegionCode: "",
      codeType: "",
      code: "",
      codeDesc: "",
      status: "",
      parentCodeType:"",
      parentCode: "",
    }
})

const rules = reactive<FormRules<RuleForm>>({
    opCtryRegionCode: [
        commonRules.required,
    ],
    status: [
        commonRules.selectRequired,
    ],
    parentCode: [],
    codeType: [
        commonRules.selectRequired,
    ],
    code: [
        commonRules.required,
        commonRules.name,
    ],
    codeDesc: [
        commonRules.required,
        commonRules.name,
    ],
})

proxy.$axios.get("/datamgmt/api/v1/comcode/parent/list").then((body) => {
  if (body.success) {
    commParentType.value = body.data;
  }
});

</script>

<style></style>