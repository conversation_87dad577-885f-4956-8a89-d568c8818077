<template> 
        <Grid url="/datamgmt/api/v1/clearingagent/custacc/list"
            :params="{ clearingAgentOid: agentForm.form.clearingAgentOid  }" 
            :onClick="accClick"
            :beforeSearch="()=>{ pageObj.accVPO = {}; }"
            :columns="[
                {title:'csscl.agent.custodianAccountCode', name:'custodianAccNo', },
                {title:'csscl.agent.custoianAccountShortName',name:'shortName', },
                {title:'csscl.agent.custodianSubAccountCode',name:'custodianSubAccNo', },
                {title:'csscl.agent.defaultIndicator',name:'defaultInd', },
                {title:'csscl.agent.positionReconcilationType',name:'stockReconType',fn:commDesc('STOCK_RECON_TYPE_CODE') },
                {title:'csscl.agent.recordStatus',name:'recordStatus',fn:getRecordStatusDesc },
            ]"
            >
        </Grid>
        <FormRow>
            <FormItemSign  :label="$t('csscl.agent.custodianAccountCode')" prop="accVPO.custodianAccNo">
                <el-input disabled v-model="pageObj.accVPO.custodianAccNo" />
            </FormItemSign>
            <FormItemSign :label="$t('csscl.agent.positionReconcilationType')" prop="accVPO.stockReconType">
                <Select disabled v-model="pageObj.accVPO.stockReconType" style="width:260px" type='STOCK_RECON_TYPE_CODE' />
            </FormItemSign>
            <FormItemSign  label="" prop="accVPO.defaultInd">
                <el-checkbox disabled v-model="pageObj.accVPO.defaultInd" true-value="Y" false-value="N" >
                {{$t('csscl.agent.defaultIndicator')}}
                </el-checkbox>
            </FormItemSign>
        </FormRow>
        <FormRow>
            <FormItemSign :label="$t('csscl.agent.custoianAccountShortName')" prop="accVPO.shortName">
                <el-input disabled v-model="pageObj.accVPO.shortName" style="width: 400px;"/>
            </FormItemSign>
        </FormRow>
        <FormRow>
            <FormItemSign :label="$t('csscl.agent.custodianSubAccountCode')" prop="accVPO.custodianSubAccNo">
                <el-input disabled v-model="pageObj.accVPO.custodianSubAccNo" style="width: 300px;"/>
            </FormItemSign>
        </FormRow>
    
        <span style="font-size: 16px; font-weight: bold;padding-top:10px">{{  $t("csscl.agent.assExBoard") }}</span>
        <div style="paddingBlock:5px;width: 100%;display: flex;flex-flow: wrap;border-top: 1px solid;"></div>  

        <Grid url="/datamgmt/api/v1/clearingagent/custacc/exb/list"
            :params="{ custodianAccOid: oid,recordStatus:exbRecordStatus}" 
            :onClick="exbClick"
            :lazy="true"
            ref="exbGrid"
            :beforeSearch="()=>{ pageObj.custaccExbVPO = {}; }"
            :afterSearch="afterSearchExb"
            :columns="[
                {title:'csscl.agent.exchangeCode',name:'exchangeCode',},
                {title:'csscl.agent.exchangeBoardCode',name:'exBoardCode'},
                {title:'csscl.agent.outInsidentifier',name:'outInstrIdentifier'},
                {title:'csscl.agent.suppressSwift',name:'suppressSwiftInd'},
                {title:'csscl.agent.settlePayMethod',width:'340',name:'settlePayMethod',fn:commDesc('CASH_SETTLE_METHOD_SI_CODE') },
                {title:'csscl.agent.caPayMethod',width:'340',name:'caPayMethod',fn:commDesc('CA_PAY_METHOD_CODE') },
                {title:'common.title.recordStatus',name:'recordStatus',fn:getRecordStatusDesc },
            ]" >
        </Grid>
        
        <FormRow>
            <FormItemSign :label="$t('csscl.agent.exchangeCode')" prop="exchangeCode">
                <GeneralSearchInput  v-model="pageObj.custaccExbVPO.exchangeCode"
                    disabled 
                    style="width: 220px"
                    searchType="exchangeCode" />
                
            </FormItemSign>
            <FormItemSign :label="$t('csscl.agent.exchangeBoardCode')"  label-width="240" prop="exBoardCode">
                <GeneralSearchInput  v-model="pageObj.custaccExbVPO.exBoardCode"
                    style="width: 380px" 
                    disabled
                    inputStyle="width: 160px" 
                    searchType="exboardCode" />
            </FormItemSign>
        </FormRow>
        <FormRow>
            <FormItemSign :label="$t('csscl.agent.outInsidentifier')" disabled prop="outInstrIdentifier">
                <Select  v-model="pageObj.custaccExbVPO.outInstrIdentifier" style="width:180px" disabled type='IDENTIFIER_TYPE_CODE' />
            </FormItemSign>
            <FormItemSign label="" prop="suppressSwiftInd">
                <el-checkbox disabled v-model="pageObj.custaccExbVPO.suppressSwiftInd" true-value="Y" false-value="N" >
                {{$t('csscl.agent.suppressSwift')}}
                </el-checkbox>  
            </FormItemSign>
        </FormRow>
        <FormRow>
            <FormItemSign :detailsRef="exbFormRef" :label="$t('csscl.agent.settlePayMethod')" prop="settlePayMethod">
                <Select v-model="pageObj.custaccExbVPO.settlePayMethod"
                    name="settlePayMethod"
                    :change="transeValue" 
                    style="width:320px"
                    :disabled="formDisabled"
                    type='CASH_SETTLE_METHOD_SI_CODE' />
            </FormItemSign>
            <FormItemSign :detailsRef="exbFormRef" :label="$t('csscl.agent.caPayMethod')" label-width="240" prop="caPayMethod">
                <Select v-model="pageObj.custaccExbVPO.caPayMethod"
                    name="caPayMethod"
                    :change="transeValue" 
                    style="width:320px"
                    :disabled="formDisabled"
                    type='CA_PAY_METHOD_CODE' />
            </FormItemSign>
        </FormRow>

        <span style="font-size: 16px; font-weight: bold;padding-top:10px">{{  $t("csscl.agent.custodianCashAccount") }}</span>
        <div style="paddingBlock:5px;width: 100%;display: flex;flex-flow: wrap;border-top: 1px solid;"></div>  
        
        <Grid url="/datamgmt/api/v1/clearingagent/custacc/cash/list"
            :params="{ custodianAccOid: oid ,recordStatus:exbRecordStatus}" 
            :beforeSearch="()=>{ pageObj.cashVPO = {}; }"
            :onClick="cashClick"
            lazy="false"
            :columns="[
                {title:'csscl.agent.currency',name:'currencyCode',},
                {title:'csscl.agent.cashAccountNumber',name:'bankAccountNo',width:'300'},
                {title:'csscl.agent.parentBankAccNo',name:'parentBankAccountNo',width:'300'},
                {title:'csscl.agent.corporateAction',name:'purposeCa',fn:changePurposeCA },
                {title:'csscl.agent.siRec',name:'purposeSiReceive',fn:changePurposeSiRec },
                {title:'csscl.agent.siDel',name:'purposeSiDeliver',fn:changePurposeSiDel },
                {title:'csscl.agent.accountStatus',name:'accountStatus',fn:commDesc('ACCOUNT_STATUS') },
                {title:'common.title.recordStatus',name:'recordStatus',fn:getRecordStatusDesc },
            ]"
            >
        </Grid>
        <FormRow>
            <FormItemSign disabled :label="$t('csscl.agent.cashAccType')" prop="cashVPO.bankAccType">
                <Select v-model="pageObj.cashVPO.bankAccType" style="width:160px" disabled type='CLR_AGENT_CASH_ACC_TYPE'/>
            </FormItemSign>
            <FormItemSign disabled :label="$t('csscl.agent.currency')" label-width="80"  prop="cashVPO.currencyCode">
                <CurrencySearchInput disabled v-model="pageObj.cashVPO.currencyCode" style="width: 280px"/>
            </FormItemSign>
            <FormItemSign :label="$t('csscl.agent.cashAccountNumber')" label-width="160"   prop="cashVPO.bankAccountNo">
                <el-input disabled v-model="pageObj.cashVPO.bankAccountNo" />
            </FormItemSign>
        </FormRow>
        <FormRow>
            <FormItemSign :label="$t('csscl.agent.cashAccShortName')" prop="cashVPO.shortName">
                <el-input disabled v-model="pageObj.cashVPO.shortName"  style="width: 400px"/>
            </FormItemSign>
            <FormItemSign disabled :label="$t('csscl.agent.accountStatus')" label-width="130" prop="cashVPO.accountStatus">
                <Select v-model="pageObj.cashVPO.accountStatus" style="width:160px" disabled type='STATUS' />
            </FormItemSign>
        </FormRow>
        <FormRow>
            <FormItemSign :label="$t('csscl.agent.parentBankAccNo')" prop="cashVPO.parentBankAccountNo">
                <el-input disabled v-model="pageObj.cashVPO.parentBankAccountNo" />
            </FormItemSign>
            <FormItemSign :label="$t('csscl.agent.subAccType')" label-width="130" prop="cashVPO.subAccType">
                <el-input disabled v-model="pageObj.cashVPO.subAccType" />
            </FormItemSign>
        </FormRow>
        <FormRow>
            <FormItemSign :label="$t('csscl.agent.productType')" prop="cashVPO.productType">
                <el-input disabled v-model="pageObj.cashVPO.productType" />
            </FormItemSign>
            <FormItemSign :label="$t('csscl.agent.productSubType')" label-width="130" prop="cashVPO.productSubType">
                <el-input disabled v-model="pageObj.cashVPO.productSubType" />
            </FormItemSign>
        </FormRow>
        <FormRow>
            <FormItemSign :label="$t('csscl.agent.purpose')" prop="cashVPO.purposeList" class="check-box">
                <div>
                <el-checkbox-group disabled v-model="pageObj.cashVPO.purposeList" style="float: left;">
                    <el-checkbox :label="$t('csscl.agent.corporateAction')" style="display: block; padding-top: 10px;margin-left:10px" value="CA" />
                    <el-checkbox :label="$t('csscl.agent.siRec')" style="display: block; padding-top: 10px;margin-left:10px" value="SI_Receive" />
                    <el-checkbox :label="$t('csscl.agent.siDel')" style="display: block; padding-top: 10px;margin-left:10px" value="SI_Deliver" />
                </el-checkbox-group>
                <el-checkbox-group disabled v-model="pageObj.cashVPO.purposeList" style="float: left;">
                    <el-checkbox :label="$t('csscl.agent.default')" value="defaultIndCa" style="display: block; padding-top: 10px;margin-left:10px"/>
                    <el-checkbox :label="$t('csscl.agent.default')" value="defaultIndSiReceive" style="display: block; padding-top: 10px;margin-left:10px"/>
                    <el-checkbox :label="$t('csscl.agent.default')" value="defaultIndSiDeliver" style="display: block; padding-top: 10px;margin-left:10px"/>
                </el-checkbox-group>
            </div>
            </FormItemSign>
          
        </FormRow>
        <FormRow>
            <FormItemSign :label="$t('csscl.agent.remarks')" prop="cashVPO.remarks">
                <el-input disabled v-model="pageObj.cashVPO.remarks"  style="width: 700px"/>
            </FormItemSign>
        </FormRow>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import Grid from '~/pages/base/Grid.vue';
const formDisabled = ref(false);
import { getCommonDesc, getOid, commDesc, getRecordStatusDesc, updateListValue} from '~/util/Function.js';
import CurrencySearchInput  from '~/pages/base/CurrencySearchInput.vue'
import { addEnterObj } from "~/util/ModifiedValidate";
const props = defineProps([ "agentForm", "details"]);

const details = props.details;
const agentForm = props.agentForm;

const oid = ref();
const exbRecordStatus = ref();
//Start SIT-Cristin-172 AMOR,20240819
const emits = defineEmits(['getExbRow','change-value']);
//End SIT-Cristin-172 AMOR,20240819
const validFields = ['caPayMethod', 'settlePayMethod', 'custodianAccExbOid'];

const transeValue = (val)=>{
    //Start SIT-Cristin-172 AMOR,20240819
    emits("change-value", false);
    //End SIT-Cristin-172 AMOR,20240819
    //let row  = pageObj.custaccExbVPO;
    // for (var i = 0;i<agentForm.form.exbDataList.length;i++){
    //     //新增之后主键变了
    //     if(agentForm.form.exbDataList[i].custodianAccExbOid == row.custodianAccExbOid || agentForm.form.exbDataList[i].custodianAccExbOid == row.pendingOid){
    //         agentForm.form.exbDataList.splice(i,1);
    //     }
    // }
    // agentForm.form.exbDataList.push(Object.assign({},row))
    agentForm.form.exbDataList = updateListValue(agentForm.form.exbDataList, pageObj.custaccExbVPO);
}
// Start SIR-Cristin-R003,amor,20240816
const changePurposeCA = (val)=>{
    if(val.defaultIndCa=='Y'){
        return 'Y(Default)';
    }
    return val.purposeCa;
}
const changePurposeSiRec = (val)=>{
    if(val.defaultIndSiReceive=='Y'){
        return 'Y(Default)';
    }
    return val.purposeSiReceive;
}
const changePurposeSiDel = (val)=>{
    if(val.defaultIndSiDeliver=='Y'){
        return 'Y(Default)';
    }
    return val.purposeSiDeliver;
}
// End SIR-Cristin-R003,amor,20240816
const pageObj = reactive({
  accVPO : {},
  cashVPO : {},
  custaccExbVPO: {},
});
const accClick = (row) => {
   pageObj.accVPO = row;
   pageObj.custaccExbVPO={};
   pageObj.cashVPO={};
   oid.value = row.custodianAccOid;
   exbRecordStatus.value = row.recordStatus;
  
}
const exbFormRef = ref({});
exbFormRef.value.addField = () => {};
const exbClick = (row) => {
    pageObj.custaccExbVPO = row;
    exbFormRef.value.currentRow = row;
    addEnterObj(pageObj.custaccExbVPO, null, "custodianAccExbOid");
}
const cashClick = (row) => {
    row.purposeList = getPurpose(row);
    row.defaultList = getPurpose(row);
    pageObj.cashVPO = row;
}

const getPurpose = (record:{}) => {

let obj = {
    purposeCa:'CA',
    purposeSiReceive:'SI_Receive',
    purposeSiDeliver:'SI_Deliver',
    defaultIndCa:'defaultIndCa',
    defaultIndSiReceive:'defaultIndSiReceive',
    defaultIndSiDeliver:'defaultIndSiDeliver'

}
let purposeList = [];

for(let k in record){
  let o = obj[k];
  let val = record[k];
  if (o && val == "Y") {
    purposeList.push(o);
  }
}

return purposeList;
};

const afterSearchExb = (params, data) => {
    formDisabled.value = false;
    //Start SIT-Cristin-172 AMOR,20240819
    emits("change-value", true);
    //End SIT-Cristin-172 AMOR,20240819
    if(data.length==0){
        formDisabled.value = true;
    }
    if(agentForm.form.isApproveDetail){
        let updateDatas =  agentForm.form.listData;
        if(updateDatas?.length > 0){
            for(let i=0; i<updateDatas.length; i++){
                let row = updateDatas[i];
                if (data?.length > 0) {
                    for(let k=0; k<data.length; k++){
                        let cRow = data[k];
                        if(row.pendingOid === cRow.currentOid){
                            data[k].settlePayMethod = row.settlePayMethod;
                            data[k].caPayMethod = row.caPayMethod;
                        }
                    }
                }
            }
        }
    } else if (agentForm.form.exbDataList?.length > 0 && data.length > 0) {
      for (let i = 0; i < agentForm.form.exbDataList.length; i++) {
        let exb = agentForm.form.exbDataList[i];
        for (let j = 0; j < data.length; j++ ) {
          let dat = data[j];
          if (exb.currentOid == dat.currentOid) {
            dat.caPayMethod = exb.caPayMethod;
            dat.settlePayMethod = exb.settlePayMethod;
          }
        }
      }
    }
}

</script>

<style scoped>
.check-box .ep-form-item__label {
    min-height: 65px ;
}
</style>