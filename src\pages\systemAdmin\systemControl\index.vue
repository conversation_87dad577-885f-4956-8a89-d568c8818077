<template> 
  <BasePanel :searchParams="searchParams" :paramListData='paramListData' url="/datamgmt/api/v1/sysctrl/list" :params="{ modeEdit: 'Y' }"
    :showDetails="showDetails" :deleteRow="deleteRow" :editRow="editRow" ref="tableRef" :sortProp="{}">
    <template v-slot:searchPanel="slotProps">
      <FormRow>
        <ElFormItemProxy :label="$t('common.title.opCtryRegionCode')" prop="ctryRegionCode">
          <CtryRegionSearchInput v-model="slotProps.form.ctryRegionCode" showDesc="false" style="width: 110px" opCtryRegion />
        </ElFormItemProxy>
        <ElFormItemProxy label-width="50px" :label="$t('csscl.admin.ctrl.name')" prop="insttuName">
          <el-input v-model="slotProps.form.insttuName" style="width: 350px" maxlength="100" input-style="text-transform:none" />
        </ElFormItemProxy>
        <ElFormItemProxy label-width="60px" :label="$t('common.title.status')" prop="status">
          <Select v-model="slotProps.form.status" style="width: 150px" type='STATUS' :change="statusType(slotProps.form.status)"/>
        </ElFormItemProxy>
      </FormRow>
      <FormRow>  
        <ElFormItemProxy  :label="$t('common.title.recordStatus')" prop="multipleRecordStatus">
          <MultipleSelect v-model="slotProps.form.multipleRecordStatus" v-model:desc="paramListData.multipleRecordStatus"  />
        </ElFormItemProxy>
      </FormRow>
    </template>
    <template v-slot:tableColumn>
        <el-table-column align="left" header-align="center" sortable="custom" prop="ctryRegionCode" :label="$t('common.title.opCtryRegionCode')" width="300" />
        <el-table-column align="left" header-align="center" sortable="custom" prop="insttuName" :label="$t('csscl.admin.ctrl.name')" />
        <el-table-column align="left" header-align="center" sortable="custom" prop="status" :label="$t('common.title.status')" width="300" >
          <template #default="scope">
            {{ getCommonDesc('STATUS', scope.row.status) }}
          </template>
        </el-table-column>
        <el-table-column align="left" header-align="center" sortable="custom" prop="recordStsMkckAction" :label="$t('common.title.recordStatus')" width="300" >
          <template #default="scope">
            {{ getRecordStatusDesc(scope.row) }}
          </template>
        </el-table-column>
       
    </template>
  </BasePanel>
  <Details ref="detailsRef" :reload="reload" />
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance, watch } from 'vue';
import {
Search
} from '@element-plus/icons-vue'
import BasePanel from '~/pages/base/index.vue'
import Details from './Details.vue'
import CtryRegionSearchInput from '~/pages/base/CtryRegionSearchInput.vue'
import  { getCommonDesc, getRecordStatusDesc } from '~/util/Function.js';
const { proxy } = getCurrentInstance()
const paramListData = ref({});
const searchParams = ref({
  //顺序和上面绑定参数一致
  ctryRegionCode:"",
  insttuName:"",
  status:"",
  multipleRecordStatus:[],
});

const tableRef = ref();
const detailsRef = ref();
const showDetails = (row, disabled) => {
  detailsRef.value.showDetails(row, disabled);
}
const deleteRow = (row) => {
  proxy.$axios.delete("/datamgmt/api/v1/sysctrl?sysCtrlId="+row.sysCtrlOid).then((body) => {
    if (body.success) {
      tableRef.value.load();
    }
  });
}
const editRow = (row) => {
  detailsRef.value.editRow(row);
}
const reload = () => {
  tableRef.value.load();
}
//paramList 参数显示用的
function statusType(value){
  paramListData._value.status =  getCommonDesc('STATUS', value);
}
function recordStatusType(value){
  paramListData._value.recordStatus =  getCommonDesc('RECORD_STATUS', value);
}

</script>

<style>

</style>