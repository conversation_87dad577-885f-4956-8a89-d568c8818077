<template>
  <div class="button-container">
    <!-- <CButtons  :action="'Approve'" type="primary" @click="showDialog" v-if="mkckRow.recordStatus==='PA' ">Approve</CButtons> -->
    <el-button type="primary" @click="showDialog" v-if="(mkckRow.recordStatus==='PA'|| mkckRow.recordStatus==='PA1' ||mkckRow.recordStatus==='PA2' ) && $currentInfoStore.currentPermission['Approve']">Approve</el-button>
    
    <el-dialog v-model="showApproveDialog" title="Approval" width="600" :close-on-click-modal="false"  class="mkck-dialog" append-to-body :show-close="false">
      <template #header>
        <div class="mkckTitle">
          <span class="title-name">Approval</span>
        </div>
      </template>
      <br>
      <!-- Start SK-COMMON-0085,AMOR,******** -->
      <!-- <div style="padding-left: 480px;max-width:600px;display: flex;" v-if="props.mkckRow.recordStatus=='PA2' && tipsFlag" >
          <span style="background: #B0E0E6;">{{showCurrency}}</span><span style="padding-left: 20px;background: #B0E0E6;">{{showAmount}}</span>
        </div> -->
        <el-form-item label="Amount" v-if="(props.mkckRow.recordStatus=='PA1'||props.mkckRow.recordStatus=='PA2')&& tipsFlag" style="padding-right: 12px; padding-left: 20px;max-width:600px;">
          <span style="background: #B0E0E6;">{{showCurrency}}</span><span style="padding-left: 20px;background: #B0E0E6;">{{ thousFormat(showAmount) }}</span>
        </el-form-item>
      <!-- End SK-COMMON-0085,AMOR,******** -->
      <div style="padding-right: 12px; padding-left: 12px;max-width:600px;display: flex;" v-if="(props.mkckRow.recordStatus=='PA1'||props.mkckRow.recordStatus=='PA2')">
        <el-form-item label="Currency" style="width: 240px;">
          <el-input v-model="currency"  uppercase />
        </el-form-item>
        <el-form-item label="Amount" style="width: 351px;padding-left: 75px;">
          <InputNumber v-model="amount"  input-style="text-transform: none;" isNegative="true" />
        </el-form-item>
      </div>
      <el-col style="padding-right: 12px; padding-left: 19px;">
        <el-form-item label="Remark">
          <el-input type="textarea" v-model="remark" input-style="text-transform: none;" />
        </el-form-item>
      </el-col>
      <br>
      <div class="button-group">
        <el-button @click="handleCancel" class="ep-button-custom">Cancel</el-button>
        <!-- Start SK-COMMON-0085,AMOR,******** -->
        <el-button type="primary" @click="next" v-if="nextFlag" class="ep-button-custom">Next</el-button>
        <el-button type="primary" @click="handleApprove" :disabled="apprDisabled" class="ep-button-custom">OK</el-button>
        <!-- End SK-COMMON-0085,AMOR,******** -->
      </div>
      <br>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
  import { ref ,getCurrentInstance} from 'vue';
    import axios from 'axios';
    import ElementPlus, { ElMessage, ElMessageBox ,ElLoading} from 'element-plus';
    import type { Action } from 'element-plus'  
    import { commonCode } from '~/store/modules/commonCode'
    import { thousFormat } from '~/util/Function.js';
    const commonCodeStore = commonCode();
    // Start R2411A-52106, Tom.Li, 2024/09/02
    const props =  defineProps(['mkckRow','handleCancel','beforeApprove','limitCheck']);
    // End R2411A-52106, Tom.Li, 2024/09/02
    const { proxy } = getCurrentInstance();
    const showApproveDialog = ref(false);
    const remark = ref('');
    const amount = ref(0);
    const currency = ref('');
    //  Start SK-COMMON-0085,AMOR,********
    const nextFlag = ref(false);
    const tipsFlag = ref(false);
    const apprDisabled = ref(false);
    const showCurrency = ref(''); 
    const showAmount = ref(''); 
    const showData = ref([]);
    let showCount = 0;
    let funcId = '';
    //  End SK-COMMON-0085,AMOR,********
    // Start R2411A-52106, Tom.Li, 2024/09/02
    const showDialog = async () => {
      if( !await props.beforeApprove()){
        return false;
      }
      
      funcId = proxy.$currentInfoStore.getCurrentFuncId();
      if(funcId=="CSSCL_CASHM001" || funcId=="CSSCL_CASHM002"){
        let limitCheck = await props.limitCheck();
        if(limitCheck){
          let msg = proxy.$t("message.appr.limit.amount")
          ElMessageBox.alert(msg,'Warning');
          return false;
      }
      }
      
    // End R2411A-52106, Tom.Li, 2024/09/02
      showApproveDialog.value = true;
      remark.value = '';
      //  Start SK-COMMON-0085,AMOR,********
      if(props.mkckRow.recordStatus == 'PA1'||props.mkckRow.recordStatus == 'PA2'){
        
        if(funcId=="CSSCL_CASHM001" ){
          proxy.$axios.post('/cashmgmt/api/v1/bankstmt/reconciliation/cust/book/list/limit', {
            reconBankStmtOid: props.mkckRow.currentOid,
            reconBankStmtPendingOid: props.mkckRow.pendingOid
                }).then((body) => {
                  if(body.success && body.data.length>0) {
                    //Start SK-COMMON-0085,AMOR,********
                    if(body.data.length>1){
                      nextFlag.value = true;
                      apprDisabled.value = true;
                    }
                    //End SK-COMMON-0085,AMOR,********
                    tipsFlag.value = true;
                    showCurrency.value = body.data[0].currencyCode;
                    showAmount.value = body.data[0].txnAmt;
                    showData.value = body.data;
                  }
          });
        }
     
        if(funcId=="CSSCL_CASHM002"){
          proxy.$axios.get("/datamgmt/api/v1/data/cashinstr/appr?objectId=" + props.mkckRow.currentOid).then((body) => {
            if(body.success && body.data.length>0) {
              //Start SK-COMMON-0085,AMOR,********
              if(body.data.length>1){
                nextFlag.value = true;
                apprDisabled.value = true;
              }
              //End SK-COMMON-0085,AMOR,********
              tipsFlag.value = true;
              showCurrency.value = body.data[0].ccy;
              showAmount.value = body.data[0].amount;
              showData.value = body.data;
            }
          });
        }
       
        
      }
      //  End SK-COMMON-0085,AMOR,********
    };
    const next = async()=>{
      if(funcId=="CSSCL_CASHM001"){
          let checkRed =await proxy.$axios.get("/datamgmt/api/v1/makerchecker/check?mkckOid=" + props.mkckRow.mkckOid);
          if(checkRed.success ){
            let limitRes =await proxy.$axios.get("/datamgmt/api/v1/makerchecker/limit?eventName=" + "RECON_BANK_STMT"+"&eventOid="+props.mkckRow.currentOid+
            "&ccy="+currency.value.toUpperCase()+"&txnAmt="+amount.value);
            if(limitRes.success){
              if(currency.value.toUpperCase() !=showData.value[showCount].currencyCode.toUpperCase() || amount.value != showData.value[showCount].txnAmt){
                ElMessageBox.alert("The Input Value not Match.",'Warning');
                return;
              }
            }else{
                return;
              }
          }else{
            return;
          }
          showCount = showCount+1;
          showCurrency.value = showData.value[showCount].currencyCode;
          showAmount.value = showData.value[showCount].txnAmt;
      }
      if(funcId=="CSSCL_CASHM002"){
        let result =await proxy.$axios.get("/datamgmt/api/v1/makerchecker/check?mkckOid=" + props.mkckRow.mkckOid);
        if(result.success){
          let limitRes =await proxy.$axios.get("/datamgmt/api/v1/makerchecker/limit?eventName=" + "JOB_IN_DOC_DTL"+"&eventOid="+props.mkckRow.currentOid+
          "&ccy="+currency.value.toUpperCase()+"&txnAmt="+amount.value);
          if(limitRes.success ){
            if(currency.value.toUpperCase() !=showData.value[showCount].ccy.toUpperCase() || amount.value != showData.value[showCount].amount){
              ElMessageBox.alert("The Input Value not Match.",'Warning');
              return;
            }
          }else{
            return;
          }
        }else{
          return;
        }
        showCount = showCount+1;
        showCurrency.value = showData.value[showCount].ccy;
        showAmount.value = showData.value[showCount].amount;
      }
      // showCount = showCount+1;
      // if(funcId=="CSSCL_CASHM001"){
      //   showCurrency.value = showData.value[showCount].currencyCode;
      //   showAmount.value = showData.value[showCount].txnAmt;
      // }
      // if(funcId=="CSSCL_CASHM002"){
      //   showCurrency.value = showData.value[showCount].ccy;
      //   showAmount.value = showData.value[showCount].amount;
      // }
      currency.value = '';
      amount.value = 0;
      if(showData.value.length==showCount+1){
        nextFlag.value = false;
        apprDisabled.value = false;
      }
    }
    const handleCancel = () => {
        showApproveDialog.value = false;
        //  Start SK-COMMON-0085,AMOR,********
        showCount = 0;
        nextFlag.value = false;
        tipsFlag.value = false;
        currency.value = '';
        amount.value = 0;
        //  End SK-COMMON-0085,AMOR,********
    }

    const handleApprove = async () => {     
      //  Start SK-COMMON-0085,AMOR,********
      funcId = proxy.$currentInfoStore.getCurrentFuncId();
      // if(props.mkckRow.amount){
        if(funcId=="CSSCL_CASHM001" && (props.mkckRow.recordStatus == 'PA1'||props.mkckRow.recordStatus == 'PA2')){
          let limitRes =await proxy.$axios.get("/datamgmt/api/v1/makerchecker/limit?eventName=" + "RECON_BANK_STMT"+"&eventOid="+props.mkckRow.currentOid+
          "&ccy="+currency.value.toUpperCase()+"&txnAmt="+amount.value);
              if(limitRes.success){
                if(currency.value.toUpperCase() !=showData.value[showCount].currencyCode.toUpperCase() || amount.value != showData.value[showCount].txnAmt){
                  ElMessageBox.alert("The Input Value not Match.",'Warning');
                  return;
                }
              }else{
                  return;
                }
        }
        if(funcId=="CSSCL_CASHM002" && (props.mkckRow.recordStatus == 'PA1'||props.mkckRow.recordStatus == 'PA2')){
        let limitRes =await proxy.$axios.get("/datamgmt/api/v1/makerchecker/limit?eventName=" + "JOB_IN_DOC_DTL"+"&eventOid="+props.mkckRow.currentOid+
          "&ccy="+currency.value.toUpperCase()+"&txnAmt="+amount.value);
          if(limitRes.success ){
            if(currency.value.toUpperCase() !=showData.value[showCount].ccy.toUpperCase() || amount.value != showData.value[showCount].amount){
              ElMessageBox.alert("The Input Value not Match.",'Warning');
              return;
            }
          }else{
            return;
          }
      }

      //  End SK-COMMON-0085,AMOR,********
      // }
      const loading = ElLoading.service({
        lock: true,
        text: 'Loading',
        background: 'rgba(0, 0, 0, 0)',
      })
      const response = await axios.post('/datamgmt/api/v1/makerchecker/approval', {
                  flowMakerCheckerOid: props.mkckRow.mkckOid,
                  remark: remark.value,
                  status: "A",
          }).then((body) => {
            loading.close();
            if(body.success){
              ElMessageBox.alert("Approve successfully.", 'Success', {
                confirmButtonText: 'OK',
                type: 'success',
                callback: (action: Action) => {
                  commonCodeStore.fetchAll();

                  proxy.$axios.post('/datamgmt/api/v1/handler/unlock', {
                    flag: true,
                });
                  props.handleCancel && props.handleCancel();
                  if (funcId == "CSSCL_SDATA009"){
                    proxy.$axios.get('/datamgmt/api/v1/market/upload/to-data-holiday?mkckOid='+props.mkckRow.mkckOid);
                  }

                }
            });

          }
        });

      };



</script>

<style>
.mkck-dialog {
  padding: 0px !important;
}

.mkck-dialog .ep-dialog__header {
  background-color: #b31a25;
  padding: 0px 0px 0px 10px;
}
.mkckTitle .title-name {
  font-family: Arial;
  font-size: var(--ep-dialog-title-font-size);
  line-height: 30px;
  color: white
}

</style>
<style scoped>
.button-group .el-button {
  width: 80px;
  margin: 0 10px;
}

.button-group {
  display: flex;
  justify-content: center;
  align-items: center;
}

::v-deep .ep-textarea__inner {
  min-height: 150px !important;
}
</style>