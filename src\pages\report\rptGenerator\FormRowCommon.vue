<template>
    <FormRow v-if="datas?.length>0" v-for="(item, index) in datas">
            <!-- <FormItemSign :detailsRef="searchRef" v-if="init(item)"></FormItemSign> -->

                <FormItemSign :detailsRef="searchRef" v-if="item.keyType === 'SearchInput'" :label="$t(item.keyDesc)"
                    :prop="item.keyField">

                    <SearchInput :style="props.style" v-model="formInline[item.keyField]" @change="keychange(item)"
                        :url="item.keyDataSource" :title="$t(item.keyDesc)" :columns="[
                            {
                                title: $t(JSON.parse(item.keyOptions || null).codeTitle),
                                colName: JSON.parse(item.keyOptions || null).code,
                                width: '380px',
                            },
                            {
                                title: $t(JSON.parse(item.keyOptions || null).nameTitle),
                                colName: JSON.parse(item.keyOptions || null).name,
                            }
                        ]">
                    </SearchInput>
                </FormItemSign>

                <FormItemSign :detailsRef="searchRef" v-if="item.keyType === 'Search'" :label="$t(item.keyDesc)"
                    :prop="item.keyField">

                    <GeneralSearchInput v-model="formInline[item.keyField]" :inputStyle="item.style"
                                        :codeTitle="$t(item.keyDesc)" :maxlength="JSON.parse(item.keyFunction||null)?.maxlength"
                                        :searchType="item.keyDataSource" style="width:500px" />
                </FormItemSign>

                <FormItemSign :detailsRef="searchRef" v-else-if="item.keyType === 'dateTimeRange'"
                    :label="$t(item.keyDesc)" :prop="item.keyField">

                    <el-date-picker :style="item.style || props.style" v-model="item.keyValue" @change="keychange(item)"
                        type="datetimerange" range-separator="To" start-placeholder="Start date"
                        end-placeholder="End date" value-format="YYYY/MM/DD HH:mm:ss" />
                </FormItemSign>

                <ElFormItemProxy v-else-if="item.keyType === 'D2D'" class="generator-form-item">

                    <FormItemSign :detailsRef="searchRef" :label="$t(item.keyDesc)" :prop="item.keyField">

                        <DateItem style="width: 170px;" v-model="formInline[item.keyField]" />
                    </FormItemSign>

                    <FormItemSign :detailsRef="searchRef" label-width="40" label="To" :prop="item.to.keyField">

                        <DateItem style="width: 170px;" v-model="formInline[item.to.keyField]" />
                    </FormItemSign>

                </ElFormItemProxy>

                <FormItemSign :detailsRef="searchRef" v-else-if="item.keyType === 'date'" :label="$t(item.keyDesc)"
                    :prop="item.keyField">

                    <DateItem :style="item.style || props.style" v-model="formInline[item.keyField]" />

                </FormItemSign>
                <FormItemSign :detailsRef="searchRef" v-else-if="item.keyType === 'select'" :label="$t(item.keyDesc)"
                    :prop="item.keyField">

                    <Select v-model="formInline[item.keyField]"
                        :source="(item.keyOptions || '').startsWith('source:') ? source : JSON.parse(item.keyOptions || null)"
                        :type="item.keyDataSource" :style="item.style || props.style" :change="(val)=>{handleSelectChange(val,item.keyField,item.keyOptions)}"/>

                </FormItemSign>
                <FormItemSign :detailsRef="searchRef" v-else-if="item.keyType === 'text'" :label="$t(item.keyDesc)"
                    :prop="item.keyField">
                    <el-input v-model="formInline[item.keyField]" :maxlength="JSON.parse(item.keyFunction||null)?.maxlength" :style="item.style || props.style" class='text-none'></el-input>
                </FormItemSign>
                <FormItemSign :detailsRef="searchRef" v-else-if="item.keyType === 'CSearch'" :label="$t(item.keyDesc)" :prop="item.keyField">
                    <CommonSearchInput  v-model="formInline[item.keyField]" maxlength="50" inputStyle="width:160px" :commType="item.keyDataSource"
                                        :codeTitle="$t(item.keyDesc)" style="width:500px"
                    showDesc="false" />
                
                </FormItemSign>
        </FormRow>
</template>
<script lang="ts" setup>
import { ref,watch,getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance()
import CommonSearchInput from '~/pages/base/CommonSearchInput.vue';
import FormItemSign from '~/pages/base/FormItemSign.vue';
const props = defineProps(['formInline','searchRef','reportTemplateCode', 'reportTemplateDesc', 'style', 'init', 'keychange','source','handleSelectChange','fieldsDtl','rules','details','genRules'])
import { commonRules, showValidateMsg } from '~/util/Validators.js';
import {currentDate, dateFormat, getEnvConfigVal} from "~/util/Function";
const datas = ref();
let curProcDate = "";
(async ()=>{ await currentDate().then( (v)=>{ curProcDate = dateFormat( v ); } ); })();
const curDate = (() => {
  let d = new Date();
  d.setHours(0, 0, 0, 0);
  return dateFormat(d)
})();

const getData = async ()=>{
    if(['R000701A','R000801A'].includes(props.reportTemplateCode)){
        const msg = await proxy.$axios.post("/rptsched/api/v1/report/criteria/list", {
            param: { reportTemplateCode: props.reportTemplateCode+'_SON',keyDataSource:props.formInline['field_1'] },
            current: 1,
            pageSize: 50,
        });
        if (msg.success) {
            datas.value = msg.data.data;
        }
    } 
}
watch(()=>props.reportTemplateCode, ()=>{
    if(!['R000701A','R000801A'].includes(props.reportTemplateCode)){
        datas.value = [];
    }
});
watch(() =>props.formInline['field_1'],()=>{
        if(!['R000701A','R000801A'].includes(props.reportTemplateCode)){
            return;
        }
        Object.keys(props.formInline).forEach(e => {
            if(e!='field_1'){
                delete (props.formInline[e]);
            }
        });
        if(props.formInline['field_1']){
            getData();
        }else{
            datas.value = [];
        }
    } 
)
watch(() => datas.value, (newVal) => {
    props.fieldsDtl.fields = {};
    props.details.fields = {};
    for (let i = 0; i < datas.value?.length; i++) {

        let data = datas.value[i];
        props.details.fields[data.keyField] = data.keyDesc;
        props.fieldsDtl.fields[data.keyField] = data.keyDesc;
        props.rules[data.keyField] = [];
    }


    let arr = (Object.keys(props.genRules));
    arr?.forEach(e => {
        delete props.genRules[e];
    });

    for (let i = 0; i < datas.value?.length; i++) {
        let data = datas.value[i];
        if (data.keyType == 'D2D') {
            data.to = datas.value[i + 1];
            datas.value.splice(i + 1, 1)
        }

        data = Object.assign({}, data);
        do {
            data.isExists = false;
            if (data.keyType == 'D2D' || data.keyType == 'date') {
              if (data.keyOptions?.startsWith("def:")) {
                let def = data.keyOptions.replace("def:", "");
                if (def == "curProcDate") {
                  props.formInline[data.keyField] = curProcDate;
                }
                if (def == "curDate") {
                  props.formInline[data.keyField] = curDate;
                }
              }
            }
            if (data.keyRules) {
                let rs = data.keyRules.split("\|");
                for (let j = 0; j < rs.length; j++) {
                    let rl = rs[j].split(",")
                    if (commonRules[rl[0]]) {
                        
                        for(let z = 1; z < rl.length; z++) {
                            rl['orig'+z] = rl[z];
                            if (rl[z].startsWith("getVal:")) { 
                                rl[z] = ()=>{ return formInline[ rl['orig'+z].split(":")[1] ] }
							} else if (rl[z].startsWith("conf:")) {
								let arr = (rl['orig'+z].split(":")[1]).split("^");
								let key = "VITE_YEAR_DAYS";
								let defVal = arr[0];
								if (arr.length > 1) {
									key = arr[0];
									defVal = arr[1];
								}
								rl[z] = getEnvConfigVal(key, defVal);
                            } else if (rl[z].startsWith("label:")) {
                                rl[z] = proxy.$t( rl['orig'+z].split(":")[1] )
                            }
                        }
                        if (typeof commonRules[rl[0]] == 'object') {
                            props.rules[data.keyField].push(commonRules[rl[0]]);
                        } else {
                            props.rules[data.keyField].push(commonRules[rl[0]](rl[1],rl[2],rl[3],rl[4],rl[5],rl[6]));
                        }
                        console.log(props.rules)
                    }

                }

            }
            if (data.keyType == 'D2D' && data.to) {
                data = Object.assign({}, data.to);
                data.isExists = true;
            }
        } while (data.isExists);
    }

});
</script>