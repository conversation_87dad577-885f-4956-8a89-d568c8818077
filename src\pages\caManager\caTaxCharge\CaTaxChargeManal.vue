<template>
    <EditGrid ref="editGridRef" :data="tableData" :formData="manalForm"
      :is-show-search="false" :showAddDeleteButtons="true"
      :formRules="manalFormRules"
      :onSaveForm="handleFormSave" :beforeSearch="beforeSearch"
      :handleChangePage="handleChangePage" :beforeChangePage="handleBeforeChangePage"
      :enableRecordStatusHighlight="true"
      :isDeleteButtonDisabled="isDeleteButtonDisabled" :fieldDisabledRules="fieldDisabledRules">

      <template #tableColumnFront>
        <el-table-column prop="tradingAccountCode" :label="$t('csscl.ca.common.custodyAccountNo')" align="center" />
        <el-table-column prop="tradeReferenceNumber" :label="$t('csscl.ca.common.siTradeReferenceNumber')" align="center"/>
        <el-table-column prop="instrumentCode" :label="$t('csscl.ca.common.securityId')" align="center"/>
        <el-table-column prop="siType" :label="$t('csscl.ca.common.siType')" align="center"/>
        <el-table-column prop="marketValueDateStock" :label="$t('csscl.ca.common.marketStockSettlementDate')" align="center"/>
        <el-table-column prop="tradeQuantity" :label="$t('csscl.ca.common.quantity')" align="center"/>
        <el-table-column prop="tranCcyCode" :label="$t('csscl.ca.common.txnCurrency')" align="center"/>
        <el-table-column prop="tranAmount" :label="$t('csscl.ca.common.txnAmount')" align="center"/>
        <el-table-column prop="marketCode" :label="$t('csscl.ca.common.exchangeCode')" align="center"/>
        <el-table-column prop="actualSettleCcyCode" :label="$t('csscl.ca.common.actualDRCRCurrency')" align="center"/>
        <el-table-column prop="actualSettleAmount" :label="$t('csscl.ca.common.actualDRCRAmount')" align="center"/>
        <el-table-column prop="cashPostMethodCa" :label="$t('csscl.ca.common.cashSettlement')" align="center">
          <template #default="scope">
            {{ getCommonDesc('CASH_POST_METHOD_CA', scope.row.cashPostMethodCa) }}
          </template>
        </el-table-column>
        <el-table-column prop="shadowActualPostingDate" :label="$t('csscl.ca.common.actualPostingDate')" align="center"/>
        <el-table-column prop="shadowActualValueDate" :label="$t('csscl.ca.common.actualValueDate')" align="center"/>
        <el-table-column prop="txnReferenceNumber" :label="$t('csscl.ca.common.transactionReferenceNumber')" align="center"/>
        <el-table-column prop="clientCashAccountNumber" :label="$t('csscl.ca.common.clientCashAccount')" align="center"/>
        <el-table-column prop="custodianShadowCashAccountNumber" :label="$t('csscl.ca.common.shadowCashAccount')" align="center"/>
        <el-table-column prop="fxIndicator" :label="$t('csscl.ca.common.fx')" align="center">
          <template #default="scope">
            {{ getCommonDesc('YES_NO', scope.row.fxIndicator) }}
          </template>
        </el-table-column>
        <el-table-column prop="fxCcyCode" :label="$t('csscl.ca.common.fxCurrency')" align="center"/>
        <el-table-column prop="fxTxnReferenceNumber" :label="$t('csscl.ca.common.fxReferenceNumber')" align="center"/>
        <el-table-column prop="payStatus" :label="$t('csscl.ca.common.payStatus')" align="center">
          <template #default="scope">
            {{ getCommonDesc('YES_NO', scope.row.payStatus) }}
          </template>
        </el-table-column>
      </template>
      <template #editForm="formSlot">
        <FormRow>
          <FormItemSign :detailsRef="formSlot.detailsRef" :label="$t('csscl.ca.common.custodyAccountNo')" prop="tradingAccountCode" label-width="150px">
            <GeneralSearchInput v-model="formSlot.formData.form.tradingAccountCode" style="width:180px" searchType="custodyAcct" showDesc="false"/>
          </FormItemSign>
          <FormItemSign :detailsRef="formSlot.detailsRef" :label="$t('csscl.ca.common.siTradeReferenceNumber')" prop="tradeReferenceNumber" label-width="150px">
            <InputText v-model="formSlot.formData.form.tradeReferenceNumber" maxlength="20" style="width: 180px" />
          </FormItemSign>
          <FormItemSign :detailsRef="formSlot.detailsRef" :label="$t('csscl.ca.common.securityId')" prop="instrumentCode" label-width="130px">
            <GeneralSearchInput v-model="formSlot.formData.form.instrumentCode" style="width:180px" searchType="instrumentCode" showDesc="false"/>
          </FormItemSign>
          <FormItemSign :detailsRef="formSlot.detailsRef" :label="$t('csscl.ca.common.siType')" prop="siType" label-width="120px">
            <Select v-model="formSlot.formData.form.siType" style="width: 180px;" type="status"/>
          </FormItemSign>
          <FormItemSign :detailsRef="formSlot.detailsRef" :label="$t('csscl.ca.common.marketStockSettlementDate')" prop="marketValueDateStock" label-width="200px">
            <DateItem v-model="formSlot.formData.form.marketStockSettlementDate" style="width: 160px;"/>
          </FormItemSign>
        </FormRow>
        <FormRow>
          <FormItemSign :detailsRef="formSlot.detailsRef" :label="$t('csscl.ca.common.quantity')" prop="tradeQuantity" label-width="150px" >
            <el-input v-model="formSlot.formData.form.tradeQuantity" style="width: 150px" type="number"/>
          </FormItemSign>
          <FormItemSign :detailsRef="formSlot.detailsRef" :label="$t('csscl.ca.common.txnCurrency')" prop="tranCcyCode" label-width="150px">
            <GeneralSearchInput v-model="formSlot.formData.form.tranCcyCode" style="width:180px" searchType="currency" showDesc="false"/>
          </FormItemSign>
          <FormItemSign :detailsRef="formSlot.detailsRef" :label="$t('csscl.ca.common.txnAmount')" prop="tranAmount" label-width="130px" >
            <el-input v-model="formSlot.formData.form.tranAmount" style="width: 180px" type="number"/>
          </FormItemSign>
          <FormItemSign :detailsRef="formSlot.detailsRef" :label="$t('csscl.ca.common.exchangeCode')" prop="marketCode" label-width="120px">
            <GeneralSearchInput v-model="formSlot.formData.form.marketCode" style="width:180px" searchType="exchangeCode" showDesc="false"/>
          </FormItemSign>
          <FormItemSign :detailsRef="formSlot.detailsRef" :label="$t('csscl.ca.common.actualDRCRCurrency')" prop="actualSettleCcyCode" label-width="200px">
            <GeneralSearchInput v-model="formSlot.formData.form.actualSettleCcyCode" style="width:160px" searchType="currency" showDesc="false"/>
          </FormItemSign>
        </FormRow>
        <FormRow>
          <FormItemSign :detailsRef="formSlot.detailsRef" :label="$t('csscl.ca.common.actualDRCRAmount')" prop="actualSettleAmount" label-width="150px" >
            <el-input v-model="formSlot.formData.form.actualSettleAmount" style="width: 150px" type="number"/>
          </FormItemSign>
          <FormItemSign :detailsRef="formSlot.detailsRef" :label="$t('csscl.ca.common.cashSettlement')" prop="cashPostMethodCa" label-width="150px">
            <Select v-model="formSlot.formData.form.cashPostMethodCa" style="width: 180px;" type="CASH_POST_METHOD_CA"/>
          </FormItemSign>
          <FormItemSign :detailsRef="formSlot.detailsRef" :label="$t('csscl.ca.common.actualPostingDate')" prop="shadowActualPostingDate" label-width="130px">
            <DateItem v-model="formSlot.formData.form.shadowActualPostingDate" type="date" style="width: 180px;"/>
          </FormItemSign>
          <FormItemSign :detailsRef="formSlot.detailsRef" :label="$t('csscl.ca.common.actualValueDate')" prop="shadowActualValueDate" label-width="120px">
            <DateItem v-model="formSlot.formData.form.shadowActualValueDate" type="date" style="width: 180px;"/>
          </FormItemSign>
          <FormItemSign :detailsRef="formSlot.detailsRef" :label="$t('csscl.ca.common.transactionReferenceNumber')" prop="txnReferenceNumber" label-width="200px">
            <InputText v-model="formSlot.formData.form.txnReferenceNumber" maxlength="20" style="width: 160px" />
          </FormItemSign>
        </FormRow>
        <FormRow>
          <FormItemSign :detailsRef="formSlot.detailsRef" :label="$t('csscl.ca.common.clientCashAccount')" prop="clientCashAccountNumber" label-width="150px" >
            <GeneralSearchInput v-model="formSlot.formData.form.clientCashAccountNumber" style="width:180px" searchType="custodianCashAcc" showDesc="false"/>
          </FormItemSign>
          <FormItemSign :detailsRef="formSlot.detailsRef" :label="$t('csscl.ca.common.shadowCashAccount')" prop="custodianShadowCashAccountNumber" label-width="150px" >
            <GeneralSearchInput v-model="formSlot.formData.form.custodianShadowCashAccountNumber" style="width:180px" searchType="custodianCashAcc" showDesc="false"/>
          </FormItemSign>
          <FormItemSign :detailsRef="formSlot.detailsRef" :label="$t('csscl.ca.common.fx')" prop="fxIndicator" label-width="130px">
            <Select v-model="formSlot.formData.form.fxIndicator" style="width: 180px;" type="YES_NO"/>
          </FormItemSign>
          <FormItemSign :detailsRef="formSlot.detailsRef" :label="$t('csscl.ca.common.fxCurrency')" prop="fxCcyCode" label-width="120px">
            <GeneralSearchInput v-model="formSlot.formData.form.fxCcyCode" style="width:180px" searchType="currency" showDesc="false"/>
          </FormItemSign>
          <FormItemSign :detailsRef="formSlot.detailsRef" :label="$t('csscl.ca.common.fxReferenceNumber')" prop="fxTxnReferenceNumber" label-width="200px">
            <InputText v-model="formSlot.formData.form.fxTxnReferenceNumber" maxlength="20" style="width: 160px" />
          </FormItemSign>
        </FormRow>
        <FormRow>
          <FormItemSign :detailsRef="formSlot.detailsRef" :label="$t('csscl.ca.common.payStatus')" prop="payStatus" label-width="150px">
            <Select v-model="formSlot.formData.form.payStatus" style="width: 150px;" type="YES_NO"/>
          </FormItemSign>
          <FormItemSign />
          <FormItemSign />
          <FormItemSign />
          <FormItemSign />
        </FormRow>
      </template>
    </EditGrid>
    <br />
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue';
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus';
import { getOid, saveMsgBox, getCommonDesc } from '~/util/Function.js';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import { commonRules, showValidateMsg } from '~/util/Validators.js';
import EditGrid from '~/components/Ca/CaEditGrid.vue';
import { getTimeZone } from "~/util/DateUtils";

const { proxy } = getCurrentInstance();
const props = defineProps(['reload']);
// 定义表格数据
const tableData = ref([]);
const editGridRef = ref();
const taxChargeHeaderRow = ref({});
const manalForm = reactive({
  form: {}
});
const manalFormRules = reactive<FormRules<manalForm>>({
  tradingAccountCode: [
    commonRules.required,
  ],
  caEventReferenceNumber:[
    commonRules.required,
  ]
});
const generateRequestparam = (data) => {
  let params = {
    header: {timezone: getTimeZone(), lang:"en_US"},
    data: data
  };
  return params;
}

const isResponseSuccess = (response) => {
  if (!response || !response.header) {
    return false;
  }
  return response.header.code === '000000';
}

const getErrorMessage = (errorMessage) => {
  return errorMessage || 'Unknown error';
}

const handleFormSave = (formData) => {
  formData.isChanged = true;
  // 查找是否已存在该记录
  const index = tableData.value.findIndex(item => item.oid === formData.oid);
  if (index !== -1) {
    // 如果找到匹配记录，更新现有记录
    Object.assign(tableData.value[index], formData);
    // 标记为更新状态
    // tableData.value[index].mkckAction = 'U';
  } else {
    tableData.value.push(formData);
  }
}

const isDeleteButtonDisabled = (record) => {
  const rtn = record?.systemIndicator === 'Y';
  return rtn;
}

// 处理保存
const handleSave = async () => {
  // TODO：(只简单实现新增&更新操作，不做特殊处理)
  const recordsToSave = [
    ...tableData.value
  ];
  if (recordsToSave.length === 0) {
    return true;
  }
  let params = generateRequestparam({
      caEventReferenceNumber: taxChargeHeaderRow.value?.caEventReferenceNumber,
      caEventTaxChargeHdrOid: taxChargeHeaderRow.value?.caEventTaxChargeHdrOid,
      taxAndChargeManualJoinVOs: recordsToSave
  });
  const response = await proxy.$axios.post("/bff/ca/api/v1/ca-event-tax-charge/add-or-update-tax-charge-manual", params);
  if (isResponseSuccess(response)) {
    loadTableData(taxChargeHeaderRow.value);
  } else {
    ElMessage.error(getErrorMessage(response?.header?.message));
    return false;
  }
  return true;
};

const showDetails = async (row:any, isdoubleCheck:any) => {
  if (editGridRef.value) {
    taxChargeHeaderRow.value = row;
    loadTableData(row);
  }
}

// 从后端获取数据的方法
const loadTableData = async (row:any) => {
  if (!row?.caEventReferenceNumber) return;
  let params = generateRequestparam({
      caEventReferenceNumber: row?.caEventReferenceNumber,
      caEventTaxChargeHdrOid: row?.caEventTaxChargeHdrOid,
  });
  const response = await proxy.$axios.post("/bff/ca/api/v1/ca-event-tax-charge/get-tax-charge-manual-page-list", params);
  if (isResponseSuccess(response)) {
    tableData.value = response.data.items;
  } else {
    const errorMsg = getErrorMessage(response?.header?.message);
    ElMessage.error('Load Ca Entitlement Details data fail: ' + errorMsg);
  }
};

const handleChangePage = async (newPage:any) => {
    await loadTableData(newPage);
}

const beforeSearch = async (search:any, params:any) => {
    return true;
}

const handleBeforeChangePage = async () => {
  return true;
}

// 定义字段禁用规则
const fieldDisabledRules = {
  // 始终禁用的字段
  caEventReferenceNumber: true,
  tradingAccountCode: true,
};

const clearData = async() => {
  tableData.value = [];
  editGridRef.value.total = 0;
  editGridRef.value.cancelForm();
}

// 添加计算属性用于判断字段是否禁用
const isFieldDisabled = (fieldName) => {
  const rule = fieldDisabledRules[fieldName];
  if (typeof rule === 'boolean') return rule;
  return false;
};

defineExpose({
  clearData,
  showDetails,
  handleSave,
});

</script>

<style>

</style>