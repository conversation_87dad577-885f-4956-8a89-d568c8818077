<template>
    <BaseDetails ref="details" :handleSave="handleSave" :reload="reload" :viewOriginalForm="viewOriginalForm" :form="ruleForm" >
        <el-form :validateOnRuleChange="false" :disabled="formDisabled" ref="ruleFormRef" style="width: 100%" :model="ruleForm.form" status-icon>
        <FormRow>  
            <FormItemSign :detailsRef="details" :label="$t('csscl.incSwift.receivedDateAndTime')" label-width="180" prop="receiveDt">
            <el-input v-model="ruleForm.form.receiveDt" maxlength="3" style="width: 180px" :disabled="editDis" /> 
            </FormItemSign>
            <FormItemSign :detailsRef="details" :label="$t('csscl.incSwift.messageID')" label-width="180" prop="messageId">
            <el-input v-model="ruleForm.form.messageId" maxlength="3" style="width: 280px" :disabled="editDis" />
            </FormItemSign>
            <FormItemSign :detailsRef="details" :label="$t('csscl.incSwift.messageType')" label-width="120" prop="messageType">
            <el-input v-model="ruleForm.form.messageType" maxlength="3" style="width: 180px" :disabled="editDis" />
            </FormItemSign>
        </FormRow>
        <FormRow>  
            <FormItemSign :detailsRef="details" :label="$t('csscl.incSwift.senderBIC')" label-width="180" prop="senderBicCode">
            <el-input v-model="ruleForm.form.senderBicCode" maxlength="3" style="width: 180px" :disabled="editDis" /> 
            </FormItemSign>
            <FormItemSign :detailsRef="details" :label="$t('csscl.incSwift.placeOfSettlement')" label-width="180" prop="placeOfSettl">
            <el-input v-model="ruleForm.form.placeOfSettl" maxlength="3" style="width: 280px" :disabled="editDis" />
            </FormItemSign>
            <FormItemSign :detailsRef="details" :label="$t('csscl.incSwift.settlementType')" label-width="120" prop="settlType">
            <el-input v-model="ruleForm.form.settlType" maxlength="3" style="width: 180px" :disabled="editDis" />
            </FormItemSign>
        </FormRow>
        <FormRow>  
            <FormItemSign :detailsRef="details" :label="$t('csscl.incSwift.pendingQueueStatus')" label-width="180" prop="status">
                <Select v-model="ruleForm.form.status" type="PEND_QUE_STS" :change="statusType(ruleForm.form.status)"  style="width: 180px"/>
            </FormItemSign>
            <FormItemSign :detailsRef="details" :label="$t('csscl.incSwift.pageCode')" label-width="180" prop="pageCode">
                <el-input v-model="ruleForm.form.pageCode" maxlength="3" style="width: 280px" :disabled="editDis" />
            </FormItemSign>
            <FormItemSign :detailsRef="details" :label="$t('csscl.incSwift.recordDate')" label-width="120" prop="recordDt">
                <el-input v-model="ruleForm.form.recordDt" maxlength="3" style="width: 180px" :disabled="editDis" />
            </FormItemSign>
        </FormRow>
        </el-form>
    </BaseDetails>
</template>

<script lang="ts" setup>
import { ref, reactive, getCurrentInstance } from 'vue';
import BaseDetails from '~/pages/base/Details.vue';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import { getOid, getCommonDesc } from '~/util/Function.js';


const { proxy } = getCurrentInstance();
const props = defineProps(['reload']);
const details = ref();
const formDisabled = ref(false);
const editDis = ref(false);

const ruleFormRef = ref();
const ruleForm = reactive({
    form: {
      opCtryRegionCode: "",
      currencyCode: "",
      descpt:"",
      restrictedCurrency: "",
      decimalPoint: "",
      calMethodCode: "",
      status: "",
    }
});

const showDetails = (row, isdoubleCheck) => {
    isdoubleCheck = true;
    if(isdoubleCheck||row.recordStatus==='PA'){
        formDisabled.value = true;
    }else{
        formDisabled.value = false;
    }
    details.value.showDetails(row, formDisabled.value)

    ruleForm.form = {};
    details.value.currentRow={};
    editDis.value = false;
    editRow(row, isdoubleCheck);

}
const editRow = (row, disabled,newId) => {
    const oid = newId || (row?.currentOid && getOid(row, disabled, proxy));
    if (oid) {
        proxy.$axios.get("/datamgmt/api/v1/swiftmsg?swiftMsgId=" + oid ).then((body) => {
            if (body.success) {
                ruleForm.form = body.data;
                details.value.currentRow = body.data;
            }
        });
        editDis.value = true;
    }
}

const viewOriginalForm = (pendingOid, isDisabled) => {
    formDisabled.value = isDisabled;
  proxy.$axios.get("/datamgmt/api/v1/swiftmsg?swiftMsgId="+pendingOid).then((body) => {
        if(body.success) {
            ruleForm.form = body.data;
            details.value.currentRow.value = body.data;
        }
    });
    editDis.value = false;
}

function statusType(value) {
  ruleForm.form.status = getCommonDesc('PEND_QUE_STS', value);
}

defineExpose({
  showDetails,
});


</script>

<style></style>