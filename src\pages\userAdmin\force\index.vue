<template>
  <BasePanel :reload="reload" :searchParams="searchParams" url="/auth/api/v1/user/login/list" :params="{ modeEdit: 'Y' }"
    :showDetails="showDetails" :deleteRow="deleteRow" :isHideSearch="true" :editRow="editRow" :sortProp="{}"
    :isMultiple="true" :selectable="selectable" ref="gridRef" :isHideParamList="true"
    :hideOperation="true">
    <template v-slot:tableColumn>
      <el-table-column sortable="custom" prop="userId" :label="$t('User ID')" width="250" />
      <el-table-column prop="userName" :label="$t('User Name')" />
      <el-table-column prop="ipAddr" :label="$t('IP Address')" width="300" />
      <el-table-column prop="sysUpdateDate" :label="$t('Last Login Date and Time')" width="250" />
    </template>
  </BasePanel>
  <el-space style="height: 39px; width: 30px; margin-right: 75px; float: right;">
    <el-button type="primary" @click="handleForceLogout">Force Logout</el-button>
  </el-space>
</template>
<script lang="ts" setup>
import { ref, getCurrentInstance } from 'vue';
import BasePanel from '~/pages/base/index.vue'
import { useCookies } from "vue3-cookies";
const { proxy } = getCurrentInstance();
const searchParams = ref({});
const { cookies } = useCookies();
const gridRef = ref();
function selectable(row) {
  if(row.userId == cookies.get("username")){
    return false;
  }
  return true;
}
const reload = () => {
  gridRef.value.load();
}
const handleForceLogout = async () => {
  let logoutList = [];
  for(let key in gridRef.value.selectedRecord) {
    logoutList.push(gridRef.value.selectedRecord[key].userId);
  }
  if (logoutList == null || logoutList.length == 0) {
    ElMessageBox.alert("At least choose one.", 'Warning');
    return;
  }
  ElMessageBox.confirm(
    'Are you sure to kill the session(s)?',
    'Warning',
    {
      confirmButtonText: 'OK',
      cancelButtonText: 'Cancel',
      type: 'warning',
    })
    .then(() => {
      proxy.$axios.post("/datamgmt/api/v1/handler/unlock/all?userIdList=" + logoutList).then((body) => {
        if (body.success) {
          proxy.$axios.post("/auth/api/v1/user/forcelogout?userIdList=" + logoutList).then((body)=>{
            if (body.success) {
              ElMessageBox.alert(body.alertMessage[0], 'Warning');
            }
            reload();
          });
        }
      });
      
      
    })
    .catch(() => {

    })

}

const detailsRef = ref();
const showDetails = (row) => {
  detailsRef.value.details.showDetails(row);
}
const deleteRow = (row) => {
  console.log("Delete ...");
}
const editRow = (row) => {
  detailsRef.value.editRow(row);
}

</script>

<style></style>