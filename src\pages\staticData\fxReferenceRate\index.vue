<template> 
  <BasePanel :searchParams="searchParams" :paramListData='paramListData' url="/datamgmt/api/v1/fxrate/list" :params="{ modeEdit: 'Y' }"
    :showDetails="showDetails" :deleteRow="deleteRow" :editRow="editRow" ref="tableRef" :sortProp="{}" :rules="rules"
    :beforeSearch="beforeSearch">
    <template v-slot:searchPanel="slotProps">
      <FormRow>
        <ElFormItemProxy :label="$t('common.title.opCtryRegionCode')" label-width="220" prop="opCtryRegionCode" >
          <CtryRegionSearchInput v-model="slotProps.form.opCtryRegionCode" 
            showDesc="false" 
            style="width: 120px" 
            opCtryRegion />
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.fxRate.fromCurrency')" label-width="120" prop="currency" >
          <CurrencySearchInput v-model="slotProps.form.currency" 
            codeTitle="csscl.fxRate.fromCurrency"
            showDesc="false" />
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.fxRate.toCurrency')" label-width="100"  prop="baseCurrency" >
          <CurrencySearchInput v-model="slotProps.form.baseCurrency" 
            codeTitle="csscl.fxRate.toCurrency"
            showDesc="false" />
        </ElFormItemProxy>
      </FormRow>

      <FormRow>
        <ElFormItemProxy>

          <ElFormItemProxy :label="$t('csscl.fxRate.rateDateFrom')" label-width="220" prop="beginRateDate">
            <DateItem :validate-event="false" v-model="slotProps.form.beginRateDate"
                      :title="$t('message.earlier.equal.curdate', [$t('csscl.fxRate.rateDateFrom')] ) + '\r' +
                        $t('message.earlier.equal.dateto', [$t('csscl.fxRate.rateDateFrom'), $t('csscl.fxRate.rateDateTo')] )" />
          </ElFormItemProxy>
          <ElFormItemProxy :label="$t('common.title.date.to')" label-width="45" prop="endRateDate" :hideLabel="$t('csscl.fxRate.rateDateTo')">
            <DateItem :validate-event="false" v-model="slotProps.form.endRateDate"
                      :title="$t('message.earlier.equal.curdate', [$t('csscl.fxRate.rateDateTo')] )"/>
          </ElFormItemProxy>

        </ElFormItemProxy>
          
        <ElFormItemProxy :label="$t('csscl.fxRate.dataSource')" label-width="120"  prop="sourceSys">
          <Select v-model="slotProps.form.sourceSys" type="FX_DATA_SOURCE" v-model:desc="paramListData.sourceSys"  />
        </ElFormItemProxy>
        <ElFormItemProxy></ElFormItemProxy>
      </FormRow>
    </template>
    <template v-slot:tableColumn>
        <el-table-column align="left" header-align="center" sortable="custom" prop="opCtryRegionCode" :label="$t('common.title.opCtryRegionCode')" width="270" />
        <el-table-column align="left" header-align="center" sortable="custom" prop="currency" :label="$t('csscl.fxRate.fromCurrency')" width="200" />
        <el-table-column align="left" header-align="center" sortable="custom" prop="baseCurrency" :label="$t('csscl.fxRate.toCurrency')" width="200" />
        <el-table-column align="right" header-align="center" sortable="custom" prop="rate" :label="$t('csscl.fxRate.rate')" width="200" >
          <template #default="scope">
            {{ Number(scope.row.rate).toFixed(6) }}
          </template>
        </el-table-column>
        <el-table-column align="left" header-align="center" sortable="custom" prop="rateDate" :label="$t('csscl.fxRate.rateDate')" width="200" />
        <el-table-column align="left" header-align="center" sortable="custom" prop="sourceSys" :label="$t('csscl.fxRate.dataSource')" >
          <template #default="scope">
            {{ getCommonDesc('FX_DATA_SOURCE', scope.row.sourceSys) }}
          </template>
        </el-table-column>
        <el-table-column align="left" header-align="center" sortable="custom" prop="status" :label="$t('common.title.status')" width="160" >
          <template #default="scope">
            {{ getCommonDesc('STATUS', scope.row.status) }}
          </template>
        </el-table-column>
        <el-table-column align="left" header-align="center" sortable="custom" prop="recordStsMkckAction" :label="$t('common.title.recordStatus')" width="220" >
          <template #default="scope">
            {{ getCommonDesc('RECORD_STATUS', scope.row.recordStatus) }}
            <span v-if="scope.row.recordStatus&&scope.row.recordStatus!=='A' ">
              for  {{ getCommonDesc('MKCK_ACTION', scope.row.mkckAction) }}
            </span>
          </template>
        </el-table-column>
       
    </template>
  </BasePanel>
  <Details ref="detailsRef" :reload="reload" />
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance, watch } from 'vue';
import {
Search
} from '@element-plus/icons-vue'
import BasePanel from '~/pages/base/index.vue'
import Details from './Details.vue'
import CtryRegionSearchInput from '~/pages/base/CtryRegionSearchInput.vue';
import CurrencySearchInput from '~/pages/base/CurrencySearchInput.vue';
import  { getCommonDesc, getOid, checkBeforeCurDt, checkDateFromTo } from '~/util/Function.js';
import  { commonRules } from '~/util/Validators';

const { proxy } = getCurrentInstance()
const paramListData = {};
const searchParams = {
  opCtryRegionCode:"",
  currency:"",
  baseCurrency:"",
  beginRateDate:"",
  endRateDate:"",
  sourceSys:""
};
const value = ref('')
const tableRef = ref();
const detailsRef = ref();
const showDetails = (row, disabled) => {
  if(row&&row.sourceSys&&row.sourceSys==='FXS'){
    detailsRef.value.showDetails(row, true);
  }else {
    detailsRef.value.showDetails(row, disabled);
  }
}
const deleteRow = (row) => {
  proxy.$axios.delete("/datamgmt/api/v1/fxrate?fxRefRateId="+getOid(row, false,null,true)).then((body) => {
    if (body.success) {
      tableRef.value.load();
    }
  });
}

const rules = reactive({
  beginRateDate:[
    commonRules.earlierEquCurProcDate,
    commonRules.earlierEquDt(()=>{ return searchParams.endRateDate }, proxy.$t("csscl.fxRate.rateDateTo"))
  ],
  endRateDate:[
    commonRules.earlierEquCurProcDate,
  ]
});

const beforeSearch = async(e) => {
  /*
  const dt = ref();
  let msgs = [];
  let msg1 = await checkBeforeCurDt(proxy, proxy.$t('csscl.fxRate.rateDateFrom'), tableRef.value.formInline.beginRateDate);
  msgs.push(msg1);
  let msg2 = await checkBeforeCurDt(proxy, proxy.$t('csscl.fxRate.rateDateTo'), tableRef.value.formInline.endRateDate);
  msgs.push(msg2);
  if (msg1 || msg2) {
    return msgs;
  }

  let msg = checkDateFromTo(proxy, tableRef.value.formInline.beginRateDate, tableRef.value.formInline.endRateDate, proxy.$t('csscl.fxRate.rateDateFrom'));
  return msg;
  */
}

const editRow = (row) => {
  detailsRef.value.editRow(row);
}
const reload = () => {
  tableRef.value.load();
}
//-------------------------------




</script>

<style>

</style>