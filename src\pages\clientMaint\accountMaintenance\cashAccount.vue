<template> 
    <div>           
        <!-- Cash Grid -->
        <Grid url="/datamgmt/api/v1/account/cash/list"
            :params="{ clientAccountOid: ruleForm.form.opearteOid, pendingOid: ruleForm.form.pendingOid }" 
            :onClick="gridCashClick"
            :beforeSearch="()=>{ pageObj.clientCashAccVPO = {}; }"
            :columns="[
                { title: 'csscl.acctCode.bankAccountNo', name: 'bankAccountNo', width:'200', } ,
                { title: 'csscl.acctCode.currencyCode', name: 'currencyCode', width:'110'},
                { title: 'csscl.acctCode.parentBankAccountNo', name: 'parentBankAccountNo', width:'240'},
                { title: 'csscl.acctCode.ca', name: 'purposeCa', },
                { title: 'csscl.acctCode.siReceiver', name: 'purposeSiReceive', },
                { title: 'csscl.acctCode.siDeliver', name: 'purposeSiDeliver', },
                { title: 'csscl.acctCode.siPenalty', name: 'purposeSiCsdr', },
                { title: 'csscl.acctCode.billing', name: 'purposeBilling', width:'130'},
                { title: 'csscl.acctCode.deposit', name: 'purposeDeposit', width:'140'},
                { title: 'csscl.acctCode.accountStatus', name: 'status', fn:commDesc('ACCOUNT_STATUS')},
                { title: 'common.title.recordStatus',name:'recordStatus', width:'140', fn:getRecordStatusDesc },
            ]"
            />
        <FormRow>
        <FormItemSign :detailsRef="details" label-width="150" :label="$t('csscl.acctCode.bankAccountNo')" prop="clientCashAccVPO.bankAccountNo">
            <el-input v-model="pageObj.clientCashAccVPO.bankAccountNo" />
        </FormItemSign>
        <FormItemSign :detailsRef="details" label-width="200" :label="$t('csscl.acctCode.currencyCode')" prop="clientCashAccVPO.currencyCode">
            <CurrencySearchInput v-model="pageObj.clientCashAccVPO.currencyCode" showDesc="false" />
        </FormItemSign>
        <FormItemSign :detailsRef="details" label-width="150" :label="$t('csscl.acctCode.accountStatus')" prop="clientCashAccVPO.status">
            <Select v-model="pageObj.clientCashAccVPO.status" type="ACCOUNT_STATUS" />
        </FormItemSign>
        </FormRow>
        <FormRow>
        <FormItemSign :detailsRef="details" label-width="150" :label="$t('csscl.acctCode.bankAccountTypeCode')" prop="clientCashAccVPO.bankAccType">
            <Select v-model="pageObj.clientCashAccVPO.bankAccType" style="width:160px" type="BANK_ACC_TYPE_CODE" />
        </FormItemSign>
        <FormItemSign :detailsRef="details" label-width="200" :label="$t('csscl.acctCode.parentBankAccountNo')" prop="clientCashAccVPO.parentBankAccountNo">
            <el-input v-model="pageObj.clientCashAccVPO.parentBankAccountNo" />
        </FormItemSign>
        <FormItemSign :detailsRef="details" label-width="150" :label="$t('csscl.acctCode.bankSubAcType')" prop="clientCashAccVPO.bankSubAcType">
            <el-input v-model="pageObj.clientCashAccVPO.bankSubAcType" />
        </FormItemSign>
        </FormRow>
        <FormRow>
        <FormItemSign :detailsRef="details" label-width="150" :label="$t('csscl.acctCode.bankAcProductType')" prop="clientCashAccVPO.bankAcProductType">
            <el-input v-model="pageObj.clientCashAccVPO.bankAcProductType" />
        </FormItemSign>
        <FormItemSign :detailsRef="details" label-width="200" :label="$t('csscl.acctCode.bankAcProductSubType')" prop="clientCashAccVPO.bankAcProductSubType">
            <el-input v-model="pageObj.clientCashAccVPO.bankAcProductSubType" />
        </FormItemSign>
        <ElFormItemProxy></ElFormItemProxy>
        </FormRow>
        <FormRow>
        <FormItemSign :detailsRef="details" label-width="150" :label="$t('csscl.acctCode.purpose')" prop="clientCashAccVPO.purpose">
            <div>
                <el-checkbox :label="$t('csscl.acctCode.ca')" v-model="pageObj.clientCashAccVPO.purposeCa" true-value="Y" false-value="N" />
                <el-checkbox :label="$t('csscl.acctCode.siReceiver')" v-model="pageObj.clientCashAccVPO.purposeSiReceive" true-value="Y" false-value="N" />
                <el-checkbox :label="$t('csscl.acctCode.siDeliver')" v-model="pageObj.clientCashAccVPO.purposeSiDeliver" true-value="Y" false-value="N" />
                <el-checkbox :label="$t('csscl.acctCode.siPenalty')" v-model="pageObj.clientCashAccVPO.purposeSiCsdr" true-value="Y" false-value="N" />
                <el-checkbox :label="$t('csscl.acctCode.billing')" v-model="pageObj.clientCashAccVPO.purposeBilling" true-value="Y" false-value="N" />
                <el-checkbox :label="$t('csscl.acctCode.deposit')" v-model="pageObj.clientCashAccVPO.purposeDeposit" true-value="Y" false-value="N" />
            </div>
        </FormItemSign>
        </FormRow>
        <FormRow>
        <FormItemSign :detailsRef="details" label-width="150" :label="$t('csscl.acctCode.flag')" prop="clientCashAccVPO.fxFlag">
            <Select v-model="pageObj.clientCashAccVPO.fxFlag" type="COM_YN" />
        </FormItemSign>
        <ElFormItemProxy></ElFormItemProxy>
        <ElFormItemProxy :detailsRef="details" label-width="150" :label="$t('csscl.acctCode.accountIncDt')" prop="clientCashAccVPO.accountIncDt">
            <DateItem v-model="pageObj.clientCashAccVPO.accountIncDt" />
        </ElFormItemProxy>
        </FormRow>

        <div class="splitLine">
            <span>{{  $t("csscl.acctCode.custodianTitle") }}</span>
            <el-divider />
        </div>

        <!-- Shawdo Cash Grid -->
        <Grid url="/datamgmt/api/v1/account/shadowcash/list"
            :params="{ clientAccountOid: ruleForm.form.opearteOid, pendingOid: ruleForm.form.pendingOid  }" 
            :onClick="gridShadowClick"
            :beforeSearch="()=>{ pageObj.clientShadowCashAccVPO = {}; }"
            :columns="[
                { title: 'csscl.acctCode.clearingAgentCode', name: 'clearingAgentCode', },
                { title: 'csscl.acctCode.clearingAgentOid', name: 'custodianAccNo', },
                { title: 'csscl.acctCode.exchangeOid', name: 'exchangeCode', },
                { title: 'csscl.acctCode.currencyCode', name: 'currencyCode', },
                { title: 'csscl.acctCode.custodianCashAccOid', name: 'bankAccountNo', },
                { title: 'csscl.acctCode.ca', name: 'purposeCa', },
                { title: 'csscl.acctCode.siReceiver', name: 'purposeSiReceive', },
                { title: 'csscl.acctCode.siDeliver', name: 'purposeSiDeliver', },
                { title: 'csscl.acctCode.accountStatus', name: 'status', fn:commDesc('ACCOUNT_STATUS')},
                { title: 'common.title.recordStatus',name:'recordStatus',fn:getRecordStatusDesc },
            ]"
            />
        <FormRow>
            <FormItemSign :detailsRef="details" label-width="350" :label="$t('csscl.acctCode.clearingAgentCode')" prop="clientShadowCashAccVPO.clearingAgentCode">
                <GeneralSearchInput v-model="pageObj.clientShadowCashAccVPO.clearingAgentCode" 
                    showDesc="false"
                    searchType="clearingAgentCode" />
            </FormItemSign>
            <FormItemSign :detailsRef="details" label-width="340" :label="$t('csscl.acctCode.clearingAgentOid')" prop="clientShadowCashAccVPO.custodianAccNo">
                
                <GeneralSearchInput v-model="pageObj.clientShadowCashAccVPO.custodianAccNo" 
                    showDesc="false"
                    :params="{ var1: ruleForm.form.clientAccountOid  }" 
                    searchType="custodianAccNo" />

            </FormItemSign>
            <FormItemSign :detailsRef="details" label-width="120" :label="$t('csscl.acctCode.accountStatus')" prop="clientShadowCashAccVPO.status">
                <Select v-model="pageObj.clientShadowCashAccVPO.status" type="ACCOUNT_STATUS" />
            </FormItemSign>
        </FormRow>
        <FormRow>
            <FormItemSign :detailsRef="details" label-width="350" :label="$t('csscl.acctCode.exchangeOid')" prop="clientShadowCashAccVPO.exchangeCode">
                
                 <GeneralSearchInput v-model="pageObj.clientShadowCashAccVPO.exchangeCode"
                    showDesc="false"
                    searchType="exchangeCode" />

            </FormItemSign>
            <FormItemSign :detailsRef="details" label-width="340" :label="$t('csscl.acctCode.currencyCode')" prop="clientShadowCashAccVPO.currencyCode">
                <CurrencySearchInput v-model="pageObj.clientShadowCashAccVPO.currencyCode" showDesc="false" />
            </FormItemSign>
            <ElFormItemProxy></ElFormItemProxy>
        </FormRow>
        <FormRow>
            <FormItemSign :detailsRef="details" label-width="350" :label="$t('csscl.acctCode.custodianCashAccOid')" prop="clientShadowCashAccVPO.bankAccountNo">
                
                <GeneralSearchInput v-model="pageObj.clientShadowCashAccVPO.bankAccountNo"
                    showDesc="false"
                    title="csscl.acctCode.custodianCashAccOid"
                    searchType="custodianCashAcc" />

            </FormItemSign>
            <ElFormItemProxy></ElFormItemProxy>
            <ElFormItemProxy></ElFormItemProxy>
        </FormRow>
        <FormRow>
            <FormItemSign :detailsRef="details" label-width="350" :label="$t('csscl.acctCode.accNo')" prop="clientShadowCashAccVPO.accNo">
                <el-input v-model="pageObj.clientShadowCashAccVPO.accNo" />
            </FormItemSign>
            <FormItemSign :detailsRef="details" label-width="340" :label="$t('csscl.acctCode.accShortName')" prop="clientShadowCashAccVPO.accShortName" style="flex: 2;">
                <el-input v-model="pageObj.clientShadowCashAccVPO.accShortName" style="width:700px" />
            </FormItemSign>
        </FormRow>
        <FormRow>
            <FormItemSign :detailsRef="details" label-width="350" :label="$t('csscl.acctCode.accName')" prop="clientShadowCashAccVPO.accName1">
                <el-input v-model="pageObj.clientShadowCashAccVPO.accName1" style="width:950px" />
            </FormItemSign>
            <ElFormItemProxy></ElFormItemProxy>
            <ElFormItemProxy></ElFormItemProxy>
        </FormRow>
        <FormRow>
            <FormItemSign :detailsRef="details" label-width="350" :label="$t('csscl.acctCode.purpose')" prop="clientShadowCashAccVPO.purpose">
                <div>
                    <el-checkbox :label="$t('csscl.acctCode.ca')" v-model="pageObj.clientShadowCashAccVPO.purposeCa" true-value="Y" false-value="N" />
                    <el-checkbox :label="$t('csscl.acctCode.siReceiver')" v-model="pageObj.clientShadowCashAccVPO.purposeSiReceive" true-value="Y" false-value="N" />
                    <el-checkbox :label="$t('csscl.acctCode.siDeliver')" v-model="pageObj.clientShadowCashAccVPO.purposeSiDeliver" true-value="Y" false-value="N" />
                </div>
            </FormItemSign>
        </FormRow>
        <FormRow>
            <FormItemSign :detailsRef="details" label-width="350" :label="$t('csscl.acctCode.accountIncDt')" prop="clientShadowCashAccVPO.accountIncDt">
                <DateItem v-model="pageObj.clientShadowCashAccVPO.accountIncDt" />
            </FormItemSign>
            <ElFormItemProxy></ElFormItemProxy>
            <ElFormItemProxy></ElFormItemProxy>
        </FormRow>


        <div class="splitLine">
            <span>{{  $t("csscl.acctCode.clientCustodianTitle") }}</span>
            <el-divider />
        </div>

        <!-- Client Account Custodian Account Maintenance -->
        <Grid url="/datamgmt/api/v1/account/custacc/list"
            :params="{ clientAccountOid: ruleForm.form.opearteOid, pendingOid: ruleForm.form.pendingOid }" 
            :onClick="gridCashBankClick"
            :beforeSearch="()=>{ pageObj.clientCustAccBankVPO = {}; }"
            :columns="[
                { title: 'csscl.acctCode.clearingAgentCode', name: 'clearingAgentCode', },
                { title: 'csscl.acctCode.custAcctNo', name: 'custodianAccNo', },
                { title: 'csscl.acctCode.currencyCode', name: 'currencyCode', },
                { title: 'csscl.acctCode.bankAccountNo', name: 'custodianCashAccNo', },
                { title: 'csscl.acctCode.ca', name: 'purposeCa', },
                { title: 'csscl.acctCode.siReceiver', name: 'purposeSiReceive', },
                { title: 'csscl.acctCode.siDeliver', name: 'purposeSiDeliver', },
                { title: 'csscl.acctCode.siPenalty', name: 'purposeSiCsdr', },
                { title: 'csscl.acctCode.billing', name: 'purposeBilling', },
                { title: 'csscl.acctCode.Reconciliation', name: 'purposeRecon', },
                { title: 'common.title.status', name: 'status', fn:commDesc('ACCOUNT_STATUS') },
                {title:'common.title.recordStatus',name:'recordStatus', fn:getRecordStatusDesc },
            ]"
            />
        <FormRow>
            <FormItemSign :detailsRef="details" label-width="280" :label="$t('csscl.acctCode.clearingAgentCode')" prop="clientCustAccBankVPO.clearingAgentCode">
                
                <GeneralSearchInput v-model="pageObj.clientCustAccBankVPO.clearingAgentCode"
                    showDesc="false"
                    searchType="clearingAgentCode" />

            </FormItemSign>
            <FormItemSign :detailsRef="details" label-width="190" :label="$t('csscl.acctCode.custAcctNo')" prop="clientCustAccBankVPO.custodianAccNo">
                
                <GeneralSearchInput v-model="pageObj.clientCustAccBankVPO.custodianAccNo"
                    showDesc="false"
                    style="width: 260px"
                    searchType="custodianAccNo" />
                
            </FormItemSign>
            <ElFormItemProxy></ElFormItemProxy>
        </FormRow>
        <FormRow>
            <FormItemSign :detailsRef="details" label-width="280" :label="$t('csscl.acctCode.currencyCode')" prop="clientCustAccBankVPO.currencyCode">
                <CurrencySearchInput v-model="pageObj.clientCustAccBankVPO.currencyCode" showDesc="false" />
            </FormItemSign>
            <FormItemSign :detailsRef="details" label-width="190" :label="$t('csscl.acctCode.bankAccountNo')" prop="clientCustAccBankVPO.custodianCashAccNo">
                <el-input v-model="pageObj.clientCustAccBankVPO.custodianCashAccNo" style="width:250px" />
            </FormItemSign>
            <FormItemSign :detailsRef="details" label-width="60" :label="$t('common.title.status')" prop="clientCustAccBankVPO.status">
                <Select v-model="pageObj.clientCustAccBankVPO.status" type="ACCOUNT_STATUS" />
            </FormItemSign>
        </FormRow>
        <FormRow>
            <FormItemSign :detailsRef="details" label-width="280" :label="$t('csscl.acctCode.purpose')" prop="clientCustAccBankVPO.purpose">
                <div>
                    <el-checkbox :label="$t('csscl.acctCode.ca')" v-model="pageObj.clientCustAccBankVPO.purposeCa" true-value="Y" false-value="N" />
                    <el-checkbox :label="$t('csscl.acctCode.siReceiver')" v-model="pageObj.clientCustAccBankVPO.purposeSiReceive" true-value="Y" false-value="N" />
                    <el-checkbox :label="$t('csscl.acctCode.siDeliver')" v-model="pageObj.clientCustAccBankVPO.purposeSiDeliver" true-value="Y" false-value="N" />
                    <el-checkbox :label="$t('csscl.acctCode.siPenalty')" v-model="pageObj.clientCustAccBankVPO.purposeSiCsdr" true-value="Y" false-value="N" />
                    <el-checkbox :label="$t('csscl.acctCode.billing')" v-model="pageObj.clientCustAccBankVPO.purposeBilling" true-value="Y" false-value="N" />
                    <el-checkbox :label="$t('csscl.acctCode.Reconciliation')" v-model="pageObj.clientCustAccBankVPO.purposeRecon" true-value="Y" false-value="N" />
                </div>
            </FormItemSign>
        </FormRow>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import Grid from '~/pages/base/Grid.vue';
import  { getCommonDesc, getRecordStatusDesc, commDesc } from '~/util/Function.js';
import CurrencySearchInput from '~/pages/base/CurrencySearchInput.vue';


const props = defineProps([ "ruleForm", "details"]);
const details = props.details;
const ruleForm = props.ruleForm;
const pageObj = reactive ({
    clientCashAccVPO:{},
    clientShadowCashAccVPO:{},
    clientCustAccBankVPO:{},
});

const gridCashClick = (row) => {
    if (row.status == 'C') {
        row.accountIncDt = row.sysUpdateDate?.substring(0,10);
    }
    pageObj.clientCashAccVPO = row;
}

const gridShadowClick = (row) => {
    if (row.status == 'C') {
        row.accountIncDt = row.sysUpdateDate?.substring(0,10);
    }
    pageObj.clientShadowCashAccVPO = row;
}

const gridCashBankClick = (row) => {
    pageObj.clientCustAccBankVPO = row;
}

</script>

<style></style>