<template>
  <BasePanel :searchParams="searchParams" :paramListData='paramListData' url="/datamgmt/api/v1/rptdocenq/list" :params="{ modeEdit: 'Y' }"
    :showDetails="showDetails"  :editRow="editRow" hideOperation="true" :clickRow="clickRow" :beforeSearch="beforeSearch" :showSummary="true" :hideFooterPaging="true"
    :afterSearch="afterSearch" :rules="rules" :selectFirstRecord="true" :changePageSize="(num:number) => subtableRef.setPageSize(num)"
    ref="tableRef">
    <template v-slot:searchPanel="slotProps">
      <FormRow>
        <ElFormItemProxy label-width="160px" :label="$t('csscl.incDoc.fileType')" prop="fileType">
            <Select v-model="slotProps.form.fileType" style="width: 400px;" type="IN_DOC_FILE_TYPE" :change="fileType(slotProps.form.fileType)"/>
        </ElFormItemProxy>
        <ElFormItemProxy></ElFormItemProxy>
        <ElFormItemProxy></ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy>
          <ElFormItemProxy label-width="160px" :label="$t('csscl.incDoc.incomingDateFrom')" prop="inDateFrom" >
          <DateItem v-model="slotProps.form.inDateFrom"
            :title="$t('message.earlier.equal.curdate', [$t('csscl.incDoc.incomingDateFrom')] ) + '\r' +
                        $t('message.earlier.equal.dateto', [$t('csscl.incDoc.incomingDateFrom'), $t('csscl.incDoc.incomingDateTo')] ) + '\r' +
                        $t('message.date.range.error', [7] ) "  />
        </ElFormItemProxy>
        <ElFormItemProxy label-width="45px" :label="$t('common.title.date.to')" :hideLabel="$t('csscl.incDoc.incomingDateTo')" prop="inDateTo" >
          <DateItem v-model="slotProps.form.inDateTo"
            :title="$t('message.earlier.equal.curdate', [$t('csscl.incDoc.incomingDateTo')] ) + '\r' +
                $t('message.date.range.error', [7] ) " />
        </ElFormItemProxy>
        </ElFormItemProxy>

        <ElFormItemProxy label-width="120px" :label="$t('csscl.incDoc.ftgidCode')" prop="ftgidCode">
          <GeneralSearchInput v-model="slotProps.form.ftgidCode" style="width: 450px" 
            searchType="ftgidCode" />
        </ElFormItemProxy>
        <ElFormItemProxy label-width="130px" :label="$t('csscl.incDoc.incomingChannel')" prop="channel">
          <Select v-model="slotProps.form.channel" style="width: 220px;" type="IN_DOC_CHANNEL" :change="channelType(slotProps.form.channel)"/>
        </ElFormItemProxy>
      </FormRow>
    </template>

    <template v-slot:tableColumn>
      <el-table-column header-align="center" sortable="custom" prop="inDtDateFmt" :label="$t('csscl.incDoc.incomingDate')" width="700" />
      <el-table-column header-align="center" sortable="custom" prop="fileTypeDesc" :label="$t('csscl.incDoc.fileType')" />
      <el-table-column prop="fileType" v-if="false"/>
      <el-table-column prop="channel" v-if="false"/>
    </template>
  <template #contentBottom>
  <br/>
  <div style="margin: 0px; padding: 3px; border-bottom: 2px solid lightgrey;">
    <el-text style="color: #b31a25;font: 16px bold;" class="mx-1" size="large">Detail</el-text>
  </div>
  <DetailPanel  url="/datamgmt/api/v1/rptdocenq/detail/list" :params="reqParams" :hideOperation="true" :isMultiple="true" :selectable="selectable" ref="subtableRef">
    <template v-slot:tableColumn>
      <el-table-column header-align="center" sortable="custom" prop="inDt" :label="$t('csscl.incDoc.incomingDateAndTime')" width="220" />
      <el-table-column header-align="center" sortable="custom" prop="channel" :label="$t('csscl.incDoc.incomingChannel')" width="220" />
      <el-table-column header-align="center" sortable="custom" prop="ftgidCode" :label="$t('csscl.incDoc.ftgidCode')" width="220" />
      <el-table-column header-align="center" sortable="custom" prop="processStatusDesc" :label="$t('csscl.outDoc.proccesingStatus')" width="250" />
      <el-table-column header-align="center" sortable="custom" prop="fileName" :label="$t('csscl.incDoc.fileName')" />
      <el-table-column header-align="center" :label="$t('common.button.open')" width="80">
        <template #default="scope">
          <el-icon-folder-opened style="width:20px;height:20px;color:orange" @click="openClick(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column header-align="center" :label="$t('common.button.download')" width="80" >
        <template #default="scope">
          <el-icon-download style="width:20px;height:20px;color:darkorange" @click="downloadClick(scope.row)" />
        </template>
      </el-table-column>
    </template>
  </DetailPanel>

    <br/>
    <el-space style="float:right;">
      <el-button type="primary" @click="downloadSelection">
            Download Selected
      </el-button>
    </el-space>
  </template>
  </BasePanel>
</template>

<script lang="ts" setup>
import { ref, reactive, getCurrentInstance } from 'vue';
import type { FormRules } from 'element-plus'
import BasePanel from '~/pages/base/index.vue'
import DetailPanel from '~/pages/base/indexNoCondition.vue'
import { checkBeforeCurDt, checkInputDate, downloadFile, downloadOpenFile, downloadBatchFile } from '~/util/Function.js';
import {  getCommonDesc } from '~/util/Function.js';
import { commonRules, showValidateMsg } from '~/util/Validators.js';

const { proxy } = getCurrentInstance();
const paramListData = ref({});
const searchParams = {
  //顺序和上面绑定参数一致
  fileType:"",
  inDateFrom:"",
  inDateTo:"",
  ftgidCode:"",
  channel:""
};
function openClick(row) {
    let funcId = proxy.$currentInfoStore.getCurrentFuncId(); 
    let params = {
      funcId: funcId,
      filePath: row.filePath,
      fileName: row.fileName
    };
    downloadOpenFile("/datamgmt/api/v1/rptdocenq/download", params);
} 
function downloadClick(row) {
    let funcId = proxy.$currentInfoStore.getCurrentFuncId(); 
    let params = {
      funcId: funcId,
      filePath: row.filePath,
      fileName: row.fileName
    };
    downloadFile("/datamgmt/api/v1/rptdocenq/download", params);
}

let reqParams = reactive({ modeEdit: 'Y' }); 
const tableRef = ref();
const subtableRef = ref();
const detailsRef = ref();
const showDetails = (row, disabled) => {
  // detailsRef.value.showDetails(row, disabled);
}

const clickRow = (row) => {
  if (row) {
    reqParams["inDtDateFmt"] = row.inDtDateFmt;
    reqParams["fileType"] = row.fileType;
    reqParams["channel"] = row.channel;
    subtableRef.value.load();
  } else {
    subtableRef.value.resetTable();
  }
}

const afterSearch = (params, data) => {
  //Start SIR-CSG-R59, ZhuangYifan, 2024/08/13
  subtableRef.value.setTableData(null);
  //End SIR-CSG-R59, ZhuangYifan, 2024/08/13
}

const editRow = (row) => {
  detailsRef.value.editRow(row);
}
const reload = () => {
  tableRef.value.load();
}
//-------------------------------
interface RuleForm {
  inDateFrom: Date
  inDateTo: Date
}
const rules = reactive<FormRules<RuleForm>>({
  inDateFrom: [
    commonRules.required,
    commonRules.earlierEquCurDate,
    commonRules.earlierEquDt(()=>{ return searchParams.inDateTo }, proxy.$t('csscl.incDoc.incomingDateTo')),
    commonRules.diffDate(7,()=>{ return searchParams.inDateTo }, proxy.$t('csscl.incDoc.incomingDateTo')),
  ],
  inDateTo: [
    commonRules.required,
    commonRules.earlierEquCurDate,
  ],
})

const beforeSearch = async() =>{
  //
  // let msgs = [];
  // let msg1 = await checkBeforeCurDt(proxy, proxy.$t('csscl.incDoc.incomingDateFrom'), tableRef.value.formInline.inDateFrom);
  // msgs.push(msg1);
  // let msg2 = await checkBeforeCurDt(proxy, proxy.$t('csscl.incDoc.incomingDateTo'), tableRef.value.formInline.inDateTo);
  // msgs.push(msg2);
  // if (msg1 || msg2) {
  //   return msgs;
  // }
  //
  // let chkMsg = checkInputDate(proxy, tableRef.value.formInline.inDateFrom, tableRef.value.formInline.inDateTo);
  // return chkMsg;
}

const downloadSelection = async () => {    
  let funcId = proxy.$currentInfoStore.getCurrentFuncId(); 
  var tableSelectRows = new Array();
  const rowsData = ref([]);
  rowsData.value = subtableRef.value.getRowsData().value;
  if(rowsData.value){
    for(var index in rowsData.value){
      tableSelectRows[index] = rowsData.value[index].filePath + "/" + rowsData.value[index].fileName;
    }
  }
  let params = {
    funcId: funcId,
    rows: tableSelectRows,
  }
  if(tableSelectRows&& tableSelectRows.length!==0){
    downloadBatchFile("/datamgmt/api/v1/rptdocenq/downloadBatch", params);
  } else {
    ElMessageBox.alert("Please select at least one row of records!", 'Warning');
  }
};

function selectable(row, index){
  return true;
}
//paramList 参数显示用的
function fileType(value){
  paramListData._value.fileType =  getCommonDesc('IN_DOC_FILE_TYPE', value);
}
function channelType(value){
  paramListData._value.channel =  getCommonDesc('IN_DOC_CHANNEL', value);
}


</script>

<style></style>