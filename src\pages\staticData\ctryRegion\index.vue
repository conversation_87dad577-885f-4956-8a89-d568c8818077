<template> 
  <BasePanel :searchParams="searchParams" :paramListData='paramListData' url="/datamgmt/api/v1/ctryregion/list" :params="{ modeEdit: 'Y' }"
    :showDetails="showDetails" :deleteRow="deleteRow" :editRow="editRow" ref="tableRef" :sortProp="{}">
    <template v-slot:searchPanel="slotProps">
      <FormRow>
        <ElFormItemProxy :label="$t('common.title.opCtryRegionCode')" label-width="220" prop="opCtryRegionCode">
        <CtryRegionSearchInput v-model="slotProps.form.opCtryRegionCode" 
            showDesc="false"
            opCtryRegion />
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('common.title.status')" label-width="180" prop="status">
          <Select v-model="slotProps.form.status" style="width: 150px" type='STATUS' :change="statusType(slotProps.form.status)"/>
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('common.title.recordStatus')" label-width="120" prop="multipleRecordStatus">
          <MultipleSelect v-model="slotProps.form.multipleRecordStatus" v-model:desc="paramListData.multipleRecordStatus"  />
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy :label="$t('csscl.ctryRegionManagement.ctryRegionCode')" label-width="220" prop="ctryRegionCode">
          <SearchInput v-model="slotProps.form.ctryRegionCode"
            style="width:110px"
            showDesc="false"
            maxlength="3"
            onlyLetters
            searchField
            url="/datamgmt/api/v1/ctryregion/list" 
            :title="$t('csscl.ctryRegionManagement.ctryRegionCode')"
            :params="{}" 
            :columns="[
              {
                title: $t('csscl.ctryRegionManagement.ctryRegionCode'),
                colName: 'ctryRegionCode',
              },
              {
                title: $t('csscl.ctryRegionManagement.descpt'),
                colName: 'ctryRegionName',
              }
            ]"
          >
          </SearchInput>
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.ctryRegionManagement.isoCode')" label-width="180" prop="isoCode">
          <SearchInput v-model="slotProps.form.isoCode"
            style="width:110px"
            showDesc="false"
            maxlength="3"
            url="/datamgmt/api/v1/ctryregion/list" 
            :title="$t('csscl.ctryRegionManagement.isoCode')"
            :params="{}" 
            :columns="[
              {
                title: $t('csscl.ctryRegionManagement.isoCode'),
                colName: 'isoCode',
              },
              {
                title: $t('csscl.ctryRegionManagement.descpt'),
                colName: 'ctryRegionName',
              }
            ]"
          >
          </SearchInput>
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.ctryRegionManagement.currencyCode')" label-width="120" prop="currencyCode">
          
          <CurrencySearchInput v-model="slotProps.form.currencyCode" showDesc="false"  />
          
        </ElFormItemProxy>
      </FormRow>
    </template>
    <template v-slot:tableColumn>
      <el-table-column align="left" header-align="center" sortable="custom" prop="opCtryRegionCode" :label="$t('common.title.opCtryRegionCode')" width="300" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="ctryRegionCode" :label="$t('csscl.ctryRegionManagement.ctryRegionCode')" width="300" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="isoCode" :label="$t('csscl.ctryRegionManagement.isoCode')" width="300" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="currencyCode" :label="$t('csscl.ctryRegionManagement.currencyCode')" width="300" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="status" :label="$t('csscl.useradmin.usr.status')" width="220" >
        <template #default="scope">
          {{ getCommonDesc('STATUS', scope.row.status) }}
        </template>
      </el-table-column>
      <el-table-column align="left" header-align="center" sortable="custom" prop="recordStatus" :label="$t('csscl.useradmin.usr.recordStatus')" >
        <template #default="scope">
          {{ getRecordStatusDesc(scope.row) }}
        </template>
      </el-table-column>
    </template>
  </BasePanel>
  <Details ref="detailsRef" :reload="reload" />
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance, watch } from 'vue';
import {
Search
} from '@element-plus/icons-vue'
import BasePanel from '~/pages/base/index.vue'
import Details from './Details.vue'
import CtryRegionSearchInput from '~/pages/base/CtryRegionSearchInput.vue'
import CurrencySearchInput from '~/pages/base/CurrencySearchInput.vue'
import  { getCommonDesc, getRecordStatusDesc } from '~/util/Function.js';
import { getOid } from '~/util/Function.js';

const { proxy } = getCurrentInstance()
const paramListData = ref({});
const searchParams = ref({
  opCtryRegionCode:"",
  status:"",
  multipleRecordStatus:[],
  ctryRegionCode:"",
  isoCode:"",
  currencyCode:""});

const tableRef = ref();
const detailsRef = ref();
const showDetails = (row, disabled) => {
  detailsRef.value.showDetails(row, disabled);
}
const deleteRow = (row) => {
  proxy.$axios.delete("/datamgmt/api/v1/ctryregion?objectId="+ getOid(row, false,null,true)).then((body) => {
    if (body.success) {
      tableRef.value.load();
    }
  });
}

//paramList 参数显示用的
function recordType(value){
  console.log(searchParams)
  paramListData._value.recordStatus =  getCommonDesc('RECORD_STATUS', value);
}
function statusType(value){
  paramListData._value.status =  getCommonDesc('STATUS', value);
}
const editRow = (row) => {
  detailsRef.value.editRow(row);
}
const reload = () => {
  tableRef.value.load();
}
//-------------------------------

</script>

<style>

</style>
