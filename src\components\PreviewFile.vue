<template>
    <div v-if="previewFileStore.enable"
        style="position: fixed;background-color: white;width: 100%;z-index: 990;top:62px; height: calc(100% - 110px);">
        <el-row :gutter="24" style="margin: 0px; padding: 3px 10px; border-bottom: 2px solid lightgrey;">
            <el-col :span="10" style="padding:0px; ">
                <el-space>
                    <el-button :icon="ArrowLeftBold" type="primary" @click="hideDetails" />
                    <el-text style="color: #b31a25;font-weight: bold;" class="mx-1" size="large">{{
                        previewFileStore.name
                        }}</el-text>
                </el-space>
            </el-col>
            <el-col :span="10" :offset="4" style="padding:0px;">
                <el-space class="detail-buttons" style="float:right;">
                    <el-button type="primary" @click="exportFile">
                        Export File
                    </el-button>
                </el-space>
            </el-col>
        </el-row>
        <el-tabs v-model="selectedTab" style="padding: 10px;width: 100%;height: 100%;" class="preview-file-tabs">
            <el-tab-pane v-for="item in sheetNames" :label="item" :name="item">
                <div :id="item.replace(/\s+/g, '')"
                    style="padding: 10px;overflow: auto;background-color: #E6E6E6;max-height: calc(100% - 120px);width: calc(100% - 40px);">
                </div>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script lang="ts" setup>
import { getCurrentInstance, nextTick, watch, ref } from 'vue';
import {
    ArrowLeftBold,
} from '@element-plus/icons-vue'
import { previewFile } from '~/store/modules/previewFile';
import { ElMessageBox} from 'element-plus';
const previewFileStore = previewFile();
const sheetNames = ref([]);
const selectedTab = ref("");
const maxLines=5000;

watch(() => previewFileStore.enable, async (newVal) => {
    if (newVal) {
        if (previewFileStore.fileType=='txt') {
            let name = previewFileStore.name;
            sheetNames.value = [name];
            await nextTick();
            selectedTab.value = name;
            let output = document.getElementById(name.replace(/\s+/g, ''));
            output.innerHTML = '<p style="white-space: nowrap;">' + previewFileStore.file.replaceAll("\n","<br/>") + '</p>';
            return;
        }
        if (previewFileStore.fileType=='csv') {
            let name = previewFileStore.name;
            sheetNames.value = [name];
            await nextTick();
            selectedTab.value = name;
            var ws = XLSX.utils.aoa_to_sheet([[]]);
            let rows = previewFileStore.file.split("\n");
            if (rows.length > maxLines) {
                hideDetails();
                ElMessageBox.alert("The data in the file is too large to preview. Please download and review it.", 'Error', {
                    confirmButtonText: 'OK',
                    type: 'error',
                });
                return;
            }
            for (let i = 0; i < rows.length; i++) {
                XLSX.utils.sheet_add_aoa(ws, [splitIgnoringQuotes(rows[i])], {origin: -1});
            }
            let output = document.getElementById(name.replace(/\s+/g, ''));
            output.innerHTML = XLSX.utils.sheet_to_html(ws);
            return;
        }
        let names = previewFileStore.excelFile["SheetNames"];
        sheetNames.value = names;
        await nextTick();
        if (names && names.length > 0) {
            selectedTab.value = names[0];
            for (let i = 0; i < names.length; i++) {
                if (names[i]) {
                    let ws = previewFileStore.excelFile.Sheets[names[i]];
                    if (ws["!data"].length > maxLines) {
                        hideDetails();
                        ElMessageBox.alert("The data in the file is too large to preview. Please download and review it.", 'Error', {
                            confirmButtonText: 'OK',
                            type: 'error',
                        });
                        return;
                    }
                    /* Generate HTML */
                    let output = document.getElementById(names[i].replace(/\s+/g, ''));
                    output.innerHTML = XLSX.utils.sheet_to_html(ws);
                }
            }
        }
    } else {
        sheetNames.value = [];
        selectedTab.value = "";
    }
});

function splitIgnoringQuotes(str:string) {
    return str? str.split(/,\s*(?![^"]*\"\,)/).map(trimQuotes) : [];  
} 

function trimQuotes(str:string) {
    return str.replace(/^"|"$/g, '');  
}

const hideDetails = () => {
    previewFileStore.setEnable(false);
    previewFileStore.clear();
}

const exportFile = () => {
    switch (previewFileStore.fileType) {
        case "xlsx":
            XLSX.writeFile(previewFileStore.excelFile, previewFileStore.name);
            break;
        case "xls":
            XLSX.writeFile(previewFileStore.excelFile, previewFileStore.name);
            break;
        case "txt":
            writeFile("text/csv;charset=utf-8", previewFileStore.file, previewFileStore.name);
            break;
        case "csv":
            writeFile("text;charset=utf-8", previewFileStore.file, previewFileStore.name);
            break;
    
        default:
            break;
    }
}

const writeFile = ( type:string,stringData:string, name:string ) => {
    const blob = new Blob([stringData], {
        type: type
    });
    const objectURL = URL.createObjectURL(blob);
    const aTag = document.createElement('a');
    aTag.href = objectURL;
    aTag.download = name;
    aTag.click();
    URL.revokeObjectURL(objectURL);
}

</script>

<style lang="css">
.preview-file-tabs .ep-tabs__content,
.preview-file-tabs .ep-tab-pane {
    width: 100%;
    height: 100%;
}
.preview-file-tabs .ep-tabs__content td{
    white-space: nowrap;
}
.preview-file-tabs .ep-tabs__content table {
  border-collapse: collapse; 
}
.preview-file-tabs .ep-tabs__content table, th, td {  
  border: 1px solid white;
  padding-inline: 10px;
  height: 18px;
} 
</style>