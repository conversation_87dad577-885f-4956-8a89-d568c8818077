<template> 
  <BasePanel :searchParams="searchParams" :paramListData='paramListData' url="/datamgmt/api/v1/client/list" :params="{ modeEdit: 'Y' }"
    :showDetails="showDetails" :deleteRow="deleteRow" :editRow="editRow"  :sortProp="{}" ref="tableRef" >
    <template v-slot:searchPanel="slotProps" >
      <FormRow>
        <ElFormItemProxy :label="$t('csscl.clientManagement.clientCode')" label-width="150" prop="clientCode">
          <GeneralSearchInput v-model="slotProps.form.clientCode" 
            showDesc="false"
            maxlength="11"
            searchType="clientCode" />
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.clientManagement.clientType')" label-width="90" prop="clientType">
          <Select v-model="slotProps.form.clientType" style="width:240px" type="CLIENT_TYPE_CODE" :change="clientType(slotProps.form.clientType)"/>
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.clientManagement.accountStatus')" label-width="120" prop="status">
          <Select v-model="slotProps.form.status" type="ACCOUNT_STATUS" :change="statusType(slotProps.form.status)" style="width: 170px;"/>
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy :label="$t('csscl.clientManagement.clientNameLine1')" label-width="150" prop="clientName1">
          <el-input v-model="slotProps.form.clientName1" style="width:450px" maxlength="70" input-style="text-transform:none" /></ElFormItemProxy>
        <ElFormItemProxy></ElFormItemProxy>
        <!-- <ElFormItemProxy :label="$t('csscl.clientManagement.investorTypeCode')" label-width="120" prop="investorTypeCode">
          <GeneralSearchInput v-model="slotProps.form.investorTypeCode" 
            showDesc="false"
            maxlength="11"
            searchType="clientCode" />
        </ElFormItemProxy> -->
      </FormRow>
      <FormRow>
        <ElFormItemProxy :label="$t('csscl.clientManagement.clientShortName')" label-width="150" prop="clientShortName">
          <el-input v-model="slotProps.form.clientShortName" style="width:380px" maxlength="50" input-style="text-transform:none" /></ElFormItemProxy>
          <ElFormItemProxy></ElFormItemProxy>
          <ElFormItemProxy :label="$t('csscl.acctCode.custodyLevel')" label-width="120" prop="custodyLevel">
            <Select v-model="slotProps.form.custodyLevel" type="CUSTODY_LEVEL" style="width: 170px;" :change="() => slotProps.form.custodyMarket = ''" />
          </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy :label="$t('csscl.clientManagement.bankCode')" label-width="150" prop="bankCode">
          <Select v-model="slotProps.form.bankCode" vkEnqual type="BANK_CODE" :change="bankCodeType(slotProps.form.bankCode)"/>
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.clientManagement.AECode')" label-width="90" prop="aeCode">
          <Select v-model="slotProps.form.aeCode" vkEnqual type="AE_CODE" :change="aeCodeType(slotProps.form.aeCode)"/>
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.acctCode.custodyMarket')" label-width="120" prop="custodyMarket">
          <GeneralSearchInput v-model="slotProps.form.custodyMarket" 
            showDesc="false"
            maxlength="11"
            searchType="custodyMarket"
            :disabled="slotProps.form.custodyLevel === 'GC'" />
        </ElFormItemProxy>
      </FormRow>
    </template>

    <template v-slot:tableColumn>
      <el-table-column sortable="custom" prop="clientCode" :label="$t('csscl.clientManagement.clientCode')" />
      <el-table-column sortable="custom" prop="clientName1" :label="$t('csscl.clientManagement.clientNameLine1')" />
      <el-table-column sortable="custom" prop="status" :label="$t('csscl.clientManagement.accountStatus')" >
        <template #default="scope">
          {{ getCommonDesc('ACCOUNT_STATUS', scope.row.status) }}
        </template>
      </el-table-column>
      <el-table-column sortable="custom" prop="closeDate" :label="$t('csscl.clientManagement.acCloseDate')" />
      <el-table-column sortable="custom" prop="bankCode" :label="$t('csscl.clientManagement.bankCode')" />
      <el-table-column sortable="custom" prop="aeCode" :label="$t('csscl.clientManagement.AECode')" />
      <el-table-column sortable="custom" prop="opCtryRegionCode" :label="$t('csscl.clientManagement.ctryRegionCode')" />
      <el-table-column sortable="custom" prop="clientType" :label="$t('csscl.clientManagement.clientType')" >
        <template #default="scope">
          {{ getCommonDesc('CLIENT_TYPE_CODE', scope.row.clientType) }}
        </template>
      </el-table-column>
      <!-- <el-table-column sortable="custom" prop="investorTypeCode" :label="$t('csscl.clientManagement.investorTypeCode')" />
      <el-table-column sortable="custom" prop="taxCtryIsoCode" :label="$t('csscl.clientManagement.taxCtryIsoCode')" /> -->
      <el-table-column sortable="custom" prop="recordStatus" :label="$t('common.title.recordStatus')" >
        <template #default="scope">
          {{ getRecordStatusDesc(scope.row) }}
          </template>
      </el-table-column>
    </template>
  </BasePanel>
  <Details ref="detailsRef" :reload="reload"  />
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance, watch } from 'vue';
import { Search } from '@element-plus/icons-vue';
import BasePanel from '~/pages/base/index.vue';
import Details from './Details.vue';
import  { getCommonDesc, getRecordStatusDesc } from '~/util/Function.js';


const { proxy } = getCurrentInstance()
const paramListData = ref({});
const searchParams = ref({
  //顺序和上面绑定参数一致
  clientCode:"",
  clientType:"",
  status:"",
  clientName1:"",
  investorTypeCode:"",
  clientShortName:"",
  custodyLevel:"",
  bankCode:"",
  aeCode:"",
  custodyMarket:""
});

const tableRef = ref();
const detailsRef = ref();
const showDetails = (row, disabled) => {
  detailsRef.value.showDetails(row, disabled);
}

const deleteRow = (row) => {
  console.log("Delete ...");
}
const editRow = (row) => {
  detailsRef.value.editRow(row);
}
//paramList 参数显示用的
function clientType(value){
  paramListData._value.clientType =  getCommonDesc('CLIENT_TYPE_CODE', value);
}
function statusType(value){
  paramListData._value.status =  getCommonDesc('ACCOUNT_STATUS', value);
}
function bankCodeType(value){
  paramListData._value.bankCode =  value;
}
function aeCodeType(value){
  paramListData._value.aeCode =  value;
}
//-------------------------------

const reload = () => {
  tableRef.value.load();
}

</script>

<style></style>