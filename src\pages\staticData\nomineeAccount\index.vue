<template> 
  <BasePanel :searchParams="searchParams" url="/datamgmt/api/v1/nominee/list" :params="{ modeEdit: 'Y' }"
    :showDetails="showDetails" :deleteRow="deleteRow" :editRow="editRow" ref="tableRef" :sortProp="{}"
    :hideOperation="true" >
    <template v-slot:searchPanel="slotProps">
      <FormRow>
        <ElFormItemProxy :label="$t('common.title.opCtryRegionCode')" prop="opCtryRegionCode">
          <CtryRegionSearchInput v-model="slotProps.form.opCtryRegionCode" showDesc="false" style="width: 120px" opCtryRegion />
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.nomineeManagement.currencyCode')" label-width="200" prop="currencyCode">
          <CurrencySearchInput v-model="slotProps.form.currencyCode" showDesc="false" style="width: 100px"/>
        </ElFormItemProxy>
      </FormRow>
      <FormRow> 
        <ElFormItemProxy :label="$t('csscl.nomineeManagement.bankAccountNo')" prop="bankAccountNo">
          <el-input style="width: 300px" v-model="slotProps.form.bankAccountNo" maxlength="18"/>
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.nomineeManagement.parentBankAccountNo')" label-width="200" prop="parentBankAccountNo">
          <el-input style="width: 300px" v-model="slotProps.form.parentBankAccountNo" maxlength="17" />
        </ElFormItemProxy>
      </FormRow>
    </template>
    <template v-slot:tableColumn>
        <el-table-column align="left" header-align="center" sortable="custom" prop="opCtryRegionCode" :label="$t('common.title.opCtryRegionCode')" width="250" />
        <el-table-column align="left" header-align="center" sortable="custom" prop="clientCode" :label="$t('csscl.nomineeManagement.clientAccountCode')" width="270" />
        <el-table-column align="left" header-align="center" sortable="custom" prop="currencyCode" :label="$t('csscl.nomineeManagement.currencyCode')" width="250" />
        <el-table-column align="left" header-align="center" sortable="custom" prop="bankAccountNo" :label="$t('csscl.nomineeManagement.bankAccountNo')" width="270" />
        <el-table-column align="left" header-align="center" sortable="custom" prop="parentBankAccountNo" :label="$t('csscl.nomineeManagement.parentBankAccountNo')" />
        <el-table-column align="left" header-align="center" sortable="custom" prop="status" :label="$t('common.title.status')" width="150" >
          <template #default="scope">
            {{ getCommonDesc('STATUS', scope.row.status) }}
          </template>
        </el-table-column>
        <el-table-column align="left" header-align="center" sortable="custom" prop="recordStatus" :label="$t('common.title.recordStatus')" width="210" >
          <template #default="scope">
            {{  getRecordStatusDesc(scope.row) }}
          </template>
        </el-table-column>
       
    </template>
  </BasePanel>
  <Details ref="detailsRef" />
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance, watch } from 'vue';
import {
Search
} from '@element-plus/icons-vue'
import BasePanel from '~/pages/base/index.vue'
import Details from './Details.vue'
import CtryRegionSearchInput from '~/pages/base/CtryRegionSearchInput.vue'
import CurrencySearchInput from '~/pages/base/CurrencySearchInput.vue'
import  { getCommonDesc, getRecordStatusDesc } from '~/util/Function.js';

const { proxy } = getCurrentInstance()
const searchParams = ref({ 
  //顺序和上面绑定参数一致
  opCtryRegionCode:"",
  currencyCode:"",
  bankAccountNo:"",
  parentBankAccountNo:"",
});

const tableRef = ref();
const detailsRef = ref();
const showDetails = (row, disabled) => {
  detailsRef.value.showDetails(row, disabled);
}
const deleteRow = (row) => {
  proxy.$axios.delete("/datamgmt/api/v1/nominee?nomineeId="+row.nomineeOid).then((body) => {
    if (body.success) {
      tableRef.value.load();
    }
  });
}
const editRow = (row) => {
  detailsRef.value.editRow(row);
}

/*const reload = () => {
  tableRef.value.load();
}*/
//-------------------------------

</script>

<style>

</style>