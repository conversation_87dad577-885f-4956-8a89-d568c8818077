<template>
  <BaseDetails ref="details" :handleSave="handleSave" :reload="reload" :viewOriginalForm="viewOriginalForm" :form="ruleForm" >
    <el-form :validateOnRuleChange="false" :disabled="formDisabled" ref="ruleFormRef" style="width: 100%" :model="ruleForm.form" :rules="rules" status-icon>
      <FormRow>
        <FormItemSign :detailsRef="details" :label="$t('common.title.opCtryRegionCode')" label-width="210" prop="opCtryRegionCode">
          <CtryRegionSearchInput v-model="ruleForm.form.opCtryRegionCode" 
            :disabled="editDis"
            showDesc="false"
            opCtryRegion />
        </FormItemSign>
      </FormRow>  
      <FormRow>  
        <FormItemSign :detailsRef="details" :label="$t('csscl.ctryRegionManagement.ctryRegionCode')" label-width="210" prop="ctryRegionCode">
          <InputText v-model="ruleForm.form.ctryRegionCode" onlyLetters="A-Z" uppercase  maxlength="3" style="width: 80px" :disabled="editDis" />
        </FormItemSign>
        <FormItemSign :detailsRef="details" :label="$t('csscl.ctryRegionManagement.isoCode')" label-width="180" prop="isoCode">
          <InputText v-model="ruleForm.form.isoCode" maxlength="3" uppercase :disabled="editDis" />
        </FormItemSign>
        <FormItemSign :detailsRef="details" :label="$t('csscl.ctryRegionManagement.mailRegion')" label-width="90" prop="mailRegion">
          <Select v-model="ruleForm.form.mailRegion" type="MAIL_REGION" />
        </FormItemSign>
      </FormRow>  
      <FormRow> 
        <FormItemSign :detailsRef="details" :label="$t('csscl.ctryRegionManagement.currencyShortName')" label-width="210" prop="ctryRegionName">
          <InputText v-model="ruleForm.form.ctryRegionName" maxlength="50" style="width: 330px" />
        </FormItemSign>
        <FormItemSign :detailsRef="details" :label="$t('csscl.ctryRegionManagement.currencyCode')" label-width="180" prop="currencyCode">
          <CurrencySearchInput v-model="ruleForm.form.currencyCode" showDesc="false" />
        </FormItemSign>
        <FormItemSign :detailsRef="details" :label="$t('common.title.status')" label-width="90" prop="status">
          <Select v-model="ruleForm.form.status" type='STATUS' />
        </FormItemSign>
      </FormRow>
      <el-row>
        <span style="font-size: 16px; font-weight: bold;">Holiday</span>
        <div style="paddingBlock:5px;width: 100%;display: flex;flex-flow: wrap;border-top: 1px solid;" ></div>
      </el-row>
      
      <el-container>
        <el-aside width="40%">
          <EditGrid v-model="holidayVpos"
            oid="ctryHolidayOid" 
            ref="holidayGridRef"
            uniqueKey="holidayDate"
            :form="holidayForm" 
            :rules="holidayRules" 
            :details="details" 
            :disabled="formDisabled"
            tableStyle="overflow: auto; height: 430px;"
             >
            <template #columns>
              <el-table-column prop="holidayDate" width="150" :label="$t('csscl.ctryRegionManagement.holiday')" />
              <el-table-column prop="holidayName" :label="$t('csscl.ctryRegionManagement.description')"/>
              <el-table-column prop="recordStatus" width="220" :label="$t('common.title.recordStatus')">
                  <template #default="scope">
                      {{ getCommonDesc('RECORD_STATUS', scope.row.recordStatus) }}
                      <span v-if="scope.row.recordStatus && scope.row.recordStatus !== 'A'">
                          for {{ getCommonDesc('MKCK_ACTION', scope.row.mkckAction) }}
                      </span>
                  </template>
              </el-table-column>
            </template>
            <template #form>
              <FormItemSign :detailsRef="details" label-width="150px" :label="$t('csscl.ctryRegionManagement.holiday')" prop="holidayDate">
                <div style="position:relative">
                  <DateItem v-model="holidayForm.holidayDate" style="width: 150px"/>
                </div>
              </FormItemSign>
              <FormItemSign :detailsRef="details" label-width="150px" :label="$t('csscl.ctryRegionManagement.description')"
                prop="holidayName">
                <el-input v-model="holidayForm.holidayName" maxlength="70" style="width: 450px" class="text-none" />
              </FormItemSign>
            </template>
          </EditGrid>
        </el-aside>
        <el-aside width="15%"></el-aside>
        <el-main >
          <ElFormItemProxy></ElFormItemProxy>
          <el-text tag="P" class="form-item-sign">{{  $t("csscl.ctryRegionManagement.uploadHolidayFile") }}</el-text>
          <FormRow>
            <ElFormItemProxy label=" ">
              <UploadItem :show-file-list="false" class="upload-demo" drag :file-list="ruleForm.form.fileList" :auto-upload="false"
                accept=".xlsx" :on-change="handleUpload" style="width: 800px">
                <el-icon><Upload /></el-icon>
                <div class="el-upload__text">Browse or drop file</div>
              </UploadItem>
            </ElFormItemProxy>
          </FormRow>
          <FormRow>
            <FormItemSign :detailsRef="details" :label="$t('csscl.ctryRegionManagement.fileName')" prop="fileName">
              <el-space>
              <el-input v-model="ruleForm.form.fileName" :disabled="true" style="width: 400px" class="text-none">
                <template #append>
                  <el-button type="primary" @click="handleDownload" :icon="Download" v-if="ruleForm.form.fileName"/>
                </template>
              </el-input>
                <ElFormItemProxy>
                    <el-button type="primary" @click="downloadTemplate">{{$t('csscl.cashinstr.upload.downloadTemplate')}}</el-button>
                </ElFormItemProxy>
              </el-space>              
            </FormItemSign>
          </FormRow>
          <FormRow>
            <FormItemSign :detailsRef="details" :label="$t('csscl.ctryRegionManagement.totalRecord')" prop="uploadRcCnt">
              <el-input v-model="ruleForm.form.uploadRcCnt" disabled style="width: 100px" />
            </FormItemSign>
          </FormRow>
          <FormRow>
            <FormItemSign :detailsRef="details" :label="$t('csscl.ctryRegionManagement.logSatus')" prop="processStatus">
              <el-input v-model="ruleForm.form.processStatusDesc" disabled style="width: 150px" />
            </FormItemSign>
          </FormRow>
          <FormRow>
            <FormItemSign :detailsRef="details" :label="$t('csscl.ctryRegionManagement.logDetails')" prop="errorLog">
              <el-input v-model="ruleForm.form.errorLog" type="textarea" disabled style="width: 550px" :rows="6" />
            </FormItemSign>
          </FormRow>
        </el-main>
      </el-container>


    </el-form>
  </BaseDetails>
</template>

<script lang="ts" setup>
import { ref, reactive, getCurrentInstance } from 'vue';
import type { FormInstance,  } from 'element-plus'
import { ElMessageBox } from 'element-plus'
import {Upload, Download} from '@element-plus/icons-vue';
import BaseDetails from '~/pages/base/Details.vue';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import CtryRegionSearchInput from '~/pages/base/CtryRegionSearchInput.vue'
import CurrencySearchInput from '~/pages/base/CurrencySearchInput.vue'
import { getCommonDesc, showErrorMsg } from '~/util/Function.js';
import { getOid, downloadFile, saveMsgBox } from '~/util/Function.js';
import { commonRules, showValidateMsg } from '~/util/Validators.js';
import { addCustValid, focusType } from '~/util/ModifiedValidate.js';


const { proxy } = getCurrentInstance();
const props = defineProps(['reload']);
const details = ref();
const formDisabled = ref(false);
const editDis = ref(false);
const ruleFormRef = ref()
const ruleForm = reactive({
  rules:()=>{ return [{ rules:rules }] },
  form:{
    fileList: [],
  }
});

const reqParams = reactive({ 
  ctryRegionOid: ruleForm.form.ctryRegionOid, 
  pendingOid: ruleForm.form.pendingOid,
  isApproveDetail:false,
  approveNumber:-1,
});

const holidayVpos = ref([]);

const holidayGridRef = ref();

const loadGrid = async () => {
  const msg = await proxy.$axios.post("/datamgmt/api/v1/ctryregion/holiday/list", {
      param: {
        ...reqParams
      },
      current: 1,
      pageSize: 999999,
      // orderBy: ,
  });
  if (msg?.success) {
      holidayVpos.value = msg.data.data;
  }
}

const editRow = (row, disabled,newId) => {
  if(row?.isApproveDetail && disabled){
    ruleForm.form = row.afterImage;
    reqParams.isApproveDetail = true;
    reqParams.approveNumber = row?.approveNumber;
    reqParams.ctryRegionOid = row?.eventPkey;
    details.value.currentRow = ruleForm.form;
    loadGrid();
  } else {
    const oid = newId || (row?.currentOid && getOid(row, disabled, proxy));
    if (oid) {
      proxy.$axios.get("/datamgmt/api/v1/ctryregion?objectId="+oid).then((body) => {
          if(body.success) {
              ruleForm.form = body.data;
              reqParams.ctryRegionOid= body.data?.ctryRegionOid;
              reqParams.pendingOid= body.data?.pendingOid;
              details.value.currentRow = body.data;
              holidayGridRef.value.clearModifyData();
              loadGrid();
              addCustValid(ruleForm.form, ()=>{
                let leg = holidayGridRef.value.getModifyRecords()?.length;
                leg = leg > 0 ? true : false;
                if (!leg){
                    let datas = holidayGridRef.value.showData;
                    for (let i = 0; i < datas.length; i++) {
                        let data = datas[i];
                        if (data.recordStatus != 'A') {
                            focusType.type = focusType.EnterObj;
                            break;
                        }
                    }
                }
                return leg;
              });
          }
          details.value.initWatch({w1:ruleForm,w2:holidayGridRef,w3:holidayForm}, ruleForm);
      });
      editDis.value = true;
    }else{
      details.value.initWatch({w1:ruleForm,w2:holidayGridRef,w3:holidayForm}, ruleForm);
    }
  }
}

const viewOriginalForm = (pendingOid, isDisabled) => {
  formDisabled.value = isDisabled;
  proxy.$axios.get("/datamgmt/api/v1/ctryregion?objectId="+pendingOid).then((body) => {
        if(body.success) {
            ruleForm.form = body.data;
            details.value.currentRow.value = body.data;
            reqParams.ctryRegionOid= body.data?.ctryRegionOid;
            reqParams.pendingOid= body.data?.pendingOid;
            loadGrid();
        }
    });
}

const handleSave = async (searchValid, isOnlyValidate, unPopping) => {
  if (holidayGridRef.value.isEditing()) {
    showErrorMsg("The sub table data has not been saved. Please confirm if you want to leave.");
    return false;
  }
  let result = await ruleFormRef.value.validate((valid, fields) => {
        if (valid) {
        } else {
          showValidateMsg(details, fields);
        }
    });
    if (isOnlyValidate) {
        return result;
    }
  if (result && searchValid && await saveMsgBox(unPopping)) {
    if (ruleForm.form.ctryRegionOid) {
        const msg = await proxy.$axios.patch("/datamgmt/api/v1/ctryregion", {
            ...ruleForm.form,
            ctryHolidayVPOList: holidayGridRef.value.getModifyRecords()
        });
        details.value.writebackId(msg.data);
        editRow(null,null,msg.data);
        holidayGridRef.value.clearModifyData()
        loadGrid();
        return msg.success;
    } else {
        const msg = await proxy.$axios.post("/datamgmt/api/v1/ctryregion", {
            ...ruleForm.form,
            ctryHolidayVPOList: holidayGridRef.value.getModifyRecords()
        });
        details.value.writebackId(msg.data);
        editRow(null,null,msg.data);
        holidayGridRef.value.clearModifyData()
        loadGrid();
        return msg.success;
    }
  }
    return false;
}

const showDetails = (row, isdoubleCheck) => {
  if(isdoubleCheck||row.recordStatus==='PA'){
      formDisabled.value = true;
      details.value.showDetails(row, true)
  }else{
      formDisabled.value = false;
      details.value.showDetails(row, false)
  }
  ruleForm.form = {};
  details.value.currentRow={};
  editDis.value = false;
  editRow(row, isdoubleCheck);
}

defineExpose({
  details,
  editRow,
  showDetails,
});
// --------------------------------------------
interface RuleForm {
  currencyCode: String
  status: String
}

const rules = reactive({
    opCtryRegionCode: [
        commonRules.required,
    ],
    ctryRegionCode: [
        commonRules.required,
        commonRules.name,
    ],
    ctryRegionName:[
        commonRules.name,
    ],
    isoCode: [
        commonRules.required,
        commonRules.name,
    ],
    currencyCode: [
        commonRules.required,
    ],
    status: [
        commonRules.selectRequired,
    ],
})

const value = ref('')

const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate((valid, fields) => {
        if (valid) {
            console.log('submit!')
        } else {
            console.log('error submit!', fields)
        }
    })
}

const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.resetFields()
}

const downloadTemplate = (e) => {
  downloadFile("/datamgmt/api/v1/ctryregion/holiday/template", {});
}

const handleDownload = () => {
    var row = ruleForm.form;
    var params = {
        filePath: row.filePath,
        fileName: row.fileName,
    }
    downloadFile("/datamgmt/api/v1/ctryregion/holiday/download", params);
}

const handleUpload = async(file) => {
  let result = await ruleFormRef.value.validate((valid, fields) => {
        if (valid) {
        } else {
          showValidateMsg(details, fields);
        }
    });
  if(result){
    ruleForm.form.fileList = [];
    ruleForm.form.fileList.push(file);
    
    if(file.size === 0){
      ElMessageBox.alert("Please upload a file.", 'Warning');
    }

    let formData = new FormData();
    formData.append('channel', 'MANUAL');
    formData.append('fileType', 'CTRYHOLDAY');
    formData.append('file', file.raw);
    formData.append('ctryRegionCode', ruleForm.form?.ctryRegionCode);
    const resp = await proxy.$axios.post("/datamgmt/api/v1/ctryregion/holidayUploadXlsx", formData);
    if(resp?.success){
      if(resp.data){
          ruleForm.form.filePath = resp.data?.filePath;
          ruleForm.form.fileName = resp.data?.fileName;
          ruleForm.form.uploadRcCnt = resp.data?.uploadRcCnt;
          ruleForm.form.errorLog = resp.data?.errorLog;
          ruleForm.form.processStatus = resp.data?.processStatus;
          ruleForm.form.processStatusDesc = resp.data?.processStatusDesc;
          let datas = resp.data?.listData;
          if(datas){
            let rows = [];
            datas.forEach(elem => {
              rows.push({
                holidayDate: elem.holidayDate,
                holidayName: elem.holidayName,
              });
            });
            holidayGridRef.value.addBatch(rows);
          }
      }
    }
    return resp?.success;
  } else {
    return false;
  }
}
const holidayForm = reactive({
  holidayDate: null,
  holidayName: ''
});

const holidayRules = reactive({
  holidayDate: [
    commonRules.required,
  ],
  holidayName: [
    commonRules.required,
    commonRules.name,
  ],
});

</script>

<style></style>