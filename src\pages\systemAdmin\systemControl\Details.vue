<template>
  <BaseDetails ref="details" :handleSave="handleSave" :reload="props.reload" :viewOriginalForm="viewOriginalForm" :form="ruleForm">
    <el-form :validateOnRuleChange="false" :disabled="formDisabled" ref="ruleFormRef" style="width: 100%" :model="ruleForm.form" :rules="rules" status-icon>
      <FormRow>
        <FormItemSign :detailsRef="details" label-width="240px" :label="$t('common.title.opCtryRegionCode')" prop="ctryRegionCode" >
          <CtryRegionSearchInput clearable v-model="ruleForm.form.ctryRegionCode" showDesc="false" style="width: 120px" opCtryRegion />  
        </FormItemSign>
        <FormItemSign :detailsRef="details" :label="$t('csscl.admin.ctrl.name')" prop="insttuName" label-width="140px">
          <InputText v-model="ruleForm.form.insttuName" style="width: 350px" maxlength="100" />
        </FormItemSign>
        <FormItemSign :detailsRef="details" label-width="130px" :label="$t('common.title.status')" prop="status">
          <Select v-model="ruleForm.form.status" style="width: 100px" type='STATUS' />
        </FormItemSign>
      </FormRow>  
      <FormRow>  
        <FormItemSign :detailsRef="details" label-width="240px" :label="$t('csscl.admin.ctrl.processDate')" prop="curPrcsDate" >
          <el-input v-model="ruleForm.form.curPrcsDate" :disabled="true" style="width: 100px"/> 
        </FormItemSign>
        <FormItemSign :detailsRef="details" label-width="140px" :label="$t('csscl.admin.ctrl.lastProcessDate')" prop="lastPrcsDate">
          <el-input v-model="ruleForm.form.lastPrcsDate" :disabled="true" style="width: 100px"/>
        </FormItemSign>
        <FormItemSign :detailsRef="details" label-width="130px" :label="$t('csscl.admin.ctrl.nextProcessDate')" prop="nextPrcsDate">
          <el-input v-model="ruleForm.form.nextPrcsDate" :disabled="true" style="width: 100px"/>
        </FormItemSign>
      </FormRow>
      <FormRow>
        <FormItemSign :detailsRef="details" label-width="240px" :label="$t('csscl.admin.ctrl.lastMounthEnd')" prop="lastMthEnd">
          <el-input v-model="ruleForm.form.lastMthEnd" :disabled="true" style="width: 100px"/> 
        </FormItemSign>
        <FormItemSign :detailsRef="details" label-width="140px" :label="$t('csscl.admin.ctrl.nextMounthEnd')" prop="nextMthEnd">
          <el-input v-model="ruleForm.form.nextMthEnd" :disabled="true" style="width: 100px"/>
        </FormItemSign> 
        <ElFormItemProxy></ElFormItemProxy>
      </FormRow>  
    <!-- </el-form> -->

    <el-text tag="P" style="font-weight: bold; font-size: larger; margin-top: 20px;"> {{ $t('csscl.admin.ctrl.generalSetting') }} </el-text>
    <el-divider style="margin: 5px 0px;" />
    <!-- <el-form :validateOnRuleChange="false" :disabled="formDisabled" ref="ruleFormRef" style="width: 100%" :model="ruleForm.form" :rules="rules" status-icon> -->
    <FormRow>
      <FormItemSign :detailsRef="details" label-width="240px" :label="$t('csscl.admin.ctrl.uploadFileType')" prop="uploadFileType" >
        <el-input type="textarea" :rows="4" v-model="ruleForm.form.uploadFileType" style="width: 280px" maxlength="400" show-word-limit/>
      </FormItemSign>
      <FormItemSign :detailsRef="details" label-width="280px" :label="$t('csscl.admin.ctrl.maxFileSize')" prop="maxFileSize" >
        <el-space>
          <InputNumber v-model="ruleForm.form.maxFileSize" style="width: 120px;" precision="10" scale="0" />
          <span class="dw">Kilobyte(s)</span>
        </el-space>
      </FormItemSign>
      <FormItemSign :detailsRef="details" label-width="240px" :label="$t('csscl.admin.ctrl.maxRowRecords')" prop="dataExtractMaxRowOfRecords">
        <InputNumber v-model="ruleForm.form.dataExtractMaxRowOfRecords" style="width: 120px;" precision="7" scale="0"  /> 
      </FormItemSign> 
    </FormRow>
    <FormRow>
      <FormItemSign :detailsRef="details" label-width="240px" :label="$t('csscl.admin.ctrl.timeOut')" prop="timeOut">
        <el-space>
          <InputNumber v-model="ruleForm.form.timeOut" style="width: 100px;" precision="3" scale="0" />
          <span class="dw">Minute(s)</span>
        </el-space>
      </FormItemSign> 
      <FormItemSign :detailsRef="details" label-width="280px" :label="$t('csscl.admin.ctrl.dualApprovalAmount')" prop="dualApproveAmt">
        <InputNumber v-model="ruleForm.form.dualApproveAmt" style="width: 200px;" >
          <template #prepend ><text >HKD</text></template>
        </InputNumber>
      </FormItemSign>
      <ElFormItemProxy></ElFormItemProxy>
    </FormRow>
    <FormRow>
      <FormItemSign :detailsRef="details" label-width="240px" :label="$t('csscl.admin.ctrl.dayCountAsOverdue')" prop="overdueGracePeriod">
        <el-space>
          <InputNumber v-model="ruleForm.form.overdueGracePeriod" precision="3" scale="0" style="width: 100px;" />
          <span class="dw">Day(s)</span>
        </el-space>
      </FormItemSign>
      <FormItemSign :detailsRef="details" label-width="280px" :label="$t('csscl.admin.ctrl.propRevDate')" prop="proposedReverseDay">
        <el-space>
          <InputNumber v-model="ruleForm.form.proposedReverseDay" precision="3" scale="0" style="width: 100px;" />
          <span class="dw">Day(s)</span>
        </el-space>
      </FormItemSign>
      <ElFormItemProxy></ElFormItemProxy>
    </FormRow>
    <!-- </el-form> -->
    <!-- <span style="font-weight: bold;"> {{ $t('csscl.admin.ctrl.clientDataHousekeeping') }} </span> -->
    <el-text tag="P" style="font-weight: bold; margin-top:10px;"> {{ $t('csscl.admin.ctrl.clientDataHousekeeping') }} </el-text>
    <el-container>
    <el-container>
        <el-aside width="33.3%">
          <EditGrid v-model="ruleForm.form.sysCtrlHousekeepVPOS" 
              :form="dialogForm.housekeep" oid="sysCtrlHousekeepOid" 
              :rules="housekeepRules"
              :details="details"
              :disabled="formDisabled"
              uniqueKey="clientType"
              ref="sysCtrlHousekeepGridRef"
              :unUpdate="true">
              <template #columns>
                <el-table-column prop="clientType" :label="$t('csscl.admin.ctrl.clientType')" header-align="center">
                  <template #default="scope">{{ getCommonDesc('CLIENT_TYPE_CODE', scope.row.clientType) }}</template>
                </el-table-column>
                <el-table-column prop="keepMths" :label="$t('csscl.admin.ctrl.mouths')" header-align="center"  align="right" />
                <el-table-column prop="recordStatus" :label="$t('common.title.recordStatus')" header-align="center">
                  <template #default="scope">
                    <!-- scope.row -->
                    {{ getRecordStatusDesc(scope.row) }}
                  </template>
                </el-table-column>
              </template>
              <template #form>
                  <FormItemSign :detailsRef="details" :label="$t('csscl.admin.ctrl.clientType')" prop="clientType" label-width="100px">
                    <Select v-model="dialogForm.housekeep.clientType" type='CLIENT_TYPE_CODE' style="width: 200px;"/>
                  </FormItemSign>
                  <FormItemSign :detailsRef="details" :label="$t('csscl.admin.ctrl.mouths')" prop="keepMths" label-width="100px">
                    <el-space>
                      <InputNumber v-model="dialogForm.housekeep.keepMths" style="width: 100px;" numLen="3" scale="0" />
                      <span class="dw">Month(s)</span>
                    </el-space>
                  </FormItemSign>
              </template>
          </EditGrid>
        </el-aside>
        <el-main>
          <FormRow>
            <FormItemSign :detailsRef="details" label-width="280px" :label="$t('csscl.admin.ctrl.userReportHousekeeping')" prop="internalRptHousekeep" >
              <el-space>
                <InputNumber v-model="ruleForm.form.internalRptHousekeep" style="width: 100px;" precision="3" scale="0" />
                <span class="dw">Day(s)</span>
              </el-space>
            </FormItemSign>
          </FormRow>
          <FormRow>
            <FormItemSign :detailsRef="details" label-width="280px" :label="$t('csscl.admin.ctrl.clientReportHousekeeping')" prop="outgoingRptHousekeep" >
              <el-space>
                <InputNumber v-model="ruleForm.form.outgoingRptHousekeep" style="width: 100px;" precision="3" scale="0" />
                <span class="dw">Day(s)</span>
              </el-space>
            </FormItemSign>
          </FormRow>
        </el-main>
      </el-container>
    </el-container>
    <FormRow>
      <FormItemSign :detailsRef="details" :label="$t('csscl.admin.ctrl.auditLogsArchive')" prop="auditLogArchive" label-width="150px">
        <el-space>
            <InputNumber v-model="ruleForm.form.auditLogArchive" style="width: 100px;" precision="3" scale="0" />
            <span class="dw">Month(s)</span>
          </el-space>
      </FormItemSign>
    </FormRow>

    <el-text tag="P" style="font-weight: bold; font-size: medium; margin-top:20px;"> {{ $t('csscl.admin.ctrl.sysMaint') }} </el-text>
    <el-divider style="margin: 5px 0px;" />
    <FormRow>
      <FormItemSign :detailsRef="details" label-width="130px" :label="$t('csscl.admin.ctrl.effectiveStartDate')" prop="lastEffectiveStartDate"
        style="width:430px" >
        <div style="position:relative">
        <DateItem v-model="ruleForm.form.lastEffectiveStartDate"
                  :title="$t('message.later.dateto', [$t('csscl.admin.ctrl.effectiveStartDate') + ' & Time', $t('csscl.admin.ctrl.effectiveEndDate') + ' & Time'])"
                  @change="checkEffectiveDate"/>
      </div>
      </FormItemSign> 
      <FormItemSign :detailsRef="details" label-width="130px" :label="$t('csscl.admin.ctrl.effectiveStartTime')" prop="lastEffectiveStartTime">
        <div style="position:relative">
          <el-time-picker v-model="ruleForm.form.lastEffectiveStartTime"
                          :label="'title:'+$t('message.later.dateto', [$t('csscl.admin.ctrl.effectiveStartDate') + ' & Time', $t('csscl.admin.ctrl.effectiveEndDate') + ' & Time'])"
                          value-format="HH:mm:ss" style="width: 150px" @change="checkEffectiveDate"/>
        </div>
      </FormItemSign>
      <ElFormItemProxy></ElFormItemProxy>
    </FormRow>
    <FormRow>
      <FormItemSign :detailsRef="details" label-width="130px" :label="$t('csscl.admin.ctrl.effectiveEndDate')" prop="nextEffectiveStartDate" 
        style="width:430px">
        <div style="position:relative">
        <DateItem v-model="ruleForm.form.nextEffectiveStartDate" type="date" style="width: 130px" @change="checkEffectiveDate"/>
      </div>
      </FormItemSign> 
      <FormItemSign :detailsRef="details" label-width="130px" :label="$t('csscl.admin.ctrl.effectiveEndTime')" prop="nextEffectiveStartTime" >
        <el-time-picker v-model="ruleForm.form.nextEffectiveStartTime"
                        label="title: "
                        value-format="HH:mm:ss" style="width: 150px" @change="checkEffectiveDate"/>
      </FormItemSign>
      <ElFormItemProxy></ElFormItemProxy>
    </FormRow>

    <el-text tag="P" style="font-weight: bold; font-size: medium; margin-top:20px;"> {{ $t('csscl.admin.ctrl.ftghkMaintWindow') }} </el-text>
    <el-divider style="margin: 5px 0px;" />
    <el-container>
      <el-main>
        <EditGrid v-model="ruleForm.form.sysCtrlFtgSettingVPOS" 
          tableWidth="60%"
          :form="dialogForm.ftgHkMaint" oid="sysCtrlFtgSettingOid" 
          :rules="ftgHkMaintRules"
          :details="details"
          :disabled="formDisabled"
          ref="sysCtrlFtgSettingGridRef"
          :unUpdate="true">
          <template #columns>
            <el-table-column prop="frenquency" :label="$t('csscl.admin.ctrl.frequency')" header-align="center" width="250">
              <template #default="scope">{{ getCommonDesc('FREQUENCY',scope.row.frenquency) }}</template>
            </el-table-column>
            <el-table-column prop="days" :label="$t('csscl.admin.ctrl.day')" header-align="center" width="120">
              <template #default="scope">{{ getCommonDesc('WEEKLY_DAY' ,scope.row.days) }}</template>
            </el-table-column>
            <el-table-column prop="startTime" :label="$t('csscl.admin.ctrl.startTime')" header-align="center" width="130"/>
            <el-table-column prop="endTime" :label="$t('csscl.admin.ctrl.endTime')" header-align="center" width="130"/>
            <el-table-column prop="recordStatus" :label="$t('common.title.recordStatus')" header-align="center">
              <template #default="scope">
                <!-- scope.row -->
                {{ getRecordStatusDesc(scope.row) }}
              </template>
            </el-table-column>
          </template>
          <template #form>
              <FormItemSign :detailsRef="details" :label="$t('csscl.admin.ctrl.frequency')" prop="frenquency" label-width="100px">
                <Select v-model="dialogForm.ftgHkMaint.frenquency" type='FREQUENCY' style="width: 250px;"/>
              </FormItemSign>
              <FormItemSign :detailsRef="details" :label="$t('csscl.admin.ctrl.day')" prop="days" label-width="100px">
                <Select v-model="dialogForm.ftgHkMaint.days" type='WEEKLY_DAY' />
              </FormItemSign>
              <FormItemSign :detailsRef="details" :label="$t('csscl.admin.ctrl.startTime')" prop="startTime" label-width="100px">
                <el-time-picker v-model="dialogForm.ftgHkMaint.startTime"
                                :label="'title:'+$t('message.later.dateto', [$t('csscl.admin.ctrl.startTime'), $t('csscl.admin.ctrl.endTime')])"
                                value-format="HH:mm:ss" style="width: 150px"/>
              </FormItemSign>
              <FormItemSign :detailsRef="details" :label="$t('csscl.admin.ctrl.endTime')" prop="endTime" label-width="100px">
                <el-time-picker v-model="dialogForm.ftgHkMaint.endTime"
                                label="title: "
                                value-format="HH:mm:ss" style="width: 150px"/>
              </FormItemSign>
          </template>
        </EditGrid>
      </el-main>
    </el-container>

    
    <el-text tag="P" style="font-weight: bold; font-size: medium; margin-top:20px;"> {{ $t('csscl.admin.ctrl.mClassMaint') }} </el-text>
    <el-divider style="margin: 5px 0px;" />
    <el-container>
        <el-main>
          <EditGrid v-model="ruleForm.form.sysCtrlMclassVPOs" 
            tableWidth="60%"
            :form="dialogForm.mclass" oid="sysCtrlMclassOid" 
            :rules="mclassRules"
            :details="details"
            :disabled="formDisabled"
            ref="sysCtrlMclassGridRef"
            :unUpdate="true">
            <template #columns>
              <el-table-column prop="opCtryRegionCode" :label="$t('common.title.opCtryRegionCode')" header-align="center"/>
              <el-table-column prop="mclassCode" :label="$t('csscl.admin.ctrl.mClassCode')" align="right" header-align="center" width="160"/>
              <el-table-column prop="apprLimitAmtMin" :label="$t('csscl.admin.ctrl.lowestApprovalLimit')+' (>)'" align="right" header-align="center">
                <template #default="scope">{{ thousFormat(scope.row.apprLimitAmtMin) }}</template>
              </el-table-column>
              <el-table-column prop="apprLimitAmtMax" :label="$t('csscl.admin.ctrl.highestApprovalLimit')+' (<=)'" align="right" header-align="center">
                <template #default="scope">{{ thousFormat(scope.row.apprLimitAmtMax) }}</template>
              </el-table-column>
              <el-table-column prop="recordStatus" :label="$t('common.title.recordStatus')" header-align="center">
                <template #default="scope">
                  {{ getRecordStatusDesc(scope.row) }}
                </template>
              </el-table-column>
            </template>
            <template #form>
                <FormItemSign :detailsRef="details" :label="$t('common.title.opCtryRegionCode')" prop="opCtryRegionCode">
                  <CtryRegionSearchInput clearable v-model="dialogForm.mclass.opCtryRegionCode" showDesc="false" style="width: 160px" disabled opCtryRegion />
                </FormItemSign>
                <FormItemSign :detailsRef="details" :label="$t('csscl.admin.ctrl.mClassCode')" prop="mclassCode">
                  <el-input v-model="dialogForm.mclass.mclassCode"  maxlength="2" style="width: 160px"/>
                </FormItemSign>
                <FormItemSign :detailsRef="details" :label="$t('csscl.admin.ctrl.lowestApprovalLimit')" prop="apprLimitAmtMin">
                  <InputNumber v-model="dialogForm.mclass.apprLimitAmtMin"   style="width: 200px;" />
                </FormItemSign>
                <FormItemSign :detailsRef="details" :label="$t('csscl.admin.ctrl.highestApprovalLimit')" prop="apprLimitAmtMax">
                  <InputNumber v-model="dialogForm.mclass.apprLimitAmtMax" style="width: 200px;" />
                </FormItemSign>
            </template>
          </EditGrid>
        </el-main>
    </el-container>

  </el-form> 
  </BaseDetails>

  <!-- Edit dialog -->
  <!-- Client Data Housekeeping -->
<!--  <el-dialog v-model="dialogForm.show.housekeep" :title="$t('csscl.admin.ctrl.clientDataHousekeeping')"
        :close-on-click-modal="false" width="400px" class="mkck-dialog" append-to-body :show-close="false">
        <template #header>
          <div class="mkckTitle">
            <span class="title-name">{{ $t('csscl.admin.ctrl.clientDataHousekeeping') }}</span>
          </div>
        </template>
        <br>
        <el-form :validateOnRuleChange="false" :model="dialogForm.housekeep" ref="housekeepRef" :rules="housekeepRules" style="padding-left: 12px;" :showMessage="false">
            <FormRow>
              <ElFormItemProxy :label="$t('csscl.admin.ctrl.clientType')" prop="clientType" label-width="100px">
                <Select v-model="dialogForm.housekeep.clientType" type='CLIENT_TYPE_CODE' style="width: 200px;"/>
              </ElFormItemProxy>
            </FormRow>
            <FormRow>
              <ElFormItemProxy :label="$t('csscl.admin.ctrl.mouths')" prop="keepMths" label-width="100px">
                <el-space>
                  <InputNumber v-model="dialogForm.housekeep.keepMths" style="width: 100px;" numLen="3" scale="0" />
                  <span class="dw">Month(s)</span>
                </el-space>
              </ElFormItemProxy>
            </FormRow>
        </el-form>
        <div class="button-group" style="display: flex; justify-content: flex-end; padding: 10px;">
          <el-button @click="dialogCancel" class="ep-button-custom">Cancel</el-button>
          <el-button type="primary" @click="dialogOk(tableRef.key.housekeep)" class="ep-button-custom">OK</el-button>
        </div>
  </el-dialog>-->

  <!-- FTG-HK Maintenance Window -->
<!--  <el-dialog v-model="dialogForm.show.ftgHkMaint" :title="$t('csscl.admin.ctrl.ftghkMaintWindow')"
        :close-on-click-modal="false" width="400px" class="mkck-dialog" append-to-body :show-close="false">
        <template #header>
          <div class="mkckTitle">
            <span class="title-name">{{ $t('csscl.admin.ctrl.ftghkMaintWindow') }}</span>
          </div>
        </template>
        <br>
        <el-form :validateOnRuleChange="false" :model="dialogForm.ftgHkMaint" ref="ftgHkMaintRef" style="padding-left: 12px;" :showMessage="false">
            <FormRow>
              <ElFormItemProxy :label="$t('csscl.admin.ctrl.frequency')" prop="frenquency" label-width="100px">
                <Select v-model="dialogForm.ftgHkMaint.frenquency" type='FREQUENCY' style="width: 250px;"/>
              </ElFormItemProxy>
            </FormRow>
            <FormRow>
              <ElFormItemProxy :label="$t('csscl.admin.ctrl.day')" prop="days" label-width="100px">
                <Select v-model="dialogForm.ftgHkMaint.days" type='WEEKLY_DAY' />
              </ElFormItemProxy>
            </FormRow>
            <FormRow>
              <ElFormItemProxy :label="$t('csscl.admin.ctrl.startTime')" prop="startTime" label-width="100px">
                <el-time-picker v-model="dialogForm.ftgHkMaint.startTime" value-format="HH:mm:ss" style="width: 150px"/>
              </ElFormItemProxy>
            </FormRow>
            <FormRow>
              <ElFormItemProxy :label="$t('csscl.admin.ctrl.endTime')" prop="endTime" label-width="100px">
                <el-time-picker v-model="dialogForm.ftgHkMaint.endTime" value-format="HH:mm:ss" style="width: 150px"/>
              </ElFormItemProxy>
            </FormRow>
        </el-form>
        <div class="button-group" style="display: flex; justify-content: flex-end; padding: 10px;">
          <el-button @click="dialogCancel" class="ep-button-custom">Cancel</el-button>
          <el-button type="primary" @click="dialogOk(tableRef.key.ftgHkMaint)" class="ep-button-custom">OK</el-button>
        </div>
  </el-dialog>-->

  <!-- M Class Maintenance -->
<!--  <el-dialog v-model="dialogForm.show.mclass" :title="$t('csscl.admin.ctrl.mClassMaint')"
        :close-on-click-modal="false" width="500px" class="mkck-dialog" append-to-body :show-close="false">
        <template #header>
          <div class="mkckTitle">
            <span class="title-name">{{ $t('csscl.admin.ctrl.mClassMaint') }}</span>
          </div>
        </template>
        <br>
        <el-form :validateOnRuleChange="false" :model="dialogForm.mclass" ref="mclassRef" :rules="mclassRules" style="padding-left: 12px;" :showMessage="false">
            <FormRow>
              <ElFormItemProxy :label="$t('common.title.opCtryRegionCode')" prop="opCtryRegionCode">
                <CtryRegionSearchInput clearable v-model="dialogForm.mclass.opCtryRegionCode" showDesc="false" style="width: 160px" disabled opCtryRegion /> 
              </ElFormItemProxy>
            </FormRow>
            <FormRow>
              <ElFormItemProxy :label="$t('csscl.admin.ctrl.mClassCode')" prop="mclassCode">
                <el-input v-model="dialogForm.mclass.mclassCode"  maxlength="2" style="width: 160px"/>
              </ElFormItemProxy>
            </FormRow>
            <FormRow>
              <ElFormItemProxy :label="$t('csscl.admin.ctrl.lowestApprovalLimit')" prop="apprLimitAmtMin">
                <InputNumber v-model="dialogForm.mclass.apprLimitAmtMin"   style="width: 200px;" />
              </ElFormItemProxy>
            </FormRow>
            <FormRow>
              <ElFormItemProxy :label="$t('csscl.admin.ctrl.highestApprovalLimit')" prop="apprLimitAmtMax">
                <InputNumber v-model="dialogForm.mclass.apprLimitAmtMax" style="width: 200px;" />
              </ElFormItemProxy>
            </FormRow>
        </el-form>
        <div class="button-group" style="display: flex; justify-content: flex-end; padding: 10px;">
          <el-button @click="dialogCancel" class="ep-button-custom">Cancel</el-button>
          <el-button type="primary" @click="dialogOk(tableRef.key.mclass)" class="ep-button-custom">OK</el-button>
      </div>
  </el-dialog>-->
</template>

<script lang="ts" setup>
import { ref, reactive, getCurrentInstance, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import BaseDetails from '~/pages/base/Details.vue';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import CtryRegionSearchInput from '~/pages/base/CtryRegionSearchInput.vue'
import Grid from '~/pages/base/Grid.vue';
import { getOid, saveMsgBox } from '~/util/Function.js';
import { commonRules, showValidateMsg } from '~/util/Validators.js';
import  { getCommonDesc, getRecordStatusDesc, thousFormat, showErrorMsg } from '~/util/Function.js';
import { timestampToDate } from '~/util/DateUtils.js';
import { addCustValid, cloneObj, compListInCustValid } from "~/util/ModifiedValidate";

const { proxy } = getCurrentInstance();
const props = defineProps(['reload']);
const details = ref();
const formDisabled = ref(false);
const editDis = ref(false);
const sysCtrlHousekeepGridRef = ref();
const sysCtrlFtgSettingGridRef = ref();
const sysCtrlMclassGridRef = ref();

const editRow = (row, disabled,newId) => {
  if(row?.isApproveDetail && disabled){
      let eventPkey = row?.eventPkey;
      let approveNumber = row?.approveNumber;
      ruleForm.form = row?.afterImage;
      if(row.afterImage.lastPrcsDate){
          ruleForm.form.lastPrcsDate=timestampToDate(row.afterImage.lastPrcsDate);
      }
      if(row.afterImage.nextPrcsDate){
          ruleForm.form.nextPrcsDate=timestampToDate(row.afterImage.nextPrcsDate);
      }
      if(row.afterImage.lastEffectiveStartDate){
          ruleForm.form.lastEffectiveStartDate=timestampToDate(row.afterImage.lastEffectiveStartDate);
      }
      if(row.afterImage.curPrcsDate){
          ruleForm.form.curPrcsDate=timestampToDate(row.afterImage.curPrcsDate);
      }
      if(row.afterImage.nextEffectiveStartDate){
          ruleForm.form.nextEffectiveStartDate=timestampToDate(row.afterImage.nextEffectiveStartDate);
      }
      if(row.afterImage.lastMthEnd){
          ruleForm.form.lastMthEnd=timestampToDate(row.afterImage.lastMthEnd);
      }
      if(row.afterImage.nextMthEnd){
          ruleForm.form.nextMthEnd=timestampToDate(row.afterImage.nextMthEnd);
      }
      details.value.currentRow = ruleForm.form;
      proxy.$axios.get("/datamgmt/api/v1/sysctrl/approve?eventPkey=" + eventPkey+ 
      "&approveNumber="+approveNumber).then((body) => {
          if(body.success) {
            if(body.data){
              ruleForm.form.sysCtrlHousekeepVPOS=body.data.sysCtrlHousekeepVPOS;
              ruleForm.form.sysCtrlFtgSettingVPOS=body.data.sysCtrlFtgSettingVPOS;
              ruleForm.form.sysCtrlMclassVPOs=body.data.sysCtrlMclassVPOs;
            }
          }
      });
  } else {
    const oid = newId || (row?.currentOid && getOid(row, disabled, proxy));
    if (oid) {
      proxy.$axios.get("/datamgmt/api/v1/sysctrl?sysCtrlId=" + oid).then((body) => {
          if(body.success) {
              ruleForm.form = body.data;
              details.value.currentRow = body.data;
              setTimeout(()=>{
                initCtrlMclass.value = cloneObj (sysCtrlMclassGridRef.value.showData);
                initFtgSettings.value = cloneObj(sysCtrlFtgSettingGridRef.value.showData);
                initHouseKeep.value = cloneObj(sysCtrlHousekeepGridRef.value.showData);
              }, 300);
              addCustValid(ruleForm.form, ()=>{
                let result = compListInCustValid(sysCtrlMclassGridRef.value.showData,
                    initCtrlMclass.value,
                    'sysCtrlMclassOid',
                    ['opCtryRegionCode', 'mclassCode', 'apprLimitAmtMax','apprLimitAmtMin','recordStatus','mkckAction',]);
                if (!result) {
                  result = compListInCustValid(sysCtrlFtgSettingGridRef.value.showData,
                      initFtgSettings.value,
                      'sysCtrlFtgSettingOid',
                      ['startTime', 'endTime', 'frenquency', 'day','recordStatus','mkckAction',]);
                }
                if (!result) {
                  result = compListInCustValid(sysCtrlHousekeepGridRef.value.showData,
                      initHouseKeep.value,
                      'sysCtrlHousekeepOid',
                      ['ctryRegionCode', 'clientType', 'keepMths', 'recordStatus','mkckAction',]);
                }
                return result;
              })
          }
          details.value.initWatch({w1:ruleForm, w2:dialogForm});
      });
      editDis.value = true;
    }else{
      details.value.initWatch({w1:ruleForm, w2:dialogForm});
    }
  }
 
}
const initCtrlMclass = ref();
const initFtgSettings = ref();
const initHouseKeep = ref();
const viewOriginalForm = (pendingOid, isDisabled) => {
  formDisabled.value = isDisabled;
  proxy.$axios.get("/datamgmt/api/v1/sysctrl?sysCtrlId="+pendingOid).then((body) => {
        if(body.success) {
            ruleForm.form = body.data;     
        }
    });
}
const handleSave = async (searchValid, isOnlyValidate, unPopping) => {
    if (sysCtrlHousekeepGridRef.value.isEditing()||sysCtrlFtgSettingGridRef.value.isEditing()||sysCtrlMclassGridRef.value.isEditing()) {
        showErrorMsg("The sub table data has not been saved. Please confirm if you want to leave.");
        return false;
    }
    let result = await ruleFormRef.value.validate((valid, fields) => {
        if (!valid) {
          showValidateMsg(details, fields);
        }
    });
    if(checkEffectiveDate()){
      result = false;
    }
    if (isOnlyValidate) {
        return result;
    }
    if (result && searchValid && await saveMsgBox(unPopping)) {
        let formData = {...ruleForm.form};
        formData.sysCtrlHousekeepVPOS = sysCtrlHousekeepGridRef.value.mergeShowData();
        formData.sysCtrlFtgSettingVPOS = sysCtrlFtgSettingGridRef.value.mergeShowData();
        formData.sysCtrlMclassVPOs = sysCtrlMclassGridRef.value.mergeShowData();
        if (ruleForm.form.sysCtrlOid) {
            const msg = await proxy.$axios.patch("/datamgmt/api/v1/sysctrl", formData);
            details.value.writebackId(msg.data);
            editRow(null,null,msg.data)
            return msg.success;
        } else {
            const msg = await proxy.$axios.post("/datamgmt/api/v1/sysctrl", formData);
            details.value.writebackId(msg.data);
            editRow(null,null,msg.data)
            return msg.success;
        }
    }
    return false;
}
const showDetails = (row, isdoubleCheck) => {
  if(isdoubleCheck||row.recordStatus==='PA'){
        formDisabled.value = true;
        details.value.showDetails(row, true)
    }else{
        formDisabled.value = false;
        details.value.showDetails(row, false)
    }
  ruleForm.form = {};
  details.value.currentRow={};
  editDis.value = false;
  editRow(row, isdoubleCheck);
}
defineExpose({
  details,
  editRow,
  showDetails,
  viewOriginalForm,
});
// --------------------------------------------
interface RuleForm {
  ctryRegionCode: String
  insttuName: String
  status: String
  recordStatus: String
}

const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive({
  rules:()=>{ return [{ rules:rules }] },
  form: {
      ctryRegionCode: "",
      insttuName: "",
      status: "",
      recordStatus: "",
      sysCtrlHousekeepVPOS: [],  
      sysCtrlFtgSettingVPOS: [],  
      sysCtrlMclassVPOs: [],
    }
})


const rules = reactive<FormRules<RuleForm>>({
  ctryRegionCode: [
    commonRules.required,
  ],
  insttuName: [
    commonRules.required,
    commonRules.name,
  ],
  status: [
    commonRules.required,
  ],
  uploadFileType: [
    commonRules.required,
    commonRules.name,
  ],
  maxFileSize: [
    commonRules.required,
  ],
  dataExtractMaxRowOfRecords: [
    commonRules.required,
  ],
  timeOut: [
    commonRules.required,
  ],
  dualApproveAmt: [
    commonRules.required,
  ],
  overdueGracePeriod: [
    commonRules.required,
  ],
  proposedReverseDay: [
    commonRules.required,
  ],
  internalRptHousekeep: [
    commonRules.required,
  ],
  outgoingRptHousekeep: [
    commonRules.required,
  ],
  auditLogArchive: [
    commonRules.required,
  ],
})

const checkEffectiveDate = () => {
  let isError = false;
  if(ruleForm.form.nextEffectiveStartDate){
    if (ruleForm.form.lastEffectiveStartDate > ruleForm.form.nextEffectiveStartDate) {
    isError = true;
    } 
  }
  if (ruleForm.form.lastEffectiveStartTime > ruleForm.form.nextEffectiveStartTime) {
    isError = true;
  }
  if(isError){
    showErrorMsg(proxy.$t('message.common.cannot.later', [proxy.$t('csscl.admin.ctrl.effectiveStartDateTime'), proxy.$t('csscl.admin.ctrl.effectiveEndDateTime')]));
  }
  return isError;
}

interface HousekeepForm {
  clientType: String
  keepMths: number
}
const housekeepRules = reactive<FormRules<HousekeepForm>>({
  clientType: [
    commonRules.required,
  ],
  keepMths: [
    commonRules.required,
  ],
})
interface ftgHkMaintForm {
  frenquency: String
  days: Date
  startTime: Date
  endTime: Date
}
const ftgHkMaintRules = reactive<FormRules<ftgHkMaintForm>>({

  frenquency: [
    commonRules.required,
  ],
  // days: [
  //   commonRules.required,
  // ],
  startTime: [
    commonRules.required,
  ],
  endTime: [
    commonRules.required,
  ],
})

interface MclassForm {
  opCtryRegionCode: String
  mclassCode: String
  apprLimitAmtMin: Number
  apprLimitAmtMax: Number
}

const mclassRules = reactive<FormRules<MclassForm>>({
  // opCtryRegionCode: [
  //   commonRules.required,
  // ],
  mclassCode: [
    commonRules.required,
    commonRules.name,
  ],
  apprLimitAmtMin: [
    commonRules.required,
  ],
  apprLimitAmtMax: [
    commonRules.required,
  ],
})

const dialogForm=ref({
  show:{
    housekeep: false,
    ftgHkMaint: false,
    mclass: false,
  },
  housekeep: {},
  ftgHkMaint: {},
  mclass: {},
});


</script>

<style></style>