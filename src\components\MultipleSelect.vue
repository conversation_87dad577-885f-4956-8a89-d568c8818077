<template #default>
 
  <el-select v-model="data" multiple collapse-tags collapse-tags-tooltip placeholder="Select" class="multiSelect"
    :disabled="disabled" 
    :style="style()" 
    @change="change">
    <el-option v-for="item in getSource()" 
      :key="value(item)" 
      :label="label(item)" 
      :value="value(item)" />
  </el-select>

</template>

<script lang="ts" setup>
import { ref, watch, computed, defineEmits, getCurrentInstance } from 'vue';
import  { parseBool, getCommonDesc } from '~/util/Function.js';

const { proxy } = getCurrentInstance()
const props = defineProps(["modelValue", "style", 'disabled', 'type', 'source', 'valueKey', 'labelKey', 'vkEnqual', 'change', 'desc']);
const emit = defineEmits(['update:modelValue', 'update:desc']);
const inpObj = ref();
const selObj = ref();
const desc = computed({
  get(){
    return props.desc;
  },
  set(val){
    emit("update:desc", val);
  }
});
const data = computed({
  get(){
    return props.modelValue;
  },
  set(val){
    emit("update:modelValue", val);
  }
});

watch( ()=> (inpObj.value?.input?.disabled), (nv) => {
  if (!selObj?.value?.disabled) {
    selObj.value.disabled=nv;
  }
} );

const type = () => {
  return props.type?.toUpperCase() || "RECORD_STATUS";
}

const style = () => {
  return props.style || selectObject[type()]?.style || "width:200px";
}

const change = (val) => {
  if (val) {
    let d = "";
    for (let i = 0; i < val.length; i++) {
      d += ((d&&",") + getCommonDesc(type() , val[i]));
    }
    desc.value = d;
  } else {
    desc.value = null;
  }

  if(props.change){
    props.change(val);
  }
}

const label = (item) => {
  return item[ props.labelKey || parseBool(props.vkEnqual) ? (props.valueKey || 'code') : 'codeDesc'];
}

const value = (item) => {
  return item[ props.valueKey || 'code' ];
}

const getSource = () => {
  return props.source || proxy.$commonCodeStore.getAll[ type()];
}

const selectObject = {

  BANK_CODE: { style: 'width:100px' },
  CLIENT_TYPE_CODE: { style: 'width:180px' },
  AE_CODE: { style: 'width:100px' },
  SERVICE_PLAN_CODE: { style: 'width:180px' },
  ACCOUNT_NATURE_CODE: { style: 'width:180px' },
  ENTRY_TYPE_CODE: { style: 'width:180px' },
  UNIQUE_REF_TYPE_CODE: { style: 'width:180px' },
  OUTGOING_CHANNEL_CODE: { style: 'width:180px' },
  OUTGOING_CHANNEL_PURPOSE_CODE: { style: 'width:180px' },
  LANGUAGE: { style: 'width:180px' },
  SETTLE_METHOD_CODE: { style: 'width:180px' },
  CASH_SETTLE_METHOD_CODE: { style: 'width:180px' },
  BANK_ACC_TYPE_CODE: { style: 'width:180px' },
  BANK_ACC_PURPOSE_CODE: { style: 'width:180px' },
  SHADOW_CASH_ACC_PURPOSE_CODE: { style: 'width:180px' },
  CUST_ACC_INCOMING_CHANNEL_CODE: { style: 'width:180px' },
  CLR_AGENT_INCOMING_CHANNEL_CODE: { style: 'width:180px' },
  CLR_AGENT_INCOMING_CHANNEL_PURPOSE_CODE: { style: 'width:180px' },
  CASH_SETTLE_METHOD_SI_CODE: { style: 'width:180px' },
  CA_PAY_METHOD_CODE: { style: 'width:180px' },
  CASH_STOCK_SETTLE_METHOD_CODE: { style: 'width:180px' },
  EX_BOARD_HOLIDAY_TYPE_CODE: { style: 'width:180px' },
  CTRY_HOLIDAY_TYPE_CODE: { style: 'width:180px' },
  SETTLE_INSTR_INCOMING_CHANNEL_CODE: { style: 'width:180px' },
  STOCK_RECON_TYPE_CODE: { style: 'width:180px' },
  IDENTIFIER_TYPE_CODE: { style: 'width:180px' },
  SWIFT_TYPE_CODE: { style: 'width:180px' },
  PRODUCT_CODE: { style: 'width:180px' },
  PENALTY_TYPE: { style: 'width:180px' },
  CCY_CAL_METHOD: { style: 'width:180px' },
  ITF_FILE_TYPE: { style: 'width:180px' },
  CUST_ACC_INCOMING_CHANNEL_PURPOSE_CODE: { style: 'width:180px' },
  RECON_PROCESS_STATUS: { style: 'width:180px' },
  RECON_CHANNEL: { style: 'width:180px' },
  RECON_STATUS: { style: 'width:180px' },
  STATUS: { style: 'width:180px' },
  RECORD_STATUS: { style: 'width:180px' },
  FREQUENCY: { style: 'width:180px' },
  JOB_TYPE: { style: 'width:180px' },
  FUNCTION_TYPE: { style: 'width:180px' },
  WEEKLY_DAY: { style: 'width:180px' },
  CLIENT_GROUP: { style: 'width:180px' },
  FUND_MANAGER: { style: 'width:180px' },
  COUNTRY_AREA: { style: 'width:180px' },
  TXN_DESC: { style: 'width:180px' },
  TXN_TYPE: { style: 'width:180px' },
  BOC_MN_CODE: { style: 'width:180px' },
  MKCK_ACTION: { style: 'width:180px' },
  FX_TYPE: { style: 'width:180px' },
  DR_CR: { style: 'width:180px' },
  JOB_STATUS: { style: 'width:180px' },
  ACCOUNT_STATUS: { style: 'width:180px' },
  DOC_TYPE: { style: 'width:180px' },
  SCHEDULER_CHANNEL: { style: 'width:180px' },
  COM_YN: { style: 'width:100px' },
  ACCOUNT_GROUP: { style: 'width:180px' },
  ACCOUNTS: { style: 'width:180px' },
  REJECT_REASON: { style: 'width:180px' },
  PROCESSING_STATUS: { style: 'width:180px' },
  DASH_RECORD_STATUS: { style: 'width:180px' },
  MAIL_REGION: { style: 'width:180px' },
  PEND_QUE_STS: { style: 'width:180px' },
  TXN_TYPE_DESC: { style: 'width:180px' },
  TXN_FUNC_TYPE: { style: 'width:180px' },
  FILE_PROCESS_STATUS: { style: 'width:180px' },
  FX_DATA_SOURCE: { style: 'width:180px' },
  CODE_TYPE: { style:"width:600px" },

}

</script>

<style>
.select-input div {
  padding: 0;
  margin: 0;
  border:none;
  outline: none;
  box-shadow: none !important;
  background: none !important;
}

.select-input div:hover {
  box-shadow: none !important;
  background: none !important;
}

.is-disabled .select-class {
  background-color: var(--ep-disabled-bg-color);
  cursor: not-allowed;
  border: 0px;
  outline: 0px;
  box-shadow: none;
  -webkit-box-shadow:none;
}

.select-class {
  width:220px;
  height: 24px;
  padding-left: 6px;
  opacity: 1;
  color: var(--ep-text-color-regular);
  border:none;
  outline: none;
  border-radius: var(--ep-border-radius-base); 
  box-shadow: var(--ep-border-color) 0px 0px 0px 1px inset;
}

.select-class:focus {
  box-shadow:  0 0 0 1px var(--ep-input-focus-border-color) inset;
}

.is-error .select-class {  
  box-shadow: 0px 0px 0px 1px var(--ep-color-danger) inset;
}

.multiSelect .ep-select__selection {
  flex-wrap: unset;
}
</style>