<template>
  <div style="margin:10px 0" class="grid-continer">
    <el-form :validateOnRuleChange="false" ref="formRef" :inline="true" :model="formInline" class="demo-form-inline"
      :rules="props.rules" :showMessage="false">
      <slot name="searchPanel" :form="formInline"></slot>
      <ElFormItemProxy v-if="parseBool(props.isShowSearch)">
        <el-button @click="onReset(formRef)">Clear</el-button>
        <el-button type="primary" @click="onSearch">Query</el-button>
      </ElFormItemProxy>
    </el-form>
    <el-row v-if="!parseBool(props.isHideOrder)" :gutter="24" class="demo-form-inline"
      style="margin: 0px;padding-block: 5px;">
      <el-col :span="24">
        <div style="width: 100%;display: table;border-bottom: 2px solid #e6e6e6;min-height: 26px;">
          <div style="color: lightgray; font-weight: bold;display: table-cell;width: 80px;align-content: center;"> Order
            By: </div>
          <el-space style="color: lightgray; width: calc(100% - 80px); padding-bottom: 2px;">
            <el-tag v-for="(tag, index) in orderByDesc" :key="tag.code" closable type="info" @close="deleteOrder(tag)">
              {{ tag.name + " " + tag.order }}
            </el-tag> </el-space>
        </div>
      </el-col>
    </el-row>
    <el-form>
      <el-pagination v-if="!parseBool(props.hidePagination)" v-model:current-page="currentPage" :disabled="editing"
        v-model:page-size="pageSize" :page-sizes="[10, 20, 30, 40]" layout="sizes, jumper, prev, pager, next, ->, slot"
        v-model:total="total" @current-change="handleChange" @size-change="handleChange"
        style="background-color: lightgrey;padding-inline: 10px;">
        Total {{ total }} records
      </el-pagination>
    </el-form>
    <el-table :data="tableData" table-layout="auto" @row-dblclick="handleDbClick" @row-click="handleClick"
      @sort-change="handleSort" ref="tableRef" :cell-style="cellStyle" :border="true" class="grid-table"
      scrollbar-always-on @current-change="handleCurrentChange" @select-all="selectAll" class-name="multiple-table"
      :row-class-name="tableRowClassName" :header-cell-style="headerCellStyle"
      :header-cell-class-name="(params: any) => { setHeaderClass(params) }">
      <slot name="tableColumnFront"></slot>
      <el-table-column v-for="item in columns" :sortable="item.sorter == false ? false : 'custom'" :prop="item.name"
        :align="item.align ? item.align : (item.dataType?.toUpperCase() == 'NUM' || item.dataType?.toUpperCase() == 'THOUS' ? 'right' : 'left')"
        :label="$t(item.title)" header-align="center" :width="item.width ? item.width : '*'"
        :label-class-name="item.labelClassName">
        <template #default="scope" v-if="item.fn">
          {{ item.fn ? item.fn(scope.row, scope.row[item.name]) : "" }}
        </template>
        <template #default="scope" v-if="item.dataType?.toUpperCase() == 'THOUS'">
          {{ thousFormat(scope.row[item.name], item.scale) }}
        </template>
        <template #default="scope" v-if="item.showRecoredStatus">
            {{ getCommonDesc('RECORD_STATUS', scope.row.recordStatus) }}
            <span v-if="scope.row.recordStatus && scope.row.recordStatus!=='A' ">
            for  {{ getCommonDesc('MKCK_ACTION', scope.row.mkckAction) }}
            </span>
        </template>

      </el-table-column>
      <slot name="tableColumnAfter"></slot>
      <el-table-column type="index" class-name="data-grid-selection-index-cell" width="1px" />
      <el-table-column v-if="props.isMultiple" :class-name="isHideCheckBox ? 'data-grid-selection-cell' : ''"
        type="selection" :width="isHideCheckBox ? 1 : 55" align="center" :selectable="props.selectable">
        <template #default="scope">
          <label v-if="!props.selectable || props.selectable(scope.row)" class="ep-checkbox">
            <span class="ep-checkbox__input">
              <span class="multiple-checkbox__inner">
                <Select class="multiple-checked" />
              </span>
            </span>
          </label>
        </template>
      </el-table-column>
    </el-table>

    <el-form>
      <el-pagination v-if="!parseBool(props.hidePagination)" v-model:current-page="currentPage" :disabled="editing"
        v-model:page-size="pageSize" :page-sizes="[10, 20, 30, 40]" layout="sizes, jumper, prev, pager, next, ->, slot"
        v-model:total="total" @current-change="handleChange" @size-change="handleChange"
        style="background-color: lightgrey;padding-inline: 10px;">
        Total {{ total }} records
      </el-pagination>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, getCurrentInstance, watch } from 'vue'
import type { FormInstance } from 'element-plus'
import { ElMessage } from 'element-plus';
import {
  Edit,
  Delete,
  Select
} from '@element-plus/icons-vue'
import { useRouter } from 'vue-router';
import { useCookies } from "vue3-cookies";
import { highlight, thousFormat, selectedRow, clearSelectedCache, selectedAllRows, validSearchInputValue, validDateItemValue, getCommonDesc } from '~/util/Function.js';
import { showValidateMsg } from '~/util/Validators.js';
import { getTimeZone } from "~/util/DateUtils";

const router = useRouter()
const { cookies } = useCookies()
const { proxy } = getCurrentInstance()
const formRef = ref<FormInstance>()
const props = defineProps(['url', 'params', 'editRow', 'deleteRow', 'sortProp', 'lazy', 'isSelectFirst', 'searchParams',
  'onClick', 'onDbClick', 'hidePagination', 'isHideOrder', 'isShowSearch', 'columns', 'beforeSearch', 'afterSearch', 'onReset', 'cellStyle',
  'selectable', 'isMultiple', 'isHideCheckBox', 'rules', 'fieldsDtl', 'beforeChangePage', 'isManual', 'beforeClick', 'headerCellStyle', 'isSelectFirst'
]);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const currentRow = ref({});
const tableRef = ref();
const tableData = ref([]);
const param = ref({});
const orderBy = ref("");
const orderByDesc = ref([]);
const sortField = ref({});
const sortFieldName = ref({});
const columns = ref(props.columns);

const formInline = reactive(props.searchParams || {});
const lazy = ref(props.lazy);
const oldParams = ref({});
const selectedRecord = ref({});
const lastSelected = ref(0);
const refreshSts = ref(true);

const editing = ref(false);

const setEditing = (flag: boolean) => {
  editing.value = flag;
}

watch(() => props.params, (newVal) => {
  if (!refreshSts.value) {
    refreshSts.value = true;
    return;
  }
  console.log("props.params change reload", "url:" + props.url, props.params, oldParams.value);
  // The following code is to solve the problem of resending a request due to changes in the param when a page has multiple Grids
  let isChange = true;
  if (Object.keys(oldParams.value || {}).length > 0) {
    isChange = false;
    let keys = Object.keys(props.params || {});
    for (let i = 0; i < keys.length; i++) {
      let key = keys[i];
      let p = props.params[key];
      let op = oldParams.value[key];
      console.log("key:" + key, "p:" + p, "op:" + op)
      if (p != op) {
        isChange = true;
        break;
      }
    }
  }
  if (isChange) {
    defalutSel();
  }
});

const defalutSel = () => {
  onSearch();
}

const loadData = async () => {

  let allParams = false;

  // The following code is used to solve the problem of requesting abnormal results from the backend when the value of params is empty
  let keys = Object.keys(props.params || {});
  for (let i = 0; i < keys.length; i++) {
    let e = props.params[keys[i]];
    if (e) {
      allParams = true;
      break;
    }
  }

  console.log("url:" + props.url, props.params, param.value, "loadData:" + allParams)
  if (!allParams) {
    return;
  }
  oldParams.value = props.params;
  let params = {
    data: {
      ...param.value,
      ...props.params,
      pageNumber: currentPage.value,
      pageSize: pageSize.value == null ? 15 : pageSize.value,
      orderBy: orderBy.value,
    },
    header: {timezone: getTimeZone(), lang:"en_US"}
  };

  const msg = await proxy.$axios.post(props.url, params);

  if (msg?.header.code === '000000') {
    selectAll([]);
    total.value = msg.data.totalCount;
    currentPage.value = msg.data.pageNumber;
    tableData.value = msg.data.items;
    setTimeout(() => {
      afterSearch(param.value, tableData.value);
    }, 300);
    if (props.isSelectFirst){
      selectFirstRecord();
    }
  }

  // if (msg?.success) {
  //   selectAll([]);
  //   tableData.value = {};
  //   total.value = msg.data.total;
  //   currentPage.value = msg.data.page;
  //   tableData.value = msg.data.items;
  //   setTimeout(() => {
  //     afterSearch(param.value, tableData.value);
  //   }, 300);
  //   selectFirstRecord();
  // }
}

const afterSearch = (params, data) => {
  if (props.afterSearch) {
    props.afterSearch(params, data);
  }
}
const selectFirstRecord = () => {
  if (!selectFirstRecord.flag) {
    setTimeout(() => {
      if (tableRef.value.data?.length > 0) {
        //handleClick(currentRow.value.oid ? currentRow.value : tableRef.value.data[0]);
        // handleClick(tableRef.value.data[0]);
        tableRef.value.$el.querySelector("tbody tr").click();
        props.isManual && mousedown({
          target: tableRef.value.$el.querySelector("tbody tr"),
        });
      }
    }, 300);
    selectFirstRecord.flag = true;
  }
}

const baseLoad = () => {
  if (lazy.value == 'false') {
    lazy.value = true;
  } else {
    loadData();
  }
}
const load = async () => {
  await onSearch();
}

const onSearch = async () => {
  selectFirstRecord.flag = false;
  param.value = formInline;
  let res = await validSearchInputValue("div.grid-continer input[searchtype]")
  res = validDateItemValue("div.grid-continer .search-date-error input[alt]") == false ? false : res;
  if (res) {
    res = await beforeSearch(param.value, props.params);
    if (res) {
      res = await formRef.value.validate((valid, fields) => {
        if (!valid) {
          showValidateMsg(props.fieldsDtl, fields);
        }
      });
    }
    if (res) {
      baseLoad();
    }
  }
}
const onReset = (ref) => {
  orderBy.value = "";
  orderByDesc.value = [];
  sortField.value = {};
  sortFieldName.value = {};
  if (ref) {
    ref.resetFields();
    tableRef.value.clearSort();
  }

  if (props.onReset) {
    props.onReset();
  }
}

const pageObj = reactive({
  pageSize: pageSize.value,
  currentPage: currentPage.value,
});
const beforeChangePage = async () => {
  if (props.beforeChangePage) {
    let ret = await props.beforeChangePage();
    if (ret?.isChangePage === false) {
      //Prevent page change
      pageSize.value = pageObj.pageSize;
      currentPage.value = pageObj.currentPage;
      return false;
    } else if (ret?.lsLoadData === false) {
      //Change page, but not auto load data
      return false;
    }
  }
  return true;
}
const handleChange = async (newPage) => {
  if (await beforeChangePage()) {
    load();
  }
  pageObj.pageSize = pageSize.value;
  pageObj.currentPage = currentPage.value;
}
const handleSort = (obj) => {
  let sts = {
    ...sortField.value
  };
  let stsName = {
    ...sortFieldName.value
  };
  if (sts[obj.prop] && !obj.order) {
    delete sts[obj.prop];
    delete stsName[obj.prop];
  } else {
    sts[obj.prop] = obj.order;
    stsName[obj.prop] = obj.column.label;
  }
  sortField.value = sts;
  sortFieldName.value = stsName;
  changeSort(sts, stsName);
  load();
}

const beforeSearch = async (search, params) => {
  let ret = true;

  if (props.beforeSearch) {
    ret = await props.beforeSearch(search, params);
    if (ret == false) {
      return false;
    } else {
      return true;
    }
  }
  return ret;
}

const handleDbClick = (row) => {
  if (props.onDbClick) {
    props.onDbClick(row);
  }
  currentRow.value = row;
}

const mousedown = (event: Event) => {
  selectedRowByDown("oid", event, props.selectable, props.isMultiple);
}

const mouseup = (event: Event) => {
  selectedRowByUp("oid", event, props.selectable, props.isMultiple);
}

const isMouseDown = ref(false);

const selectedRowByDown = (propsOid, event, getSelectable, isMultiple) => {
  isMouseDown.value = false;
  let isClickCheckbox = event.target.className == 'multiple-checkbox__inner';
  if (!isClickCheckbox && event.target.closest("td.ep-table-column--selection")) {
    isClickCheckbox = true;
  }
  let rowIndex = event.target.closest("tr.ep-table__row").querySelector("td.data-grid-selection-index-cell>div>div").innerText;
  let row = tableRef.value.data[rowIndex - 1];
  let oid = row[propsOid];
  let selectable = true;
  if (getSelectable) {
    selectable = getSelectable(row);
  }
  if (isMultiple && event.shiftKey) {
    tableRef.value.clearSelection();
    selectedRecord.value = {};
    if (lastSelected.value == 0) {
      lastSelected.value = rowIndex;
    }
    let start = parseInt(lastSelected.value), end = parseInt(rowIndex);
    if (start > end) {
      start = rowIndex;
      end = lastSelected.value;
    }
    let r;
    for (let j = start - 1; j < end; j++) {
      selectable = true;
      r = tableRef.value.data[j];
      if (getSelectable) {
        selectable = getSelectable(r);
      }
      if (selectable) {
        oid = r[propsOid];
        selectedRecord.value[oid] = r;
        tableRef.value.toggleRowSelection(r, true);
      }
    }
  } else if (isMultiple && (event.ctrlKey || isClickCheckbox)) {
    if (selectable) {
      if (selectedRecord.value[oid]) {
        isMouseDown.value = true;
      } else {
        selectedRecord.value[oid] = row;
        tableRef.value.toggleRowSelection(row, true);
      }
    }
  } else {
    if (selectedRecord.value[oid]) {
      isMouseDown.value = true;
    } else {
      tableRef.value.clearSelection();
      selectedRecord.value = {};
      if (selectable) {
        selectedRecord.value[oid] = row;
        tableRef.value.toggleRowSelection(row, true);
      }
    }
  }
  lastSelected.value = rowIndex;
}

const selectedRowByUp = (propsOid, event, getSelectable, isMultiple) => {

  let isClickCheckbox = event.target.className == 'multiple-checkbox__inner';
  if (!isClickCheckbox && event.target.closest("td.ep-table-column--selection")) {
    isClickCheckbox = true;
  }
  let rowIndex = event.target.closest("tr.ep-table__row").querySelector("td.data-grid-selection-index-cell>div>div").innerText;
  let row = tableRef.value.data[rowIndex - 1];
  let oid = row[propsOid];
  let selectable = true;
  if (getSelectable) {
    selectable = getSelectable(row);
  }
  if (isMultiple && event.shiftKey) {

  } else if (isMultiple && (event.ctrlKey || isClickCheckbox)) {
    if (selectable) {
      if (isMouseDown.value && selectedRecord.value[oid]) {
        delete selectedRecord.value[oid];
        tableRef.value.toggleRowSelection(row, false);
      }
    }
  } else {
    if (selectedRecord.value[oid]) {
      tableRef.value.clearSelection();
      selectedRecord.value = {};
      if (selectable) {
        selectedRecord.value[oid] = row;
        tableRef.value.toggleRowSelection(row, true);
      }
    }
  }
  lastSelected.value = rowIndex;
}

const handleClick = async (row: any, column: any, event: Event) => {
  if (props.beforeClick && !props.beforeClick(row, column, event)) {
    event.stopPropagation();
    return;
  }
  !props.isManual && event && selectedRow("oid", selectedRecord, tableRef, lastSelected, row, column, event, props.selectable, props.isMultiple);
  if (props.onClick) {
    let ret = await props.onClick(row);
    if (ret === false) {
      //no change row when edit row validation failed
      tableRef.value!.setCurrentRow(currentRow.value);
      return;
    }
  }
  currentRow.value = row;
  tableRef.value!.setCurrentRow(row);
}

const selectAll = (selection) => {
  if (selection && selection.length > 0) {
    selectedAllRows("oid", selection, selectedRecord, tableRef, lastSelected, props.selectable);
  } else {
    clearSelectedCache(selectedRecord, tableRef, lastSelected);
    currentRow.value = {};
    tableRef.value!.setCurrentRow({});
  }

}

const tableRowClassName = ({ row }) => {
  let selectedClass = "";
  let oid = row["oid"];
  if (selectedRecord.value[oid]) {
    selectedClass = ' selected-row';
  }
  return selectedClass;
}

const setHeaderClass = (params: any) => {
  params.column.order = sortField.value[params.column.property];
}

const changeSort = (sts, stsName) => {
  if (Object.keys(sts).length == 0) {
    orderBy.value = "";
    orderByDesc.value = [];
  } else {
    let obv = "";
    let obvNames = [];
    for (let key in sts) {
      obvNames.push({
        name: stsName[key],
        order: sts[key],
        code: key
      });
      if (props.sortProp && props.sortProp[key]) {
        let o = sts[key].charAt(0).toUpperCase();
        let d = "";
        for (let i = 0; i < props.sortProp[key].length; i++) {
          let ele = props.sortProp[key][i];
          d += ";" + ele + "-" + o;
        }
        obv += ";" + d.substring(1);
      } else {
        obv += ";" + key + "-" + sts[key].charAt(0).toUpperCase();
      }
    }
    orderBy.value = obv.substring(1);
    orderByDesc.value = obvNames;
  }
}

const deleteOrder = (tag) => {
  let sts = {
    ...sortField.value
  };
  let stsName = {
    ...sortFieldName.value
  };
  delete sts[tag.code];
  delete stsName[tag.code];
  sortField.value = sts;
  sortFieldName.value = stsName;
  changeSort(sts, stsName);
  load();
}

const parseBool = (val) => {
  return Boolean(String(val) == 'false' ? false : String(val) == '' ? true : val);
}

const cellStyle = (row, column, rowIndex, columnIndex) => {
  if (props.cellStyle) {
    let style = props.cellStyle(row, column, rowIndex, columnIndex);
    if (style) {
      return style;
    }
  }
  return highlight(row);
}

const headerCellStyle = ({ row, column, rowIndex, columnIndex }) => {
  if (props.headerCellStyle) {
    let style = props.headerCellStyle(row, column, rowIndex, columnIndex);
    if (style) {
      return style;
    }
  }
  return '';
}

const handleCurrentChange = (val: any | undefined) => {
  //currentRow.value = val
}

const setCurrentRow = (idx, click) => {
  idx = idx || 0;
  //tableRef.value.setCurrentRow(row);
  //handleClick(row);
  tableRef.value.$el.querySelectorAll("tbody tr")[idx]?.click();
}

const init = () => {
  onReset(formRef.value);
}

const setAutoRefreshSts = (sts) => {
  refreshSts.value = sts;
}

defineExpose({
  load,
  tableRef,
  tableData,
  total,
  setCurrentRow,
  currentPage,
  pageSize,
  orderBy,
  currentRow,
  selectAll,
  selectedRecord,
  init,
  setAutoRefreshSts,
  mousedown,
  mouseup,
  setEditing,
})
</script>

<style>
.demo-form-inline .el-input {
  --el-input-width: 220px;
}

.demo-form-inline {}

.demo-form-inline .el-select {
  --el-select-width: 220px;
}

.grid-table .ep-table__body {
  padding: 0;
}

.cell-color-deep-blue {
  color: #16A0A0;
}
</style>