<template>
    <el-space>
        <el-text class="mx-1" style="width: 200px;font-weight: bold;">
            <slot name="name"></slot>
        </el-text>
        <div>
            <el-input v-model="leftInput" maxlength="150" style="width: 600px;" class="text-none" :disabled="props.disabled">
                <template #append>
                    <el-button :icon="Search" @click="searchLeft(leftInput)" :disabled="props.disabled"/>
                </template>
            </el-input>
            <el-table border @current-change="handleLeftCurrentChange"
                :header-row-style="getHeaderStyle" :data="Object.values(showLeftData)"
                @row-click="clickLeftRow" ref="leftGrid" :row-class-name="leftGridRowClassName"
                class-name="multiple-table"
                style="width: 100%;height: 400px;border: 1px;">
                <el-table-column type="index" class-name="data-grid-selection-index-cell" width="1px" />
                <el-table-column type="selection" class-name="data-grid-selection-cell" width="1px" />
                <slot name="leftCols"></slot>
            </el-table>
        </div>
        <el-space direction="vertical">
            <el-button :disabled="props.disabled" :icon="ArrowRight" @click="leftToRight" />
            <el-button :disabled="props.disabled" :icon="DArrowRight" @click="leftAllToRight" />
            <el-button :disabled="props.disabled" :icon="ArrowLeft" @click="rightToLeft" />
            <el-button :disabled="props.disabled" :icon="DArrowLeft" @click="rightAllToLeft" />
        </el-space>
        <div>
            <el-input v-model="rightInput" maxlength="150" style="width: 600px;" class="text-none" :disabled="props.disabled">
                <template #append>
                    <el-button :icon="Search" @click="searchRight(rightInput)" :disabled="props.disabled"/>
                </template>
            </el-input>
            <el-table border @current-change="handleRightCurrentChange"
                :header-row-style="getHeaderStyle" :data="Object.values(showRightData)"
                @row-click="clickRightRow" ref="rightGrid" :row-class-name="rightGridRowClassName"
                class-name="multiple-table" :cellStyle="(row)=>highlight(row)"
                style="width: 100%;height: 400px;border: 1px;">
                <el-table-column type="index" class-name="data-grid-selection-index-cell" width="1px" />
                <el-table-column type="selection" class-name="data-grid-selection-cell" width="1px" />
                <slot name="rightCols"></slot>
            </el-table>
        </div>
    </el-space>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import {
    ArrowRight,
    DArrowRight,
    ArrowLeft,
    DArrowLeft,
    Search
} from '@element-plus/icons-vue';
import { selectedRow, clearSelectedCache, highlight } from '~/util/Function.js';
const props = defineProps(['leftData', 'rightData', 'leftOid', 'rightOid', 'disabled', 'showName']);
const modifyRecords = ref({});
const currentLeftData = ref({});
const currentRightData = ref({});
const showLeftData = ref({});
const showRightData = ref({});
const rightDataFormDB = ref({});
const selectedLeftRecord = ref({});
const selectedRightRecord = ref({});
const leftInput = ref("");
const rightInput = ref("");
const leftLastSelected = ref(0);
const rightLastSelected = ref(0);
const leftGrid = ref();
const rightGrid = ref();

const leftGridRowClassName = ({
  row,
  rowIndex,
}) => {
    let oid = row[props.leftOid];
  if (selectedLeftRecord.value[oid]) {
    return 'selected-row'
  }
  return ''
}

const rightGridRowClassName = ({
  row,
  rowIndex,
}) => {
    let oid = row[props.rightOid];
  if (selectedRightRecord.value[oid]) {
    return 'selected-row'
  }
  return ''
}

const clickLeftRow = (row: any, column: any, event: Event) => {
    selectedRow(props.leftOid, selectedLeftRecord, leftGrid, leftLastSelected, row, column, event, null, true);
}

const clickRightRow = (row: any, column: any, event: Event) => {
    selectedRow(props.rightOid, selectedRightRecord, rightGrid, rightLastSelected, row, column, event, null, true);
}

const handleLeftCurrentChange = (val) => {
    // selectedLeftRecord.value = val
}
const handleRightCurrentChange = (val) => {
    // selectedRightRecord.value = val
}

const addItem = (selectedRec) => {
    let oid = selectedRec[props.leftOid];
    if (currentRightData.value[oid]) {
        currentRightData.value[oid] = rightDataFormDB.value[oid];
        showRightData.value[oid] = rightDataFormDB.value[oid];
    } else {
        currentRightData.value[oid] = {
            ...currentLeftData.value[oid],
            recordStatus: 'PD',
            mkckAction: 'C',
        };
        showRightData.value[oid] = {
            ...currentLeftData.value[oid],
            recordStatus: 'PD',
            mkckAction: 'C',
        };
        modifyRecords.value[oid] = {
            ...currentLeftData.value[oid],
            recordStatus: 'PD',
            mkckAction: 'C',
        };
    }
    delete currentLeftData.value[oid];
    delete showLeftData.value[oid];
}
const leftAllToRight = () => {
    for (let key in showLeftData.value) {
        addItem(showLeftData.value[key]);
    }
    currentLeftData.value = { ...currentLeftData.value };
    currentRightData.value = { ...currentRightData.value };
    modifyRecords.value = { ...modifyRecords.value };
    showLeftData.value = { ...showLeftData.value };
    showRightData.value = { ...showRightData.value };
    clearSelectedCache(selectedLeftRecord, leftGrid, leftLastSelected);
    if (props.leftData)  props.leftData( currentLeftData.value);
    if (props.rightData) props.rightData( currentRightData.value );
}
const leftToRight = () => {
    let isChange = false;
    for(let key in selectedLeftRecord.value){
        let selectedRec = selectedLeftRecord.value[key];
        if (selectedRec && Object.keys(selectedRec).length > 0) {
            isChange = true;
            addItem(selectedRec);
        }
    }
    if(isChange) {
        currentRightData.value = { ...currentRightData.value };
        currentLeftData.value = { ...currentLeftData.value };
        modifyRecords.value = { ...modifyRecords.value };
        showLeftData.value = { ...showLeftData.value };
        showRightData.value = { ...showRightData.value };
        clearSelectedCache(selectedLeftRecord, leftGrid, leftLastSelected);
        if (props.leftData)  props.leftData( currentLeftData.value);
        if (props.rightData) props.rightData( currentRightData.value );
    }
}
const rightAllToLeft = () => {
    for (let key in showRightData.value) {
        removeItem(showRightData.value[key]);
    }
    currentRightData.value = { ...currentRightData.value };
    currentLeftData.value = { ...currentLeftData.value };
    modifyRecords.value = { ...modifyRecords.value };
    showLeftData.value = { ...showLeftData.value };
    showRightData.value = { ...showRightData.value };
    clearSelectedCache(selectedRightRecord, rightGrid, rightLastSelected);
    if (props.leftData)  props.leftData( currentLeftData.value);
    if (props.rightData) props.rightData( currentRightData.value );
}
const rightToLeft = () => {
    let isChange = false;
    for(let key in selectedRightRecord.value) {
        let selectedRec = selectedRightRecord.value[key];
        if (selectedRec && Object.keys(selectedRec).length > 0) {
            isChange = true;
            removeItem(selectedRec);
        }
    }
    if(isChange) {
        currentRightData.value = { ...currentRightData.value };
        currentLeftData.value = { ...currentLeftData.value };
        modifyRecords.value = { ...modifyRecords.value };
        showLeftData.value = { ...showLeftData.value };
        showRightData.value = { ...showRightData.value };
        clearSelectedCache(selectedRightRecord, rightGrid, rightLastSelected);
        if (props.leftData)  props.leftData( currentLeftData.value);
        if (props.rightData) props.rightData( currentRightData.value );
    }
}
const removeItem = (selectedRec) => {
    let oid = selectedRec[props.leftOid];
    if (currentRightData.value[oid].recordStatus == "PD" && currentRightData.value[oid].mkckAction == "D") {
        return;
    }
    currentLeftData.value[oid] = {
        ...currentRightData.value[oid],
        recordStatus: 'A',
    };
    showLeftData.value[oid] = {
        ...currentRightData.value[oid],
        recordStatus: 'A',
    };
    if (currentRightData.value[oid][props.rightOid] && currentRightData.value[oid].recordStatus == "A") {
        currentRightData.value[oid] = {
            ...currentRightData.value[oid],
            recordStatus: 'PD',
            mkckAction: 'D',
        };
        showRightData.value[oid] = {
            ...currentRightData.value[oid],
            recordStatus: 'PD',
            mkckAction: 'D',
        };
        modifyRecords.value[oid] = {
            ...rightDataFormDB.value[oid],
            recordStatus: 'PD',
            mkckAction: 'D',
        };
    } else {
        delete currentRightData.value[oid];
        delete showRightData.value[oid];
        if (rightDataFormDB.value[oid]) {
            modifyRecords.value[oid] = {
                ...rightDataFormDB.value[oid],
                recordStatus: 'PD',
                mkckAction: 'D',
            };
        } else {
            delete modifyRecords.value[oid];
        }
    }
}
const searchLeft = (value) => {
    let obj = { ...currentLeftData.value };
    fuzzyQuery(obj, value);
    showLeftData.value = { ...obj };
}
const fuzzyQuery = (obj :object, value :string) => {
    if(!value){
        return ;
    }
    let val = value.toLowerCase();
    let showVal,reg;
    if(val.indexOf("%")<0) {
        for (let key in obj) {
            showVal = props.showName(obj[key]).toLowerCase();
            if (val != showVal) {
                delete obj[key];
            }
        }
    } else {
        val = val.replaceAll('%','.*');
        reg = new RegExp("^"+val+"$");
        for (let key in obj) {
            showVal = props.showName(obj[key]).toLowerCase();
            if (!reg.test(showVal)) {
                delete obj[key];
            }
        }
    }
}
const searchRight = (value) => {
    let obj = { ...currentRightData.value };
    fuzzyQuery(obj, value);
    showRightData.value = { ...obj };
}

const getHeaderStyle = (data) => {
    if (data.rowIndex == 0) {
        return { textAlignLast: "center" }
    }
    return {};
}

const loadData = (num, data) => {
    let obj = {};
    for (let j = 0; j < data.length; j++) {
        const ele = data[j];
        obj[ele[props.leftOid]] = ele;
    }
    if (num == 1) {
        currentLeftData.value = { ...obj };
        showLeftData.value = { ...obj };
    } else if (num == 2) {
        currentRightData.value = { ...obj };
        rightDataFormDB.value = { ...obj };
        showRightData.value = { ...obj };
    }
    modifyRecords.value = {};
}
const getData = () => {
    return {
        currentLeftData: { ...currentLeftData.value },
        currentRightData: { ...currentRightData.value },
        showLeftData: { ...showLeftData.value },
        showRightData: { ...showRightData.value },
        rightDataFormDB: { ...rightDataFormDB.value },
        modifyRecords: { ...modifyRecords.value },
    }
}

const setData = (obj) => {
    currentLeftData.value = { ...obj.currentLeftData };
    currentRightData.value = { ...obj.currentRightData };
    showLeftData.value = { ...obj.showLeftData };
    showRightData.value = { ...obj.showRightData };
    rightDataFormDB.value = { ...obj.rightDataFormDB };
    modifyRecords.value = { ...obj.modifyRecords }
}

defineExpose({
    modifyRecords,
    loadData,
    getData,
    setData,
})
</script>
