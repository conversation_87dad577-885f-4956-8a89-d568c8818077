<template>
    <el-row style="width: 100%; margin: 0; padding: 0;">
        <el-table :data="showData" table-layout="auto" @row-dblclick="handleDbClick" @row-click="handleClick"
            ref="gridRef" :cell-style="cellStyle" :border="true" scrollbar-always-on
            :style="'width: ' + ($attrs.tableWidth?$attrs.tableWidth:'calc(100% - 80px)') + '; margin: 0; padding: 0; float: left;' + $attrs.tableStyle"
            @current-change="handleCurrentChange" class-name="multiple-table" :row-class-name="tableRowClassName">
            <slot name="columns"></slot>
        </el-table>
        <el-space style="margin-left: 10px; float: left;" direction="vertical" v-if="!readonly">
            <el-button :icon="Plus" @click="addRecord" />
            <el-button :icon="Minus" @click="deleteRecord" />
        </el-space>
        <el-card :style="'width: ' + ($attrs.tableWidth?$attrs.tableWidth:'calc(100% - 80px)') + ';margin-top: 10px;'" v-show="isShowDetail && !readonly">
            <el-form :validateOnRuleChange="false" ref="ruleFormRef" :rules="props.rules" :model="props.form"
                label-position="left" :disabled="$attrs.disabled || isDeleteRecord">
                <slot name="form"></slot>
                <div v-if="!$attrs.disabled && !isDeleteRecord" style="justify-content: right;display: flex;">
                    <el-button @click="cancelGrid" class="ep-button-custom">Cancel</el-button>
                    <el-button type="primary" @click="saveGrid" class="ep-button-custom">OK</el-button>
                </div>
            </el-form>
        </el-card>
    </el-row>
</template>

<script lang="ts" setup>
import { onUnmounted, ref, watch, } from 'vue'
import type { FormInstance } from 'element-plus'
import {
    Plus,
    Minus,
} from '@element-plus/icons-vue'
import { showValidateMsg } from '~/util/Validators.js';
import { randomHashCode, highlight, isGridModified, showErrorMsg } from '~/util/Function';
import { ElMessageBox} from 'element-plus';

const isShowDetail = ref(false);
const props = defineProps(['data', 'form', 'details', 'oid', 'funcMod', 'editRow', 'deleteRow', 'lazy', 'isSelectFirst', 'clickOk',
    'onClick', 'onDbClick', 'modelValue', 'isShowSearch', 'columns', 'beforeSearch', 'afterSearch', 'onReset', 'cellStyle',
    'selectable', 'isMultiple', 'isHideCheckBox', 'rules', 'fieldsDtl', 'unUpdate', 'isManual', 'beforeClick', 'beforeSave', 'uniqueKey','readonly']);

const ruleFormRef = ref<FormInstance>();
const gridRef = ref();
const showData = ref([]);
const currentRow = ref({});
const midifyData = ref({});
const isDeleteRecord = ref(false);
const isEditRecord = ref(false);
const lastRow = ref(null);

const emit = defineEmits(['showData','add-record','save','row-click','on-db-click']);

watch(()=>props.modelValue, (newVal)=>{
    if (newVal&&showData.value!=[]) {
        showData.value = [...newVal];
    }
});

watch(() => showData.value, (newVal)=>{
    if (newVal) {
        emit('showData', newVal);
    }
}, { deep: true });

onUnmounted(()=>{
    showData.value = [];
});

const clearModifyData = () => {
  midifyData.value = {};
}
const addRecord = () => {
    if (isShowDetail.value&&isGridModified(props.form,lastRow.value)) {
        return false;
    }
    clearForm();
    currentRow.value = {};
    gridRef.value!.setCurrentRow(null);
    isShowDetail.value = true;
    isDeleteRecord.value = false;
    isEditRecord.value = false;
    lastRow.value = {};
    emit('add-record')
}

const deleteRecord = async () => {
    let rec = currentRow.value;
    if (rec==null|| Object.keys(rec).length==0) {
        return;
    }
    if (rec.mkckAction == 'D') {
      return;
    }
    ElMessageBox.confirm(
        'Delete the record?',
        'Warning',
        {
            confirmButtonText: 'OK',
            cancelButtonText: 'Cancel',
            type: 'warning',
        }
    ).then(async () => {
        if (rec?.mkckAction == 'C' && rec?.recordStatus == 'PD') {
            showData.value = showData.value.filter(function(item){
                return item?.oid !== rec?.oid;
            });
            if (!rec.sysCreateDate) {
                delete midifyData.value[rec.oid];
            } else {
                midifyData.value[rec.oid] = {
                    ...rec,
                    mkckAction: 'D',
                };
            }
        } else {
            rec.mkckAction= 'D';
            rec.recordStatus= 'PD';
            midifyData.value[rec.oid] = {
                ...rec,
                mkckAction: 'D',
                recordStatus: 'PD',
            };
        }
        rec.isDelete = true;
        cancelGrid();
    }).catch(() => {
    });
}

const cancelGrid = () => {
    clearForm();
    isShowDetail.value = false;
    currentRow.value = {};
    gridRef.value!.setCurrentRow(null);
    lastRow.value = null;
}

const saveGrid = async () => {
    let result = await ruleFormRef.value.validate((valid, fields) => {
        if (valid) {

        } else {
            showValidateMsg(props.details, fields);
        }
    });

    if(props.clickOk){
      result =await props.clickOk()==false?false:true;
    }

  if (result) {
    emit('save'); // 触发 save 事件
  }
    if (result) {
        let rec = {
            ...props.form,
        };
        if (props.uniqueKey) {
            let rows = showData.value.filter(function(item){
                return item[props.uniqueKey] == rec[props.uniqueKey] && rec.oid != item.oid;
            });
            if (rows&&rows.length >0) {
                showErrorMsg("The same data already exists.");
                return false;
            }
        }
        if (props.beforeSave&&!props.beforeSave(rec)) {
            return false;
        }

        if (!rec.sysCreateDate) {
            rec.mkckAction = 'C';
            rec.recordStatus = 'PD';
            rec.sysCreateDate = null;
            if (rec.oid) {
                updateRow(currentRow.value,rec);
            } else {
                rec.oid = Math.trunc(randomHashCode());
                showData.value.push(rec);
            }
        } else {
            rec.mkckAction = 'U';
            rec.recordStatus = 'PD';
            updateRow(currentRow.value,rec);
        }
        midifyData.value[rec.oid] = rec;
        cancelGrid();
    }
}

const updateRow = (currentObj: Object, newObj: Object) => {
    for(let key in newObj) {
        currentObj[key] = newObj[key];
    }
}

const handleClick = async (row: any, column: any, event: Event) => {
    if (props.readonly) {
      return; // 只读模式下不响应点击
    }
    if (isShowDetail.value&&isGridModified(props.form,lastRow.value)) {
        return false;
    }
    if (row.mkckAction && row.mkckAction == 'D') {
        isDeleteRecord.value = true;
    } else {
        isDeleteRecord.value = false;
    }
    for (let key in row) {
        props.form[key] = row[key];
    }
    currentRow.value = row;
    gridRef.value!.setCurrentRow(row);
    isShowDetail.value = false;
    isEditRecord.value = false;
    lastRow.value = { ...row };
    emit('row-click', row, showData.value.indexOf(row));
}

const handleDbClick = (row) => {
    if (props.onDbClick) {
        props.onDbClick(row);
    }
    if(props.unUpdate) {
        return false;
    }
    for (let key in row) {
        props.form[key] = row[key];
    }
    isShowDetail.value = true;
    isEditRecord.value = true;
    emit('on-db-click', row, showData.value.indexOf(row))
}

const cellStyle = (row, column, rowIndex, columnIndex) => {
    if (props.cellStyle) {
        let style = props.cellStyle(row, column, rowIndex, columnIndex);
        if (style) {
            return style;
        }
    }
    return highlight(row);
}

const handleCurrentChange = (val: any | undefined) => {
    //currentRow.value = val
}

const tableRowClassName = ({ row }) => {
    let selectedClass = "";
    if (currentRow.value.oid == row.oid) {
        selectedClass = ' selected-row';
    }
    return selectedClass;
}

const clearForm = () => {
    for(let key in props.form) {
        props.form[key] = null;
    }
}

const getModifyRecords = () => {
    return Object.values(midifyData.value);
}

const isEditing = () => {
    return isShowDetail.value;
}

const mergeShowData = () => {
    return showData.value.filter(function(item){
        return !(midifyData.value[item?.oid]&&item?.mkckAction == 'D');
    });
}

const addBatch = (data) => {
    let obj = {};
    let ele;
    let key = props.uniqueKey;
    if (!key) {
        key = "oid";
    }
    for (let i = 0; i < showData.value.length; i++) {
        ele = showData.value[i];
        obj[ele[key]] = ele;
    }
    let rec;
    for (let i = 0; i < data.length; i++) {
        ele = data[i];
        if (obj[ele[key]]) {
            rec = obj[ele[key]];
            if (rec.mkckAction == 'C') {

            } else {
                rec.mkckAction = 'U';
                rec.recordStatus = 'PD';
            }
            updateRow(rec, ele);
        } else {
            ele.mkckAction = 'C';
            ele.recordStatus = 'PD';
            rec = { ...ele };
            rec.oid = Math.trunc(randomHashCode());
            showData.value.push(rec);
        }
        midifyData.value[rec.oid] = rec;
    }
}

defineExpose({
    getModifyRecords,
    isEditing,
    showData,
    clearModifyData,
    mergeShowData,
    addBatch,
})
</script>