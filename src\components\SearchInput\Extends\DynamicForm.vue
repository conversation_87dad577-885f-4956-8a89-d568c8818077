<template>
  <el-form
    ref="elFormRef"
    :model="formModel"
    :rules="formRules"
    label-width="auto"
    label-position="top"
    :disabled="disabled"
    :inline="false"
  >
    <el-row :gutter="20">
      <el-col
        v-for="schema in props.schemas"
        :key="schema.field"
        v-bind="schema.colProps || { span: 12 }"
      >
        <el-form-item
          :label="schema.label"
          :prop="schema.field"
          :required="schema.required"
          v-bind="schema.formItemProps"
        >
          <!-- 自定义插槽渲染 -->
          <slot
            v-if="schema.slot"
            :name="schema.field"
            :form-item="{
              schema,
              value: formModel[schema.field],
            }"
          >
            <component
              :is="getComponent(schema.component)"
              v-model="formModel[schema.field]"
              v-bind="schema.componentProps"
            />
          </slot>
          <!-- 针对 ElementPlus 组件解析失败处理 -->
          <el-switch
            v-else-if="schema.component === 'el-switch'"
            v-model="formModel[schema.field]"
            v-bind="schema.componentProps"
          ></el-switch>
          <el-cascader
            v-else-if="schema.component === 'el-cascader'"
            :options="schema?.options || []"
            v-model="formModel[schema.field]"
            placeholder=" "
            v-bind="schema.componentProps"
          >
          </el-cascader>
          <el-input-number
            v-else-if="schema.component === 'el-input-number'"
            v-model="formModel[schema.field]"
            v-bind="schema.componentProps"
          ></el-input-number>
          <el-input
            v-else-if="schema.component === 'el-input'"
            v-model="formModel[schema.field]"
            v-bind="schema.componentProps"
          ></el-input>
          <!-- 使用 componentMap 动态渲染组件 -->
          <component
            v-else
            :is="getComponent(schema.component)"
            v-model="formModel[schema.field]"
            v-bind="schema.componentProps"
          >
            <!-- 处理需要子组件的组件，如 el-select -->
            <template v-if="schema.component === 'el-select'">
              <el-option
                v-for="option in getOptions(
                  schema?.options || [],
                  schema.optionProps || {}
                )"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </template>
          </component>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, watch, ref, defineExpose } from "vue";
import type { FormSchemaVO } from "./types";
import { ElForm } from "element-plus";
import { componentMap } from "./componentMap";

const props = defineProps<{
  schemas: FormSchemaVO[];
  modelValue?: Record<string, any>;
  disabled?: boolean;
}>();
const emit = defineEmits(["update:modelValue", "change"]);

const elFormRef = ref<InstanceType<typeof ElForm>>();
const formModel = reactive<Record<string, any>>({});
const formRules = reactive<Record<string, any>>({});

/** 初始化表单数据与校验 */
const initForm = async () => {
  props.schemas.forEach((schema) => {
    formModel[schema.field] = props.modelValue?.[schema.field] ?? "";
    /** 暂时不做表单校验，如果需要可以打开 */
    if (schema.required || schema.rules) {
      formRules[schema.field] = schema.rules || [
        {
          required: true,
          message: `${schema.label}不能为空`,
          trigger: "blur",
        },
      ];
    }
  });
};

watch(() => props.schemas, initForm, { immediate: true });
watch(
  () => formModel,
  (newData) => {
    emit("update:modelValue", newData);
    emit("change", newData);
  },
  { deep: true }
);

/** 组件映射 */
const getComponent = (component: string) => {
  return componentMap[component] || "el-input";
};

const getOptions = (options: any, optionProps?: any) => {
  return options.map((item: any) => {
    return {
      ...item,
      label: item[optionProps.label] || "",
      value: item[optionProps.value] || "",
    };
  });
};

/** 对外暴露方法 */
defineExpose({
  formModel,
  validate: () => elFormRef.value?.validate(),
  resetFields: () => elFormRef.value?.resetFields(),
});
</script>
<style lang="less" scoped></style>
