// import dark theme
@use "element-plus/theme-chalk/src/dark/css-vars.scss" as *;

:root {
  --el-font-size-base: 13px;
  --ep-form-label-font-size:13px; // detail footer label font size and detail dialog label font size
  --ep-component-size-large: 32px;
  --ep-component-size: 24px;
  --ep-component-size-small: 16px;
  --ep-disabled-bg-color:#E6E6E6; //detail's flow message of disabled backgroud-color, original color is #f5f7fa
}

html,body {
  height: 100%;
}

body {
  font-family: Arial;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
  font-size: 13px;
  width: 100% !important;
}

a {
  color: var(--ep-color-primary);
}

code {
  border-radius: 2px;
  padding: 2px 4px;
  background-color: var(--ep-color-primary-light-9);
  color: var(--ep-color-primary);
}

input {
  //text-transform: uppercase;
}

.text-none input {
  text-transform: none;
}

.text-upper input {
  text-transform: uppercase;
}

.ep-button {
  padding: 5px 8px;
  height: unset;
  border-radius: 5px !important;
}

.ep-button-custom {
  padding: 5px 20px;
}

.ep-select__input {
  height: unset !important;
}

.ep-select>div {
  padding: 1px 3px;
}

.ep-select .ep-select__selected-item span.ep-tag {
  height: 18px;
}

.ep-select__popper {
  margin-top: -8px;
}

.ep-select__popper .ep-popper__arrow {
  display: none;
}

.ep-select__popper .ep-select-dropdown__item {
  height: 24px;
  line-height: 24px;
}

text {
  color: var(--ep-text-color-regular);
}

.ep-menu--horizontal>.ep-sub-menu .ep-sub-menu__title {
  color: white;
}

.ep-checkbox {
  height: 20px !important;
}

.ep-pagination,
.ep-table,
.ep-checkbox__label,
.ep-select__wrapper {
  font-size: 13px !important;
  --ep-pagination-font-size: 13px !important;
  --ep-pagination-button-width: 24px !important;
  --ep-pagination-button-height: 24px !important;
  --ep-pagination-border-radius: 0px !important;
}

.ep-select__wrapper {
  min-height: 24px !important;
  line-height: 16px !important;
}

.ep-sub-menu.is-active {
  background-color: white;
}

.ep-pager li {
  margin: 3px;
}

.ep-table {
  --ep-table-header-bg-color: #f4f4f5 !important;
}

.ep-table .ep-table__cell {
  padding: 3px 0 !important;
}
// Start R2411A-38517, Tom.Li, 2024/08/20
.ep-table .cell {
  padding: 0 6px !important;
}
// End R2411A-38517, Tom.Li, 2024/08/20
.ep-table__body {
  padding-bottom: 0px;
}

.ep-scrollbar__bar.is-horizontal {
  position: unset;
  height: unset!important;
}

.ep-table--scrollable-x .ep-scrollbar__bar.is-horizontal > div {
  height: 10px!important;
}

.ep-pagination .btn-prev {
  margin-left: 30% !important;
}

.ep-header,
.ep-footer {
  height: unset !important;
  padding: unset !important;
}
.ep-main {
  --ep-main-padding: 0px;
}

.ep-overlay.is-message-box .ep-overlay-message-box {
  z-index: 999;
}

.ep-form-item.is-required:not(.is-no-asterisk).asterisk-left > .ep-form-item__label:after, .ep-form-item.is-required:not(.is-no-asterisk).asterisk-left > .ep-form-item__label-wrap > .ep-form-item__label:after {
  content: "*";
  color: var(--ep-color-danger);
  margin-left: 4px;
  vertical-align: middle;
}
.ep-form-item.is-required:not(.is-no-asterisk).asterisk-left > .ep-form-item__label:before, .ep-form-item.is-required:not(.is-no-asterisk).asterisk-left > .ep-form-item__label-wrap > .ep-form-item__label:before{
  content: "" !important;
  margin-right: 0px !important;
}

.ep-message {
  right: 30px;
  left: unset !important;
  transform: unset !important;
}

.ep-menu--horizontal > .ep-sub-menu:hover .ep-sub-menu__title {
  background-color: var(--ep-menu-hover-bg-color);
}

.input-number input{
  text-align: right;
}

.ep-popper.header-popper-class {
  inset: unset !important;
}

.ep-menu--horizontal > .ep-sub-menu.is-active .ep-sub-menu__title {
  border: 0px;
}

.ep-input .ep-input__wrapper:hover,
.ep-textarea .ep-textarea__inner:hover,
.hoverHighline:hover {
  -webkit-box-shadow:  0 0 0px 2px #5679da inset;
}

.is-disabled .hoverHighline:hover {
  box-shadow: none;
}

.ep-input .ep-input__icon {
  display: none !important;
}

input:-webkit-autofill,  
input:-webkit-autofill:hover,   
input:-webkit-autofill:focus,   
textarea:-webkit-autofill,  
textarea:-webkit-autofill:hover,  
textarea:-webkit-autofill:focus,  
select:-webkit-autofill,  
select:-webkit-autofill:hover,  
select:-webkit-autofill:focus {  
    -webkit-box-shadow: 0 0 0px 1000px #fffff inset !important;  
    transition: background-color 5000s ease-in-out 0s;
}

.ep-checkbox__inner::after {
  border: 2px solid transparent !important;
  border-color: var(--ep-checkbox-checked-icon-color) !important;
  border-left: 0 !important;
  border-top: 0 !important;
}
.ep-checkbox__input:hover {
  box-shadow:  0 0 0px 2px #5679da !important;
}
.ep-checkbox__input.is-focus {
  -webkit-box-shadow: 0 0 0px 2px var(--ep-color-primary);
  box-shadow:  0 0 0px 2px var(--ep-color-primary) ;
}

.ep-form-item.is-error .is-disabled .ep-input__validateIcon {
  display: none;
}

.ep-form-item.is-error .is-disabled {
  box-shadow: unset !important;
}

.ep-form-item.is-error select {  
  box-shadow: red 0px 0px 0px 1px inset;
}
.ep-form-item select {
  box-shadow: #E6E6E6 0px 0px 0px 1px inset;
}

.splitLine {
  margin: 30px 0 10px 0;
}

.splitLine .ep-divider {
  margin:5px 0px;
}
.splitLine span {
  font-size: 16px; 
  font-weight: bold;
  padding-top:10px;
}

.ep-message-box {
  .ep-message-box__header {
    background-color: var(--ep-color-primary);
    height: 28px;

    .ep-message-box__title {
      padding-left: 10px;
    }
  }

  .ep-message-box__headerbtn {
    height: 30px !important;
    width: 30px !important;

    .ep-message-box__close {
      color: white;
    }
  }

  .ep-message-box__content {
    padding: 10px;
  }

  .ep-message-box__btns {
    padding: 0px 0px 10px 0px;
    justify-content: center;
  }

  .ep-message-box__btns {
    .ep-button {
      padding: 5px 20px;
    }
  }
}

.data-grid-selection-index-cell .cell,
.data-grid-selection-cell .cell {
  display: none !important;
}
.multiple-table .ep-table__body .cell {
  cursor: default; 
}
.ep-table__body tr.selected-row > td.ep-table__cell,
.ep-table__body tr.selected-row > td.ep-table__cell .ep-checkbox {
  background-color: var(--ep-table-current-row-bg-color) !important;
  color: black;
}
.multiple-table ::selection {
  color: black;
  background-color: var(--ep-table-current-row-bg-color);
}

.multiple-table ::-moz-selection {
  color: black;
  background-color: var(--ep-table-current-row-bg-color);
}

.multiple-table ::-webkit-selection {
  color: black;
  background-color: var(--ep-table-current-row-bg-color);
}
.multiple-checked {
margin-bottom: 3px;
display: none;
}
.ep-table__body tr.selected-row > td.ep-table__cell .multiple-checked {
display: unset;
}
.multiple-checkbox__inner {
  display: inline-block;
  position: relative;
  border: var(--ep-checkbox-input-border);
  border-radius: var(--ep-checkbox-border-radius);
  box-sizing: border-box;
  width: var(--ep-checkbox-input-width);
  height: var(--ep-checkbox-input-height);
  background-color: var(--ep-checkbox-bg-color);
  z-index: var(--ep-index-normal);
  transition: border-color 0.25s cubic-bezier(0.71, -0.46, 0.29, 1.46), background-color 0.25s cubic-bezier(0.71, -0.46, 0.29, 1.46), outline 0.25s cubic-bezier(0.71, -0.46, 0.29, 1.46);
}
.multiple-checkbox__inner:hover {
  border-color: var(--ep-checkbox-input-border-color-hover);
}
.ep-time-panel__btn.confirm{
  color: var(--ep-color-white) !important;
  font-weight: var(--ep-button-font-weight) !important;
  background-color: var(--ep-color-primary);
  border-color: var(--ep-color-primary);
  height: unset;
  border-radius: 5px !important;
  padding: 5px 20px;
  outline-color: var(--ep-color-primary-light-5);
  line-height: 14px;
}
.ep-time-panel__btn.confirm:hover{
  color: var(--ep-color-white);
  background-color: var(--ep-color-primary-light-3);
}
.ep-time-panel__btn.cancel{
  color: var(--ep-button-text-color) !important;
  font-weight: var(--ep-button-font-weight) !important;
  background-color: var(--ep-button-bg-color);
  border-color: var(--ep-button-border-color);
  height: unset;
  border: var(--ep-border);
  border-radius: 5px !important;
  padding: 5px 10px;
  outline-color: var(--ep-color-primary-light-5);
  line-height: 14px;
}
.ep-time-panel__btn.cancel:hover{
  color: var(--ep-color-primary) !important;
  border-color: var(--ep-color-primary-light-7) !important;
  background-color: var(--ep-color-primary-light-9) !important;
}

.lightyellow {
  background-color: lightyellow;
  border-bottom-color: #e6e6e6 !important;
}
.ep-tabs__item.is-top.is-active {
  border-bottom-color: var(--ep-bg-color) !important;
}

.unlightyellow {
  background-color: unset;
}

.ep-table__body tr.current-row > td.ep-table__cell {
  background-color: var(--ep-table-current-row-bg-color) !important;
}

.ep-overlay-message-box:has(.message-box-full-coverage) {
  background-color: white;
}

.ep-form-item {
  margin-bottom: unset !important;
}

.ep-pager {
  li {
    margin: 2px 1px 2px 0px;
  }
}

.ep-pagination {
  .btn-prev {
    margin-right: 1px;
  }
}

.ep-select__wrapper {
  padding: 4px 6px;
}