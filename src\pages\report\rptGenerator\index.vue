<template>
  <el-container style="height:100%; position: relative; z-index: 98;">
    <el-header>
      <el-row :gutter="24" style="margin: 0px; padding: 3px 10px; border-bottom: 2px solid lightgrey; ">
        <el-col :span="10" style="padding:0px;align-content: center;">
          <el-text style="color: #b31a25;font-weight: bold;margin:0px;" class="mx-1" size="large">{{
            $t($currentInfoStore.getHeaderTitle) }}</el-text>
        </el-col>
        <el-col :span="10" :offset="4" style="padding:0px;">
          <el-space style="float:right;">
            <el-button :icon="CloseBold" type="primary" @click="goHome" v-if="$route.path != '/'" />
          </el-space>
        </el-col>
      </el-row>
    </el-header>
    <el-main style="height: calc(100vh - 155px);">
      <el-container :class="$attrs.contentClass" style="height:100%; padding-inline: 10px;">
        <el-aside class="scrollable-container" :width="cLeftWidth" style="padding-top: 5px; transition: width 0.4s ease-out, opacity 0.2s ease-in, visibility 0.2s ease-in; ">
          <div>
            <el-row
                style="font-size: 14px; font-weight: bold; background-color: lightgrey; width: 100%; min-height: 28px; line-height: 28px;"
                :gutter="24">
              <el-col :span="8">
                <el-space style="height: 100%;padding-left: 3px;">
                  <el-button v-if="expandAll" title="Expand All" type="primary" link :icon="Plus" @click="expandAllClick" />
                  <el-button v-if="!expandAll" title="Collapse All" type="primary" link :icon="Minus" @click="expandAllClick" />
                </el-space>
              </el-col>
              <el-col align="middle" :span="8">
                Category
              </el-col>
            </el-row>
            <el-tree class="tree-demo" style="max-width: 400px;" :data="dataTree" node-key="key"
                     :highlight-current="true" :expand-on-click-node="false"  :check-strictly="true" ref="treeRef"
                     @node-click="handleTreeNodeClick"
            />
          </div>
        </el-aside>
        <el-button style="border-radius: 0px !important; width: 15px" v-if="contentLeftWidth && !isCollect"  :icon="ArrowLeftBold" type="info" plain
                   class="aside-button" @click="collectContentLeft" />
        <el-button style="border-radius: 0px !important; width: 15px" v-if="contentLeftWidth && isCollect" :icon="ArrowRightBold" type="info" plain
                   class="aside-button" @click="shrinkContentLeft" />
        <el-main style="padding: 0px; overflow: auto;">
          <el-main style="padding-inline: 10px;position: relative;">
            <FormRow>
              <ElFormItemProxy :label="$t('csscl.reportCenter.search.reportDesc')" prop="reportTemplateCode">
                <el-input disabled :value="reportTemplateCode" style="width: 120px;" />
                <el-input disabled :value="reportTemplateDesc" style="width: 360px;" />
              </ElFormItemProxy>
            </FormRow>
            <!-- 动态组件根据json数据渲染页面 -->
            <div class="reportTemp">
              <GeneratorForm :reportTemplateCode="reportTemplateCode" :reportTemplateDesc="reportTemplateDesc"
                :data="data" :currentFormData="currentFormData" style="width:410px" :onGenerator="dtGen" :beforeGen="beforeGen" @changeReport="changeReport"/>
            </div>
          </el-main>
        </el-main>
      </el-container>
    </el-main>
  </el-container>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router';
import { ref, reactive, computed, onMounted, getCurrentInstance, watch } from 'vue';
import GeneratorForm from './GeneratorForm.vue';
import {ArrowLeftBold, ArrowRightBold, CloseBold, Minus, Plus,} from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance()
const tableRef = ref();
const reportTemplateCode = ref('');
const reportTemplateDesc = ref('');
const data = ref()
const isGen = ref(false);
const router = useRouter()
let formInline = reactive({});
let currentFormData = ref<{ version?: number }>({});
const treeRef = ref();
const expandAll = ref(true);
const contentLeftWidth = "310px"
const isCollect = ref(false);
const cLeftWidth = ref(contentLeftWidth ? contentLeftWidth : '0px');
watch(() => reportTemplateCode.value, () => {
  if (reportTemplateCode.value) {
    fetchData();
  } else {
    //when clean reportTemplateCode
    data.value = {};
  }
})
onMounted(() => {
  getDataTree();
});
const collectContentLeft = ()=>{
  let width = contentLeftWidth.replace("px", "");
  isCollect.value = true;
  cLeftWidth.value = Number(width) * 0 + "px";
}
const shrinkContentLeft = ()=>{
  let width = contentLeftWidth.replace("px", "");
  isCollect.value = false;
  cLeftWidth.value = width + "px";
}
const expandAllClick = ()=>{
  expandAll.value = !expandAll.value;
  let nodes = treeRef.value?.store.nodesMap;
  if(nodes){
    for (let key in nodes) {
      nodes[key].expanded = !expandAll.value;
    }
  }
}
interface Tree {
  key: string
  reportId: string
  label: string
  desc: string
  children?: Tree[]
}
const handleTreeNodeClick = (dataClick: Tree) => {
  if(dataClick.reportId){
    reportTemplateCode.value = dataClick.reportId;
    reportTemplateDesc.value = dataClick.desc;
  }else{
    reportTemplateCode.value = '';
    reportTemplateDesc.value =  '';
  }
  // 初始化
  currentFormData = ref<{ version?: number }>({});
}
const dataTree =  ref([])

const fetchData = async () => {
  data.value = {};
  const msg = await proxy.$axios.post("/rptsched/api/v1/report/criteria/list", {
    param: { reportTemplateCode: reportTemplateCode.value,  keyScene: 1 },
    current: 1,
    pageSize: 50,
  });
  if (msg.success) {
    data.value = msg.data.data;
  }
}

const dtGen = (form) => {
  return true;
}

const beforeGen = () => {
  return true;
}

const goHome = () => {
  proxy.$axios.post('/datamgmt/api/v1/makerchecker/unlock', {
    logout: true,
  });
  proxy.$axios.post('/datamgmt/api/v1/handler/unlock', {
    flag: true,
  });
  proxy.$router.push("/");
}

const getDataTree = () => {
  proxy.$axios.get("/rptsched/api/v1/report/template/getReportTemplateTree").then((body) => {
    if (body.success){
     dataTree.value = body.data;
     // Start R2411A-32583 LiShaoyi 2025/04/09
     setTimeout(() => {
      expandAllClick();
     }, 30);
     // End R2411A-32583 LiShaoyi 2025/04/09
    }else {
     dataTree.value = []
    }
  })
}

const changeReport = (reportId, formData) => {
    proxy.$axios.get("/rptsched/api/v1/report/template/reportCode?reportTemplateCode="+reportId).then((body) => {
      if (body.success) {
          if(body.data){
            reportTemplateCode.value = reportId;
            reportTemplateDesc.value = body.data.reportDesc;
            // CAP1-204, huzhongbin, 2025/2/6
            if (formData) {
              currentFormData.value = formData;
              currentFormData.value.version = formData.version ? formData.version + 1 : 1;
            }
          }
      }
    });
}


</script>

<style>
/* 针对 WebKit 浏览器 */
.scrollable-container::-webkit-scrollbar {
  width: 0; /* 隐藏滚动条宽度 */
}

/* 针对 Firefox 浏览器 */
.scrollable-container {
  scrollbar-width: none; /* Firefox */
}
</style>