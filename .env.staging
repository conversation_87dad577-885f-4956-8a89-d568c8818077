VITE_APP_VERSION = v0.13.0
VITE_SYSTEM = Custody and Clearing Platform(DEV1)
VITE_BASEPATH=/
VITE_FRONTEND_HOME=https://csscl-web.apps.ocp-dev.ftn.bochkuatcloud.com
VITE_FRONTEND=https://csscl-web.apps.ocp-dev.ftn.bochkuatcloud.com
VITE_OIDCURL= https://csscl.dev-apigw.ftn.bochkuatcloud.com/eaphk/bapi/v1/csscl
VITE_REDIRECTURL=https://csscl-web.apps.ocp-dev.ftn.bochkuatcloud.com
VITE_SERVICE= https://dev-apigw.ftn.bochkuatclout.com
# 接口请求地址，会设置到 axios 的 baseURL 参数上
VITE_API_BASE_URL = https://csscl.dev-apigw.ftn.bochkuatcloud.com/eaphk/bapi/v1/csscl/protected
# message 请求的网关地址
VITE_API_DEV_URL = https://csscl.apps.ocp-dev.ftn.bochkuatcloud.com/csscl-gateway
VITE_WS_PROTOCOL = wss