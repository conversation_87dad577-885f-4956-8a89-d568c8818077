<template>
  <BasePanel :searchParams="searchParams" :paramListData='paramListData' url="/datamgmt/api/v1/instrument/list" :params="{ modeEdit: 'Y' }"
    :showDetails="showDetails" :deleteRow="deleteRow" :editRow="editRow" :hideOperation="true">
    <template v-slot:searchPanel="slotProps">
      <FormRow>
        <ElFormItemProxy :label="$t('csscl.instrument.identifierType')" label-width="160" prop="identifierType">
          <Select v-model="slotProps.form.identifierType" style="width: 340px" type='IDENTIFIER_TYPE_CODE' :change="identifierType(slotProps.form.identifierType)"/>
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.instrument.identifierCode')" label-width="110" prop="instrumentCode">
          <el-input style="width: 300px" maxlength="20" v-model="slotProps.form.instrumentCode" />
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy :label="$t('csscl.instrument.search.exBoardCode')" label-width="160" prop="exBoardCode">
          <GeneralSearchInput v-model="slotProps.form.exBoardCode"  style="width: 500px" searchType="exboardCode" />
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy :label="$t('csscl.instrument.instrumentShortName')"  label-width="160" prop="instrumentShortName">
          <el-input style="width: 500px" v-model="slotProps.form.instrumentShortName" maxlength="50" input-style="text-transform:none" />
        </ElFormItemProxy>
      </FormRow>
    </template>
    <template v-slot:tableColumn>
      <el-table-column align="left" header-align="center" sortable="custom" prop="exBoardCode"
        :label="$t('csscl.instrument.exBoardCode')" width="260" />
      <!-- Start R2411A-69396 LiShaoyi 2025/04/08 -->
      <el-table-column align="left" header-align="center" sortable="custom" prop="identifierType"
      :label="$t('csscl.instrument.defIdentifierType')" width="260" >
        <template #default="scope">
          {{ getCommonDesc('IDENTIFIER_TYPE_CODE', scope.row.identifierType) }}
        </template>
        </el-table-column>
      <!-- End R2411A-69396 LiShaoyi 2025/04/08 -->
      <el-table-column align="left" header-align="center" sortable="custom" prop="instrumentCode"
        :label="$t('csscl.instrument.defIdentifierCode')" width="260" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="instrumentShortName"
        :label="$t('csscl.instrument.instrumentShortName')" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="status"
        :label="$t('common.title.status')" width="200">
        <template #default="scope">
          {{ getCommonDesc('STATUS', scope.row.status) }}
        </template>
      </el-table-column>
      <el-table-column align="left" header-align="center" sortable="custom" prop="recordStatus"
        :label="$t('common.title.recordStatus')" width="210">
        <template #default="scope">
          <!-- scope.row -->
          {{ getCommonDesc('RECORD_STATUS', scope.row.recordStatus) }}
          <span v-if="scope.row.recordStatus && scope.row.recordStatus !== 'A'">
            for {{ getCommonDesc('MKCK_ACTION', scope.row.mkckAction) }}
          </span>
        </template>
      </el-table-column>
    </template>
  </BasePanel>
  <Details ref="detailsRef" />
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance, watchEffect } from 'vue';
import {
  Search
} from '@element-plus/icons-vue'
import BasePanel from '~/pages/base/index.vue'
import Details from './Details.vue'
import { getCommonDesc } from '~/util/Function.js';


const { proxy } = getCurrentInstance()
const paramListData = ref({});
const searchParams = ref({
   //顺序和上面绑定参数一致
   identifierType:"",
  instrumentCode:"",
  exBoardCode:"",
  instrumentShortName:"",
});
//paramList 参数显示用的
function identifierType(value){
  paramListData._value.identifierType =  getCommonDesc('IDENTIFIER_TYPE_CODE', value);
}
const detailsRef = ref();
const showDetails = (row, disabled) => {
  detailsRef.value.showDetails(row, disabled);
}
const deleteRow = (row) => {
  console.log("Delete ...");
}
const editRow = (row) => {
  detailsRef.value.editRow(row);
}
//-------------------------------

</script>

<style></style>