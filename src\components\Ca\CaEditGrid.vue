<template>
  <div style="margin:10px 0" class="grid-continer">
    <el-form :validateOnRuleChange="false" ref="formRef" :inline="true" :model="formInline" class="demo-form-inline"
      :rules="props.rules" :showMessage="false">
      <slot name="searchPanel" :form="formInline"></slot>
      <ElFormItemProxy v-if="parseBool(props.isShowSearch)">
        <el-button @click="onReset(formRef)">Clear</el-button>
        <el-button type="primary" @click="load">Query</el-button>
      </ElFormItemProxy>
    </el-form>
    <el-row v-if="!parseBool(props.isHideOrder)" :gutter="24" class="demo-form-inline"
      style="margin: 0px;padding-block: 5px;">
      <el-col :span="24">
        <div style="width: 100%;display: table;border-bottom: 2px solid #e6e6e6;min-height: 26px;">
          <div style="color: lightgray; font-weight: bold;display: table-cell;width: 80px;align-content: center;"> Order
            By: </div>
          <el-space style="color: lightgray; width: calc(100% - 80px); padding-bottom: 2px;">
            <el-tag v-for="(tag, idx) in orderByDesc" :key="tag.code" closable type="info" @close="deleteOrder(tag)">
              {{ tag.name + " " + tag.order }}
            </el-tag> </el-space>
        </div>
      </el-col>
    </el-row>

    <!-- 表格和按钮区域包装容器 -->
    <div class="table-wrapper" :style="{ 'position': 'relative' }">
      <!-- 分页控件(上) -->
      <el-form :style="{ 'width': props.showAddDeleteButtons ? 'calc(100% - 30px)' : '100%' }">
        <el-pagination v-if="!parseBool(props.hidePagination)" v-model:current-page="currentPage" :disabled="editing"
          v-model:page-size="pageSize" :page-sizes="[10, 20, 30, 40]"
          layout="sizes, jumper, prev, pager, next, ->, slot" v-model:total="total" @current-change="onChangePage"
          @size-change="onChangePage" style="background-color: lightgrey;padding-inline: 10px;">
          Total {{ total }} records
        </el-pagination>
      </el-form>

      <!-- 表格 -->
      <div :style="{ 'width': props.showAddDeleteButtons ? 'calc(100% - 30px)' : '100%' }">
        <el-table :data="tableData" table-layout="auto" @row-dblclick="handleDbClick" @row-click="handleClick"
          @sort-change="handleSort" ref="tableRef" :cell-style="cellStyle" :border="true" class="grid-table"
          @selection-change="handleSelectionChange"
          scrollbar-always-on @current-change="handleCurrentChange" @select-all="selectAll" class-name="multiple-table"
          :row-class-name="tableRowClassName" :header-cell-class-name="setHeaderClass">
          <slot name="tableColumnFront"></slot>
          <el-table-column v-for="(item, idx) in columns" :key="idx" :sortable="item.sorter == false ? false : 'custom'"
            :prop="item.name"
            :align="item.align ? item.align : (item.dataType?.toUpperCase() == 'NUM' || item.dataType?.toUpperCase() == 'THOUS' ? 'right' : 'left')"
            :label="$t(item.title)" header-align="center" :width="item.width ? item.width : '*'">
            <template #default="scope">
              <template v-if="item.fn">
                {{ item.fn ? item.fn(scope.row, scope.row[item.name]) : "" }}
              </template>
              <template v-else-if="item.dataType?.toUpperCase() == 'THOUS'">
                {{ thousFormat(scope.row[item.name], item.scale) }}
              </template>
              <template v-else>
                {{ scope.row[item.name] }}
              </template>
            </template>
          </el-table-column>
          <slot name="tableColumnAfter"></slot>
          <el-table-column type="index" class-name="data-grid-selection-index-cell" width="1px" />
          <el-table-column v-if="props.isMultiple" :class-name="isHideCheckBox ? 'data-grid-selection-cell' : ''"
            type="selection" :width="isHideCheckBox ? 1 : 55" align="center" :selectable="props.selectable">
            <template #default="scope">
              <label v-if="!props.selectable || props.selectable(scope.row)" class="ep-checkbox">
                <span class="ep-checkbox__input">
                  <span class="multiple-checkbox__inner">
                    <Select class="multiple-checked" />
                  </span>
                </span>
              </label>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页控件(下) -->
      <el-form :style="{ 'width': props.showAddDeleteButtons ? 'calc(100% - 30px)' : '100%' }">
        <el-pagination v-if="!parseBool(props.hidePagination)" v-model:current-page="currentPage" :disabled="editing"
          v-model:page-size="pageSize" :page-sizes="[10, 20, 30, 40]"
          layout="sizes, jumper, prev, pager, next, ->, slot" v-model:total="total" @current-change="onChangePage"
          @size-change="onChangePage" style="background-color: lightgrey;padding-inline: 10px;">
          Total {{ total }} records
        </el-pagination>
      </el-form>

      <!-- 添加/删除按钮 -->
      <div v-if="props.showAddDeleteButtons" class="grid-add-delete-buttons">
        <el-space direction="vertical" size="small">
          <el-button :icon="Plus" circle size="small" @click="handleAdd" :disabled="isAddButtonDisabled" />
          <el-button :icon="Minus" circle size="small" @click="handleDelete" :disabled="isDeleteButtonDisabled" />
        </el-space>
      </div>
      <!-- 添加表单卡片 -->
      <el-card v-if="showFormCard" class="form-card" :style="'width: 100%; margin-top: 10px;'">
        <el-form :validateOnRuleChange="false" ref="formCardRef" :model="formData.form" :rules="props.formRules"
          label-position="left" :showMessage="true" :disabled="props.disabled">
          <slot name="editForm" :formData="formData" :detailsRef="formDetailsRef" :disabled="props.disabled"></slot>

        </el-form>
        <div style="justify-content: right; display: flex; margin-top: 15px;">
            <el-button @click="cancelForm" :disabled="false">{{ $t('csscl.ca.common.cancel') }}</el-button>
            <el-button type="primary" v-if="!props.disabled" @click="saveForm">{{ $t('csscl.ca.common.ok') }}</el-button>
          </div>
      </el-card>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, getCurrentInstance, watch, computed } from 'vue'
import type { FormInstance } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Edit,
  Delete,
  Select,
  Plus,
  Minus
} from '@element-plus/icons-vue'
import { useRouter } from 'vue-router';
import { useCookies } from "vue3-cookies";
import { highlight, thousFormat, selectedRow, clearSelectedCache, selectedAllRows, validSearchInputValue, validDateItemValue } from '~/util/Function.js';
import { showValidateMsg } from '~/util/Validators.js';

const router = useRouter()
const { cookies } = useCookies()
const { proxy } = getCurrentInstance()
const formRef = ref<FormInstance>()
const props = defineProps(['params', 'editRow', 'deleteRow', 'sortProp', 'lazy', 'isSelectFirst', 'searchParams',
  'onClick', 'onDbClick', 'hidePagination', 'isHideOrder', 'isShowSearch', 'columns', 'beforeSearch', 'afterSearch', 'onReset', 'cellStyle',
  'selectable', 'isMultiple', 'isHideCheckBox', 'rules', 'fieldsDtl', 'beforeChangePage', 'isManual', 'beforeClick',
  'showAddDeleteButtons', 'onAdd', 'onDelete', 'data', 'formData', 'formRules', 'onSaveForm', 'enableMakerChecker', 'handleChangePage',
  'onQuery', 'enableRecordStatusHighlight', 'isDeleteButtonDisabled', 'fieldDisabledRules', 'handleSelectionChange', 'disabled']);

const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const currentRow = ref({});
const tableRef = ref();
const tableData = ref([]);
const param = ref({});
const orderBy = ref("");
const orderByDesc = ref([]);
const sortField = ref({});
const sortFieldName = ref({});
const columns = ref(props.columns);

const formInline = reactive(props.searchParams || {});
const lazy = ref(props.lazy);
const oldParams = ref({});
const selectedRecord = ref({});
const lastSelected = ref(0);
const refreshSts = ref(true);

// 表单卡片相关
const showFormCard = ref(false);
const formCardRef = ref();
const formDisabled = ref(false);
const formData = reactive(props.formData || {})
const formDetailsRef = ref({
  fields: {},
  addField: (code, desc) => {
    formDetailsRef.value.fields[code] = desc;
  }
});

const enableMakerChecker = ref(true);

const isMakerCheckerEnabled = computed(() => {
  return props.enableMakerChecker !== false;
});

const editing = ref(false);

const isEditMode = ref(false);

const setEditing = (flag: boolean) => {
  editing.value = flag;
}

watch(() => props.params, (newVal) => {
  if (!refreshSts.value) {
    refreshSts.value = true;
    return;
  }
  console.log("props.params change reload", "url:" + props.url, props.params, oldParams.value);
  // The following code is to solve the problem of resending a request due to changes in the param when a page has multiple Grids
  let isChange = true;
  if (Object.keys(oldParams.value || {}).length > 0) {
    isChange = false;
    let keys = Object.keys(props.params || {});
    for (let i = 0; i < keys.length; i++) {
      let key = keys[i];
      let p = props.params[key];
      let op = oldParams.value[key];
      console.log("key:" + key, "p:" + p, "op:" + op)
      if (p != op) {
        isChange = true;
        break;
      }
    }
  }
  if (isChange) {
    defalutSel();
  }
});

watch(() => props.data, (newData) => {
  if (newData && Array.isArray(newData)) {
    tableData.value = newData;
    console.log("CaEditGrid接收到新的data:", tableData.value?.length);
  }
}, { immediate: true, deep: true });

const defalutSel = () => {
  load();
}


const afterSearch = (params, data) => {
  if (props.afterSearch) {
    props.afterSearch(params, data);
  }
}
const selectFirstRecord = () => {
  if (!selectFirstRecord.flag) {
    setTimeout(() => {
      if (tableRef.value.data?.length > 0) {
        //handleClick(currentRow.value.oid ? currentRow.value : tableRef.value.data[0]);
        // handleClick(tableRef.value.data[0]);
        tableRef.value.$el.querySelector("tbody tr").click();
        props.isManual && mousedown({
          target: tableRef.value.$el.querySelector("tbody tr"),
        });
      }
    }, 300);
    selectFirstRecord.flag = true;
  }
}

const baseLoad = () => {
  if (lazy.value == 'false') {
    lazy.value = true;
  } else {
    // loadData();
  }
}
const load = async () => {
  selectFirstRecord.flag = false;
  param.value = formInline;
  let res = await validSearchInputValue("div.grid-continer input[searchtype]")
  res = validDateItemValue("div.grid-continer .search-date-error input[alt]") == false ? false : res;
  if (res) {
    res = await beforeSearch(param.value, props.params);
    if (res) {
      res = await formRef.value.validate((valid, fields) => {
        if (!valid) {
          showValidateMsg(props.fieldsDtl, fields);
        }
      });
    }
    if (res) {
      if (props.onQuery) {
        await props.onQuery(param.value);
      } else {
        baseLoad();
      }
    }
  }
}
const onReset = (ref) => {
  orderBy.value = "";
  orderByDesc.value = [];
  sortField.value = {};
  sortFieldName.value = {};
  if (ref) {
    ref.resetFields();
    tableRef.value.clearSort();
  }

  if (props.onReset) {
    props.onReset();
  }
}

const pageObj = reactive({
  pageSize: pageSize.value,
  currentPage: currentPage.value,
});
const beforeChangePage = async () => {
  if (props.beforeChangePage) {
    let ret = await props.beforeChangePage();
    if (ret?.isChangePage === false) {
      //Prevent page change
      pageSize.value = pageObj.pageSize;
      currentPage.value = pageObj.currentPage;
      return false;
    } else if (ret?.lsLoadData === false) {
      //Change page, but not auto load data
      return false;
    }
  }
  return true;
}

const onChangePage = async (newPage) => {
  console.log("onChangePage, enter");
  if (await beforeChangePage()) {
    if (props.handleChangePage) {
      await props.handleChangePage(newPage);
    } else {
      load();
    }
  }
  pageObj.pageSize = pageSize.value;
  pageObj.currentPage = currentPage.value;
  console.log("onChangePage, exit");
}

const handleSort = (obj) => {
  let sts = {
    ...sortField.value
  };
  let stsName = {
    ...sortFieldName.value
  };
  if (sts[obj.prop] && !obj.order) {
    delete sts[obj.prop];
    delete stsName[obj.prop];
  } else {
    sts[obj.prop] = obj.order;
    stsName[obj.prop] = obj.column.label;
  }
  sortField.value = sts;
  sortFieldName.value = stsName;
  changeSort(sts, stsName);
  load();
}

const beforeSearch = async (search, params) => {
  let ret = true;

  if (props.beforeSearch) {
    ret = await props.beforeSearch(search, params);
    if (ret == false) {
      return false;
    } else {
      return true;
    }
  }
  return ret;
}

const handleDbClick = (row) => {
  isEditMode.value = true;
  if (props.onDbClick) {
    props.onDbClick(row);
  }

  if (showFormCard.value) {
    cancelForm();
    return;
  }


  // 隔离原始数据，避免修改原始数据
  formData.form = JSON.parse(JSON.stringify(row));

  // currentRow.value = row;
  showFormCard.value = true;
  setEditing(true);
}

const mousedown = (event: Event) => {
  selectedRowByDown("oid", event, props.selectable, props.isMultiple);
}

const mouseup = (event: Event) => {
  selectedRowByUp("oid", event, props.selectable, props.isMultiple);
}

const isMouseDown = ref(false);

const selectedRowByDown = (propsOid, event, getSelectable, isMultiple) => {
  isMouseDown.value = false;
  let isClickCheckbox = event.target.className == 'multiple-checkbox__inner';
  if (!isClickCheckbox && event.target.closest("td.ep-table-column--selection")) {
    isClickCheckbox = true;
  }
  let rowIndex = event.target.closest("tr.ep-table__row").querySelector("td.data-grid-selection-index-cell>div>div").innerText;
  let row = tableRef.value.data[rowIndex - 1];
  let oid = row[propsOid];
  let selectable = true;
  if (getSelectable) {
    selectable = getSelectable(row);
  }
  if (isMultiple && event.shiftKey) {
    tableRef.value.clearSelection();
    selectedRecord.value = {};
    if (lastSelected.value == 0) {
      lastSelected.value = rowIndex;
    }
    let start = parseInt(lastSelected.value), end = parseInt(rowIndex);
    if (start > end) {
      start = rowIndex;
      end = lastSelected.value;
    }
    let r;
    for (let j = start - 1; j < end; j++) {
      selectable = true;
      r = tableRef.value.data[j];
      if (getSelectable) {
        selectable = getSelectable(r);
      }
      if (selectable) {
        oid = r[propsOid];
        selectedRecord.value[oid] = r;
        tableRef.value.toggleRowSelection(r, true);
      }
    }
  } else if (isMultiple && (event.ctrlKey || isClickCheckbox)) {
    if (selectable) {
      if (selectedRecord.value[oid]) {
        isMouseDown.value = true;
      } else {
        selectedRecord.value[oid] = row;
        tableRef.value.toggleRowSelection(row, true);
      }
    }
  } else {
    if (selectedRecord.value[oid]) {
      isMouseDown.value = true;
    } else {
      tableRef.value.clearSelection();
      selectedRecord.value = {};
      if (selectable) {
        selectedRecord.value[oid] = row;
        tableRef.value.toggleRowSelection(row, true);
      }
    }
  }
  lastSelected.value = rowIndex;
}

const selectedRowByUp = (propsOid, event, getSelectable, isMultiple) => {

  let isClickCheckbox = event.target.className == 'multiple-checkbox__inner';
  if (!isClickCheckbox && event.target.closest("td.ep-table-column--selection")) {
    isClickCheckbox = true;
  }
  let rowIndex = event.target.closest("tr.ep-table__row").querySelector("td.data-grid-selection-index-cell>div>div").innerText;
  let row = tableRef.value.data[rowIndex - 1];
  let oid = row[propsOid];
  let selectable = true;
  if (getSelectable) {
    selectable = getSelectable(row);
  }
  if (isMultiple && event.shiftKey) {

  } else if (isMultiple && (event.ctrlKey || isClickCheckbox)) {
    if (selectable) {
      if (isMouseDown.value && selectedRecord.value[oid]) {
        delete selectedRecord.value[oid];
        tableRef.value.toggleRowSelection(row, false);
      }
    }
  } else {
    if (selectedRecord.value[oid]) {
      tableRef.value.clearSelection();
      selectedRecord.value = {};
      if (selectable) {
        selectedRecord.value[oid] = row;
        tableRef.value.toggleRowSelection(row, true);
      }
    }
  }
  lastSelected.value = rowIndex;
}

const handleClick = async (row: any, column: any, event: Event) => {
  if (props.beforeClick && !props.beforeClick(row, column, event)) {
    event.stopPropagation();
    return;
  }
  !props.isManual && event && selectedRow("oid", selectedRecord, tableRef, lastSelected, row, column, event, props.selectable, props.isMultiple);
  if (props.onClick) {
    let ret = await props.onClick(row);
    if (ret === false) {
      //no change row when edit row validation failed
      tableRef.value!.setCurrentRow(currentRow.value);
      return;
    }
  }
  currentRow.value = row;
  tableRef.value!.setCurrentRow(row);
}

const selectAll = (selection) => {
  if (selection && selection.length > 0) {
    selectedAllRows("oid", selection, selectedRecord, tableRef, lastSelected, props.selectable);
  } else {
    clearSelectedCache(selectedRecord, tableRef, lastSelected);
    currentRow.value = {};
    tableRef.value!.setCurrentRow({});
  }

}

const tableRowClassName = ({ row }) => {
  let selectedClass = "";
  let oid = row["oid"];
  if (selectedRecord.value[oid]) {
    selectedClass = ' selected-row';
  }
  return selectedClass;
}

const setHeaderClass = (params: any) => {
  params.column.order = sortField.value[params.column.property];
}

const changeSort = (sts, stsName) => {
  if (Object.keys(sts).length == 0) {
    orderBy.value = "";
    orderByDesc.value = [];
  } else {
    let obv = "";
    let obvNames = [];
    for (let key in sts) {
      obvNames.push({
        name: stsName[key],
        order: sts[key],
        code: key
      });
      if (props.sortProp && props.sortProp[key]) {
        let o = sts[key].charAt(0).toUpperCase();
        let d = "";
        for (let i = 0; i < props.sortProp[key].length; i++) {
          let ele = props.sortProp[key][i];
          d += ";" + ele + "-" + o;
        }
        obv += ";" + d.substring(1);
      } else {
        obv += ";" + key + "-" + sts[key].charAt(0).toUpperCase();
      }
    }
    orderBy.value = obv.substring(1);
    orderByDesc.value = obvNames;
  }
}

const deleteOrder = (tag) => {
  let sts = {
    ...sortField.value
  };
  let stsName = {
    ...sortFieldName.value
  };
  delete sts[tag.code];
  delete stsName[tag.code];
  sortField.value = sts;
  sortFieldName.value = stsName;
  changeSort(sts, stsName);
  load();
}

const parseBool = (val) => {
  return Boolean(String(val) == 'false' ? false : String(val) == '' ? true : val);
}

const cellStyle = (row, column, rowIndex, columnIndex) => {

  if (props.enableRecordStatusHighlight) {
    if (row?.row?.recordStatus === 'PD') {
      return { backgroundColor: 'lightyellow' };
    }
  }
  
  // 如果父组件提供了自定义样式,优先使用父组件的cellStyle
  if (props.cellStyle) {
    let style = props.cellStyle(row, column, rowIndex, columnIndex);
    if (style) {
      return style;
    }
  }
  
  return highlight(row);
}


const handleCurrentChange = (val: any | undefined) => {
  //currentRow.value = val
}

const handleSelectionChange = (val: any[]) => {
  if (props.handleSelectionChange) {
    props.handleSelectionChange(val);
  }
}

const setCurrentRow = (idx, click) => {
  idx = idx || 0;
  //tableRef.value.setCurrentRow(row);
  //handleClick(row);
  tableRef.value.$el.querySelectorAll("tbody tr")[idx]?.click();
}

const init = () => {
  onReset(formRef.value);
}

const setAutoRefreshSts = (sts) => {
  refreshSts.value = sts;
}

setTimeout(() => {
  load();
}, 200);

// 添加记录处理函数
const handleAdd = () => {
  isEditMode.value = false;
  // 清空表单数据(创建新的空白记录)
  const emptyForm = {};
  Object.keys(formData.form).forEach(key => {
    emptyForm[key] = null;
  });


  // 使用深拷贝避免引用问题
  formData.form = JSON.parse(JSON.stringify(emptyForm));

  showFormCard.value = true;
  setEditing(true);

  // 调用父组件实质的onAdd方法
  if (props.onAdd) {
    props.onAdd(formData.form);
  }
}

const cancelForm = () => {
  // 清空表单数据(创建新的空白记录)
  const emptyForm = {};
  Object.keys(formData.form).forEach(key => {
    emptyForm[key] = null;
  });

  // 使用深拷贝避免引用问题
  formData.form = JSON.parse(JSON.stringify(emptyForm));

  showFormCard.value = false;
  setEditing(false);
}

const saveForm = async () => {
  try {
    // 使用传入的formRules进行验证
    await formCardRef.value.validate((valid, fields) => {
      if (!valid) {
        showValidateMsg(formDetailsRef.value, fields);
        throw new Error('表单验证失败');
      }
    });

    if (formData.form.oid) {
      // 控制字段处理
      if (isMakerCheckerEnabled) {
        // 更新记录
        formData.form.mkckAction = 'U';
        formData.form.recordStatus = 'PD';
      } else {
        // 暂时不接入Maker Chacker 流程
        formData.form.mkckAction = null;
        formData.form.recordStatus = 'A';
      }
    } else {
      // 控制字段处理
      if (isMakerCheckerEnabled) {
        formData.form.mkckAction = 'C';
        formData.form.recordStatus = 'PD';
      } else {
        // 暂时不接入Maker Chacker 流程
        formData.form.mkckAction = null;
        formData.form.recordStatus = 'A';
      }
      // 新增记录
      // 需要生成临时ID
      formData.form.oid = Date.now();
    }


    // 调用父组件实质的onSaveForm方法
    if (props.onSaveForm) {
      // 传递form子对象
      props.onSaveForm(formData.form);
    }

    showFormCard.value = false;
    setEditing(false);
  } catch (error) {
    console.error('表单验证失败:', error);
  }
}

// 删除记录处理函数
const handleDelete = async () => {
  if (!currentRow.value || Object.keys(currentRow.value).length === 0) {
    ElMessage.warning('Please select a record');
    return;
  }

  try {
    await ElMessageBox.confirm(
      `Are you sure to delete the selected record?`,
      'Warning',
      {
        confirmButtonText: 'OK',
        cancelButtonText: 'Cancel',
        type: 'warning',
      }
    )
  } catch (e) {
    console.log("user cancel delete");
    return;
  }

  if (currentRow.value.mkckAction == 'D') {
    return;
  }

  let rec = currentRow.value;

  if (rec?.mkckAction == 'C' && rec?.recordStatus == 'PD') {
    const index = tableData.value.findIndex(item => item?.oid === rec?.oid);
    if (index !== -1) {
      tableData.value.splice(index, 1);
    }
    // 清除当前选中行
    currentRow.value = null;

    // 通知父组件记录已被删除
    if (props.onDelete) {
      props.onDelete(rec);
    }
  } else {
    // 如果是已有记录，标记为删除
    rec.mkckAction = 'D';
    rec.recordStatus = 'PD';
    rec.isDelete = true;
    
    if (props.onDelete) {
      props.onDelete(rec);
    }
  }

  // 关闭表单（如果打开）
  if (showFormCard.value) {
    cancelForm();
  }
}


// 控制+ - 按钮禁用状态
const isAddButtonDisabled = computed(() => {
  return editing.value || showFormCard.value || props?.disabled;
});

const isDeleteButtonDisabled = computed(() => {
  // 如果正在显示表单卡片，禁用删除按钮
  if (showFormCard.value) {
    return true;
  }
  
  // 父组件的控制函数
  if (typeof props?.isDeleteButtonDisabled === 'function') {
    return props?.isDeleteButtonDisabled(currentRow.value);
  }

  // 默认逻辑
  return editing.value || !currentRow.value || Object.keys(currentRow.value).length === 0 || props?.disabled;
});

const isFieldDisabled = computed(() => {
  return (fieldName: string) => {
    const rule = props.fieldDisabledRules[fieldName];
    if (!rule) return false;
    
    if (typeof rule === 'boolean') return rule;
    if (typeof rule === 'function') return rule(isEditMode.value);
    return false;
  };
});

defineExpose({
  load,
  tableRef,
  tableData,
  total,
  setCurrentRow,
  currentPage,
  pageSize,
  orderBy,
  currentRow,
  selectAll,
  selectedRecord,
  init,
  setAutoRefreshSts,
  mousedown,
  mouseup,
  setEditing,
  handleAdd,
  handleDelete,
  formData,
  cancelForm,
  saveForm,
  isEditMode,
})
</script>

<style>
.demo-form-inline .el-input {
  --el-input-width: 220px;
}

.demo-form-inline {}

.demo-form-inline .el-select {
  --el-select-width: 220px;
}

.grid-table .ep-table__body {
  padding: 0;
}

.table-wrapper {
  position: relative;
}

.grid-add-delete-buttons {
  position: absolute;
  top: 0;
  right: -5px;
  z-index: 1;
  background-color: white;
  border-radius: 4px;
  padding: 4px;
}

.form-card {
  margin-top: 15px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.form-card .el-form-item {
  margin-bottom: 18px;
}
</style>