# DepositoryEditGrid.vue 功能复刻 TODO 清单

## 项目概述
将EditGrid.vue的核心业务功能和特性复刻到DepositoryEditGrid.vue组件中，保持现有的inline编辑交互方式。

---

## 🚀 第一阶段：核心功能

### ✅ 1. 数据状态管理增强
- [✅] **1.1** 添加`midifyData`修改数据缓存机制
  - [✅] 创建`midifyData` ref对象跟踪所有修改
  - [✅] 在保存时更新修改缓存
  - [✅] 在删除时更新修改缓存
- [✅] **1.2** 实现`getModifyRecords()`方法
  - [✅] 返回所有修改过的记录
  - [✅] 支持新建、更新、删除状态的记录
- [✅] **1.3** 实现`clearModifyData()`方法
  - [✅] 清空修改数据缓存
  - [✅] 重置所有修改状态
- [✅] **1.4** 完善数据状态跟踪
  - [✅] 新建记录的完整生命周期管理
  - [✅] 更新记录的状态跟踪
  - [✅] 删除记录的状态管理

### ✅ 2. 数据保护机制
- [ ] **2.1** 实现`isGridModified()`检查机制
  - [ ] 检查当前编辑数据是否有未保存修改
  - [ ] 集成到行切换逻辑中
- [ ] **2.2** 添加切换行时的数据保护
  - [ ] 在`selectedRow`中添加修改检查
  - [ ] 在`currencySelectedRow`中添加修改检查
- [ ] **2.3** 实现离开编辑状态确认
  - [ ] 添加未保存数据的提示消息
  - [ ] 防止意外数据丢失
- [ ] **2.4** 完善取消编辑的数据恢复
  - [ ] 恢复到编辑前的原始状态
  - [ ] 清理临时修改数据

### ✅ 3. 表单验证体系
- [ ] **3.1** 集成Element Plus表单验证
  - [ ] 添加`rules`属性支持
  - [ ] 为存管表和货币表添加验证规则
- [ ] **3.2** 实现验证消息显示
  - [ ] 集成`showValidateMsg()`函数
  - [ ] 统一验证错误提示样式
- [ ] **3.3** 添加自定义验证回调
  - [ ] 支持`beforeSave`保存前验证
  - [ ] 支持`clickOk`自定义确认回调
- [ ] **3.4** 完善字段级验证
  - [ ] 必填字段验证
  - [ ] 格式验证（如SWIFT BIC码）
  - [ ] 业务规则验证

---

## 🔧 第二阶段：增强功能

### ✅ 4. 视觉反馈系统 (按EditGrid原版实现)
- [✅] **4.1** 实现单元格高亮功能
  - [✅] 集成`highlight()`函数
  - [✅] 支持自定义cellStyle属性
- [✅] **4.2** 实现选中行样式
  - [✅] 复刻EditGrid的tableRowClassName逻辑
  - [✅] 使用全局selected-row样式
- [✅] **4.3** 添加`cellStyle`属性支持
  - [✅] 支持自定义单元格样式
  - [✅] 集成到表格列定义中
- [✅] **4.4** 保持简洁的视觉反馈
  - [✅] 只实现EditGrid原有的功能
  - [✅] 不添加额外的状态样式

### ✅ 5. 事件系统完善
- [ ] **5.1** 添加标准事件触发
  - [ ] 实现`save`事件触发
  - [ ] 实现`add-record`事件
  - [ ] 实现`row-click`事件
- [ ] **5.2** 完善事件参数传递
  - [ ] 传递行数据和索引
  - [ ] 传递操作类型信息
- [ ] **5.3** 支持更多自定义回调
  - [ ] `beforeClick`点击前回调
  - [ ] `onDbClick`双击回调
  - [ ] `afterSearch`搜索后回调
- [ ] **5.4** 实现事件冒泡控制
  - [ ] 防止事件冲突
  - [ ] 优化事件处理顺序

### ✅ 6. 权限控制增强
- [ ] **6.1** 实现`readonly`模式
  - [ ] 只读模式下禁用所有编辑操作
  - [ ] 隐藏操作按钮
- [ ] **6.2** 添加`unUpdate`控制
  - [ ] 禁止更新现有记录
  - [ ] 只允许查看和新增
- [ ] **6.3** 细粒度权限控制
  - [ ] 按操作类型控制权限
  - [ ] 支持字段级权限控制
- [ ] **6.4** 权限状态同步
  - [ ] 权限变化时更新UI状态
  - [ ] 动态启用/禁用功能

---

## 🎯 第三阶段：高级功能

### ✅ 7. 批量操作支持
- [ ] **7.1** 实现`addBatch()`方法
  - [ ] 支持批量数据导入
  - [ ] 处理重复数据检查
- [ ] **7.2** 批量状态更新
  - [ ] 批量设置记录状态
  - [ ] 批量应用业务规则
- [ ] **7.3** 批量验证机制
  - [ ] 批量数据的唯一性验证
  - [ ] 批量业务规则验证
- [ ] **7.4** 批量操作反馈
  - [ ] 批量操作进度提示
  - [ ] 批量操作结果报告

### ✅ 8. 生命周期管理
- [ ] **8.1** 添加组件销毁清理
  - [ ] 实现`onUnmounted`清理逻辑
  - [ ] 清理事件监听器
- [ ] **8.2** 实现`mergeShowData()`方法
  - [ ] 过滤已删除的数据
  - [ ] 返回有效数据集合
- [ ] **8.3** 完善资源管理
  - [ ] 内存泄漏防护
  - [ ] 定时器清理
- [ ] **8.4** 状态重置机制
  - [ ] 组件重新初始化
  - [ ] 状态完全重置

### ✅ 9. 唯一性验证增强
- [ ] **9.1** 通用`uniqueKey`支持
  - [ ] 支持单字段唯一性
  - [ ] 支持多字段组合唯一性
- [ ] **9.2** 跨表唯一性验证
  - [ ] 存管表和货币表的关联验证
  - [ ] 层级数据的唯一性检查
- [ ] **9.3** 动态唯一性规则
  - [ ] 根据业务状态调整验证规则
  - [ ] 支持条件性唯一性验证
- [ ] **9.4** 唯一性冲突处理
  - [ ] 智能冲突解决建议
  - [ ] 用户友好的错误提示

### ✅ 10. 数据处理优化
- [ ] **10.1** 实现`updateRow()`通用方法
  - [ ] 统一的行数据更新逻辑
  - [ ] 支持部分字段更新
- [ ] **10.2** 优化数据拷贝策略
  - [ ] 深拷贝和浅拷贝的合理使用
  - [ ] 性能优化的数据操作
- [ ] **10.3** 改进数据同步机制
  - [ ] 主从表数据同步
  - [ ] 实时数据状态同步
- [ ] **10.4** 数据一致性保证
  - [ ] 事务性数据操作
  - [ ] 数据完整性检查

---

## 📋 实施说明

### 任务状态说明
- [ ] 待完成
- [🔄] 进行中  
- [✅] 已完成
- [❌] 已取消

### 实施原则
1. **保持现有交互**: 不改变inline编辑的用户体验
2. **渐进式增强**: 每次只实施一个功能点
3. **充分测试**: 每个功能完成后进行测试验证
4. **文档同步**: 及时更新相关文档

### 验收标准
每个功能点完成后需要：
1. 功能正常工作
2. 不破坏现有功能
3. 代码质量良好
4. 通过基本测试

---

## 📝 进度跟踪

**开始日期**: 2025-01-28
**当前阶段**: 第二阶段
**完成进度**: 8/40 (20%)

**最近完成**: 4.1-4.4 视觉反馈系统 ✅ (包含页面状态修复)
**测试页面**: `/test/depository-edit-grid` - DepositoryEditGrid.test.vue
**重要修复**: 修复了highlight函数的页面状态依赖问题
**下一个任务**: 2.1 实现`isGridModified()`检查机制

---

## 🧪 测试说明

### 测试页面访问
访问路径: `http://localhost:3000/test/depository-edit-grid`

### 数据状态管理功能测试步骤
1. **查看预置数据**: 页面加载后自动显示4条不同状态的测试数据
   - NORMAL001: 正常状态 (mkckAction: 'A', recordStatus: 'A')
   - NEW001: 新建状态 (mkckAction: 'C', recordStatus: 'PD')
   - UPDATE001: 更新状态 (mkckAction: 'U', recordStatus: 'PD')
   - DELETE001: 删除状态 (mkckAction: 'D', recordStatus: 'PD')
2. **查看修改缓存**: 点击"获取修改记录"查看非正常状态的记录
3. **测试选中行高亮**: 点击不同行查看选中行的高亮效果
4. **测试新增功能**:
   - 点击存管表的➕按钮新增记录
   - 填写必要信息并保存
   - 查看新记录的状态和修改缓存
5. **测试更新功能**:
   - 双击编辑现有记录
   - 修改信息并保存
   - 查看修改记录中的状态变化
6. **测试删除功能**:
   - 点击删除按钮删除记录
   - 查看删除状态的处理逻辑
7. **测试货币表级联**:
   - 选中存管记录，在货币表中进行增删改操作
   - 查看父级存管记录的状态更新
8. **测试清空功能**: 点击"清空修改缓存"按钮，验证缓存被清空

---

## ⚠️ 重要技术说明

### 页面状态依赖问题
在实现视觉反馈系统时发现了一个重要问题：

**问题**: highlight()函数依赖于`currentPageInfo.isCreatePage()`的状态判断
```javascript
export const highlight = (data) => {
    if (currentPageInfo.isCreatePage()) {
        return {};  // 创建页面不显示高亮
    }
    if (data.row.recordStatus != 'A') {
        return { backgroundColor: "lightyellow" }
    }
    return {};
}
```

**解决方案**: 在使用DepositoryEditGrid的页面中必须正确设置页面状态
```javascript
// 在组件初始化时设置
currentPageInfo.setEditPage();  // 或 setCreatePage()
```

**影响**: 如果页面状态未设置或设置错误，会导致：
- 新建记录(recordStatus: 'PD')不显示黄色背景
- 更新记录(recordStatus: 'PD')不显示黄色背景
- 删除记录(recordStatus: 'PD')不显示黄色背景

**测试验证**: 测试页面已添加"检查页面状态"和"测试Highlight行为"按钮来验证状态设置

### highlight函数的正确行为逻辑
通过分析Details.vue和Function.js，发现了highlight函数的正确行为逻辑：

**页面状态设置逻辑** (Function.js中的setDetailPageStatus):
```javascript
setDetailPageStatus: (row) => {
    if(!row || !row.hasOwnProperty("recordStatus") ||
       (row.mkckAction == 'C') ||
       (row.afterImage && row.afterImage.mkckAction == 'C')) {
        currentPageInfo.setCreatePage();  // 新建记录设置为CreatePage
    } else {
        currentPageInfo.setEditPage();    // 其他记录设置为EditPage
    }
}
```

**highlight函数逻辑**:
```javascript
export const highlight = (data) => {
    if (currentPageInfo.isCreatePage()) {
        return {};  // CreatePage状态下不显示任何高亮
    }
    if (data.row.recordStatus != 'A') {
        return { backgroundColor: "lightyellow" }
    }
    return {};
}
```

**正确的视觉效果**:
- **新建记录** (mkckAction: 'C'): 在CreatePage状态下，**不显示**黄色背景
- **更新记录** (mkckAction: 'U'): 在EditPage状态下，显示黄色背景
- **删除记录** (mkckAction: 'D'): 在EditPage状态下，显示黄色背景
- **正常记录** (recordStatus: 'A'): 无论什么状态都不显示黄色背景

**测试页面修正**: 添加了highlight行为测试功能，可以验证不同页面状态下的正确行为

### cellStyle参数格式问题
在实现cellStyle功能时发现了参数格式的问题：

**问题**: Element Plus的TypeScript类型定义期望对象参数格式，但EditGrid.vue使用直接参数格式
- **EditGrid.vue**: `cellStyle(row, column, rowIndex, columnIndex)` - 直接参数
- **Element Plus TS**: `cellStyle({row, column, rowIndex, columnIndex})` - 对象参数

**解决方案**: 严格按照EditGrid.vue的实现方式，使用类型断言解决TypeScript检查
```javascript
// 完全复刻EditGrid的cellStyle实现
const cellStyle = (row, column, rowIndex, columnIndex) => {
  if (props.cellStyle) {
    let style = props.cellStyle(row, column, rowIndex, columnIndex);
    if (style) return style;
  }
  return highlight(row);
}

// 在模板中使用类型断言
:cell-style="cellStyle as any"
```

**原则**: 优先保持与EditGrid.vue的完全一致性，而不是适配TypeScript类型

### oid字段统一化
按照EditGrid.vue的标准，统一了所有对象的ID字段：

**修改内容**:
- 将`depoCodeOid`统一改为`oid`
- 将`currencyOid`统一改为`oid`
- 使用`Math.trunc(randomHashCode())`生成新的oid
- 更新所有相关的引用和比较逻辑

**影响范围**:
- 表格的`row-key`属性
- 修改缓存`midifyData`的键值
- 行比较和选中逻辑
- 测试数据结构

**与EditGrid.vue保持一致**:
```javascript
// 新增记录时生成oid
rec.oid = Math.trunc(randomHashCode());

// 修改缓存使用oid作为键
midifyData.value[rec.oid] = rec;

// 行比较使用oid
if (currentRow.value.oid == row.oid) {
  selectedClass = ' selected-row';
}
```

---

## 删除方法逻辑修复

修复了DepositoryEditGrid中的删除方法，使其与EditGrid.vue的deleteRecord方法逻辑完全一致：

### 修复的问题
1. **缺少数据过滤逻辑**: 新建记录删除时没有从显示数据中过滤掉
2. **缺少确认对话框**: currencyRemoveRow方法缺少删除确认
3. **缺少isDelete标记**: 没有设置删除标记
4. **状态处理不一致**: 删除状态的处理逻辑与EditGrid不一致

### 修复内容

**removeRow方法**:
```javascript
if (row?.mkckAction == 'C' && row?.recordStatus == 'PD') {
    // 复刻EditGrid逻辑：从显示数据中过滤掉新建记录
    depositoryTableList.value = depositoryTableList.value.filter(item => item?.oid !== row?.oid);

    if (!row.sysCreateDate) {
        delete midifyData.value[row.oid];  // 新建未提交：删除缓存
    } else {
        midifyData.value[row.oid] = { ...row, mkckAction: 'D' };  // 新建已提交：标记删除
    }
} else {
    // 其他记录：标记删除状态
    row.mkckAction = 'D';
    row.recordStatus = 'PD';
    midifyData.value[row.oid] = { ...row, mkckAction: 'D', recordStatus: 'PD' };
}
row.isDelete = true;  // 设置删除标记
```

**currencyRemoveRow方法**:
- 添加了删除确认对话框 (`ElMessageBox.confirm`)
- 统一了删除逻辑处理
- 保留了DepositoryEditGrid特有的UI清理逻辑

### 保留的DepositoryEditGrid特有逻辑
- UI状态清理 (highlightRow, depositoryInfoDisabled等)
- 货币数据保存到父级存管机构
- 父级存管机构修改状态更新
- 数据变化事件触发

### 与EditGrid.vue的一致性
现在删除逻辑在以下方面与EditGrid.vue完全一致：
- 删除确认对话框
- 新建记录的数据过滤
- 删除状态的标记方式
- isDelete标记的设置
- 修改缓存的处理逻辑
