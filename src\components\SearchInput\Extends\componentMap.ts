import { Component } from "vue";
import {
  ElInput,
  ElSelect,
  ElDatePicker,
  ElRadioGroup,
  ElCheckbox,
  ElTimePicker,
} from "element-plus";

// 组件映射表
export const componentMap: Record<string, any> = {
  // Element Plus 组件
  "el-input": ElInput,
  "el-select": ElSelect,
  "el-date-picker": ElDatePicker,
  "el-time-picker": ElTimePicker,
  "el-radio-group": ElRadioGroup,
  "el-checkbox": ElCheckbox,

  // 支持自定义组件
};

// 注册自定义组件的函数
export function registerComponent(name: string, component: Component) {
  componentMap[name] = component;
}
