<template>
    <span v-if="props.reportTemplateCode" style="position: absolute;top: 0px;right: 0px; margin-right: 10px;">
        <el-button style="font-size: 22px;" size="large" :icon="List" link @click="cacheUserSearchParam" />
    </span>
    <el-dialog draggable overflow class="parameter-list" :close-on-click-modal="false" :close-on-press-escape="false"
        :destroy-on-close="true" v-model="showParam" :title="$t('csscl.common.paramList')"
        style="width: 1500px;height:430px">
        <div style="max-width: 800px;width: 500px;height:35px;padding-top: 22px;padding-left:10px;">
            <el-space>
                <span style="padding-right: 20px;width: 120px;">{{ $t('csscl.reportCenter.search.reportDesc') }}</span>
                <el-input disabled v-model="props.reportTemplateCode" style="width: 120px;" />
                <el-input :value="props.reportTemplateDesc" disabled style="width: 360px;" />
                <span style="padding-left: 60px;width: 150px;">Parameter Name</span>
                <el-input v-model="parameter" input-style="text-transform:none"
                    style="margin-left: -30px;width: 280px;" />
                <el-button style="margin-left: 25px;width: 70px;" @click="saveParam">Add</el-button>
            </el-space>
        </div>
        <div>
            <el-button :icon="Minus" style="margin-left: 96%; margin-bottom: -40px;" @click="deleteRow"></el-button>
        </div>
        <el-table highlight-current-row scrollbar-always-on @current-change="handleSelectionChange" border
            :data="paramData" style="width: 95%; height:243px; margin-left: 10px" ref="cacheTableRef">
            <el-table-column prop="paraName" label="Parameter" width="90" />
            <el-table-column prop="queryShow" label="Search" />
            <el-table-column prop="defaultInd" label="Default" width="70">
                <template v-slot="scope">
                    <el-checkbox v-model="scope.row.defaultInd" @change="changeDefaultInd(scope.row)" true-value="Y"
                        false-value="N" />
                </template>
            </el-table-column>
        </el-table>
        <template #footer>
            <div style="padding: 20px 10px;text-align: center;height: 35px">
                <el-space>
                    <el-button @click="exit" class="ep-button-custom">Exit</el-button>
                    <el-button type="primary" @click="saveDefault" class="ep-button-custom">OK</el-button>
                </el-space>
            </div>
        </template>
    </el-dialog>
    <el-form ref="formRef" :inline="true" :model="formInline" class="genratorForm" :rules="rules">
        <div v-if="props.reportTemplateCode" >
            <template v-for="(item, index) in $props.data" :key="index">
                <FormRow v-if="item.keyType !== 'hide'" style="width: 100%" :class="item.keyField">
                    <FormItemSign :detailsRef="searchRef" v-if="init(item)"></FormItemSign>
                    <el-row v-else-if="item.keyType === 'title'" style="margin-top: 25px;">
                        <span style="font-size: 16px; font-weight: bold;">{{item.keyDesc}}</span>
                        <div style="paddingBlock:5px;width: 100%;display: flex;flex-flow: wrap;border-top: 1px solid;"></div>
                    </el-row>
                    <div v-else-if="item.keyType === 'accountList'">
                        <el-row >
                            <FormItemSign :detailsRef="searchRef" v-if="item.keyType === 'accountList'" :prop="item.keyField"></FormItemSign>
                            <el-form ref="accountFormRef" :inline="true" :model="accountFormInline" :disabled="formDisabled" style="width: 100%;">
                                <el-row style="width: 100%;">
                                    <el-col :span="22">
                                        <el-table :data="accountFormInline.reportAccounts" height="250" highlight-current-row
                                        :row-class-name="tableRowClassName" ref="tabRef" @row-click="handleClick" :cell-style="cell"
                                        @row-dblclick="handleDbAccountClick" @current-change="handleCurrentAccountChange" border
                                            style="width: 100%; margin: 0; padding: 0;">
                                            <el-table-column prop="accountGroup" :label="$t('csscl.reportScheduler.accountGroup')" >
                                                <template #default="scope">
                                                    {{ getCommonDesc('ACCOUNT_GROUP', scope.row.accountGroup) }}
                                                </template>
                                            </el-table-column>
                                            <el-table-column prop="accountGroupDetails" :label="$t('csscl.reportScheduler.accountGroupDetails')" >
                                                <template #default="scope">
                                                    {{ getCommonDesc('CLIENT_GROUP', scope.row.accountGroupDetails) }}
                                                </template>
                                            </el-table-column>

                                            <el-table-column prop="accounts"
                                                :label="$t('csscl.reportScheduler.numOfCustodyAccount')">
                                                <template #default="scope">
                                                    {{ getCommonDesc('ACCOUNTS', scope.row.accounts) }}
                                                </template>

                                            </el-table-column>

                                            <el-table-column  v-if="props.isReportScheduler" prop="recordStatus" :label="$t('common.title.recordStatus')">
                                                <template #default="scope">
                                                    {{ getRecordStatusDesc(scope.row) }}
                                                </template>
                                            </el-table-column>
                                        </el-table>
                                    </el-col>
                                    <el-col :span="2">
                                        <el-space style="margin-top: 10px;margin-left: 10px;" direction="vertical">
                                            <el-button @click="showAddAccount" style="width: 30px" :icon="Plus" size="small"></el-button>
                                            <el-button @click="deleteAccount" style="width: 30px" :icon="Minus" size="small"></el-button>
                                        </el-space>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </el-row>
                        <el-form label-position="left">
                            <br>
                            <el-row style="margin:5px 0" v-if="isShowAddAccount">
                                <FormItemSign :detailsRef="details" label="Account Group" prop="accountGroup">
                                    <Select v-model="newAccountsRow.accountGroup" style="width: 150px" :type="item.keyDataSource" :change="changeAccountGroup" />
                                </FormItemSign>
                            </el-row>
                            <br>
                            <el-col :span="24" v-show="isShowAddAccount">
                                <el-card>
                                    <FormItemSign v-if="newAccountsRow.accountGroup === 'CG'" :detailsRef="details" label="Client Group" prop="clientGroup" label-width="130px">
                                        <CommonSearchInput v-model="newAccountsRow.clientGroup"
                                            :change="(val)=>{
                                                newAccountsRow.clientGroup=val;
                                                changeClientGroup(val)
                                            }"
                                            :dbClick="(row)=>{
                                                newAccountsRow.clientGroup=row.code;
                                                changeClientGroup()
                                            }"
                                            codeTitle="Client Group"
                                            maxlength="50"
                                            showDesc="false"
                                            commType='CLIENT_GROUP' />
                                    </FormItemSign>
                                    <FormItemSign v-if="newAccountsRow.accountGroup === 'CNC'" :detailsRef="details" label="Client Number CIN" prop="clientGroup" label-width="130px">
                                        <GeneralSearchInput  v-model="newAccountsRow.clientGroup"
                                                style="width:200px"
                                                maxlength="11"
                                                searchType="clientCode"
                                                showDesc="false"
                                                :change="(val)=>{
                                                    newAccountsRow.clientMasterOid='1';
                                                    changeClientGroup()
                                                }"
                                                :dbClick="(row)=>{
                                                    newAccountsRow.clientGroup=row.code;
                                                    newAccountsRow.clientMasterOid=row.var1;
                                                    changeClientGroup()
                                                }"/>
                                    </FormItemSign>
                                    <FormItemSign v-if="newAccountsRow.accountGroup === 'FM'" :detailsRef="details" label="Fund Manager" prop="clientGroup" label-width="130px">
                                        <CommonSearchInput v-model="newAccountsRow.clientGroup"
                                                        :change="(val)=>{
                                                            newAccountsRow.clientGroup=val;
                                                            changeClientGroup(val)
                                                        }"
                                                        :dbClick="(row)=>{
                                                            newAccountsRow.clientGroup=row.code;
                                                            changeClientGroup()
                                                        }"
                                                        codeTitle="Fund Manager"
                                                        maxlength="50"
                                                        showDesc="false"
                                                        commType='FUND_MANAGER' />
                                    </FormItemSign>
                                    <FormItemSign v-if="newAccountsRow.accountGroup === 'CA'" :detailsRef="details" label="Custody Account" prop="clientGroup" label-width="130px">
                                        <GeneralSearchInput v-model="newAccountsRow.clientGroup"
                                                style="width:200px"
                                                searchType="custodyAcct"
                                                showDesc="false"
                                                :params="{ recordStatus: 'A'  }"
                                                codeTitle="Custody Account"
                                                :change="(val)=>{
                                                    newAccountsRow.clientGroup=val;
                                                    changeClientGroup(val)
                                                }"
                                                :dbClick="(row)=>{
                                                    newAccountsRow.clientGroup=row.code;
                                                    changeClientGroup()
                                                }"/>
                                    </FormItemSign>
                                    <FormItemSign v-if="newAccountsRow.accountGroup === 'SP'" :detailsRef="details" label="Service Plan" prop="clientGroup" label-width="130px">
                                        <CommonSearchInput v-model="newAccountsRow.clientGroup"
                                                            :change="(val)=>{
                                                                newAccountsRow.clientGroup=val;
                                                                changeClientGroup(val)
                                                            }"
                                                            :dbClick="(row)=>{
                                                                newAccountsRow.clientGroup=row.code;
                                                                changeClientGroup()
                                                            }"
                                                            codeTitle="Service Plan"
                                                            showDesc="false"
                                                            commType='SERVICE_PLAN_CODE' />
                                    </FormItemSign>
                                    <br>
                                    <FormItemSign :detailsRef="details" label="Accounts" prop="accounts" label-width="130px">
                                        <Select v-if="!disabledSelecte" v-model="newAccountsRow.accounts" style="width: 150px" type='ACCOUNTS' />
                                        <Select v-if="disabledSelecte" v-model="newAccountsRow.accounts" style="width: 150px" type='ACCOUNTS'  :disabled="true"/>
                                    </FormItemSign>
                                    <SelectionGrid url="/datamgmt/api/v1/account/list" ref="accountGrid" 
                                        :params="accountParams" 
                                        :beforeSearch="()=>{ return beforeSearchValidate();}" 
                                        :columns="[
                                            { title: 'csscl.acctCode.clientAccountOid', name: 'tradingAccountCode' },
                                            { title: 'csscl.acctCode.accountShortName', name: 'accountShortName' },
                                            { title: 'common.title.recordStatus', name: 'recordStatus', fn: getRecordStatusDesc },
                                        ]">
                                        <template v-slot:tableColumnAfter>
                                            <el-table-column type="selection"  width="55" :selectable="checkSelectable" />
                                        </template>
                                    </SelectionGrid>
                                    <div style="justify-content: right;display: flex;">
                                        <el-button @click="hiddenAddAccount" class="ep-button-custom">Cancel</el-button>
                                        <el-button type="primary" @click="addAccount" class="ep-button-custom">OK</el-button>
                                    </div>
                                </el-card>
                            </el-col>
                        </el-form>
                    </div>

                    <FormItemSign :detailsRef="searchRef" v-else-if="item.keyType === 'SearchInput'" :label="$t(item.keyDesc)"
                        :prop="item.keyField">

                        <SearchInput :style="props.style" v-model="formInline[item.keyField]" @change="keychange(item)"
                            :url="item.keyDataSource" :title="$t(item.keyDesc)" :columns="[
                                {
                                    title: $t(JSON.parse(item.keyOptions || null).codeTitle),
                                    colName: JSON.parse(item.keyOptions || null).code,
                                    width: '380px',
                                },
                                {
                                    title: $t(JSON.parse(item.keyOptions || null).nameTitle),
                                    colName: JSON.parse(item.keyOptions || null).name,
                                }
                            ]">
                        </SearchInput>
                    </FormItemSign>

                    <FormItemSign :detailsRef="searchRef" v-else-if="item.keyType === 'Search'" :label="$t(item.keyDesc)"
                        :prop="item.keyField">
                        <GeneralSearchInput v-model="formInline[item.keyField]" :inputStyle="item.style"
                                            :codeTitle="$t(item.keyDesc)" :maxlength="JSON.parse(item.keyFunction||null)?.maxlength"
                                            :searchType="item.keyDataSource" style="width:500px" 
                                            :disabled="item.keyFunction ? changeDisabled[JSON.parse(item.keyFunction)?.disabled] : false"/>
                    </FormItemSign>

                    <FormItemSign :detailsRef="searchRef" v-else-if="item.keyType === 'MSearch'" :label="$t(item.keyDesc)"
                                :prop="item.keyField">
                        <MultipleGeneralSearchInput v-model="formInline[item.keyField]" :inputStyle="item.style"
                                        :codeTitle="$t(item.keyDesc)" :maxlength="JSON.parse(item.keyFunction||null)?.maxlength"
                                        :searchType="item.keyDataSource" style="width:500px" />
                    </FormItemSign>

                    <FormItemSign :detailsRef="searchRef" v-else-if="item.keyType === 'dateTimeRange'"
                        :label="$t(item.keyDesc)" :prop="item.keyField">

                        <el-date-picker :style="style" v-model="item.keyValue" @change="keychange(item)"
                            type="datetimerange" range-separator="To" start-placeholder="Start date"
                            end-placeholder="End date" value-format="YYYY/MM/DD HH:mm:ss" />
                    </FormItemSign>

                    <ElFormItemProxy v-else-if="item.keyType === 'D2D'" class="generator-form-item">

                        <FormItemSign :detailsRef="searchRef" :label="$t(item.keyDesc)" :prop="item.keyField">

                            <DateItem style="width: 130px;" v-model="formInline[item.keyField]" />
                        </FormItemSign>

                        <FormItemSign :detailsRef="searchRef" label-width="40" label="To" :prop="item.to.keyField" :hideLabel="$t(item.to.keyDesc)">

                            <DateItem style="width: 130px;" v-model="formInline[item.to.keyField]" :alt="$t(item.to.keyDesc)"/>
                        </FormItemSign>

                    </ElFormItemProxy>

                    <FormItemSign :detailsRef="searchRef" v-else-if="item.keyType === 'date'" :label="$t(item.keyDesc)"
                        :prop="item.keyField">

                        <DateItem style="width: 130px;" v-model="formInline[item.keyField]" />

                    </FormItemSign>
                    <FormItemSign :detailsRef="searchRef" v-else-if="item.keyType === 'select' && item.keyField !== 'changeReport'" :label="$t(item.keyDesc)"
                        :prop="item.keyField">
                        <Select v-model="formInline[item.keyField]" 
                            :source="(item.keyOptions || '').startsWith('source:') ? source : JSON.parse(item.keyOptions || null)"
                            :type="item.keyDataSource" :style="item.style || props.style" :class="keyClazz(item.keyField)" 
                            :disabled="item.keyFunction ? changeDisabled[JSON.parse(item.keyFunction)?.disabled] : false"/>
                    </FormItemSign>
                    <FormItemSign :detailsRef="searchRef" v-else-if="item.keyType === 'select' && item.keyField === 'changeReport'" :label="$t(item.keyDesc)"
                        :prop="item.keyField">
                        <Select v-model="formInline[item.keyField]" :change="(rptId)=>{ changeReport(rptId, item.keyFunction); }" 
                            :source="(item.keyOptions || '').startsWith('source:') ? source : JSON.parse(item.keyOptions || null)"
                            :type="item.keyDataSource" :style="item.style || props.style" />
                    </FormItemSign>
                    <FormItemSign :detailsRef="searchRef" v-else-if="item.keyType === 'text'" :label="$t(item.keyDesc)"
                        :prop="item.keyField">
                        <el-input v-model="formInline[item.keyField]" :maxlength="JSON.parse(item.keyFunction||null)?.maxlength" :style="item.style || props.style" class='text-none'></el-input>
                    </FormItemSign>
                    <FormItemSign :detailsRef="searchRef" v-else-if="item.keyType === 'hide'" :prop="item.keyField">
                        <el-input v-model="formInline[item.keyField]" ></el-input>
                    </FormItemSign>
                    <FormItemSign :detailsRef="searchRef" v-else-if="item.keyType === 'readOnly'" :label="$t(item.keyDesc)"
                        :prop="item.keyField">
                        <el-input v-model="formInline[item.keyField]" disabled :style="style" class='text-none'></el-input>
                    </FormItemSign>
                    <FormItemSign :detailsRef="searchRef" v-else-if="item.keyType === 'radio'" :label="$t(item.keyDesc)" :prop="item.keyField">
                        <el-radio-group v-model="formInline[item.keyField]">
                            <el-radio v-for="(rw, index) in JSON.parse(item.keyOptions || null)" :key="index" :init="chkRadio(item, rw)" :label="$t(rw.label)" 
                            :value="rw.value" @change="changeFormat"/>
                        </el-radio-group>
                    </FormItemSign>
                    <FormItemSign :detailsRef="searchRef" v-else-if="item.keyType === 'checkBox' && item.keyField !== 'fileFormatList'" :label="$t(item.keyDesc)" :prop="item.keyField" class="check-box">
                        <el-checkbox-group v-model="formInline[item.keyField]" style="float: left;">
                            <el-checkbox v-for="(rw, index) in JSON.parse(item.keyOptions || null)" :key="index" :label="$t(rw.label)" 
                            :value="rw.value" @change="changeBox(item.keyField, rw.value, rw?.show)" :style="item.style || props.style"/>
                        </el-checkbox-group>
                    </FormItemSign>
                    <FormItemSign :detailsRef="searchRef" v-else-if="item.keyType === 'number'" :label="$t(item.keyDesc)"
                        :prop="item.keyField">
                        <InputNumber v-model="formInline[item.keyField]" style="width: 190px;" :maxlength="JSON.parse(item.keyFunction||null)?.maxlength" scale="0"/>
                    </FormItemSign>
                </FormRow>
            </template>
            

            <component :is="FormRowCommon" :reportTemplateCode="props.reportTemplateCode" :formInline="formInline" :searchRef="searchRef" :fieldsDtl="fieldsDtl" 
                :rules="rules" :details="details" :genRules="genRules"></component>

            <FormRow>
                <ElFormItemProxy>
                    <el-button v-if="props.reportTemplateCode && (props.data?.length > 0)"
                        style="float:right; margin-top: 50px; margin-right: 40px;" type="primary"
                        @click="onGenerator">Generate</el-button>
                </ElFormItemProxy>
            </FormRow>
        </div>
    </el-form>
</template>

<script lang="ts" setup>
import { ref, reactive, getCurrentInstance, watch, toRaw, defineEmits, onMounted} from 'vue';
import type { FormInstance } from 'element-plus';
import { ElMessageBox, ElMessage } from 'element-plus';
import { useCookies } from "vue3-cookies";
import FormItemSign from '~/pages/base/FormItemSign.vue';
import MultipleGeneralSearchInput from "~/components/SearchInput/MultipleGeneralSearchInput.vue";
import FormRowCommon from './FormRowCommon.vue'
import { commonRules, showValidateMsg, anyOneValid } from '~/util/Validators.js';
import { validSearchInputValue, getRecordStatusDesc, getCommonDesc, currentDate, dateFormat, getEnvConfigVal, preProDate } from '~/util/Function.js';

import { List, Plus, Minus } from '@element-plus/icons-vue';
import SelectionGrid   from '~/pages/base/SelectionGrid.vue';
import CommonSearchInput from '~/pages/base/CommonSearchInput.vue';
import cloneDeep from 'lodash/cloneDeep';


const formRef = ref<FormInstance>();
const { proxy } = getCurrentInstance();
const props = defineProps(['reportTemplateCode', 'reportTemplateDesc', 'data', 'onGenerator', 'beforeGen', 'isReportScheduler', 'currentFormData']);
const emit = defineEmits(['changeReport']);

const accountFormRef = ref<FormInstance>();

const { cookies } = useCookies()
let formInline = reactive({});
const tabRef = ref();
const source = ref({}); // 下拉列表的数据源
const rules = reactive({});
const genRules = reactive({});
const details = ref({ fields: {} });
const fieldsDtl = { fields: {} }
const anyOneFieldsAndMsg = {anyOneFields: {}};
let curProcDate = "";
let preProcDate = "";
(async ()=>{ await currentDate().then( (v)=>{ curProcDate = dateFormat( v ); } ); })();
(async ()=>{ await preProDate().then( (v)=>{ preProcDate = dateFormat( v ); } ); })();
const curDate = (() => {
  let d = new Date();
  d.setHours(0, 0, 0, 0);
  return dateFormat(d)
})();


let accountFormInline = reactive({
    reportAccounts: [],
});
const isShowAddAccount = ref(false);
const currentAccountRow = ref({});
const newAccountsRow = ref({});
const accountParams= ref({});
const disabledSelecte=ref(false);
const accountGrid = ref();
const isdoubleCheck=ref(false);
const isdoubleCheckRow=ref();
const doubleCheckIndex = ref(-1);
const changeDisabled = ref({});

watch(() => props.currentFormData, (newVal) => {
  // 监听：只有当新值包含内容时才合并（防止初始化时的空值覆盖）
  if (Object.keys(newVal).length > 0) {
    Object.assign(formInline, newVal);
  }
  accountFormInline.reportAccounts = newVal.reportAccounts || [];
});

watch(() => props.data, (newVal) => {
    fieldsDtl.fields = {};
    anyOneFieldsAndMsg.anyOneFields = {};
    details.value.fields = {};
    for (let i = 0; i < props.data?.length; i++) {
        let data = props.data[i];
        if("anyOne" === data.keyType){
            anyOneFieldsAndMsg.anyOneFields[data.keyOptions] = data.keyFunction;
        } else {
            details.value.fields[data.keyField] = data.keyDesc;
            fieldsDtl.fields[data.keyField] = data.keyDesc;
            rules[data.keyField] = [];
            // CAP1-431 lisy
            if(data.keyFunction && "changeReport" != data.keyField){
            // CAP1-431 lisy
                const funStr = JSON.parse(data.keyFunction);
                if(funStr.default){
                    changeDisabled.value[funStr.disabled] = true;
                }
            }
        }
    }

    if (!props.currentFormData || !props.currentFormData.version) {
        Object.keys(formInline || {}).forEach(e => {
            delete (formInline[e]);
        });
        accountFormInline.reportAccounts = [];
        //切换报表关闭Custody Account Criteria
        isShowAddAccount.value = false;
    }

    let arr = (Object.keys(genRules));
    arr?.forEach(e => {
        delete genRules[e];
    });

    for (let i = 0; i < props.data?.length; i++) {
        let data = props.data[i];
        if (data.keyType == 'D2D') {
            data.to = props.data[i + 1];
            props.data.splice(i + 1, 1)
        }

        data = Object.assign({}, data);
        do {
            data.isExists = false;
            if (data.keyType == 'D2D' || data.keyType == 'date') {
                if (data.keyOptions?.startsWith("def:")) {
                    let def = data.keyOptions.replace("def:", "");
                    if (def == "curProcDate") {
                        formInline[data.keyField]= curProcDate;
                    }
                    if (def == "curDate") {
                        formInline[data.keyField] =curDate;
                    }
                    if (def == "preProcDate") {
                        formInline[data.keyField] = preProcDate;
                    }
                }
            }
            if (data.keyRules) {
                let rs = data.keyRules.split("\|");
                for (let j = 0; j < rs.length; j++) {
                    let rl = rs[j].split(",")
                    if (commonRules[rl[0]]) {
                        for(let z = 1; z < rl.length; z++) {
                            rl['orig'+z] = rl[z];
                            if (rl[z].startsWith("getVal:")) {
								rl[z] = () => { return formInline[rl['orig' + z].split(":")[1]] }
							} else if (rl[z].startsWith("conf:")) {
								let arr = (rl['orig'+z].split(":")[1]).split("^");
								let key = "VITE_YEAR_DAYS";
								let defVal = arr[0];
								if (arr.length > 1) {
									key = arr[0];
									defVal = arr[1];
								}
								rl[z] = getEnvConfigVal(key, defVal);
							} else if (rl[z].startsWith("label:")) {
                                rl[z] = proxy.$t( rl['orig'+z].split(":")[1] )
                            }
                        }
                        if (typeof commonRules[rl[0]] == 'object') {
                            rules[data.keyField].push(commonRules[rl[0]]);
                        } else {
                            rules[data.keyField].push(commonRules[rl[0]](rl[1],rl[2],rl[3],rl[4],rl[5],rl[6]));
                        }
                    }
                }
            }
            if (data.keyType == 'D2D' && data.to) {
                data = Object.assign({}, data.to);
                data.isExists = true;
            }
        } while (data.isExists);
    }
    funcIdCode.value = "MENU_RPT_GENERAT_" + props.reportTemplateCode;
    refreshCache(true);
});

const chkRadio = (item, rw) => {
  if (rw?.checked) {
    formInline[item.keyField] = formInline[item.keyField] || rw.value;
  }
  return rw.value;
}

const init = (item) => {
    if (item) {
        initPage(item);
    }
    if(item && item.keyField === 'changeReport'){
        formInline[item.keyField] = props.reportTemplateCode;
    }
    return false;
}

const initPage = async (item) => {
    if (item.keyType == 'select') {
        if (item.keyOptions?.startsWith("source:")) { // 可以通过数据库定义 KEY_OPTIONS source:Xxx 来表示下拉列表需要自定义
            let sou = item.keyOptions.replace("source:", "");
            if (sou.toUpperCase() == 'commType'.toUpperCase()) { // KEY_OPTIONS = source:commType
                proxy.$axios.get("/datamgmt/api/v1/comcode/typelist").then((body) => {
                    if (body.success) {
                        source.value = body.data;
                    }
                });
            } else if (true) { // 把 true 改成 KEY_OPTIONS = source:xxx 即可
                
            }
        } 
    } 
    if (item.keyType == 'readOnly' || item.keyType == 'hide') {
        if (item.keyOptions?.startsWith("source:")) { 
            let sou = item.keyOptions.replace("source:", "");
            if (sou.toUpperCase() == 'overdueGracePeriod'.toUpperCase()) { 
                let msg = await proxy.$axios.get("/datamgmt/api/v1/sysctrl/query");
                if(msg.success) {
                    formInline[item.keyField] = msg.data.overdueGracePeriod;
                }
            }
        } 
    }
    return false;
}

function keychange(item) {
    formInline[item.keyField] = item.keyValue;
}

const valid = (reportId) => {
    let ret = true;
    let arr = (Object.keys(genRules));
    arr?.forEach(e => {});
    return ret;
}

const onGenerator = async () => {
    formInline.reportId = props.reportTemplateCode;
    formInline.userId = proxy.$currentInfoStore.getUserInfo.userId;
    formInline.reportAccounts = accountFormInline.reportAccounts;

    if(!anyOneValid(formInline, anyOneFieldsAndMsg)){
        return false;
    }

    let result = await formRef.value.validate((valid, fields) => {
        if (!valid) {
            showValidateMsg(fieldsDtl, fields);
        }
    });
    //Non mandatory date verification
    if (result) {
        let dateInputs = document.querySelectorAll("div.search-date-error input[alt]");
        for (let i = 0; i < dateInputs.length; i++) {
            let e = dateInputs[i];
            let alt = e.alt;
            result = false;
            ElMessage({
                message: alt + ": Invalid date format!",
                type: 'error',
                duration: 100000,
                offset: 100,
                showClose: true,
            });
        }
    }
    if (result) {
        result = await validSearchInputValue(".reportTemp input[searchtype]");
    }

    if (props.beforeGen) {
        let res = await props.beforeGen();
        if (!res) {
            return false;
        }
    }

    if (!valid(formInline.reportId)) {
        return false;
    }

    if (result) {
        let result = true;
        if (props.onGenerator) {
            result = props.onGenerator(formInline);
        }
        if (result == false) {
            return;
        }
        
        formInline.executeType = 'A'
        let alertMsg = "Confirm to onGenerator?";
        let onlineInd = "";
        await proxy.$axios.get("/rptsched/api/v1/report/template/reportCode?reportTemplateCode="+formInline.reportId).then((body) => {
            if (body.success) {
                if(body.data){
                    if(body.data?.onlineInd === 'Y') {
                        onlineInd = "Y";
                        alertMsg = "Are you sure to generate report? Report will export directly soon after you confirm.";
                    }
                    if(body.data?.onlineInd === 'N') {
                        onlineInd = "N";
                        alertMsg = "Are you sure to generate report? Please download from report center after you confirm.";
                    }
                }
            }
        });
        ElMessageBox.confirm(alertMsg,'Warning',{
            confirmButtonText: 'OK',
            cancelButtonText: 'Cancel',
            type: 'warning',
        }).then(() => {
            if (onlineInd === "N") {
                ElMessageBox.alert('Submit Success.', 'Success');
            }
            if (onlineInd === "Y") {
                ElMessage({
                    message: "Submit success, please wait a moment.",
                    type: 'success',
                    duration: 10000,
                    offset: 100,
                    showClose: true,
                });
            }
            proxy.$axios.post("/rptsched/api/v1/report/template/genrpt", formInline, {
                responseType: 'blob',
            }).then((data) => {
                if(data?.data && data?.headers['content-disposition'] && onlineInd === "Y"){
                    let fileName = data?.headers['content-disposition']?.match(/filename=(.*)/)[1]
                    const blob = new Blob([data?.data]);
                    const link = document.createElement('a');
                    link.style.display = "none";
                    link.href = URL.createObjectURL(blob);
                    link.download = fileName;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                }
            });
            
        }).catch(() => {
        })
    }
    return result;
};


const showAddAccount = async () => {
    await accountGrid.value[0].emptySelection();
    isdoubleCheck.value=false;
    newAccountsRow.value={};
    newAccountsRow.value.accountGroup='';
    accountParams.value= {noResult: 'A'};
    isShowAddAccount.value = true;
    
}

const hiddenAddAccount = () => {
    // 当输入错误的信息后，点击 Cancel 按钮时，把数据清除
    newAccountsRow.value = {};
    //Start CAP1-415, Tom.Li, 2025-02-19
    accountParams.value= {noResult: 'A'};
    //End CAP1-415, Tom.Li, 2025-02-19
    isShowAddAccount.value = false;
};

const handleCurrentAccountChange = (val) => {
    currentAccountRow.value = val;
};

const tableRowClassName=({ row, rowIndex })  =>{
      row.index = rowIndex;
}

const deleteAccount = () => {
    const index = accountFormInline.reportAccounts.findIndex(item => item === currentAccountRow.value);
    if (index !== -1) {
        accountFormInline.reportAccounts.splice(index, 1);
        currentAccountRow.value = {};
        hiddenAddAccount();
    }
};

const addAccount = () => {
    const selectedData = toRaw(accountGrid.value[0].getSelectedData());
    const allData = toRaw(accountGrid.value[0].getAllData());
    let tmpCount = 'A';
    if (newAccountsRow.value.accounts !== 'A') {
        tmpCount=  selectedData.length
    }
    if (tmpCount === 0 || allData.length === 0) {
        ElMessage({
            message: 'At least one active custody account number',
            type: 'error',
            duration: 10000,
            offset: 100,
            showClose: true,
        });
        return true;
    }

    if (!Array.isArray(accountFormInline.reportAccounts)) {
        accountFormInline.reportAccounts = [];
    }
    if(isdoubleCheck.value){
        const tmpRow = reactive({
            reportCustodyAccountOid:isdoubleCheckRow.value.reportCustodyAccountOid,
            accountGroup: newAccountsRow.value.accountGroup,
            accountGroupDetails: newAccountsRow.value.clientGroup,
            clientMasterOid: newAccountsRow.value.clientMasterOid,
            tradingAccountCode: newAccountsRow.value.tradingAccountCode, 
            accounts: tmpCount,
            recordStatus: 'PD',
            mkckAction: isdoubleCheckRow.value.recordStatus==='A'?'U':isdoubleCheckRow.value.mkckAction,
            selectedData: cloneDeep(selectedData),
            selectParams: cloneDeep(accountParams.value)
        });
        accountFormInline.reportAccounts[doubleCheckIndex.value] = tmpRow;
    }else{
        const tmpRow = reactive({
        accountGroup: newAccountsRow.value.accountGroup,
        accountGroupDetails: newAccountsRow.value.clientGroup,
        clientMasterOid: newAccountsRow.value.clientMasterOid,
        tradingAccountCode: newAccountsRow.value.tradingAccountCode, 
        accounts: tmpCount,
        recordStatus: 'PD',
        mkckAction: 'C',
        selectedData: cloneDeep(selectedData),
        selectParams: cloneDeep(accountParams.value)
    });
        accountFormInline.reportAccounts.push(tmpRow);
    }
    
    hiddenAddAccount();
   
};

const beforeSearchValidate = ()=>{
    return true;
}

const changeAccountGroup= () => {
    newAccountsRow.value.clientGroup= "";
    newAccountsRow.value.clientMasterOid= "";
    accountParams.value={noResult: 'A'};
    disabledSelecte.value=false;
    newAccountsRow.value.accounts = '';
    if(newAccountsRow.value.accountGroup==='CA'){
    	//Start CAP1-376, Tom.li, 2025-02-26
        accountGrid.value[0].emptySelection();
        accountParams.value={tradingAccountCode: "",recordStatus: 'A',noResult: "Y"};
        //End CAP1-376, Tom.li, 2025-02-26
    }
};

const changeClientGroup= async() => {
    await accountGrid.value[0].emptySelection();
    let noResult='A';
    disabledSelecte.value=false;
    if(newAccountsRow.value.accountGroup&&newAccountsRow.value.accountGroup!=""&&newAccountsRow.value.clientGroup&&newAccountsRow.value.clientGroup!=""){
        noResult='Y';
    }
    if(newAccountsRow.value.accountGroup==='CG'){
        accountParams.value={clientGroupCode: newAccountsRow.value.clientGroup,recordStatus:'A',noResult: noResult};
    }else if(newAccountsRow.value.accountGroup==='SP'){
        accountParams.value={servicePlan: newAccountsRow.value.clientGroup,recordStatus:'A',noResult: noResult};
    }else if(newAccountsRow.value.accountGroup==='FM'){
        accountParams.value={fundMgrCode: newAccountsRow.value.clientGroup,recordStatus:'A',noResult: noResult};
    }else if(newAccountsRow.value.accountGroup==='CNC'){
        accountParams.value={clientCode: newAccountsRow.value.clientGroup,clientMasterOid: newAccountsRow.value.clientMasterOid,recordStatus: 'A',noResult: noResult};
    }else if(newAccountsRow.value.accountGroup==='CA'){
    	//Start CAP1-376, Tom.li, 2025-02-26
        if ( newAccountsRow.value.clientGroup ) {
            disabledSelecte.value=true;
            newAccountsRow.value.accounts = 'S';
        }
        accountParams.value={tradingAccountCode: newAccountsRow.value.clientGroup,recordStatus: 'A',noResult: 'Y'};
		//End CAP1-376, Tom.li, 2025-02-26
    }else{
        accountParams.value={noResult: noResult};
    }
};

const  checkSelectable=(row)=>{
    if(!props.formDisabled){
        //Start CAP1-415, Tom.Li, 2025-02-19
        if ( newAccountsRow.value.accounts=='A' ) {
            accountGrid.value[0].emptySelection();
        }
        //End CAP1-415, Tom.Li, 2025-02-19
        return newAccountsRow.value.accounts=='S';
    }else{
        return false;
    }
}

const handleDbAccountClick = async (val, event, column) => {
    await accountGrid.value[0].emptySelection();
    isShowAddAccount.value = true;
    isdoubleCheck.value = true;
    isdoubleCheckRow.value = val;
    doubleCheckIndex.value = val.index;
    newAccountsRow.value = {};
    newAccountsRow.value.accountGroup = val.accountGroup;
    newAccountsRow.value.clientGroup = val.accountGroupDetails;
    newAccountsRow.value.accounts = 'S';
    newAccountsRow.value.clientMasterOid = val.clientMasterOid;
    newAccountsRow.value.tradingAccountCode = val.clientMasterOid;
    // newParams.value = val.selectParams;
    if (val.accounts === 'A') {
        newAccountsRow.value.accounts = 'A';
    }
    // 确保 initSelectedData 包含所选的数据
    if (!val.initSelectedData) {
        val.initSelectedData = [];
    }
    // 合并 initSelectedData 和 selectedData
    if (val.selectedData) {
        const combinedData = [...val.initSelectedData, ...val.selectedData];
        const uniqueData = Array.from(new Map(combinedData.map(item => [item.tradingAccountCode, item])).values());
        val.initSelectedData = uniqueData;
    }
    await changeClientGroup().then(() => {
        accountGrid.value[0].initSelection(val.initSelectedData);
    });
};
// CAP1-431 lisy
//const changeReport = (reportId) => {
const changeReport = (reportId, keyFunc) => {
// CAP1-431 lisy
    if(reportId && reportId !== null && reportId !== "" && reportId !== props.reportTemplateCode){
        // CAP1-431 lisy
		if (keyFunc) {
			let kf = JSON.parse(keyFunc);
			let fileds = (kf.setNull || "").split(",");
			for (let k = 0; k < fileds.length; k++) {
				if (fileds[k])
					delete formInline[fileds[k]];
			}
			let op = (kf.op || "").split("\|");
			for (let k = 0; k < op.length; k++) {
				let f = op[k].split(",");
				if (f[0])
					formInline[f[0]] = f[1];
			}
		}
        // CAP1-431 lisy
        // CAP1-204, huzhongbin, 2025/2/6
        const currentFormData = cloneDeep(formInline);
        currentFormData.reportAccounts = accountFormInline.reportAccounts || [];
        emit('changeReport', reportId, currentFormData);
    }
};

const changeFormat = (fileFormat) => {
    const divClazz = document.querySelector(".LanguageClazz");
    if(divClazz){
        const selectDom = divClazz.querySelector("select");
        if(selectDom){
            let optTw = divClazz.querySelector("select option[value=TW]");
            formInline['fileLanguage'] = "";
            if(optTw){
                if("TXT" === fileFormat || "CSV" === fileFormat ){
                    optTw.style.display="none"
                } else {
                    optTw.style.display="";
                }
            }
        }
    }
};

watch(()=>formInline['fileFormat'],(newVal)=>{
    changeFormat(newVal);
});

const keyClazz = (keyField) => {
    if("fileLanguage" === keyField){
        return "LanguageClazz";
    }
    return "";
};

const changeBox = (keyField, value, toKeyFiled) => {
    if(toKeyFiled){
        if((formInline[keyField] && formInline[keyField].includes(value))
            || (formInline[keyField] && formInline[keyField].includes(value))){
            changeDisabled.value[toKeyFiled] = false;
            rules[toKeyFiled].push(commonRules['selectRequired']);
        } else {
            changeDisabled.value[toKeyFiled] = true;
            formInline[toKeyFiled] = "";
            rules[toKeyFiled] = [];
        }
    }
};

const paramData = ref([]);
const showParam = ref(false);
const parameter = ref("");
const searchFields = ref({});
const funcIdCode = ref("MENU_RPT_GENERAT_" + props.reportTemplateCode);
const cacheTableRef = ref();
const currentRow = ref({});
const searchRef = ref({
    addField: (code: string, desc: string) => {
        searchFields.value[code] = desc;
    }
});
const cacheUserSearchParam = () => {
    refreshCache();
    showParam.value = true;
    currentRow.value = {};
}
const refreshCache = (isSetDefault: boolean) => {
    parameter.value = "";
    paramData.value = [];
    proxy.$axios.get("/auth/api/v1/user/cache/byfuncid?funcId=" + funcIdCode.value).then((body) => {
        if (body.success && body.data.length > 0) {
            if (isSetDefault) {
                let r;
                for (let i = 0; i < body.data.length; i++) {
                    r = body.data[i];
                    if (r.defaultInd == 'Y') {
                        updateSearchParams(r);
                    }
                }
            } else {
                paramData.value = body.data;
            }

        }
    });
}
const saveParam = async () => {
    if (!parameter.value) {
        ElMessage({
            message: "Please input Parameter Name",
            type: 'error',
        })
    } else {
        if (await validSearchInputValue(".reportTemp input[searchtype]")) {
            let queryShow = "";
            let formInlineCriteria;
            // if ( funcIdCode.value == 'MENU_RPT_GENERAT_R000101C' || funcIdCode.value =='MENU_RPT_GENERAT_R000201C') {
            //     if (accountFormInline != null) {
            //         formInlineCriteria = {...accountFormInline}
            //     }
            // } else {
            if (accountFormInline != null) {
                formInline.reportAccounts = accountFormInline.reportAccounts
                searchFields.value['reportAccounts'] = 'Report Account';
            }
            if (formInline != null) {
                formInlineCriteria = {...formInline}
            }
            // }
            if (formInlineCriteria) {
                for (let index in formInlineCriteria) {
                        if(formInlineCriteria[index]){
                            if(index === 'reportAccounts'){
                                let reportAccountList = "";
                                for(let index in formInlineCriteria.reportAccounts){
                                    reportAccountList += formInlineCriteria.reportAccounts[index].accountGroupDetails+",";
                                }
                                queryShow += searchFields.value[index] + "=" + reportAccountList.substring(0, reportAccountList.length - 1) + ".";
                            }else {
                                queryShow += searchFields.value[index] + "=" + formInlineCriteria[index] + ".";
                            }
                        } else {
                            delete formInlineCriteria[index];
                        }
                    }
            }
            proxy.$axios.post("/auth/api/v1/user/cache", {
                userId: cookies.get('username'),
                funcId: funcIdCode.value,
                queryCriteria: JSON.stringify({
                    param: {
                        ...formInlineCriteria,
                    },
                    orderByDesc: null,
                    sortFieldName: null,
                    sortField: null,
                    pageSize: null,
                }),
                sortOrder: null,
                sortShow: null,
                paraName: parameter.value,
                queryShow: queryShow,
                pageShow: null,
                defaultInd: "",
            }).then((body) => {
                if (body.success) {
                    refreshCache();
                }
            });
        }
    }
}

const saveDefault = async () => {
    if (currentRow.value != null) {
        updateSearchParams(currentRow.value);
    }
    showParam.value = false;
}
const updateSearchParams = (row) => {
    if(!row||!row.queryCriteria){
        return ;
    }
    let obj = "";
    let filine = formInline;
    // if ( funcIdCode.value == 'MENU_RPT_GENERAT_R000101C' || funcIdCode.value =='MENU_RPT_GENERAT_R000201C') {
    //     filine = accountFormInline;
    // }
    for (var i in filine) {
        filine[i] = null;
    }
    obj = JSON.parse(row.queryCriteria);
    for (let key in obj.param) {
        if(key === 'reportAccount'){
            accountFormInline.reportAccounts = obj.param[key];
        }else {
            filine[key] = obj.param[key];
        }
        
    }
}
function exit() {
    showParam.value = false;
}
function changeDefaultInd(row) {
    let favoriteOid = row.userFavoritesSettingOid;
    for (var i in paramData.value) {
        if (paramData.value[i].userFavoritesSettingOid != row.userFavoritesSettingOid) {
            paramData.value[i].defaultInd = 'N';
        }
    }
    proxy.$axios.post("/auth/api/v1/user/cache/saveDefault?funcId=" + funcIdCode.value + "&oid=" + favoriteOid);
}
function deleteRow() {
    let favoriteOid = currentRow.value.userFavoritesSettingOid;
    if (favoriteOid) {
        proxy.$axios.delete("/auth/api/v1/user/cache?funcId=" + funcIdCode.value + "&oid=" + favoriteOid).then((body) => {
            if (body.success) {
                refreshCache();
            }
        });
    }
}

function handleSelectionChange(val) {
    currentRow.value = val
}

</script>

<style scoped>
.genratorForm {
    width: 900px !important;
}

.generator-form-item .ep-form-item__content {
    display: inline-flex;
}

.el-message--error{
  white-space:pre-line
}

.check-box .ep-form-item__label {
    min-height: 0px ;
}

.ep-radio /deep/ .ep-radio__inner {
    border-radius: 2px !important;
}

.ep-radio /deep/ .ep-radio__input.is-checked .ep-radio__inner {
    background-color: #ffffff;
    border-color: silver;
}

.ep-radio /deep/ .ep-radio__input.is-checked .ep-radio__inner::after {
    content: '';
    width: 7px;
    height: 3px;
    border: 2px solid black;
    border-top: transparent;
    border-right: transparent;
    text-align: center;
    display: block;
    position: absolute;
    top: 3px;
    left: 2px;
    transform: rotate(-45deg);
    border-radius: 0px;
    background: none;
}

.parameter-list {
    padding: 0px;
}

.parameter-list .ep-dialog__header {
    background-color: var(--ep-color-primary);
    ;
    padding: 0px 0px 0px 10px;
}

.parameter-list .ep-dialog__header .ep-dialog__title,
.parameter-list .ep-dialog__header .ep-dialog__close {
    color: white;
}

.parameter-list .ep-dialog__header button {
    height: 32px;
    width: 32px;
}

</style>