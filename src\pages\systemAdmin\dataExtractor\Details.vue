<template>
    <BaseDetails ref="details" :handleSave="handleSave" :reload="props.reload">
        <el-form :validateOnRuleChange="false" :disabled="formDisabled" ref="dataFormRef" style="width: 60%" :model="dataForm.form" status-icon>
            <FormRow style="width: 740px">
                
                    <UploadItem :show-file-list="false" class="upload-demo" drag :file-list="dataForm.form.fileList" :auto-upload="false" :on-change="handleUpload"
                        >
                        <el-icon><Upload /></el-icon>
                        <div class="el-upload__text"><em>Click</em> or drag file to this area to upload</div>
                    </UploadItem>
                
            </FormRow>
            <FormRow>
                <ElFormItemProxy label-width="190px" :label="$t('csscl.admin.dataExtractor.fileName')" prop="fileName">
                    <el-input v-model="dataForm.form.fileName" :disabled="true" style="width: 500px;" class="text-none" >
                        <template #append v-if="dataForm.form.logStatus=='Passed'">
                            <el-form :validateOnRuleChange="false" >
                                <el-button style="color:darkorange" type="primary" @click="handleDownload" :icon="Download"/>
                            </el-form>
                            <!-- <el-button type="primary" @click="handleDownload" :icon="Download" v-if="dataForm.form.fileName"/> -->
                        </template>
                    </el-input>
                </ElFormItemProxy>
            </FormRow>
            <FormRow>
              <ElFormItemProxy label-width="190px" :label="$t('csscl.admin.dataExtractor.uploadedDate')" prop="totalRecord">
                <el-col :span="4"><el-input v-model="dataForm.form.uploadedDate" :disabled="true" style="width: 150px;"/></el-col>
              </ElFormItemProxy>
            </FormRow>
            <FormRow>
                <ElFormItemProxy label-width="190px" :label="$t('csscl.admin.dataExtractor.totalRecords')" prop="totalRecord">
                    <el-col :span="4"><el-input v-model="dataForm.form.totalRecord" :disabled="true" style="width: 150px;"/></el-col>
                </ElFormItemProxy>
            </FormRow>
            <FormRow>
                <ElFormItemProxy label-width="190px" :label="$t('csscl.admin.dataExtractor.logStatus')" prop="logStatus">
                    <el-col :span="4"><el-input v-model="dataForm.form.logStatus" :disabled="true" style="width: 150px;"/></el-col>
                </ElFormItemProxy>
            </FormRow>
            <FormRow>
                <ElFormItemProxy label-width="190px" :label="$t('csscl.admin.dataExtractor.logDetails')" prop="logDetails">
                    <el-input v-model="dataForm.form.logDetailsDesc" type="textarea" rows="8" :disabled="true" style="width: 500px;" />
                </ElFormItemProxy>
            </FormRow>
        </el-form>
    </BaseDetails>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue';
import {Upload, Download} from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import { getOid } from '~/util/Function.js';
import BaseDetails from '~/pages/base/Details.vue';
import { downloadFile, showErrorMsg } from '~/util/Function.js';
import { timestampToDate, timestampToDateTime } from '~/util/DateUtils.js';
const { proxy } = getCurrentInstance();
const props = defineProps(['reload']);
const details = ref();
const formDisabled = ref(false);
const editRow = (row, disabled,newId) => {
    if(row?.isApproveDetail && disabled){
        dataForm.form = row?.afterImage;
        if(row.afterImage?.sysCreateDate){
            dataForm.form.sysCreateDate=timestampToDateTime(row.afterImage.sysCreateDate);
        }
        if(row.afterImage?.sysUpdateDate){
            dataForm.form.sysUpdateDate=timestampToDateTime(row.afterImage.sysUpdateDate);
        }
        if(row.afterImage?.uploadedDate){
            dataForm.form.uploadedDate = timestampToDate(row.afterImage.uploadedDate);
        }
        details.value.currentRow = dataForm.form;
    } else {
        const oid = newId || (row?.currentOid && getOid(row, disabled, proxy));
        if (oid) {
            proxy.$axios.get("/rptsched/api/v1/data/extractor?rptDataExtractorOid=" + oid + "&modeEdit=Y").then((body) => {
                if(body.success) {
                    dataForm.form = body.data;
                    details.value.currentRow = body.data;
                    dataForm.form.fileList = [];
                    dataForm.form.logDetailsDesc = proxy.$t(dataForm.form.logDetails);
                }
                details.value.initWatch(dataForm);
            });
        } else {
            dataForm.form = {};
            dataForm.form.fileList = [];
            details.value.initWatch(dataForm);
        }
    }
}

const showDetails = (row, isdoubleCheck) => {
    if(isdoubleCheck||row.recordStatus==='PA'){
        formDisabled.value = true;
    }else{
        formDisabled.value = false;
    }

    details.value.showDetails(row, formDisabled.value)

    dataForm.form = {};
    details.value.currentRow={};
    editRow(row, isdoubleCheck);
}

defineExpose({
    details,
    editRow,
    showDetails,
});
// --------------------------------------------

// interface DataForm {
//     rptDataExtractorOid: string,
//     fileName: string
//     totalRecord: number
//     logStatus: string
//     logDetails: string
//     fileList: []
// }

const dataFormRef = ref<FormInstance>()
const dataForm = reactive({
    form: {
        rptDataExtractorOid: "",
        fileName: "",
        uploadedDate: "",
        totalRecord: "",
        logStatus: "",
        logDetails: "",
        fileList: [],
    }
})

const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate((valid, fields) => {
        if (valid) {
            console.log('submit!')
        } else {
            console.log('error submit!', fields)
        }
    })
}

const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.resetFields()
}

const options = Array.from({ length: 10000 }).map((_, idx) => ({
    value: `${idx + 1}`,
    label: `${idx + 1}`,
}))

const handleUpload = async (file) => {
    dataForm.form.fileList = [];
    dataForm.form.fileList.push(file);
    const formData = new FormData();
    if (dataForm.form.fileList.length > 0) {
        formData.append('file', dataForm.form.fileList[0].raw);
    }
    if (dataForm.form?.rptDataExtractorOid) {
        formData.append('rptDataExtractorOid', dataForm.form.rptDataExtractorOid);
    }
    let msg = {};
    msg = await proxy.$axios.post("/rptsched/api/v1/data/extractor/upload", formData);
    editRow(null,null,msg.data);
    return msg.success;
}

const handleSave = async () => {
    const logStatus = dataForm.form.logStatus
    if(!logStatus||logStatus!='Passed'){
      showErrorMsg(proxy.$t('csscl.admin.dataExtractor.logStatus')+": "+(logStatus?logStatus:''));
      return false;
    }
    details.value.writebackId(dataForm.form.rptDataExtractorOid);
    return true;
}

function handleDownload(){
    const row = dataForm.form;
    var params = {
        rptDataExtractorOid: row.rptDataExtractorOid,
        fileName: row.fileName,
    }
    downloadFile("/rptsched/api/v1/data/extractor/download", params);
}

</script>

<style></style>