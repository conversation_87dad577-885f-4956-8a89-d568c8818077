import OIDCClient from 'oidc-frontend-integrator-lib'
import {
	VITE_AUTHORIZATION_URL,
	VITE_TOKEN_URL,
	VITE_LOGOUT_URL,
	VITE_REFRESH_URL,
	VITE_OIDCURL
} from './env-config'

const oidcClient = new (OIDCClient as any)({
	authorizationUrl: VITE_AUTHORIZATION_URL,
	tokenUrl: VITE_TOKEN_URL,
	logoutUrl: VITE_LOGOUT_URL,
	refreshUrl: VITE_REFRESH_URL,
	oidcUrl: VITE_OIDCURL
})

export default oidcClient