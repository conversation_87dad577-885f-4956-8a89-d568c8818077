export interface FormSchema {
  field?: string | undefined; // 字段名
  label: string; // 标签
  component: string; // 组件类型,支持 ElementPlus组件 及支持 自定义组件
  colProps?: Record<string, any>; // 栅格列属性
  componentProps?: Record<string, any>; // 组件绑定属性
  optionProps?: Record<string, any>; // options 属性（用于Select）
  options?: { [key: string]: any }[]; // 下拉选项（用于Select）
  required?: boolean; // 是否必填
  rules?: any[]; // 校验规则
  slot?: string; // 自定义slot名称
  columnProps?: Record<string, any>; // 表格列属性
  formItemProps?: Record<string, any>; // 表单item属性
  maxlength?: number | string; // 最大输入长度
}

export interface FormSchemaVO extends FormSchema {
  field: string;
}

export interface FormItem {
  schema: FormSchema;
  value: any;
  onChange: (value: any) => void;
}
export interface FormVO {
  code?: string;
  codeDesc?: string;
  var1?: string;
  var2?: string;
  var3?: string;
  var4?: string;
  var5?: string;
  var6?: string;
}

export interface TableVO extends FormVO {}
