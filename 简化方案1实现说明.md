# 简化方案1：通过标记变量判断CommonSearchInput请求来源

## 实现思路

使用最简单的方式：通过一个布尔变量标记请求来源，当RemarkForm触发时设置标记，根据标记动态调整CommonSearchInput的请求参数。

## 核心实现

### 1. 在general.vue中添加简单的标记变量

```javascript
// 简单的方案1：通过标记变量判断请求来源
const isFromRemarkForm = ref(false);

// 设置来源为RemarkForm的方法
const setFromRemarkForm = () => {
    isFromRemarkForm.value = true;
}

// 获取动态参数
const getCustodyMarketParams = () => {
    const baseParams = { "searchType": "custodyMarket", "status": null };
    
    if (isFromRemarkForm.value) {
        return JSON.stringify({
            ...baseParams,
            "source": "remarkForm"
        });
    }
    
    return JSON.stringify(baseParams);
}

const showDetails = () => {
    // 重置标记为正常状态
    isFromRemarkForm.value = false;
    cashMgmtServiceIndChange(ruleForm.form.cashMgmtServiceInd)
}
```

### 2. 修改CommonSearchInput使用动态参数

```vue
<CommonSearchInput v-model="ruleForm.form.custodyMarket"
    commType="CUSTODY_MARKET"
    url="/datamgmt/api/v1/searchinput"
    :params="getCustodyMarketParams()"
    codeTitle="csscl.acctCode.custodyMarket"
    style="width:750px" 
    :disabled="ruleForm.form.custodyLevel === 'GC'" />
```

### 3. 暴露setFromRemarkForm方法

```javascript
defineExpose({
    handleSave,
    showDetails,
    setFromRemarkForm,
});
```

### 4. 在Details.vue中调用设置方法

```javascript
const viewOriginalForm = (pendingOid, isDisabled) => {
    debugger;
    formDisabled.value = isDisabled;
    
    // 设置来源为RemarkForm
    if (generalRef.value && generalRef.value.setFromRemarkForm) {
        generalRef.value.setFromRemarkForm();
    }
    
    // 其他原有逻辑...
}
```

## 工作原理

1. **初始状态**：`isFromRemarkForm = false`，CommonSearchInput发送正常参数
2. **RemarkForm触发**：调用`setFromRemarkForm()`设置`isFromRemarkForm = true`
3. **动态参数**：`getCustodyMarketParams()`根据标记返回不同参数
4. **自动重置**：`showDetails()`方法重置标记为false

## 请求参数对比

### 正常请求
```json
{ "searchType": "custodyMarket", "status": null }
```

### RemarkForm请求
```json
{ "searchType": "custodyMarket", "status": null, "source": "remarkForm" }
```

## 后端处理

```javascript
// 后端可以通过检查source字段判断来源
if (params.source === 'remarkForm') {
    // 来自RemarkForm的请求处理
    return handleRemarkFormRequest(params);
} else {
    // 正常请求处理
    return handleNormalRequest(params);
}
```

## 优点

1. **极简实现**：只用一个布尔变量和几个简单方法
2. **无侵入性**：不需要修改CommonSearchInput和RemarkForm组件
3. **易于理解**：逻辑清晰，代码简洁
4. **自动重置**：通过showDetails方法自动重置状态
5. **可扩展**：可以轻松添加更多来源标记

## 调用流程

1. RemarkForm点击按钮 → BaseDetails.viewOriginalForm → Details.viewOriginalForm
2. Details.viewOriginalForm调用general.setFromRemarkForm()设置标记
3. CommonSearchInput使用getCustodyMarketParams()获取动态参数
4. 后端根据source字段判断来源并处理
5. general.showDetails()重置标记为正常状态

这个方案非常简单，只需要几行代码就能实现需求，是最优雅的解决方案。
