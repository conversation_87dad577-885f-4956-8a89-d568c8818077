<template>
    <el-container class="section-title-wrapper">
      <div class="title-wrapper">
        <div class="title-text" :style="{ color: titleColor }"> {{ title }} </div>
        <div class="title-underline" :style="{ backgroundColor: lineColor }"></div>
      </div>
    </el-container>
  </template>
  
  <script setup>
  defineProps({
    title: {
      type: String,
      required: true
    },
    titleColor: {
      type: String,
      default: '#1E90FF'
    },
    lineColor: {
      type: String,
      default: '#1E90FF'
    }
  });
  </script>
  
  <style scoped>
  .section-title-wrapper {
    display: flex;
    flex-direction: flex-start;
    gap: 10px;
    padding-inline: 10px;
    margin-bottom: 15px;
  }
  
  .title-wrapper {
    display: flex;
    flex-direction: column;
    gap: 5px;
    width: 100%;
  }
  
  .title-text {
    font-size: 18px;
    font-weight: bold;
  }
  
  .title-underline {
    height: 2px;
    width: 100%;
  }
  </style>