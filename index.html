<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
  <meta http-equiv="Pragma" content="no-cache" />
  <meta http-equiv="Expires" content="0" />
  <link rel="icon" href="/favicon.svg" />
  <!-- <script type="text/javascript"  src="/env.js"></script> -->
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Bank of China (Hong Kong) Limited</title>
  <style>
  </style>
</head>

<body style="overflow: auto ;">
  <div id="app"></div>
  <script type="module" src="/src/main.ts"></script>
  <script type="text/javascript">
    // window.addEventListener("resize", () => {
    //   resize();
    // });
    // window.addEventListener("load", () => {
    //   resize();
    // });
    // function resize() {
    //   let cw = window.outerWidth;
    //   if (document.styleSheets[0].rules.length > 0) {
    //     document.styleSheets[0].removeRule(0);
    //   }
    //   if (cw >= 1920) {
    //     document.styleSheets[0].insertRule('html {zoom: 1}', 0);
    //   } else {
    //     document.styleSheets[0].insertRule('html {zoom: ' + (cw / 1920) + '}', 0);
    //   }
    // }
// Start SK-COMMON-0083, Tom.Li, 2024/08/19
  window.addEventListener('load', function(e,a) {
    if ( sessionStorage.getItem("isLoggedIn") ) {
      let obj = JSON.parse(localStorage.getItem("loginedPage"));
      obj[sessionStorage.getItem("pageName")] = 'Y';
      localStorage.setItem("loginedPage", JSON.stringify(obj));
    }
  });
  window.addEventListener('unload', function(e,a) {
    if ( sessionStorage.getItem("isLoggedIn") ) {
      let obj = JSON.parse(localStorage.getItem("loginedPage"));
      delete obj[sessionStorage.getItem("pageName")];
      localStorage.setItem("loginedPage", JSON.stringify(obj));
    }
  });
// End SK-COMMON-0083, Tom.Li, 2024/08/19
  </script>
</body>
<style>
  .el-button {
    padding: 5px 8px;
    height: unset;
    border-radius: 5px !important;
  }
  .el-button--primary {
    color: #ffffff;
    border-color: #b31a25;
    background-color: #b31a25;
    outline: none;
  }
  .el-button:hover {
    color: #ffffff;
    border-color: #ca5f66;
    background-color: #ca5f66;
    outline: none;
  }
  .el-message-box {
    padding: 0px;
    --el-messagebox-border-radius: 0px;
  }
  .el-message-box__title {
    font-size: 18px;
    line-height: 28px;
    color: #ffffff;
  }
  .el-message-box .el-message-box__header {
    background-color: #b31a25;
    height: 28px;
    padding-bottom: 0px;
  }

  .el-message-box .el-message-box__header .el-message-box__title {
    padding-left: 10px;
  }

  .el-message-box .el-message-box__headerbtn {
    height: 30px !important;
    width: 30px !important;
  }
  .el-message-box .el-message-box__headerbtn .el-message-box__close {
      color: white;
    }

  .el-message-box .el-message-box__content {
    padding: 10px;
    color: #000000;
  }

  .el-message-box .el-message-box__btns {
    padding: 0px 0px 10px 0px;
    justify-content: center;
  }

  .el-message-box .el-message-box__btns .el-button {
    padding: 5px 20px;
  }

  .el-message-box__status.el-message-box-icon--warning {
    color: #f2711c;
  }
</style>
</html>