import { getCurrentInstance } from 'vue';
import { ElMessageBox } from 'element-plus';


/**
 * 检查 array 某个对象值是否相同
 * 
 * @param {*} list  数组
 * @param {*} propName 对象key
 * @returns 
 */
export const checkPropEqual = (list, propName) => {
    if (!Array.isArray(list) || list.length === 0) return false;

    const firstValue = list[0][propName];
    return list.every(item => item[propName] === firstValue);
}

/**
 * 检查 array 某个对象是否为空
 * 
 * @param {*} list 数组
 * @param {*} propName 对象key
 * @returns 
 */
export const checkPropEmpty = (list, propName) => {
    if (!Array.isArray(list) || list.length === 0) return false;

    return list.every(item => !item[propName] || item[propName] === '');
}

/**
 * 检查 array 某个对象值是否匹配
 * 
 * @param {*} list 数组
 * @param {*} propName 对象key
 * @param {*} propValue 对象Value
 * @returns 
 */
export const checkPropValueEqual = (list, propName, propValue) => {
    if (!Array.isArray(list) || list.length === 0) return false;

    let res = list.every((item, index, ary) => {
        return item[propName] === propValue;
    });
    return res;
}

/**
 * 检查 array 某个对象值是否匹配
 * 
 * @param {*} list 数组
 * @param {*} propName 对象key
 * @param {*} propValueList 对象Value数组
 * @returns 
 */
export const checkPropValueIncludes = (list, propName, propValueList) => {
    if (!Array.isArray(list) || list.length === 0) return false;

    let res = list.every((item, index, ary) => {
        return propValueList.includes(item[propName]);
    });
    return res;
}