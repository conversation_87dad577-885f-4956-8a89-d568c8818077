{"name": "element-plus-vite-starter", "private": true, "version": "0.1.0", "scripts": {"dev": "vite", "build": "vite build --mode production", "build:stage": "vite build --mode staging", "build:sit1": "vite build --mode sit1", "build:sit3": "vite build --mode sit3", "build:usmf": "vite build --mode usmf", "build:usmk": "vite build --mode usmk", "generate": "vite-ssg build", "preview": "vite preview", "typecheck": "vue-tsc --noEmit"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "axios": "1.6.8", "axios-retry": "4.4.2", "echarts": "5.5.0", "element-plus": "^2.6.3", "jwt-decode": "3.1.2", "moment": "2.30.1", "pinia": "2.2.0", "uuid": "10.0.0", "vue": "3.4.21", "vue-i18n": "9.11.0", "vue-router": "4.3.0", "vue3-cookies": "1.0.6"}, "devDependencies": {"@babel/parser": "7.26.1", "@iconify-json/ep": "1.1.15", "@types/node": "20.12.3", "@unhead/dom": "1.11.13", "@unhead/schema": "1.11.13", "@unhead/shared": "1.11.13", "@unhead/vue": "1.11.13", "@vitejs/plugin-vue": "5.0.4", "sass": "1.72.0", "typescript": "5.4.3", "unocss": "0.58.9", "unplugin-auto-import": "0.17.8", "unplugin-vue-components": "0.26.0", "vite": "^5.2.7", "vite-ssg": "0.23.6", "vue-tsc": "2.0.7"}, "license": "MIT"}