<template>
    <BaseDetails ref="details" :handleSave="handleSave" :reload="props.reload" :viewOriginalForm="viewOriginalForm" >
        <el-form :validateOnRuleChange="false" :disabled="formDisabled" ref="ruleFormRef" style="width: 100%" :model="swiftForm.form"
            status-icon>
            <FormRow>
                <FormItemSign :detailsRef="details" :label="$t('csscl.incSwift.messageID')" showBef="false"
                    prop="messageId">
                    <el-input :disabled="formDisabled"  v-model="swiftForm.form.messageId" style="width: 300px;"/>
                </FormItemSign>
                <FormItemSign :detailsRef="details" :label="$t('csscl.incSwift.pendingQueueStatus')" prop="status" selectType="PEND_QUE_STS">
                    <Select :disabled="formDisabled" v-model="swiftForm.form.status" type="PEND_QUE_STS" />
                </FormItemSign>
            </FormRow>
            <FormRow>
                <FormItemSign :detailsRef="details" :label="$t('csscl.incSwift.messageType')" showBef="false" prop="messageType">
                    <el-input :disabled="formDisabled"  v-model="swiftForm.form.messageType" style="width: 100px;"/>
                </FormItemSign>
            </FormRow>
            <FormRow>
                <FormItemSign :detailsRef="details" :label="$t('csscl.incSwift.swiftMessage')" showBef="false" prop="swiftMsgRaw">
                  <!--  Start fix:BAU-70 2025-04-17   -->
                  <el-input v-if="formDisabled && swiftForm.form.parseSwiftMsgRaw"  type="textarea" :rows="17" v-model="swiftForm.form.parseSwiftMsgRaw" style="width: 1186px; overflow-y: true;"/>
                  <el-input v-else :disabled="formDisabled"  type="textarea" :rows="17" v-model="swiftForm.form.swiftMsgRaw" style="width: 1186px; overflow-y: true;"/>
                  <!--  End fix:BAU-70  LiHaiBin 2025-04-17 -->
                </FormItemSign>
            </FormRow>
            <FormRow>
                <FormItemSign :detailsRef="details" :label="$t('csscl.incSwift.failReason')" showBef="false" prop="errorLog">
                    <el-input :disabled="formDisabled"  type="textarea" :rows="8" v-model="swiftForm.form.errorLog" style="width: 1186px; overflow-y: true;"/>
                </FormItemSign>
            </FormRow>
           
        </el-form>
    </BaseDetails>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue';
import type { FormInstance, FormRules } from 'element-plus'
import BaseDetails from '~/pages/base/Details.vue';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import { getOid, saveMsgBox } from '~/util/Function.js';
const { proxy } = getCurrentInstance();
const props = defineProps(['reload']);
const details = ref();
const formDisabled = ref(false);
const editRow = (row) => {
    // formDisabled.value = isDoubleCheck;
    const oid = row.pendingOid!=null?row.pendingOid:row.currentOid
    proxy.$axios.get("/datamgmt/api/v1/swiftmsg?swiftMsgId=" + oid ).then((body) => {
        if (body.success) {
            swiftForm.form = body.data;
            details.value.currentRow = body.data;
        }
    });
}

const viewOriginalForm = (pendingOid, isDisabled) => {
  formDisabled.value = isDisabled;
  proxy.$axios.get("/datamgmt/api/v1/swiftmsg?swiftMsgId="+pendingOid).then((body) => {
    if(body.success) {
      swiftForm.form = body.data;
      details.value.currentRow.beforeImage = body.data.beforeImage;
    }
  });
}

const handleSave = async (searchValid, isOnlyValidate, unPopping) => {
    let result = await ruleFormRef.value.validate((valid, fields) => {
        if (valid) {

        } else {
            console.log('error submit!', fields)
        }
    });
    if (isOnlyValidate) {
        return result;
    }
    if (result && searchValid && await saveMsgBox(unPopping)) {
        if (swiftForm.form.userOid) {
            const msg = await proxy.$axios.patch("/auth/api/v1/user", {
                ...swiftForm.form,
            });
            return msg.success;
        } else {
            const msg = await proxy.$axios.post("/auth/api/v1/user", {
                ...swiftForm.form,
            });
            return msg.success;
        }
    }
    return false;
}
const showDetails = (row, isdoubleCheck) => {
    isdoubleCheck = true;
    if(isdoubleCheck || row.recordStatus==='PA'){
        formDisabled.value = true;
        details.value.showDetails(row, true)
    }else{
        formDisabled.value = false;
        details.value.showDetails(row, false)
    }
    // formDisabled.value = disabled;
    details.value.showDetails(row, formDisabled.value)
    editRow(row, isdoubleCheck);
}
defineExpose({
    details,
    editRow,
    showDetails,
});
// --------------------------------------------

interface SwiftForm {
    messageID:string
    messageType:string
    swiftMessage:string 
    errorLog:string
}

const ruleFormRef = ref<FormInstance>()
const swiftForm = reactive({
    form: {
        messageId:'',
        messageType:'',
        swiftMsgRaw:'',
        parseSwiftMsgRaw:'',
        errorLog:''
    }
})

const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate((valid, fields) => {
        if (valid) {
            console.log('submit!')
        } else {
            console.log('error submit!', fields)
        }
    })
}

const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.resetFields()
}

const options = Array.from({ length: 10000 }).map((_, idx) => ({
    value: `${idx + 1}`,
    label: `${idx + 1}`,
}))

</script>

<style></style>