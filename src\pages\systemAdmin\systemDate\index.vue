<template> 
  <!-- <BasePanel :searchParams="searchParams" url="/datamgmt/api/v1/sysdate/list" :params="{ modeEdit: 'Y' }" -->
  <BasePanel :searchParams="searchParams" url="/datamgmt/api/v1/bank/holiday/list" :params="{ modeEdit: 'Y' }" :rules="rules"
    :showDetails="showDetails" :deleteRow="deleteRow" :editRow="editRow" ref="tableRef" :sortProp="{}" :beforeSearch="beforeSearch">
    <template v-slot:searchPanel="slotProps">
      <FormRow>
        <ElFormItemProxy label-width="230" :label="$t('common.title.opCtryRegionCode')" prop="opCtryRegionCode">
          <CtryRegionSearchInput v-model="slotProps.form.opCtryRegionCode" showDesc="false" style="width: 120px" opCtryRegion />
        </ElFormItemProxy>
        <ElFormItemProxy>
          <ElFormItemProxy label-width="150" :label="$t('csscl.admin.sysDate.holidayDateFrom')" prop="lowestSearchHolidayDt">
            <DateItem :validate-event="false" v-model="slotProps.form.lowestSearchHolidayDt"
                      :title="$t('message.earlier.equal.dateto', [fieldsDtl.fields.lowestSearchHolidayDt, fieldsDtl.fields.searchHolidayDt]) + '\r' +
                        $t('message.date.range.error', [366] )" />
            </ElFormItemProxy>
            <ElFormItemProxy label-width="45" :label="$t('common.title.date.to')" :hideLabel="fieldsDtl.fields.searchHolidayDt" prop="searchHolidayDt">
              <DateItem :validate-event="false"
                        v-model="slotProps.form.searchHolidayDt"
                        :title="$t('message.date.range.error', [366] )"
              />
          </ElFormItemProxy>
        </ElFormItemProxy>
        <ElFormItemProxy></ElFormItemProxy>
      </FormRow>
    </template>
    <template v-slot:tableColumn>
      <el-table-column align="left" header-align="center" sortable="custom" prop="opCtryRegionCode" :label="$t('common.title.opCtryRegionCode')" width="450" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="holidayDate" :label="$t('csscl.admin.sysDate.holidayDate')" width="450" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="status" :label="$t('common.title.status')" width="400" >
        <template #default="scope">
          {{ getCommonDesc('STATUS', scope.row.status) }}
        </template>
      </el-table-column>
      <el-table-column align="left" header-align="center" sortable="custom" prop="recordStatus" :label="$t('common.title.recordStatus')">
        <template #default="scope">
          {{ getCommonDesc('RECORD_STATUS', scope.row.recordStatus) }}
            <span v-if="scope.row.recordStatus&&scope.row.recordStatus!=='A' ">
              for  {{ getCommonDesc('MKCK_ACTION', scope.row.mkckAction) }}
            </span>   
        </template>
      </el-table-column>
    </template>
  </BasePanel>
  <Details ref="detailsRef" :reload="reload" />
</template>

<script lang="ts" setup>
import { ref, getCurrentInstance, reactive } from 'vue';
import type { FormRules } from 'element-plus'
import BasePanel from '~/pages/base/index.vue'
import Details from './Details.vue'
import CtryRegionSearchInput from '~/pages/base/CtryRegionSearchInput.vue'
import  { getCommonDesc, getOid, checkDateFromTo, checkInputDate } from '~/util/Function.js';
import { commonRules, showValidateMsg } from '~/util/Validators.js';

const beforeSearch = async (e) => {
  // Start SIR-WZC-107,amor,20240816
  if (!beforeSearch.first &&(!searchParams.opCtryRegionCode || !searchParams.lowestSearchHolidayDt || !searchParams.searchHolidayDt) ){
     // End SIR-WZC-107,amor,20240816
    beforeSearch.first = 1;
    return false;
  }
  //
  // let result = await tableRef.value.formRef.validate((valid, fields) => {
  //   if (!valid) {
  //     showValidateMsg(fieldsDtl, fields);
  //   }
  // });
  // if (!result) {
  //   return false;
  // }
  //
  // let msg = checkDateFromTo(proxy, tableRef.value.formInline.lowestSearchHolidayDt, tableRef.value.formInline.searchHolidayDt, proxy.$t('csscl.admin.sysDate.holidayDateFrom'));
  // if(msg){
  //   return msg;
  // }
  //
  // msg = checkInputDate(proxy, tableRef.value.formInline.lowestSearchHolidayDt, tableRef.value.formInline.searchHolidayDt, 366);
  // return msg;
}

const { proxy } = getCurrentInstance()
// Start SIR-WZC-107,amor,20240816
const searchParams = {
  // End SIR-WZC-107,amor,20240816
  //顺序和上面绑定参数一致
  opCtryRegionCode:"",
  lowestSearchHolidayDt:"",
  searchHolidayDt:"",
};

const tableRef = ref();
const detailsRef = ref();

const fieldsDtl = {
  fields:{
    opCtryRegionCode: proxy.$t('common.title.opCtryRegionCode'),
    lowestSearchHolidayDt: proxy.$t('csscl.admin.sysDate.holidayDateFrom'),
    searchHolidayDt: proxy.$t('csscl.admin.sysDate.holidayDateTo'),
  }
}

const showDetails = (row, disabled) => {
  detailsRef.value.showDetails(row, disabled);
}
const deleteRow = (row) => {
  proxy.$axios.delete("/datamgmt/api/v1/bank/holiday?bankHolidayId="+ getOid(row, false,null,true)).then((body) => {
    if (body.success) {
      tableRef.value.load();
    }
  });
}
const editRow = (row) => {
  detailsRef.value.editRow(row);
}
const reload = () => {
  tableRef.value.load();
}
//-------------------------------
interface RuleForm {
  opCtryRegionCode: String
  lowestSearchHolidayDt: Date
  searchHolidayDt: Date
}
const rules = reactive<FormRules<RuleForm>>({
  opCtryRegionCode: [
    commonRules.required,
  ],
  lowestSearchHolidayDt: [
    commonRules.required,
    commonRules.earlierEquDt(()=>{ return searchParams.searchHolidayDt }, fieldsDtl.fields.searchHolidayDt ),
    commonRules.diffDate(366,()=>{ return searchParams.searchHolidayDt }, fieldsDtl.fields.searchHolidayDt),
  ],
  searchHolidayDt: [
    commonRules.required,
  ],
})

</script>

<style>

</style>