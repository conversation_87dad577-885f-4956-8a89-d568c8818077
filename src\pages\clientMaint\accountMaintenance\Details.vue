<template> 
    <BaseDetails ref="details" :handleSave="handleSave" :reload="props.reload" :viewOriginalForm="viewOriginalForm">
        <el-form :validateOnRuleChange="false" :disabled="true" ref="ruleFormRef" style="width: 100%" :model="ruleForm.form" status-icon>
            <FormRow>
                <FormItemSign :detailsRef="details" label-width="190" :label="$t('csscl.acctCode.clientMasterOid')" style="width:1100px">
                    <el-input v-model="ruleForm.form.clientCode" style="width:200px" />
                </FormItemSign>
                <FormItemSign :detailsRef="details" label-width="110" :label="$t('csscl.acctCode.accountStatus')" prop="accountStatus">
                    <Select v-model="ruleForm.form.accountStatus" type="ACCOUNT_STATUS" />
                </FormItemSign>
            </FormRow>
            <FormRow>    
                <FormItemSign :detailsRef="details" label-width="190" :label="$t('csscl.acctCode.clientName')" prop="clientName1">
                    <el-input :value="ruleForm.form.clientName1 + ' ' + ruleForm.form.clientName2" style="width:750px" class="text-none" />
                </FormItemSign>
                <ElFormItemProxy></ElFormItemProxy>
            </FormRow>
            <FormRow>
                <FormItemSign :detailsRef="details" label-width="190" :label="$t('csscl.acctCode.clientShortName')" prop="clientShortName">
                    <el-input v-model="ruleForm.form.clientShortName" style="width:650px" class="text-none" />
                </FormItemSign>
                <ElFormItemProxy></ElFormItemProxy>
            </FormRow>
            <FormRow>
                <FormItemSign :detailsRef="details" label-width="190" :label="$t('csscl.acctCode.clientAccountOid')" prop="tradingAccountCode">
                    <el-input v-model="ruleForm.form.tradingAccountCode" style="width:400px" />
                </FormItemSign>
                <ElFormItemProxy></ElFormItemProxy>
            </FormRow>
            <FormRow>
                <FormItemSign :detailsRef="details" label-width="190" :label="$t('csscl.acctCode.accountName')" prop="accountName1">
                    <el-input :value="ruleForm.form.accountName1 + ' ' + ruleForm.form.accountName2" class="text-none" style="width:750px" />
                </FormItemSign>
                <ElFormItemProxy></ElFormItemProxy>
            </FormRow>
            <FormRow>
                <FormItemSign :detailsRef="details" label-width="190" :label="$t('csscl.acctCode.accountShortName')" prop="accountShortName">
                    <el-input v-model="ruleForm.form.accountShortName" class="text-none" style="width:650px" />
                </FormItemSign>
                <ElFormItemProxy></ElFormItemProxy>
            </FormRow>
        
            <el-tabs @tab-change="tabsClick" type="card" v-model="activeName" style="margin:15px 30px 0">
                <el-tab-pane :label="$t('csscl.acctCode.general')" name="tab1" :lazy="true" >
                    
                    <general :details="details" :ruleForm="ruleForm" :disabled="formDisabled" ref="generalRef" />
                
                </el-tab-pane>
                <el-tab-pane :label="$t('csscl.acctCode.channel')" name="tab2" :lazy="true">
                    
                    <channel :details="details" :ruleForm="ruleForm" :disabled="formDisabled" ref="channelRef" />
                    
                </el-tab-pane>
                <el-tab-pane :label="$t('csscl.acctCode.settlement')" name="tab3" :lazy="true">
                    
                    <settlement :details="details" :ruleForm="ruleForm" :disabled="formDisabled" ref="settlementRef" />
                    
                </el-tab-pane>
                <el-tab-pane :label="$t('csscl.acctCode.ca')" name="tab4" :lazy="true">
                    
                    <ca :details="details" :ruleForm="ruleForm" :disabled="formDisabled" ref="caRef" />

                </el-tab-pane>
                <el-tab-pane :label="$t('csscl.acctCode.cashAccount')" name="tab5" :lazy="true">

                    <cashAccount :details="details" :ruleForm="ruleForm" :disabled="formDisabled" ref="cashAccountRef" />

                </el-tab-pane>
                <el-tab-pane :label="$t('csscl.acctCode.documentDesc')" name="tab6" :lazy="tabLazy">
                
                    <documentDesc :details="details" :ruleForm="ruleForm" :disabled="formDisabled" :lightyellowTabs="lightyellowTabs"  ref="documentRef" />
                
                </el-tab-pane>
            </el-tabs>
        </el-form>
    </BaseDetails>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance, nextTick } from 'vue';
import type { FormInstance } from 'element-plus'
import BaseDetails from '~/pages/base/Details.vue';
import FormItemSign from '~/pages/base/FormItemSign.vue';

import ca from './ca.vue';
import cashAccount from './cashAccount.vue';
import channel from './channel.vue';
import documentDesc from './documentDesc.vue';
import general from './general.vue';
import settlement from './settlement.vue';
import {removeObjectNull, getOid, rowCompareWithBeforeImage, saveMsgBox, getDate, showErrorMsg} from '~/util/Function.js';
import { timestampToDate } from '~/util/DateUtils.js';
import { addModifiedFlag, clearEnterObjects } from "~/util/ModifiedValidate";

const { proxy } = getCurrentInstance();
const props = defineProps(['reload']);
const details = ref();
const formDisabled = ref(false);
const ruleFormRef = ref({});
const ruleForm = reactive ({
    form : {
        clientGroupCode:null,
        fundMgrCode:null,
        proxyCtryCode:null,
        clientFundId:null,
        custodyLevel:'',
        custodyMarket:'',
        omnibusSegregate:'',
        cashMgmtServiceInd:null,
        startDate:null,
        endDate:null,
        clientCode:'',
        clientName1:'',
        clientName2:'',
        clientShortName:'',
        opearteOid:'',
        dateOpen:null,
        approveNumber:1,
        isApproveDetail:false,
        clientAccCaInstructionPrefList: [],
        clientAccCaTaxSetupList: [],
        caEdit: ''
    }
});
const activeName = ref("tab1");
const tabLazy = ref(true);
const tempKey = ref("");
const caRef = ref();
const cashAccountRef = ref();
const channelRef = ref();
const documentRef = ref();
const generalRef = ref();
const settlementRef = ref();
let lightyellowTabs = [];
// Start, 2025/07/25, LiKunBiao, SET-142
const validFields = ['clientGroupCode', 'fundMgrCode', 'proxyCtryCode', 'clientFundId', 'cashMgmtServiceInd', 'startDate', 'endDate', 
'custodyLevel', 'custodyMarket', 'omnibusSegregate'];
// Start, 2025/07/25

const editRow = (row, disabled, newId) => {
    if(row?.isApproveDetail && disabled){
        ruleForm.form = row.afterImage;
        if(row.afterImage.endDate){
            ruleForm.form.endDate=timestampToDate(row.afterImage.endDate);
        }
        if(row.afterImage.startDate){
            ruleForm.form.startDate=timestampToDate(row.afterImage.startDate);
        }
        if(row.afterImage.dateOpen){
            ruleForm.form.dateOpen=timestampToDate(row.afterImage.dateOpen);
        }
        let oid = row?.eventPkey;
        proxy.$axios.get("/datamgmt/api/v1/account?objectId="+oid + "&modeEdit=Y").then((body) => {
            if(body.success) {
                let data = (body.data)
                ruleForm.form.clientCode = data?.clientCode;
                ruleForm.form.clientName1 = data?.clientName1;
                ruleForm.form.clientName2 = data?.clientName2;
                ruleForm.form.clientShortName = data?.clientShortName;
                details.value.currentRow = ruleForm.form;
                tabLazy.value = false;
                rowCompareWithBeforeImage(ruleForm.form, "tab-tab1", lightyellowTabs, validFields);
                hightlightChannel();
                //Start, CA Phase2, LingZeFei, ********
                caRef.value?.initData(data);
                //End, CA Phase2, LingZeFei, ********
            }
            details.value.initWatch(ruleForm);
        });
        ruleForm.form.opearteOid = oid;
        ruleForm.form.isApproveDetail = true;
        ruleForm.form.approveNumber = row?.approveNumber;
    } else {
        const oid = newId || (row?.currentOid && getOid(row, disabled, proxy));
        if (oid) {
            proxy.$axios.get("/datamgmt/api/v1/account?objectId="+oid + "&modeEdit=Y").then((body) => {
                if(body.success) {
                    let data = (body.data)
                    ruleForm.form = data;
                    ruleForm.form.opearteOid = oid;
                    ruleForm.form.tempKey = oid + "_" + tempKey.value;
                    details.value!.currentRow = data;
                    tabLazy.value = false;
                    rowCompareWithBeforeImage(ruleForm.form, "tab-tab1", lightyellowTabs, validFields);
                    hightlightChannel();
                    //Start, CA Phase2, LingZeFei, ********
                    caRef.value?.initData(data);
                    //End, CA Phase2, LingZeFei, ********
                }
                details.value.initWatch(ruleForm);
            });
        }else{
            details.value.initWatch(ruleForm);
        }
    }
}

const hightlightChannel = async () => {
    if (ruleForm.form.recordStatus != 'A') {
      const msg = await proxy.$axios.get("/datamgmt/api/v1/account/channel/identifier?objectId="+ruleForm.form.opearteOid);
      if (msg.success) {
        if (msg.data > 0) {
          rowCompareWithBeforeImage({a:1, master:1}, "tab-tab2", lightyellowTabs)
          addModifiedFlag(ruleForm.form, 'identifierPrefVPOs')
        }
      }
    }
}

const handleSave = async (searchValid, isOnlyValidate, unPopping) => {
    if (documentRef.value.editDis) {
        showErrorMsg("The sub table data has not been saved. Please confirm if you want to leave.");
        return false;
    }
    let result = await ruleFormRef.value.validate((valid, fields) => {
        if (valid) {

        } else {
            console.log('error submit!', fields)
        }
    });
    if (result && generalRef.value) {
        result = await generalRef.value?.handleSave() ;
    }
    if (result && documentRef.value) {
        result = await documentRef.value?.handleSave() ;
    }
    if (isOnlyValidate) {
        return result;
    }
    if (result && searchValid && await saveMsgBox(unPopping)) {
        if (ruleForm.form.clientAccountOid) {
            //Start, CA Phase2, LingZeFei, ********
            caRef.value?.handleSave();
            //End, CA Phase2, LingZeFei, ********
            let form = { ...ruleForm.form };
            form.beforeImage = null;
            const msg = await proxy.$axios.patch("/datamgmt/api/v1/account", form);
            if (msg.success) {
              clearEnterObjects();
              //Start, CA Phase2, LingZeFei, ********
              caRef.value?.clearModifyData();
              //End, CA Phase2, LingZeFei, ********
              details.value.writebackId(msg.data);
              editRow(null,formDisabled.value, msg.data);
              ruleForm.form.opearteOid = msg.data;
            }
            return msg.success;
        } else {
            // const msg = await proxy.$axios.post("/datamgmt/api/v1/account", {
            //     ...ruleForm.form,
            // });
            // details.value.writebackId(msg.data);
            // editRow(null,msg.data);
            // return msg.success;
        }
    }
    return false;
}

const viewOriginalForm = (pendingOid, isDisabled) => {
    debugger;
    formDisabled.value = isDisabled;

    // 设置请求来源为RemarkForm
    if (generalRef.value && generalRef.value.setRequestSource) {
        generalRef.value.setRequestSource('remarkForm');
    }

    // View Origi 时把高亮颜色去掉
    // currentModel = currentModel == "Nor" ? "Orig" : "Nor";
    proxy.$axios.get("/datamgmt/api/v1/account?objectId="+pendingOid).then((body) => {
        if(body.success) {
            ruleForm.form = body.data;
            ruleForm.form.opearteOid = body.data.clientAccountOid;
            //details.value!.currentRow.beforeImage = body.data.beforeImage;
        }
    });
}
// View Origi 时把高亮颜色去掉
//let currentModel = "Nor";
const tabsClick = (tag) => {
    nextTick(()=>{
        for(let i = 0; i < lightyellowTabs.length; i++) {
            // View Origi 时把高亮颜色去掉
            //document.querySelector( "#"+lightyellowTabs[i] ).className += (currentModel == "Nor" ? " lightyellow" : " unlightyellow");
            document.querySelector( "#"+lightyellowTabs[i] ).className += " lightyellow";
        }
    });
}


const showDetails = (row:any, disabled:boolean) => {
    lightyellowTabs = [];
    tempKey.value = getDate('yyyyMMddHHmmss');
    activeName.value = "tab1";
    // // Start SIR1212 Lisy 2024/08/12 
    //formDisabled.value = disabled;
    if(disabled||row.recordStatus==='PA'){
        formDisabled.value = true;
    }else{
        formDisabled.value = false;
    }
    // End SIR1212 Lisy 2024/08/12 
    details.value.showDetails(row, disabled)
    editRow(row, disabled, null);
    setTimeout(()=>{
        if (generalRef.value) {
            generalRef.value.showDetails();
        }
        //     document.getElementById("tab-tab1").style.backgroundColor="yellow";
    }, 1000);
    /*
    setTimeout(()=> {
        let yellows = document.querySelectorAll(".lightyellow");
        if (yellows.length) {
            for(let i = 0; i < yellows.length; i++) {
                if (!lightyellowTabs.includes(yellows[i].id)) {
                    lightyellowTabs.push(yellows[i].id)
                }
            }
        }
    }, 2000);
    */
}


defineExpose({
    details,
    editRow,
    showDetails,
    viewOriginalForm,
});
</script>

<style></style>