<template>
  <BaseDetails ref="details" :handleSave="handleSave" :reload="reload" :viewOriginalForm="viewOriginalForm" :form="ruleForm">
    <el-form :validateOnRuleChange="false" :disabled="formDisabled" ref="ruleFormRef" style="width: 100%" :model="ruleForm.form" :rules="rules" status-icon>
      <FormRow>
        <FormItemSign label-width="220px" :detailsRef="details" :label="$t('common.title.opCtryRegionCode')" prop="opCtryRegionCode">
          <CtryRegionSearchInput v-model="ruleForm.form.opCtryRegionCode" showDesc="false" style="width: 120px" opCtryRegion />
        </FormItemSign>
        <FormItemSign label-width="110px" :detailsRef="details" :label="$t('csscl.bankHolidayManagement.holidayDate')" prop="holidayDate">
          <div style="position:relative">
            <DateItem v-model="ruleForm.form.holidayDate" style="width: 150px"/>
          </div>
        </FormItemSign>
        <FormItemSign label-width="70px" :detailsRef="details" :label="$t('common.title.status')" prop="status">
          <Select v-model="ruleForm.form.status" style="width: 180px" type='STATUS' />
        </FormItemSign>
      </FormRow>
    </el-form>
  </BaseDetails>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue';
import type { FormInstance, FormRules } from 'element-plus'
import BaseDetails from '~/pages/base/Details.vue';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import CtryRegionSearchInput from '~/pages/base/CtryRegionSearchInput.vue'
import { commonRules, showValidateMsg } from '~/util/Validators.js';
import { saveMsgBox } from '~/util/Function.js';

const { proxy } = getCurrentInstance();
const props = defineProps(['reload']);
const details = ref();
const formDisabled = ref(false);
const editDis = ref(false);
const editRow = (row, disabled,newId) => {
  const oid = newId || row?.bankHolidayOid;
  if (oid) {
    proxy.$axios.get("/datamgmt/api/v1/bank/holiday?bankHolidayId="+oid).then((body) => {
        if(body.success) {
            ruleForm.form = body.data;
            details.value.currentRow.value = body.data;
        }
        details.value.initWatch(ruleForm);
    });
    editDis.value = true;
  } else {
    ruleForm.form = {};
    editDis.value = false;
    details.value.initWatch(ruleForm);
  }
  
}

const viewOriginalForm = (pendingOid, isDisabled) => {
  formDisabled.value = isDisabled;
  proxy.$axios.get("/datamgmt/api/v1/bank/holiday?bankHolidayId="+pendingOid).then((body) => {
        if(body.success) {
            ruleForm.form = body.data;
            details.value.currentRow.value = body.data;
        }
    });
    editDis.value = false;
}

const handleSave = async (searchValid, isOnlyValidate, unPopping) => {
  let result = await ruleFormRef.value.validate((valid, fields) => {
      if (valid) {

      } else {
        showValidateMsg(details, fields);
      }
  });
    if (isOnlyValidate) {
        return result;
    }
  if (result && searchValid && await saveMsgBox(unPopping)) {
      if (ruleForm.form.bankHolidayOid) {
          const msg = await proxy.$axios.patch("/datamgmt/api/v1/bank/holiday", {
              ...ruleForm.form,
          });
          details.value.writebackId(msg.data);
          editRow(null,null,msg.data);
          return msg.success;
      } else {
          const msg = await proxy.$axios.post("/datamgmt/api/v1/bank/holiday", {
              ...ruleForm.form,
          });
          details.value.writebackId(msg.data);
          editRow(null,null,msg.data);
          return msg.success;
      }
  }
  return false;
}
const showDetails = (row, disabled) => {
  formDisabled.value = disabled;
  details.value.showDetails(row, disabled)
  ruleForm.form = {};
  if(row.bankHolidayOid){
    editRow(row, disabled); 
    details.value.initMkck(row,row.bankHolidayOid,"CSSCL_SYSAD009")
  }
}
defineExpose({
  details,
  editRow,
  showDetails,
});
// --------------------------------------------

interface RuleForm {
  opCtryRegionCode: String
  holidayDate: String
  status: string
}

const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive({
  form: {
      opCtryRegionCode: "",
      holidayDate: "",
      status: "",
  }
})

const rules = reactive<FormRules<RuleForm>>({
  opCtryRegionCode: [
      { required: true, message: 'Please select', trigger: 'change' },
  ],
  holidayDate: [
      { required: true, message: 'Please select time', trigger: 'change' },
  ],
  status: [
      { required: true, message: 'Please select', trigger: 'change' },
  ],
})

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
      if (valid) {
          console.log('submit!')
      } else {
          console.log('error submit!', fields)
      }
  })
}

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
}

const options = Array.from({ length: 10000 }).map((_, idx) => ({
  value: `${idx + 1}`,
  label: `${idx + 1}`,
}))

</script>

<style></style>