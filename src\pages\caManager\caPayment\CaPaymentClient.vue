<template>
    <Grid url="/bff/ca/api/v1/ca-event-client-payment-info/get-ca-event-client-payment-info-page-list"
          isShowSearch="false"
          ref="gridRef"
          :params="params"
          :selectable="false"
    >
      <template v-slot:tableColumnFront="slotProps">
        <el-table-column sortable="custom" prop="tradingAccountCode"
                         :label="$t('csscl.ca.common.custodyAccountNo')" align="center"  />
        <el-table-column sortable="custom" prop="clientAccountName"
                         :label="$t('csscl.ca.common.custodyAccountName')" align="center" />
        <el-table-column sortable="custom" prop="custodianAccountNo"
                         :label="$t('csscl.ca.common.custodianAccount')" align="center"  />
        <el-table-column sortable="custom" prop="cashPaymentShadowInd"
                         :label="$t('csscl.ca.common.shadow')" align="center" />
        <el-table-column sortable="custom" prop="cashPaymentMethodCode"
                         :label="$t('csscl.ca.common.paymentMethod')" align="center"  />
        <el-table-column sortable="custom" prop="selEntitleQuantity"
                         :label="$t('csscl.ca.common.entitledQuantity')" align="center" />
        <el-table-column sortable="custom" prop="cashPaymentAmt"
                         :label="$t('csscl.ca.common.payCash')" align="center" />
        <el-table-column sortable="custom" prop="scripPaymentQuantity"
                         :label="$t('csscl.ca.common.payScrip')" align="center"  />
        <el-table-column sortable="custom" prop="paymentReference"
                         :label="$t('csscl.ca.common.paymentReference')" align="center" />
        <el-table-column sortable="custom" prop="cashPaymentStatus"
                         :label="$t('csscl.ca.common.cashPaymentStatus')" align="center" />
        <el-table-column sortable="custom" prop="taxPaymentAmount"
                         :label="$t('csscl.ca.common.taxAmount')" align="center" />
        <el-table-column sortable="custom" prop="chargePaymentAmount"
                         :label="$t('csscl.ca.common.chargeAmount')" align="center" />
        <el-table-column sortable="custom" prop="fxInd"
                         :label="$t('csscl.ca.common.fxSI')" align="center" />
        <el-table-column sortable="custom" prop="fxCurrencyCode"
                         :label="$t('csscl.ca.common.fxCCY')" align="center" />
        <el-table-column sortable="custom" prop="fxStatus"
                         :label="$t('csscl.ca.common.fxStatus')" align="center" />
        <el-table-column sortable="custom" prop="fxReference"
                         :label="$t('csscl.ca.common.fxReference')" align="center" />
      </template>
    </Grid>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, defineExpose} from 'vue';

const gridRef = ref();
const params = ref({
    caEventReferenceNumber: '',
    caEventOptionSequenceNo: 0
});

const showDetails = async (row: any, eventRefNo: any) => {
    params.value = { caEventReferenceNumber: eventRefNo , caEventOptionSequenceNo: row.caEventOptionSequenceNumber};
    // 触发 Grid 组件重新加载数据
    await gridRef.value.load();
};

const clearData = () => {
  // 清空表格数据
  gridRef.value.tableData = [];
  // 清空总数
  gridRef.value.total = 0;
}

defineExpose({ 
    showDetails,
    clearData
});
</script>

<style>

</style>