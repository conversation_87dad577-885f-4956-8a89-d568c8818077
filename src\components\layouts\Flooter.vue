<template>
  <div style="padding-block: 5px;background-color: lightgrey;padding-inline: 10px;margin-inline: 10px;">
    <span>Last Login Time : {{ $currentInfoStore.getUserInfo.lastLoginTime }} {{ $currentInfoStore.getUserInfo.timeZone }}</span>
    <span style="float:right;">Version {{ version }}</span>
  </div>
  <!-- Start SK-COMMON-0118, Tom.Li, 2024/08/28 -->
  <div style="padding-block: 5px;text-align: center;background-color: white;">
  <!-- End SK-COMMON-0118, Tom.Li, 2024/08/28 -->
      <span style="color:var(--ep-color-primary);" >Copyright © 2024 Bank of China (Hong Kong) Limited All rights reserved.</span>
  </div>
</template>

<script lang="ts" setup>
import { ref, getCurrentInstance } from "vue";
const version = import.meta.env.VITE_APP_VERSION;
const { proxy } = getCurrentInstance();

</script>
