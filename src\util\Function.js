import { getCurrentInstance } from 'vue';
import { ElMessageBox, ElMessage, ElLoading } from 'element-plus';
import { v4 as uuidv4 } from "uuid";
import axios from '../axios';
import moment from 'moment';
import { useCookies } from "vue3-cookies";
import { commonRules } from '~/util/Validators.js';
import { previewFile } from '~/store/modules/previewFile';
import { removeModifiedFlag } from "~/util/ModifiedValidate";
const { cookies } = useCookies();
let userInfo = {};

export const initialize = async (proxy) => {
    let body1 = await proxy.$axios.get('/auth/api/v1/user/role/assignment');
    if (body1.success) {
      proxy.$currentInfoStore.setMenus(body1.data.menus);
      if (body1.data.menus?.length == 0) {
  
        let wraningMsg = proxy.$t("message.auth.login.user.not.right")
        //let wraningMsg = app.config.globalProperties?.$t("message.auth.login.user.not.right");
        wraningMsg = wraningMsg ? wraningMsg : "You have no right to access the system. Please contact system administrator.";
        ElMessageBox.alert(wraningMsg, 'Login Failed', {
          confirmButtonText: 'OK',
          type: 'wraning',
          customClass: 'message-box-full-coverage',
        }).then(()=>{
            clearCookies(true);
        }).catch(()=>{
            clearCookies(true);
        });
        return Promise.resolve(false);
      }
    }
    //	Start SK-COMMON-0123, Tom.Li, 2024/09/02
    await proxy.$currentInfoStore.fetchUserInfo();
    //	End SK-COMMON-0123, Tom.Li, 2024/09/02
    await proxy.$commonCodeStore.fetchAll();

    userInfo = proxy.$currentInfoStore.getUserInfo; 
    return Promise.resolve(true);
  }

export const getExplorer = () => {
    const ua = window.navigator.userAgent
    const isExplorer = (exp) => {
        return ua.indexOf(exp) > -1
    }
    if(isExplorer('MSIE')) return 'IE'
    else if(isExplorer('Firefox')) return 'Firefox'
    else if(isExplorer('Chrome')) return 'Chrome'
    else if(isExplorer('Opera')) return 'Opera'
    else if(isExplorer('Safari')) return 'Safari'
}

export const randomString = () => {
    return uuidv4();
}

export const randomHashCode = () => {  
    let hash = 0;  
    let str = randomString();
    for (let i = 0; i < str.length; i++) {  
      const char = str.charCodeAt(i);  
      hash = ((hash << 5) - hash) + char;  
      hash |= 0; // Convert to 32bit integer  
    }  
    return hash;  
  }

export const highlight = (data) => {
    if (currentPageInfo.isCreatePage()) {
        return {};
    }
    //{ row: any, column: any, rowIndex: number, columnIndex: number }
    // Start SIR-HLH-R78,Tom.Li, 2024/08/15
    // if (data.row.recordStatus == 'PD' && data.row.mkckAction == 'D') {
    //     return {
    //         backgroundColor: "lightyellow"
    //     }
    // }
    // if (data.row.recordStatus == 'PD' && data.row.mkckAction == 'U') {
    //     return {
    //         backgroundColor: "lightyellow"
    //     }
    // }
    // if (data.row.recordStatus == 'PD' && data.row.mkckAction == 'C') {
    //     return {
    //         backgroundColor: "lightyellow"
    //     }
    // }
    if (data.row.recordStatus != 'A') {
        return {
            backgroundColor: "lightyellow"
        }
    }
    // End SIR-HLH-R78,Tom.Li, 2024/08/15
    if (data.row.beforeImage) {
        if (data.row[data.column.property] != data.row.beforeImage[data.column.property]) {
            return {
                backgroundColor: "lightyellow"
            }
        }
    }
    return {};
}

export const highLightColor = (recordStatus) => {
    //{ row: any, column: any, rowIndex: number, columnIndex: number }
    if (recordStatus == 'D') {
        // return "color:red";
    }
    if (recordStatus == 'C') {
        // return "color:green";
    }
    return "";
}

export const getOid = (row, isDoubleClick, proxy,isDelete) => {
    // $currentInfoStore.currentPermission && $currentInfoStore.currentPermission['Approve']
    let id = row.pendingOid ? row.pendingOid : row.currentOid;
    if (isDelete) {
        if(row.recordStatus?.startsWith("P")) {
            id = row.pendingOid ? row.pendingOid : row.currentOid;
        } else {
            id = row.currentOid;
        }
    }
    if (isDoubleClick) {
        return row.currentOid;
    }
    //2024/05/30, temporary modifeid,
    //when user is a maker has 'Edit / New / Enquire', and data is pending for edit,  save multiple record to DB
    // if (proxy && !proxy?.$store?.getters?.currentPermission['Approve']) {
    //     return row.currentOid;
    // }


    return id;
}

export const updateListValue = (list, val, idKey) => {
    if (!list) {
        list = [];
    }
    if (!idKey) {
        idKey = 'currentOid';
    }
    if (val && idKey) {
        let isUpdate = false;
        for (let i = 0; i < list.length; i++) {
            let e = list[i];
            if (e[idKey] == val[idKey]) {
                Object.assign(list[i], val);
                isUpdate = true;
                break;
            }
        }
        removeModifiedFlag(list);
        if (!isUpdate) {
            list.push(Object.assign({}, val));
        }
    }
    return list;
}

export const getCommonDesc = (coms, val) => {
    if (val) {
        if (!getCommonDesc.getters) {
            getCommonDesc.getters = getCurrentInstance()?.proxy.$commonCodeStore;
        }
        if (typeof coms == 'string') {
            coms = getCommonDesc.getters?.getAll[coms];
        }
        if(val == 'PA1' || val == 'PA2' ){
            val = 'PA';
        }
        let desc = val;
        for (let i in coms) {
            let e = coms[i];
            if (e.code == val) {
                desc = e.codeDesc;
                break;
            }
        }
        return desc;
    }
    return "";
}

export const commDesc = (comTyp) => {
    return (e,v)=>{ return getCommonDesc(comTyp, v) }
}

export const getRecordStatusDesc = (row) => {
    if (row) {
        let rs = row.recordStatus;
        let sts = getCommonDesc('RECORD_STATUS', rs);
    
        if (rs && rs !== 'A' && row['mkckAction']) {
            sts += " for " + getCommonDesc('MKCK_ACTION', row['mkckAction'])
        }
        return sts;
    }
    return "";
}

export const getEnvConfigVal = (key, def) => {
    let val = import.meta.env[key];
    if (!val) {
        // val = import.meta.env[key];
        // 不同系统环境独自写法
    }
    if (val) {
        return val;
    }
    return def;
}

export const thousFormat = (val, scale = 2) => {
    if ( val === 0 ) { val = '0'; }
    if (val) {
        val = thousParser(val);
        let num = val.split(".");
        if (scale > 0 && num.length > 2) {
            num[1] = num.slice(1).join('');
        }
        if (scale > 0) {
            num[1] = "."+ String((num[1]||"")+"0000000000").substring(0,scale)
        } else {
            num[1] = "";
        }
        return String(num[0]).replace(/\B(?=(\d{3})+(?!\d))/g, ",") + num[1];
    }
    return "";
}
/**
 * 兼容负数（包含括号的特殊处理）
 * @param {*} val 
 * @param {*} scale 
 * @returns 
 */
export const thousFormatK = (val, scale = 2) => {
    if ( val === 0 ) { val = '0'; }
    if (val) {
        if(val.includes("(") && val.includes(")")){
            val = val.replace("(","");
            val = val.replace(")","");
            val = thousParser(val);
            let num = val.split(".");
            if (scale > 0 && num.length > 2) {
                num[1] = num.slice(1).join('');
            }
            if (scale > 0) {
                num[1] = "."+ String((num[1]||"")+"0000000000").substring(0,scale)
            } else {
                num[1] = "";
            }
            return "("+ String(num[0]).replace(/\B(?=(\d{3})+(?!\d))/g, ",") + num[1] + ")";
        } else {
            val = thousParser(val);
            let num = val.split(".");
            if (scale > 0 && num.length > 2) {
                num[1] = num.slice(1).join('');
            }
            if (scale > 0) {
                num[1] = "."+ String((num[1]||"")+"0000000000").substring(0,scale)
            } else {
                num[1] = "";
            }
            return String(num[0]).replace(/\B(?=(\d{3})+(?!\d))/g, ",") + num[1];

        }
    }
    return "";
}
/**
 * 数字带千分位格式化输入
 * @param val 输入的值
 * @param scale 小数位数
 * @param numLen 整数位数
 * @param isNegative 是否可负数
 * @returns {string} 格式化后结果 #,###.##
 */
export const thousEditFormat = (val, scale = 2, numLen = 11, isNegative = false) => {
    if ( val === 0 ) { val = '0'; }
    if ( val ) { // 非 '' null undefined
        val = val == '.' ? "0." : val; // 处理不输入0只输入.
        val = val == '-.' ? "-0." : val; // 处理不输入0只输入-.
        val = thousParser(val);
        // 添加负数的验证 SK-COMMON-0111
        let negative = "";
        if (val.charAt() == "-" && isNegative) {
            negative = "-";
        }
        // 处理连续输入的非数字， 如字母
        if (scale > 0) {
            val = val.replace(/([^0-9\.])/g, '');
        } else {
            val = val.replace(/([^0-9])/g, '');
        }
        // After replace all '-',  re-append frist character '-'
        // maybe value is -123456 or 123456
        val = negative + val;
        if (val != "") {
            // First character is '-', it is the negative numbers first character
            if (val == negative) {
                return val;
            }
            let num = String(val).split(".");
            // input multiple points, merge the all character after first point
            if (scale > 0 && num.length > 2) {
                num[1] = num.slice(1).join('');
            }
            // Negative numbers like -123456
            if (negative) {
                numLen += 1;
            }
            if (num[0]?.length > numLen) {
                val = Number(val)/10;
                num = String(val).split(".");
            }
            if ((num[1]?.length > scale)) {
                num[1] = String(num[1]).substring(0,scale)
            }
            val = String(num[0]).replace(/\B(?=(\d{3})+(?!\d))/g, ",") + ( num.length>1&&scale>0 ? ("." + num[1]) : "");
            return val;
        }
    }
    return "";
}

export const thousBlur = (obj, key, e, scale = 2) => {
    let val = e.value;
    if (val === 0) { val = '0'; }
    if (val) {
        val = thousParser(val)
        let num = val.split(".")
        if (scale > 0 && num.length > 2) {
            num[1] = num.slice(1).join('');
        }
        if (scale > 0) {
            num[1] = String((num[1]||"")+"0000000000").substring(0,scale)
        }
        obj[key] = num[0] + ( scale>0 ? ("." + num[1]) : "");
        e.value = thousFormat(val, scale);
    }
    return obj[key];
}

export const thousParser = (val) => {
    if (!val) {
        return "";
    }
    return String(val).replace(/\$\s?|(,*)/g, '');
}

export const dateFormat = (date) => {
    let pattern = 'yyyy/MM/dd';
    if (!date || isNaN(Date.parse(date))) { return ''; }
    let aZero = (s) => { return s < 10 ? "0"+s : s; }
    date = new Date(Date.parse(date))
    pattern = pattern.replace("yyyy", date.getFullYear()).replace("MM", aZero(date.getMonth()+1)).replace('dd', aZero(date.getDate()));
    return pattern;
}

export const datetimeFormat = (date) => {
    let pattern = 'yyyy/MM/dd HH:mm:ss';
    if (!date || isNaN(Date.parse(date))) { return ''; }
    let aZero = (s) => { return s < 10 ? "0"+s : s; }
    date = new Date(Date.parse(date))
    pattern = pattern.replace("yyyy", date.getFullYear()).replace("MM", aZero(date.getMonth()+1)).replace('dd', aZero(date.getDate()));
    pattern = pattern.replace("HH", aZero(date.getHours())).replace("mm", aZero(date.getMinutes())).replace('ss', aZero(date.getSeconds()))
    return pattern;
}

export const daysBetween = (dateFrom, dateTo) => {
    if(dateFrom && dateTo){
        let diff = Math.abs(new Date(dateTo) - new Date(dateFrom));
        diff = diff / (1000 * 60 * 60 * 24);
        return diff;
    }
}
/**
 *
 * @param pattern  yyyy:Year, MM:Month, dd:Day, HH:24Hours mm:minute ss:second Default pattern: yyyy/MM/dd HH:mm:ss
 * @param date Date Object or Date String
 * @returns {string}
 */
export const getDate = (pattern, date) => {
    pattern = pattern ||  'yyyy/MM/dd HH:mm:ss';
    date = date || new Date();
    if (!date || isNaN(Date.parse(date))) { return ''; }
    let aZero = (s) => { return s < 10 ? "0"+s : s; }
    date = new Date(Date.parse(date))
    pattern = pattern.replace("yyyy", date.getFullYear()).replace("MM", aZero(date.getMonth()+1)).replace('dd', aZero(date.getDate()));
    pattern = pattern.replace("HH", aZero(date.getHours())).replace("mm", aZero(date.getMinutes())).replace('ss', aZero(date.getSeconds()))
    return pattern;
}

export const serverCurrentDate = async () => {
    let curDt = new Date(userInfo.currentTime);
    curDt.setHours(0, 0, 0, 0);
    return curDt;
}

export const currentDate = async () => {
    let curDt;
    if(userInfo?.loginTime){
        curDt = new Date(userInfo.loginTime);
    }else{
        let sysCtrlDate = await getSysCtrlDate();
        curDt = new Date(sysCtrlDate);
    }
    curDt.setHours(0, 0, 0, 0);
    return curDt;
}

export const preProDate = async () => {
    let curDt;
    let sysCtrlDate = await getSysCtrlPreDate();
    curDt = new Date(sysCtrlDate);
    curDt.setHours(0, 0, 0, 0);
    return curDt;
}


export const getSysCtrlDate = async ()=>{
   let msg = await axios.get("/datamgmt/api/v1/sysctrl/query");
   return msg.data.curPrcsDate;
}
//获取交易日的上一日
export const getSysCtrlPreDate = async ()=>{
    let msg = await axios.get("/datamgmt/api/v1/sysctrl/query");
    return msg.data.lastPrcsDate;
 }


export const getDateAndTime = () => {
    return moment(new Date()).format('YYYY/MM/DD HH:mm:ss');
}

export const rowCompareWithBeforeImage = (row, el, tabs, fields, exFields) => {
    let ret = false;
    tabs = tabs || [];
    let exs = ['oid', 'pid', 'beforeImage'].concat(exFields || [])
    let beforeImage = row.beforeImage;
    if (row.master) {
        beforeImage = beforeImage || {};
    }
    if (beforeImage && row.recordStatus != 'A') {
        if (!(fields?.length)) {
            let r = Object.keys(row);
            let bf = Object.keys(beforeImage);
            fields = r.concat(bf);
        }
        let keys = new Set(fields);
        for(let field of keys) {
            if (exs.includes(field)) {
                continue;
            }
            if((beforeImage[field] || "") != (row[field] || "")){
                if (el) {
                    document.getElementById(el).className += ' lightyellow';
                    if (!tabs.includes(el)) {
                        tabs.push(el);
                    }
                }
                ret = true;
                break;
            }
        }
    }
    return ret;
}

export const checkBeforeCurDt = async (proxy, label, dateStr) => {
    let curDate = await currentDate();
    let date = dateStr && new Date(dateStr);
    if (date > curDate) {
        //xxx: Date must be earlier than the current date.
        let msg = proxy.$t('message.earlier.equal.curdate', [label]);
        if(!label){
            msg = msg.replace(":", "");
        }
        return msg;
    }
}
export const checkBeforeCalendarDt = (proxy, label, dateStr) => {
    let date = new Date(dateStr)
    if (date > new Date()) {
        //xxx: Date must be earlier than the current date.
        let msg = proxy.$t('message.earlier.equal.curdate', [label]);
        if(!label){
            msg = msg.replace(":", "");
        }
        return msg;
    }
}
export const checkAfterCurDt = async (proxy, label, dateStr) => {
    let curDate = await currentDate();
    let date = dateStr && new Date(dateStr);
    if (date < curDate) {
        //xxx: Date must be earlier than the current date.
        let msg = proxy.$t('message.earlier.equal.curdate', [label]);
        if(!label){
            msg = msg.replace(":", "");
        }
        return msg;
    }
}
export const checkDateFromTo = (proxy, dateFrom, dateTo, label) => {
    if(dateFrom && dateTo && dateFrom > dateTo){
        //Date From must be earlier than or equal to Date To.
        if(!label){
            label = proxy.$t('csscl.home.table.dateFrom');
        }
        return proxy.$t('message.earlier.equal.dateto', [label, proxy.$t('csscl.common.dateTo')]);
    }
}
export const checkDateBetween = (proxy, dateFrom, dateTo, dtRange) => {
    let diff = daysBetween(dateFrom, dateTo);
    if(diff && dtRange && diff > dtRange){
        //Submit Date range must be less than or equal to 7 days.
        return proxy.$t('message.date.range.error', [dtRange]);
    }
}

export const chkDateMoreCurDate = (dt) => {
    let d = new Date();
    d.setHours(0);
    d.setMinutes(0);
    d.setSeconds(0);
    d.setMilliseconds(0);
    dt = new Date(dt);
    if (dt > d) {
        return true;
    }
    return false;
}

export const checkInputDate = (proxy, dateFrom, dateTo, dtRange) => {
    let msg = checkDateFromTo(proxy, dateFrom, dateTo);
    if(msg){
        return msg;
    }
    if(!dtRange) dtRange = 7;
    msg = checkDateBetween(proxy, dateFrom, dateTo, dtRange);
    return msg;
  }

export const setInputLabel = (detailForm, rules, isSearchPanel, area) => {
    let rulesArr;
    if (detailForm) {
        rulesArr = typeof(detailForm.rules) == 'function' ? detailForm.rules() : detailForm.rules;
    }
    
    let nv = document.querySelectorAll( (area || "")+"input.ep-input__inner");
    if (nv?.length > 0) {
        for(let i = 0; i < nv.length; i++) {
            let e = nv[i];
            let attr = null;
            // Start R2411A-10370 LiShaoyi 20240825
            let label = e.ariaLabel;
            let _title = null;
            if (label) {
                if (label.startsWith("title:")) {
                    _title = label.substring(6);
                    label = null;
                }
            }
            if (!label) {
                label = document.querySelector("label[for=" + e.id + "]");
            }
            // End R2411A-10370 LiShaoyi 20240825            
            if (label) {
                label = label.textContent || label;
                e.setAttribute("alt", label, e.selectinput);
                if (!(e.disabled || e.getAttribute("selectinput") == '' ) || parseBool(e.getAttribute('setTitle'))) {
                    //label.setAttribute("title", e.getAttribute("maxlength") ? (label.textContent + " length:" + e.getAttribute("maxlength")) : "");
                    let numInput = e.closest("div.input-number");
                    let title = e.getAttribute("maxlength") ? (label + " length:  " + e.getAttribute("maxlength")) : "";
                    if(numInput||parseBool(e.getAttribute("number"))) {
                        title += "\r" + "Allow input number";
                        // SK-COMMON-0111
                        // append the max value tip
                        if (e.getAttribute("max")) {
                            title += "\r Max value is " + e.getAttribute("max");
                        }
                        // append the min value tip
                        if (e.getAttribute("min")) {
                            title += "\r Min value is " + e.getAttribute("min");
                        }
                    } else {
                        let space = e.closest("div.ep-space--horizontal");
                        let code,ruls,isCommon=true;
                        if(space&&space.getAttribute("code")){
                            code = space.getAttribute("code");
                            if(rulesArr){
                                for (let jk = 0; jk < rulesArr.length; jk++) {
                                    if(rulesArr[jk].rules[code]){
                                        ruls = rulesArr[jk].rules[code]
                                        break;
                                    }
                                }
                            } else if (rules != undefined) {
                                if(!rules[code]) {
                                    rules[code] = [];
                                }
                            }
                        }
                        title += "\r" + "Allow input Character Set:  ";
                        attr = e.getAttribute("onlyLetters");
                        if (parseBool(attr)) {
                            title += attr || "a-z A-Z";
                        } else {
                            title += "a-z A-Z";
                        }
                        if (!parseBool(attr)) {
                            title += " 0-9 / - ? : ( ) . , ' +";
                        }
                        if (parseBool(e.getAttribute("email"))) {
                            isCommon=false;
                            rules&&rules[code].push(commonRules.email);
                            title += " ! # & % * = ^ _ ' { | } ~ \" ; @ [ \\ ] $ < >";
                        } else if (parseBool(e.getAttribute("specialTxt"))) {
                            isCommon=false;
                            rules&&rules[code].push(commonRules.specialTxt);
                            title += " ! # & % * = ^ _ ' { | } ~ \" ; @ [ \\ ]";
                        } else {
                            if (parseBool(e.getAttribute("searchField")) || isSearchPanel) {
                                title += " %";
                            }
                            if (parseBool(e.getAttribute("chinese"))) {
                                isCommon=false;
                                title += " and Chinese";
                                if (parseBool(e.getAttribute("searchField")) || isSearchPanel) {
                                    rules&&rules[code].push(commonRules.nameIncChineseForSearch);
                                } else {
                                    rules&&rules[code].push(commonRules.nameIncChinese);
                                }
                            }
                        }
                        if (ruls&&isCommon) {
                            ruls.push(commonRules.name);
                        } else if (code&&rules&&isCommon) {
                            if (parseBool(e.getAttribute("searchField")) || isSearchPanel) {
                                rules[code].push(commonRules.nameForSearch);
                            } else {
                                rules[code].push(commonRules.name);
                            }
                        }
                        
                    }
                    // Start R2411A-10370 LiShaoyi 20240825
                    //e.setAttribute("title", title);
                    e.setAttribute("title", _title ? _title : title);
                    // End R2411A-10370 LiShaoyi 20240825
                }
            }
        }
    }
}
/**
 * 仅同步target里未定义的，copying里有值信息
 * @param {*} target 
 * @param {*} copying 
 * @returns 
 */
export function assignUndef(target, copying) {
    if (target && copying && typeof target == 'object' && typeof copying == 'object') {
        let keys = Object.keys(copying);
        for (let i = 0; i < keys.length; i++) {
            let e = keys[i];
            if (!target.hasOwnProperty(e) && copying[e] != undefined && copying[e] != null) {
                target[e] = copying[e];
            }
        }
    }
    return target;
}

export const removeObjectNull = (obj) => {
    if (!obj || typeof obj != 'object') {
        return obj;
    }
    let objKeys = Object.keys(obj);
    for(let i = 0; i < objKeys.length; i++) {
        let k = objKeys[i];
        let e = obj[k];
        if (e == null) {
            delete obj[k];
        } else {
            removeObjectNull(e);
        }
    }
    return obj;
}

export const parseBool = (val) => {
    return Boolean(String(val) == 'false' ? false : String(val) == '' ? true : val);
}

export const downloadFile = async (url, params) => {
    await axios.get(url, {
        params: params,
        responseType: 'blob',
    }).then((res) => {
        if(res?.data?.success===undefined && res?.data?.type === 'application/json'){
            const fileReader = new FileReader()
            fileReader.readAsText(res?.data,'utf-8')
            fileReader.onload = function(){
                ElMessageBox.alert(fileReader.result, 'Warning');
            }
            return;
        }
        if (res?.data && res?.headers['content-disposition']) {
            let fileName = res?.headers['content-disposition']?.match(/filename=(.*)/)[1];
            const blob = new Blob([res?.data]);
            const link = document.createElement('a');
            link.style.display = "none";
            link.href = URL.createObjectURL(blob);
            link.download = decodeURIComponent(fileName);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    }).catch((error) => {
        ElMessageBox.alert('Download error.', 'Warning');
    });
}

/**
 * 下载文件 post方式
 * @param url
 * @param params
 * @returns {Promise<void>}
 */
export const downloadFilePost = async (url, params) => {
    await axios.post(url, params, {
        responseType: 'blob',
    }).then((res) => {
        if(res?.data?.success===undefined && res?.data?.type === 'application/json'){
            const fileReader = new FileReader()
            fileReader.readAsText(res?.data,'utf-8')
            fileReader.onload = function(){
                ElMessageBox.alert(fileReader.result, 'Warning');
            }
            return;
        }
        if (res?.data && res?.headers['content-disposition']) {
            let fileName = res?.headers['content-disposition']?.match(/filename=(.*)/)[1];
            const blob = new Blob([res?.data]);
            const link = document.createElement('a');
            link.style.display = "none";
            link.href = URL.createObjectURL(blob);
            link.download = decodeURIComponent(fileName);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    }).catch((error) => {
        ElMessageBox.alert('Download error.', 'Warning');
    });
}


export const downloadOpenFile = async (url, params) => {
    let view_ind = import.meta.env.VITE_VIEW_IND;
    await axios.get(url, {
        params: params,
        responseType: 'blob',
    }).then(async (res) => {
        if(res?.data?.success===undefined && res?.data?.type === 'application/json'){
            const fileReader = new FileReader()
            fileReader.readAsText(res?.data,'utf-8')
            fileReader.onload = function(){
                ElMessageBox.alert(fileReader.result, 'Warning');
            }
            return;
        }
        if (res?.data && res?.headers['content-disposition']) {
            let fileName = res?.headers['content-disposition']?.match(/filename=(.*)/)[1];
            const extension = fileName.split('.').pop().toLowerCase();
            let contentType = "";
            if(extension=="pdf"){
                contentType = "application/pdf";
            }
            if(extension=="xls"){
                contentType = "application/vnd.ms-excel";
            }
            if(extension=="xlsx"){
                contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            }
            if (view_ind&&view_ind=='Y') {
                if (extension=="xlsx"||extension=="xls") {
                    const blob = new Blob([res?.data], {type: contentType});
                    const wb = XLSX.read(await new Response(blob).arrayBuffer(), {dense: true});
                    const previewFileStore = previewFile();
                    previewFileStore.setName(fileName);
                    previewFileStore.setExcel(wb);
                    previewFileStore.setFileType(extension);
                    previewFileStore.setEnable(true);
                    return;
                }
                if(extension=="csv"||extension=="txt"){
                    const fileReader = new FileReader()
                    const previewFileStore = previewFile();
                    fileReader.readAsText(res?.data,'utf-8');
                    previewFileStore.setName(fileName);
                    previewFileStore.setFileType(extension);
                    fileReader.onload = function(){
                        previewFileStore.setFile(fileReader.result);
                        previewFileStore.setEnable(true);
                    }
                    return;
                }
            }
            if(extension=="text"){
                contentType = "text";
            }
            const blob = new Blob([res?.data], {type: contentType});
            window.open(window.URL.createObjectURL(blob), "_blank");
        }
    }).catch((error) => {
        ElMessageBox.alert('Download error:' + error, 'Warning');
    });
}

/**
 * Validate Date item input value
 * @param {*} area 
 * @returns 
 */
export const validDateItemValue = (area) => {
    let ret = true;
    let dateInputs = document.querySelectorAll(area || "div.details-dialog .search-date-error input[alt]");
    for (let i = 0; i < dateInputs.length; i++) {
      let e = dateInputs[i];
      let alt = e.alt;
      ret = false;
      setTimeout(()=>{
          ElMessage({
            message: alt + ": Invalid date format!",
            type: 'error',
            duration: 10000,
            offset: 100,
            showClose: true,
          });
      }, 50);
    }
    return ret;
}

export const validSearchInputValue = async (area) => {
    let ret = true;
    let searchInputs = document.querySelectorAll(area || "div.details-dialog input[searchtype]:not(div.grid-continer input[searchtype]) ");
    for (let i = 0; i < searchInputs.length; i++) {
        let e = searchInputs[i];
        let alt = e.alt;
        let val = e?.value;
        if (val != '') {
            let valid = await e.onsearch();
            if (!valid) {
                ret = false;
                setTimeout(()=>{
                    ElMessage({
                        message: alt + ": The input value is not in the Combo List!",
                        type: 'error',
                        duration: 10000,
                        offset: 100,
                        showClose: true,
                    });
                }, 50);
            }
        }
    }
    
    return ret;
}

export const downloadBatchFile = async (url, params) => {
    let loading = ElLoading.service({
        lock: true,
        text: 'Loading',
        background: 'rgba(0, 0, 0, 0.0)',
      });
    await axios.post(url, params,{
        headers: { 'content-type': 'application/json' },
        responseType: 'blob',
    }).then((res) => {
      if(res?.data?.success===undefined && res?.data?.type === 'application/json'){
        const fileReader = new FileReader()
        fileReader.readAsText(res?.data,'utf-8')
        fileReader.onload = function(){
            ElMessageBox.alert(fileReader.result, 'Warning');
        }
        return;
      }
      if (res?.data && res?.headers['content-disposition']) {
        let fileName = res?.headers['content-disposition']?.match(/filename=(.*)/)[1]
        const blob = new Blob([res?.data]);
        const link = document.createElement('a');
        link.style.display = "none";
        link.href = URL.createObjectURL(blob);
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } 
    }).finally(() => {
        loading.close();
    });
}

export const getBaseUrl = () => {
    let baseUrl = import.meta.env.BASE_URL;
    if (!baseUrl.endsWith("/")) {
        baseUrl = baseUrl + "/";
    }
    return baseUrl;
}

export const selectedRow = (propsOid, selectedRecord, gridRef, lastSelected, row, column, event, getSelectable, isMultiple) => {
    // window.getSelection ? window.getSelection().removeAllRanges() : document.selection.empty();
    let isClickCheckbox = event.target.className=='multiple-checkbox__inner';
    if(!isClickCheckbox&&event.target.closest("td.ep-table-column--selection")) {
        isClickCheckbox = true;
    }
    let oid = row[propsOid];
    let rowIndex = event.target.closest("tr.ep-table__row").querySelector("td.data-grid-selection-index-cell>div>div")?.innerText;
    let selectable = true;
    if(getSelectable) {
      selectable = getSelectable(row);
    }
    if (isMultiple&&event.shiftKey) {
        gridRef.value.clearSelection();
        selectedRecord.value={};
        if(lastSelected.value==0){
            lastSelected.value = rowIndex;
        }
        let start=parseInt(lastSelected.value),end=parseInt(rowIndex);
        if(start > end) {
            start=rowIndex;
            end=lastSelected.value;
        }
        let r;
        for (let j = start-1; j < end; j++) {
          selectable = true;
          r = gridRef.value.data[j];
          if(getSelectable) {
            selectable = getSelectable(r);
          }
          if (selectable) {
            oid = r[propsOid];
            selectedRecord.value[oid]=r;
            gridRef.value.toggleRowSelection(r, true);
          }
        }
    } else if (isMultiple&&(event.ctrlKey||isClickCheckbox)) {
      if (selectable) {
        if(selectedRecord.value[oid]){
            delete selectedRecord.value[oid];
            gridRef.value.toggleRowSelection(row, false);
        } else {
            selectedRecord.value[oid]=row;
            gridRef.value.toggleRowSelection(row, true);
        }
      }
    } else {
      gridRef.value.clearSelection();
      selectedRecord.value={};
      if (selectable) {
        selectedRecord.value[oid]=row;
        gridRef.value.toggleRowSelection(row, true);
      }
    }
    lastSelected.value = rowIndex;
}

export const clearSelectedCache = (selectedRecord, gridRef, lastSelected) => {
    selectedRecord.value={};
    gridRef?.value?.clearSelection();
    lastSelected.value = 0;
    window.getSelection ? window.getSelection().removeAllRanges() : document.selection.empty();
}

export const selectedAllRows = ( propsOid, rows, selectedRecord, gridRef, lastSelected, getSelectable) => {
    selectedRecord.value={};
    gridRef.value.clearSelection();
    lastSelected.value = 0;
    let selectable,r,oid;
    for (let j = 0; j < rows.length; j++) {
        selectable = true;
        r = rows[j];
        if(getSelectable) {
          selectable = getSelectable(r);
        }
        if (selectable) {
          oid = r[propsOid];
          selectedRecord.value[oid]=r;
          gridRef.value.toggleRowSelection(r, true);
        }
      }
}

export const showErrorMsg = (msg) => {
    ElMessage({
        message: msg,
        type: 'error',
        duration: 10000,
        offset: 100,
        showClose: true,
        appendTo: document.body
    });
}

export const showWarningMsg = (msg) => {
    ElMessage({
        message: msg,
        type: 'warning',
        duration: 10000,
        offset: 100,
        showClose: true,
        appendTo: document.body
    });
}

export const clearCookies = (isBackCover, noRedirecturl) => {
    cookies.remove("id_token");
    cookies.remove("access_token");
    cookies.remove("refresh_token");
    cookies.remove("username");
    cookies.remove("x-esession");
    // Start SK-COMMON-0083, Tom.Li, 2024/08/19
    localStorage.removeItem("loginedPage");
    localStorage.removeItem("apiDecode");
    localStorage.removeItem("isDev");
    sessionStorage.removeItem("isLoggedIn");
    sessionStorage.removeItem("pageName");
    sessionStorage.removeItem("page-status");
    // End SK-COMMON-0083, Tom.Li, 2024/08/19
    if(isBackCover) {
        // Start SK-COMMON-0083, Tom.Li, 2024/08/22
        localStorage.clear();
        sessionStorage.clear();
        // End SK-COMMON-0083, Tom.Li, 2024/08/22
        if (!noRedirecturl) {
            location.href = import.meta.env.VITE_REDIRECTURL;
        } 
    }
}

export const saveMsgBox = async (unPopping) => {
    if (unPopping) {
        return true;
    }
    try {
        let result = ElMessageBox.confirm(
            'Save the changes?',
            'Warning',
            {
                confirmButtonText: 'OK',
                cancelButtonText: 'Cancel',
                type: 'warning',
            }
        );
        return result;
    } catch (error) {
        return false;
    }
}

export const currentPageInfo = {
    statusCode: {
        SearchPage:"SearchPage",
        CreatePage:"CreatePage",
        EditPage:"EditPage",
        ReadOnly:"ReadOnly",
        AprrovePage:"AprrovePage",
    },
    getPageStatus: () => {
        return sessionStorage.getItem("page-status");
    },
    setCreatePage: () => {
        sessionStorage.setItem("page-status",currentPageInfo.statusCode.CreatePage);
    },
    isCreatePage: () => {
        return sessionStorage.getItem("page-status") == currentPageInfo.statusCode.CreatePage;
    },
    setEditPage: () => {
        sessionStorage.setItem("page-status",currentPageInfo.statusCode.EditPage);
    },
    setSearchPage: () => {
        sessionStorage.setItem("page-status",currentPageInfo.statusCode.SearchPage);
    },
    setDetailPageStatus: (row) => {
		// Start SIR-HLH-R97, Tom.Li, 2024/08/20
        if(!row||!row.hasOwnProperty("recordStatus")||(row.mkckAction == 'C')||(row.afterImage&&row.afterImage.mkckAction == 'C')) {
		// Start SIR-HLH-R97, Tom.Li, 2024/08/20
            currentPageInfo.setCreatePage();
        } else {
            currentPageInfo.setEditPage();
        }
    }
};

// Start SK-COMMON-0083, Tom.Li, 2024/08/19
export const haveLoggedIn = () => {
    if(sessionStorage.getItem("isLoggedIn") && cookies.get("x-esession")){
        return true;
    } else if (cookies.get("x-esession")) {
        let obj = localStorage.getItem("loginedPage");
        if (obj&&obj!="{}") {
            afterLoginOperat();
            return true;
        } else {
            clearCookies();
            return false;
        }
    } else {
        clearCookies();
        return false;
    }
}

export const afterLoginOperat = () => {
    sessionStorage.setItem("isLoggedIn", 'Y');
    let name = "page"+ (new Date().getTime());
    sessionStorage.setItem("pageName", name);

    let obj = JSON.parse(localStorage.getItem("loginedPage"));
    if(!obj) {
        obj = {};
    }
    obj[name]="Y";
    localStorage.setItem("loginedPage", JSON.stringify(obj));
}
// End SK-COMMON-0083, Tom.Li, 2024/08/19

export const isGridModified = (formVpo, curRow) => {
    ElMessage.closeAll();
    if(isModified(formVpo, curRow)){
        showErrorMsg("The sub table data has not been saved. Please confirm if you want to leave.");
        return true;
    }
    return false;
}

export const isModified = (formVpo, curRow) => {
    if (formVpo === curRow) return false; 
    if (curRow==null) {
        return false;
    } 
    if (typeof formVpo !== 'object' || formVpo === null ||  
        typeof curRow !== 'object' || curRow === null) {  
        return true;  
    }  
    let cpFormVpo = { ...formVpo };
    delete cpFormVpo.version;
    delete cpFormVpo.sysUpdateDate;
    delete cpFormVpo.sysUpdater;
    const keys1 = Object.keys(formVpo);  
    const keys2 = Object.keys(curRow); 
  
    for (let key of keys1) {  
        if (!equalObj(formVpo[key], curRow[key])) {
            return true;  
        }  
    }
  
    return false;  
}
/**
 * 格式化文件名
 * @param fileName 文件名
 * @returns {string} 
 *  test_20231010120000_ACK_20231010120000.xls --> 输出: test.xls
 *  test_ACK_20231010120000.xls --> 输出: test.xls
 *  test_20231010120000.xls --> 输出: test.xls
 */
export const normalizeFileName = (fileName) => {
    const pattern1 = /^(.+?)_\d{14}_ACK_\d{14}(\.[a-zA-Z0-9]+)$/i;
    const pattern2 = /^(.+?)_ACK_\d{14}(\.[a-zA-Z0-9]+)$/i;
    const pattern3 = /^(.+?)_\d{14}(\.[a-zA-Z0-9]+)$/i;
    if (pattern1.test(fileName)) {
        return fileName.replace(pattern1, '$1$2');
    } else if (pattern2.test(fileName)) {
        return fileName.replace(pattern2, '$1$2');
    } else if (pattern3.test(fileName)) {
        return fileName.replace(pattern3, '$1$2');
    }
    return fileName;
}

const equalObj = (objA, objB) => {
    if(!objA&&!objB) {
        return true;
    }
    if (objA == objB) {
        return true;
    }
    return false;
}