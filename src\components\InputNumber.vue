<template #default>

  <el-input v-model="data" 
    class="input-number"  
    :style="style"
    :disabled="disabled"
    @change="change"
    @blur="(e)=>{ delete $.f; thousBlur( e.target, scale()) }"
    @focus="(e)=>{ $.f = true; }"
    :formatter="(v)=>{return $.f?thousEditFormat(v,scale(),precision(), parseBool(props.isNegative)):thousFormat(v,scale()) }"
    :parser="thousParser">
    <template #prepend v-if="$slots.prepend">
      <slot name="prepend"></slot>
    </template>
    <template #append v-if="$slots.append">
      <slot name="append"></slot>
    </template>
    </el-input>
  
</template>

<script lang="ts" setup>
import { computed, defineEmits } from 'vue';
import  { thousEditFormat, thousFormat, thousParser, parseBool } from '~/util/Function.js';

const props = defineProps(["modelValue", "style", 'disabled', 'scale', 'precision', 'change', 'isNegative']);
const emit = defineEmits(['update:modelValue']);
const data = computed({
  get(){
    return props.modelValue;
  },
  set(val){
    emit("update:modelValue", val);
  }
});

const scale = () => {
  return props.scale >= 0 ? props.scale : 2;
}

const precision = () => {
  return (props.precision >= 0  ? props.precision : 13) - scale();
}

const change = (val) => {
  if(props.change){
    props.change(val);
  }
}

const thousBlur = (e, scale = 2) => {
    let val = e.value;
    if (val === 0) { val = '0'; }
    if (val) {
        if (val == data.value) {
          return;
        }
        val = thousParser(val)
        let num = val.split(".")
        if (scale > 0 && num.length > 2) {
            num[1] = num.slice(1).join('');
        }
        if (scale > 0) {
            num[1] = String((num[1]||"")+"0000000000").substring(0,scale)
        }
        data.value = num[0] + ( scale>0 ? ("." + num[1]) : "");
        e.value = thousFormat(val, scale);
    } else {
      data.value = "";
    }
    return data.value;
}

</script>

<style>
</style>