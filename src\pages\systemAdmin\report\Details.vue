<template>
    <BaseDetails ref="details" :handleSave="handleSave" :viewOriginalForm="viewOriginalForm" :reload="props.reload" :form="jobForm">
        <el-form :validateOnRuleChange="false" :disabled="formDisabled" ref="formRef" style="width: 100%" :model="jobForm.form" :rules="rules" status-icon>
            <FormRow>
                <FormItemSign :detailsRef="details" label-width="170px" :label="$t('csscl.reportScheduler.schedulerId')" prop="schedulerId">
                    <el-input v-model="jobForm.form.schedulerId" style="width: 185px" maxlength="10" disabled  />
                </FormItemSign>

                <FormItemSign :detailsRef="details" :label="$t('csscl.reportCenter.search.reportDesc')" prop="reportTemplateCode" label-width="140px">
                    <!-- Start CAP1-423, Tom.li, 2025-02-20 -->
                    <GeneralSearchInput inputStyle="width: 180px" style="width: 440px" v-model="jobForm.form.reportTemplateCode"  :searchType="formDisabled?'outDocFileType':'outDocFileTypeP1'"
                        :eventChange="(val)=>{changeReportId(val)}" :dbClick="(row)=>{changeReportId(row.code)}" codeTitle="csscl.reportCenter.search.reportDesc" maxlength="8"/>
                    <!-- End CAP1-423, Tom.li, 2025-02-20 -->
                </FormItemSign>

                <FormItemSign :detailsRef="details" label-width="100px" :label="$t('common.title.status')" prop="status">
                    <el-col :span="8">
                        <Select v-model="jobForm.form.status" style="width: 150px" type='STATUS' />
                    </el-col>
                </FormItemSign>
            </FormRow>
            <FormRow>
                <FormItemSign :detailsRef="details" label-width="170px"  :label="$t('csscl.reportScheduler.deliveryChannel')" prop="channel">
                    <!-- Start CAP1-423, Tom.li, 2025-02-20 -->
                    <Select v-model="jobForm.form.channel"  style="width: 185px"  :type="formDisabled?'DELIVERY_CHANNEL':'DELIVERY_CHANNEL_P1'"  :change="changeChannel" />
                    <!-- End CAP1-423, Tom.li, 2025-02-20 -->
                </FormItemSign>
                <FormItemSign :detailsRef="details" label-width="140px" :label="$t('csscl.jobScheduler.ftgidCode')" prop="ftgidCode">
                    <GeneralSearchInput inputStyle="width: 180px" style="width: 440px" v-model="jobForm.form.ftgidCode"
                        searchType="ftgidCode" setTitle="true" :disabled="!isFTGChannel" />
                </FormItemSign>
                <FormItemSign></FormItemSign>
            </FormRow>
            <FormRow>
                <FormItemSign :detailsRef="details" label-width="170px" :label="$t('csscl.reportScheduler.description')" prop="description">
                    <el-input v-model="jobForm.form.description" style="width:900px" maxlength="100" />
                </FormItemSign>
            </FormRow>

            <br>
            <!-- ///////////////////////////////////////////////Schedule Criteria Start/////////////////////////////////////////////////-->
            <el-row>
                <span style="font-size: 16px; font-weight: bold;">Schedule Criteria</span>
                <div style="paddingBlock:5px;width: 100%;display: flex;flex-flow: wrap;border-top: 1px solid;"></div>
            </el-row>
            <FormRow>
                <ElFormItemProxy style="width:590px">
                    <FormRow>
                        <FormItemSign :detailsRef="details" label-width="140px" :label="$t('csscl.jobScheduler.effectiveDateFrom')" prop="effectiveDateFrom">
                            <DateItem v-model="jobForm.form.effectiveDateFrom"
                                :title="$t('message.later.equal.curdate', [$t('csscl.jobScheduler.effectiveDateFrom')]) + '\r' +
                                       $t('message.later.dateto', [$t('csscl.jobScheduler.effectiveDateFrom'), $t('csscl.jobScheduler.effectiveDateTo')] ) " />
                        </FormItemSign>
                        <FormItemSign :detailsRef="details" label-width="45px" :label="$t('common.title.date.to')" :hideLabel="$t('csscl.jobScheduler.effectiveDateTo')" prop="effectiveDateTo">
                            <DateItem v-model="jobForm.form.effectiveDateTo"
                                :title="$t('message.later.curdate', [$t('csscl.jobScheduler.effectiveDateTo')])" />
                        </FormItemSign>
                    </FormRow>
                </ElFormItemProxy>
                <FormItemSign :detailsRef="details" label-width="80px" :label="$t('csscl.jobScheduler.frequency')" prop="frequency" style="width:300px">
                    <Select v-model="jobForm.form.frequency" style="width: 220px" type='FREQUENCY' :change="changeFrequency" />
                </FormItemSign>
                <ElFormItemProxy></ElFormItemProxy>
            </FormRow>
            <FormRow>
                <el-checkbox-group v-model="jobForm.form.tmpFrequencyDays">
                    <el-checkbox label="Monday" value="Monday" class="days-checkbox" :disabled="!isWeek"
                        @change="handleCheckChange('Monday')"></el-checkbox>
                    <el-checkbox label="Tuesday" value="Tuesday" class="days-checkbox" :disabled="!isWeek"
                        @change="handleCheckChange('Tuesday')"></el-checkbox>
                    <el-checkbox label="Wednesday" value="Wednesday" class="days-checkbox" :disabled="!isWeek"
                        @change="handleCheckChange('Wednesday')"></el-checkbox>
                    <el-checkbox label="Thursday" value="Thursday" class="days-checkbox" :disabled="!isWeek"
                        @change="handleCheckChange('Thursday')"></el-checkbox>
                    <el-checkbox label="Friday" value="Friday" class="days-checkbox" :disabled="!isWeek"
                        @change="handleCheckChange('Friday')"></el-checkbox>
                    <el-checkbox label="Saturday" value="Saturday" class="days-checkbox" :disabled="!isWeek"
                        @change="handleCheckChange('Saturday')"></el-checkbox>
                    <el-checkbox label="Sunday" value="Sunday" class="days-checkbox" :disabled="!isWeek"
                        @change="handleCheckChange('Sunday')"></el-checkbox>
                </el-checkbox-group>
            </FormRow>
            <EditGrid v-model="jobForm.form.flowJobScheduleTimeVPOS" 
                tableWidth="50%"
                :form="newTimeRow" oid="flowJobScheduleTimeOid" 
                :rules="newTimeRowRules"
                :details="details"
                :disabled="formDisabled"
                uniqueKey="scheduleTime"
                ref="scheduleTimeGridRef"
                >
                <template #columns>
                    <el-table-column prop="scheduleTime" :label="$t('csscl.jobScheduler.sheduleTime')" />
                    <el-table-column prop="status" :label="$t('common.title.status')">
                        <template #default="scope">
                            {{ getCommonDesc('STATUS', scope.row.status) }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="recordStatus" width="220" :label="$t('common.title.recordStatus')">
                        <template #default="scope">
                            {{ getCommonDesc('RECORD_STATUS', scope.row.recordStatus) }}
                            <span v-if="scope.row.recordStatus && scope.row.recordStatus !== 'A'">
                                for {{ getCommonDesc('MKCK_ACTION', scope.row.mkckAction) }}
                            </span>
                        </template>
                    </el-table-column>
                </template>
                <template #form>
                    <FormItemSign :detailsRef="details" label-width="120px" prop="scheduleTime" :label="$t('csscl.jobScheduler.sheduleTime')">
                        <el-time-picker v-model="newTimeRow.scheduleTime" format="HH:mm" value-format="HH:mm" style="width: 300px;"></el-time-picker>
                    </FormItemSign>
                    <FormItemSign :detailsRef="details" label-width="120px" prop="status" :label="$t('common.title.status')">
                        <Select v-model="newTimeRow.status" style="width: 300px;" type="STATUS" />
                    </FormItemSign>
                </template>
            </EditGrid>
            <!-- ///////////////////////////////////////////////Schedule Criteria  End/////////////////////////////////////////////////-->
            <br>
            <!-- ///////////////////////////////////////////////Report Criteria Start/////////////////////////////////////////////////-->
            <div v-if="jobForm.form.reportTemplateCode">
                <template v-for="(item, index) in reportCriteriaItem" :key="index">
                    <FormRow :detailsRef="details" v-if="item.keyType !== 'hide'" style="width: 100%">
                        <FormItemSign :detailsRef="details" v-if="initItem(item)"></FormItemSign>
                        <el-row v-else-if="item.keyType === 'title'" style="margin-top: 25px;">
                            <span style="font-size: 16px; font-weight: bold;">{{item.keyDesc}}</span>
                            <div style="paddingBlock:5px;width: 100%;display: flex;flex-flow: wrap;border-top: 1px solid;"></div>
                        </el-row>
                        <div v-else-if="item.keyType === 'accountList'"  style="display: flex; align-items: center;">
                            <!-- <FormItemSign :detailsRef="details" v-if="item.keyType === 'accountList'" :prop="item.keyField"></FormItemSign> -->
                            <el-row style="width: 100%;">
                                <el-col :span="12">
                                    <el-table :data="jobForm.form.reportAccounts" height="250" highlight-current-row
                                    :row-class-name="tableRowClassName" ref="accountTabRef" @row-click="handleClick" :cell-style="cell"
                                    @row-dblclick="handleDbAccountClick" @current-change="handleCurrentAccountChange" border
                                        style="width: 100%; margin: 0; padding: 0;">
                                        <el-table-column prop="accountGroup" :label="$t('csscl.reportScheduler.accountGroup')" >
                                            <template #default="scope">
                                                {{ getCommonDesc('ACCOUNT_GROUP', scope.row.accountGroup) }}
                                            </template>
                                        </el-table-column>
                                        <el-table-column prop="accountGroupDetails" :label="$t('csscl.reportScheduler.accountGroupDetails')" >
                                            <template #default="scope">
                                                {{ getCommonDesc('CLIENT_GROUP', scope.row.accountGroupDetails) }}
                                            </template>
                                        </el-table-column>
                                        <el-table-column prop="accounts" :label="$t('csscl.reportScheduler.numOfCustodyAccount')">
                                            <template #default="scope">
                                                {{ getCommonDesc('ACCOUNTS', scope.row.accounts) }}
                                            </template>
                                        </el-table-column>
                                        <el-table-column  prop="recordStatus" :label="$t('common.title.recordStatus')">
                                            <template #default="scope">
                                                {{ getRecordStatusDesc(scope.row) }}
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                </el-col>
                                <el-col :span="12">
                                    <el-space style="margin-top: 10px;margin-left: 10px;" direction="vertical">
                                        <el-button @click="showAddAccount" style="width: 30px" :icon="Plus" size="small"></el-button>
                                        <el-button @click="deleteAccount" style="width: 30px" :icon="Minus" size="small"></el-button>
                                    </el-space>
                                </el-col>
                            </el-row>
                            <el-form label-position="left">
                                <br>
                                <el-row style="margin:5px 0" v-if="isShowAddAccount">
                                    <FormItemSign :detailsRef="details" label="Account Group" prop="accountGroup">
                                        <Select v-model="newAccountsRow.accountGroup" style="width: 150px" :type="item.keyDataSource" :change="changeAccountGroup" :disabled="formDisabled"/>
                                    </FormItemSign>
                                </el-row>
                                <br>
                                <el-col :span="14" v-show="isShowAddAccount">
                                    <el-card>
                                        <FormItemSign v-if="newAccountsRow.accountGroup === 'CG'" :detailsRef="details" label="Client Group" prop="clientGroup" label-width="130px">
                                            <CommonSearchInput v-model="newAccountsRow.clientGroup"
                                                :change="(val)=>{
                                                    newAccountsRow.clientGroup=val;
                                                    changeClientGroup(val)
                                                }"
                                                :dbClick="(row)=>{
                                                    newAccountsRow.clientGroup=row.code;
                                                    changeClientGroup()
                                                }"
                                                :disabled="formDisabled"
                                                maxlength="50"
                                                codeTitle="Client Group"
                                                showDesc="false"
                                                commType='CLIENT_GROUP' />
                                        </FormItemSign>
                                        <FormItemSign v-if="newAccountsRow.accountGroup === 'CNC'" :detailsRef="details" label="Client Number CIN" prop="clientGroup" label-width="130px">
                                            <GeneralSearchInput  v-model="newAccountsRow.clientGroup"
                                                    style="width:200px"
                                                    maxlength="11"
                                                    searchType="clientCode"
                                                    showDesc="false"
                                                    :change="(val)=>{
                                                        newAccountsRow.clientMasterOid='1';
                                                        changeClientGroup()
                                                    }"
                                                    :disabled="formDisabled"
                                                    :dbClick="(row)=>{
                                                        newAccountsRow.clientGroup=row.code;
                                                        newAccountsRow.clientMasterOid=row.var1;
                                                        changeClientGroup()
                                                    }"/>
                                        </FormItemSign>
                                        <FormItemSign v-if="newAccountsRow.accountGroup === 'FM'" :detailsRef="details" label="Fund Manager" prop="clientGroup" label-width="130px">
                                            <CommonSearchInput v-model="newAccountsRow.clientGroup"
                                                            :change="(val)=>{
                                                                newAccountsRow.clientGroup=val;
                                                                changeClientGroup(val)
                                                            }"
                                                            :dbClick="(row)=>{
                                                                newAccountsRow.clientGroup=row.code;
                                                                changeClientGroup()
                                                            }"
                                                            :disabled="formDisabled"
                                                            codeTitle="Fund Manager"
                                                            maxlength="50"
                                                            showDesc="false"
                                                            commType='FUND_MANAGER' />
                                        </FormItemSign>
                                        <FormItemSign v-if="newAccountsRow.accountGroup === 'CA'" :detailsRef="details" label="Custody Account" prop="clientGroup" label-width="130px">
                                            <GeneralSearchInput v-model="newAccountsRow.clientGroup"
                                                    style="width:200px"
                                                    searchType="custodyAcct"
                                                    showDesc="false"
                                                    :params="{ recordStatus: 'A'  }"
                                                    codeTitle="Custody Account"
                                                    :change="(val)=>{
                                                        newAccountsRow.clientGroup=val;
                                                        changeClientGroup(val)
                                                    }"
                                                    :disabled="formDisabled"
                                                    :dbClick="(row)=>{
                                                        newAccountsRow.clientGroup=row.code;
                                                        changeClientGroup()
                                                    }"/>
                                        </FormItemSign>
                                        <FormItemSign v-if="newAccountsRow.accountGroup === 'SP'" :detailsRef="details" label="Service Plan" prop="clientGroup" label-width="130px">
                                            <CommonSearchInput v-model="newAccountsRow.clientGroup"
                                                                :change="(val)=>{
                                                                    newAccountsRow.clientGroup=val;
                                                                    changeClientGroup(val)
                                                                }"
                                                                :dbClick="(row)=>{
                                                                    newAccountsRow.clientGroup=row.code;
                                                                    changeClientGroup()
                                                                }"
                                                                :disabled="formDisabled"
                                                                codeTitle="Service Plan"
                                                                showDesc="false"
                                                                commType='SERVICE_PLAN_CODE' />
                                        </FormItemSign>
                                        <br>
                                        <FormItemSign :detailsRef="details" label="Accounts" prop="accounts" label-width="130px">
                                            <Select v-if="!disabledSelecte" v-model="newAccountsRow.accounts" style="width: 150px" type='ACCOUNTS' :disabled="formDisabled"/>
                                            <Select v-if="disabledSelecte" v-model="newAccountsRow.accounts" style="width: 150px" type='ACCOUNTS'  :disabled="true"/>
                                        </FormItemSign>
                                        <SelectionGrid url="/datamgmt/api/v1/account/list" ref="accountGrid" 
                                            :params="accountParams" 
                                            :disabled="formDisabled"
                                            :beforeSearch="()=>{ return beforeSearchValidate();}" 
                                            :columns="[
                                                { title: 'csscl.acctCode.clientAccountOid', name: 'tradingAccountCode' },
                                                { title: 'csscl.acctCode.accountShortName', name: 'accountShortName' },
                                                { title: 'common.title.recordStatus', name: 'recordStatus', fn: getRecordStatusDesc },
                                            ]">
                                            <template v-slot:tableColumnAfter>
                                                <el-table-column type="selection"  width="55" :selectable="checkSelectable" />
                                            </template>
                                        </SelectionGrid>
                                        <div style="justify-content: right;display: flex;">
                                            <el-button @click="hiddenAddAccount" class="ep-button-custom">Cancel</el-button>
                                            <el-button type="primary" @click="addAccount" class="ep-button-custom" :disabled="formDisabled">OK</el-button>
                                        </div>
                                    </el-card>
                                </el-col>
                            </el-form>
                        </div>
                        <FormItemSign :detailsRef="details" v-else-if="item.keyType === 'SearchInput'" :label="$t(item.keyDesc)" :prop="item.keyField">
                            <SearchInput :style="props.style" v-model="reportCriteriaData[item.keyField]" @change="keychange(item)"
                                :url="item.keyDataSource" :title="$t(item.keyDesc)" :columns="[
                                    {
                                        title: $t(JSON.parse(item.keyOptions || null).codeTitle),
                                        colName: JSON.parse(item.keyOptions || null).code,
                                        width: '380px',
                                    },
                                    {
                                        title: $t(JSON.parse(item.keyOptions || null).nameTitle),
                                        colName: JSON.parse(item.keyOptions || null).name,
                                    }
                                ]">
                            </SearchInput>
                        </FormItemSign>

                        <FormItemSign :detailsRef="details" v-else-if="item.keyType === 'Search'" :label="$t(item.keyDesc)" :prop="item.keyField">
                            <GeneralSearchInput :style="style" v-model="reportCriteriaData[item.keyField]" :inputStyle="item.style"
                                                :codeTitle="$t(item.keyDesc)" :searchType="item.keyDataSource" style="width:500px" :maxlength="JSON.parse(item.keyFunction||null)?.maxlength"
                                                :disabled="item.keyFunction ? changeDisabled[JSON.parse(item.keyFunction)?.disabled] : false"/>
                        </FormItemSign>

                        <FormItemSign :detailsRef="details" v-else-if="item.keyType === 'MSearch'" :label="$t(item.keyDesc)" :prop="item.keyField">
                            <MultipleGeneralSearchInput :style="style" v-model="reportCriteriaData[item.keyField]" :inputStyle="item.style" :maxlength="JSON.parse(item.keyFunction||null)?.maxlength"
                                                :codeTitle="$t(item.keyDesc)" :searchType="item.keyDataSource" style="width:500px" />
                        </FormItemSign>

                        <FormItemSign :detailsRef="details" v-else-if="item.keyType === 'dateTimeRange'" :label="$t(item.keyDesc)" :prop="item.keyField">
                            <el-date-picker :style="style" v-model="item.keyValue" @change="keychange(item)"
                                type="datetimerange" range-separator="To" start-placeholder="Start date"
                                end-placeholder="End date" value-format="YYYY/MM/DD HH:mm:ss" />
                        </FormItemSign>
                        <ElFormItemProxy v-else-if="item.keyType === 'D2D'" class="generator-form-item">
                            <FormItemSign :detailsRef="details" :label="$t(item.keyDesc)" :prop="item.keyField">
                                <DateItem style="width: 130px;" v-model="reportCriteriaData[item.keyField]" />
                            </FormItemSign>
                            <FormItemSign :detailsRef="details" label-width="40" label="To" :prop="item.to.keyField" :hideLabel="$t(item.to.keyDesc)">
                                <DateItem style="width: 130px;" v-model="reportCriteriaData[item.to.keyField]" :alt="$t(item.to.keyDesc)"/>
                            </FormItemSign>
                        </ElFormItemProxy>
                        <el-form-item v-else-if="item.keyType === 'T2T'" :label="$t(item.keyDesc)" class="generator-form-item" >
                            <el-space direction="vertical">
                                <FormItemSign v-if="item.to || ['CIB005','CIB006','CIB009','CIB010','CA018','CA023'].includes(item.reportTemplateCode)" :detailsRef="details" label-width="40" label="From" :prop="item.keyField">
                                    <div style="width: 500px;">
                                        <el-input style="width: 60px;" v-model="reportCriteriaData[item.keyField]" oninput="value=value.replace(/[^\d.]/g,'')" class='text-none'></el-input>
                                        <span style="padding-left: 10px; font-size: 12px;">day(s) before Report Generation Date</span>
                                    </div>
                                </FormItemSign>
                                <FormItemSign v-if="item.to" :detailsRef="details" label-width="40" label="To" :prop="item.to.keyField" :hideLabel="$t(item.to.keyDesc)">
                                    <div style="width: 500px;">
                                        <el-input style="width: 60px;" v-model="reportCriteriaData[item.to.keyField]" oninput="value=value.replace(/[^\d.]/g,'')" class='text-none'></el-input>
                                        <span style="padding-left: 10px; font-size: 12px;">day(s) after Report Generation Date</span>
                                    </div>
                                </FormItemSign>
                                <FormItemSign v-if="!item.to && ['CIB003','CIB008'].includes(item.reportTemplateCode)" :detailsRef="details" label-width="40" :prop="item.keyField" :hideLabel="$t(item.keyDesc)">
                                    <div style="width: 1000px;">
                                        <span style="padding-right: 10px; font-size: 12px;">From Report Generation Date to</span>
                                        <el-input style="width: 60px;" v-model="reportCriteriaData[item.keyField]" oninput="value=value.replace(/[^\d.]/g,'')" class='text-none'></el-input>
                                        <span style="padding-left: 10px; font-size: 12px;">day(s) after Report Generation Date</span>
                                    </div>
                                </FormItemSign>
                            </el-space>
                        </el-form-item>
                        <FormItemSign :detailsRef="details" v-else-if="item.keyType === 'date'" :label="$t(item.keyDesc)" :prop="item.keyField">
                            <DateItem style="width: 130px;" v-model="reportCriteriaData[item.keyField]" />
                        </FormItemSign>
                        <FormItemSign :detailsRef="details" v-else-if="item.keyType === 'select' && item.keyField !== 'changeReport'" :label="$t(item.keyDesc)" :prop="item.keyField">
                            <Select v-model="reportCriteriaData[item.keyField]"
                                :source="(item.keyOptions || '').startsWith('source:') ? source : JSON.parse(item.keyOptions || null)" 
                                :type="item.keyDataSource" :style="item.style || props.style"  
                                :disabled="item.keyFunction ? changeDisabled[JSON.parse(item.keyFunction)?.disabled] : false"/>
                        </FormItemSign>
                        <FormItemSign :detailsRef="details" v-else-if="item.keyType === 'text'" :label="$t(item.keyDesc)" :prop="item.keyField">
                            <el-input v-model="reportCriteriaData[item.keyField]" :maxlength="JSON.parse(item.keyFunction||null)?.maxlength" :style="style" class='text-none'></el-input>
                        </FormItemSign>
                        <FormItemSign :detailsRef="details" v-else-if="item.keyType === 'hide'" :prop="item.keyField">
                            <el-input v-model="reportCriteriaData[item.keyField]" ></el-input>
                        </FormItemSign>
                        <FormItemSign :detailsRef="details" v-else-if="item.keyType === 'readOnly'" :label="$t(item.keyDesc)" :prop="item.keyField">
                            <el-input v-model="reportCriteriaData[item.keyField]" disabled :style="style" class='text-none'></el-input>
                        </FormItemSign>
                        <FormItemSign :detailsRef="searchRef" v-else-if="item.keyType === 'checkBox'" :label="$t(item.keyDesc)" :prop="item.keyField" class="check-box">
                            <el-checkbox-group v-model="reportCriteriaData[item.keyField]" style="float: left;">
                                <el-checkbox v-for="(rw, index) in JSON.parse(item.keyOptions || null)" :key="index" :label="$t(rw.label)" :init="chkBox(item, rw)" 
                                :value="rw.value" @change="changeBox(item.keyField, rw.value, rw?.show)" :style="item.style || props.style"/>
                            </el-checkbox-group>
                        </FormItemSign>
                        <FormItemSign :detailsRef="details" v-else-if="item.keyType === 'number'" :label="$t(item.keyDesc)" :prop="item.keyField">
                            <InputNumber v-model="reportCriteriaData[item.keyField]" style="width: 190px;" :maxlength="JSON.parse(item.keyFunction||null)?.maxlength" scale="0"/>
                        </FormItemSign>
                    </FormRow>
                </template>
            </div>
            <!-- ///////////////////////////////////////////////Report Criteria End/////////////////////////////////////////////////-->

        </el-form>
    </BaseDetails>
</template>

<script lang="ts" setup>
import { ref, reactive, getCurrentInstance, watch, toRaw, nextTick } from 'vue';
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessageBox, ElMessage } from 'element-plus';
import BaseDetails from '~/pages/base/Details.vue';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import { getOid, getCommonDesc, getRecordStatusDesc, highlight, showErrorMsg, getSysCtrlDate, saveMsgBox, currentDate, dateFormat, getEnvConfigVal, preProDate } from '~/util/Function.js';
import { commonRules, showValidateMsg, anyOneValid } from '~/util/Validators.js';
import { Plus, Minus} from '@element-plus/icons-vue';
import CommonSearchInput from '~/pages/base/CommonSearchInput.vue';
import SelectionGrid  from '~/pages/base/SelectionGrid.vue';
import cloneDeep from 'lodash/cloneDeep';
import { cloneObj, addCustValid, compListInCustValid, focusType } from "~/util/ModifiedValidate";
import { RefSymbol } from '@vue/reactivity';


const { proxy } = getCurrentInstance();
const props = defineProps(['reload']);
const details = ref();
let curProcDate = "";
let preProcDate = "";
(async ()=>{ await currentDate().then( (v)=>{ curProcDate = dateFormat( v ); } ); })();
(async ()=>{ await preProDate().then( (v)=>{ preProcDate = dateFormat( v ); } ); })();
const curDate = (() => {
  let d = new Date();
  d.setHours(0, 0, 0, 0);
  return dateFormat(d)
})();
const jobForm = reactive({
    rules:()=>{ return [{ rules:rules }] },
    form: {
        tmpFrequencyDays:[],
        flowJobScheduleTimeVPOS:[],
        reportCriteriaVPO: {
            reportAccounts:[],
            field_1:"",
            field_2:"",
            field_3:"",
            field_4:"",
            field_5:"",
            field_6:"",
            field_7:"",
            field_8:"",
            field_9:"",
            field_10:"",
            field_11:"",
            field_12:"",
            field_13:"",
            field_14:"",
            field_15:"",
            field_16:"",
            field_17:"",
            field_18:"",
            field_19:"",
            field_20:"",
            flowJobControlOid:null,
            reportCriteriaOid:null,
            reportTemplateCode:"",
            groupByType:"",
            fileFormat:"",
            fileLanguage:"",
            language:"",
        } as any,
        reportAccounts:[],
    }
})

const formDisabled = ref(false);
const validateFrequency = (rule: any, value: any, callback: any) => {
    if (value) {
        if(value!=='D'){
            if(jobForm.form?.tmpFrequencyDays){
                callback();
            return;
            }else{
                callback(new Error('Days of week must be entered if Frequency=Weekly,Biweekly,Every Third Week,Every FourthWeek'));
                return;
            }
        }
    }
    callback();
}
const validateFTGIdCode = (rule: any, value: any, callback: any) => {
    if(jobForm.form?.channel ==='FTG'){
        if (value) {
            callback()
            return;
        }else {
            callback(new Error('Please input '))
            return;
        }
    }
    callback()
}
const validateDateFrom =  (rule: any, value: any, callback: any) => {
    let currentDate = new Date(curPrcsDate);
    currentDate.setHours(0, 0, 0, 0);
    const effectiveDateTo = jobForm.form?.effectiveDateTo;
    if (value) {
        let valueDate = new Date(value);
        if (valueDate < currentDate) {
            callback(new Error('Date must be later than or equal to the current date.'));
            return;
        }
        if (effectiveDateTo) {
            const effectiveDateToDate = new Date(effectiveDateTo);
            if (valueDate > effectiveDateToDate) {
                callback(new Error(proxy.$t('message.earlier.equal.dateto', [proxy.$t('csscl.jobScheduler.effectiveDateFrom'), proxy.$t('csscl.common.dateTo')])));
                return;
            }
        }
    }
    callback();
};
const rules = reactive<FormRules<JobForm>>({
    opCtryRegionCode: [
        commonRules.required,
    ],
    status: [
        commonRules.required,
    ],
    ftgidCode: [
        { validator: validateFTGIdCode, trigger: 'blur' },
    ],
    effectiveDateFrom: [
        commonRules.required,
       { validator: validateDateFrom, trigger: 'blur' },
    ],
    effectiveDateTo:[
        commonRules.laterCurProcDate,
    ],
    frequency: [
        commonRules.required,
        { validator: validateFrequency, trigger: 'blur' },
    ],
    description:[
        commonRules.name,
    ],
    reportTemplateCode: [
        commonRules.required
    ]
})
interface JobForm {
    opCtryRegionCode: string;
    status: string;
    ftgidCode:string;
    effectiveDateFrom: string;
    effectiveDateTo: string;
    frequency: string;
    description: string;
    reportTemplateCode: string;
    flowJobScheduleTimeVPOS: TimeItem[];
    channel: string;
    schedulerId: string;
}
export type TimeItem = {
    index:number;
    flowJobControlOid: string;
    scheduleTime: string;
    status: string;
    recordStatus: string;
    mkckOid: string;
    pendingOid: string;
};
const formRef = ref<FormInstance>()
const newTimeRow = ref({});
const isFTGChannel= ref(false);
const isWeek= ref(false);
const newTimeRowRules = reactive<FormRules>({
    scheduleTime: [commonRules.required],
    status: [commonRules.required],
});
const initJobScheduleTime = ref();
const initReportAccounts = ref();
const scheduleTimeGridRef = ref();
const reportCriteriaFormRef = ref();
const reportCriteriaItem = ref();
const accountTabRef = ref();
const isShowAddAccount = ref(false);
const newAccountsRow = ref({});
const disabledSelecte=ref(false);
const accountGrid = ref();
const isdoubleCheck=ref(false);
const isdoubleCheckRow=ref();
const doubleCheckIndex = ref(-1);
const accountParams= ref({});
const currentAccountRow = ref({});
const source = ref({});
const reportCriteriaData = ref({});
const genRules = reactive({});
const fieldsDtl = {fields: {
    'reportTemplateCode': 'Report ID / Name',
    'frequency':'Frequency',
    'status':'Status',
    'ftgidCode':'FTGID Code',
    'description':'Description',
    'scheduleTime':'Schedule Time',
    'effectiveDateFrom':'Effective Date From',
    'effectiveDateTo':'Effective Date To',
}}
const changeDataRules = reactive({});
const checkFieldKey = reactive({});
const anyOneFieldsAndMsg = {anyOneFields: {}};
const changeDisabled = ref({});

const changeReportId= (val) => {
    cleanInit()
    fetchData(val);
}

const cleanInit = () =>{
    jobForm.form.reportAccounts = [];
    jobForm.form.tmpFrequencyDays = [];
    jobForm.form.flowJobScheduleTimeVPOS = [];
    reportCriteriaData.value = {};
    reportCriteriaItem.value = [];
    hiddenAddAccount();
}

const showDetails = (row, isdoubleCheck, jobNames) => {
    cleanInit();
    if(isdoubleCheck||row.recordStatus==='PA'){
        formDisabled.value = true;
        details.value.showDetails(row, true)
    }else{
        formDisabled.value = false;
        details.value.showDetails(row, false)
    }
    
    jobForm.form = {};

    if (row.currentOid) {
        editRow(row,isdoubleCheck);
    } else {
        details.value.initWatch({w1:jobForm,w2:newTimeRow});
    }
    // jobForm.form.validFields=['flowJobScheduleTimeVPOS','tmpFrequencyDays'];
}

const changeBox = (keyField, value, toKeyFiled) => {
    if(toKeyFiled){
        if((reportCriteriaData.value[keyField] && reportCriteriaData.value[keyField].includes(value))
            || (reportCriteriaData.value[keyField] && reportCriteriaData.value[keyField].includes(value))){
            changeDisabled.value[toKeyFiled] = false;
            changeDataRules[toKeyFiled].push(commonRules['selectRequired']);
            checkFieldKey[toKeyFiled] = toKeyFiled;
        } else {
            changeDisabled.value[toKeyFiled] = true;
            reportCriteriaData.value[toKeyFiled] = "";
            changeDataRules[toKeyFiled] = [];
            delete checkFieldKey[toKeyFiled];
        }
        Object.assign(rules, changeDataRules);
    }
};

const editRow = (row, disabled,newId) => {
    if(row?.isApproveDetail && disabled){
        let eventPkey = row?.eventPkey;
        let approveNumber = row?.approveNumber;
        proxy.$axios.get("/rptsched/api/v1/report/scheduler/approve?eventPkey=" + eventPkey+ 
        "&approveNumber="+approveNumber).then((body) => {
            if (body.success) {
                if(body.data){
                    jobForm.form = body.data;
                }
            }
            
            Object.assign(jobForm.form, row?.afterImage);
            if(row?.afterImage?.listData[0]){
                Object.assign(jobForm.form.reportCriteriaVPO, row?.afterImage?.listData[0]);
                if(jobForm.form.reportCriteriaVPO.reportAccounts){
                    jobForm.form.reportAccounts=[];
                    jobForm.form.reportAccounts= cloneObj(jobForm.form.reportCriteriaVPO.reportAccounts);
                }
              // start herg BAU-142 ********
              if(jobForm.form.reportCriteriaVPO.language){
                jobForm.form.fileLanguage = jobForm.form.reportCriteriaVPO.language;
              }
              // end herg BAU-142 ********
                
                Object.assign(reportCriteriaData.value, jobForm.form.reportCriteriaVPO)
            }
            if(jobForm.form?.reportTemplateCode){
                fetchData(jobForm.form.reportTemplateCode);
            }
            details.value.currentRow = jobForm.form;
        });
    } else {
        const oid = newId || (row?.currentOid && getOid(row, disabled, proxy));
        if (oid) {
            proxy.$axios.get("/rptsched/api/v1/report/scheduler?flowJobControlOid=" + oid).then((body) => {
                if (body.success) {
                    jobForm.form = body.data;
                    details.value.currentRow = body.data;
                    if(jobForm.form.reportCriteriaVPO.reportAccounts){
                        jobForm.form.reportAccounts=[];
                        jobForm.form.reportAccounts= cloneObj(jobForm.form.reportCriteriaVPO.reportAccounts);
                    }
                    // start herg BAU-142 ********
                    // if(jobForm.form.reportCriteriaVPO.fileLanguage){
                    //     jobForm.form.fileLanguage = jobForm.form.reportCriteriaVPO.fileLanguage;
                    // }
                    if(jobForm.form.reportCriteriaVPO.language){
                        jobForm.form.fileLanguage = jobForm.form.reportCriteriaVPO.language;
                    }
                    // end herg BAU-142 ********
                    if(jobForm.form.reportCriteriaVPO.fileFormat){
                        jobForm.form.fileFormat = jobForm.form.reportCriteriaVPO.fileFormat;
                    }
                    if(jobForm.form.reportCriteriaVPO.groupByType){
                        jobForm.form.groupByType = jobForm.form.reportCriteriaVPO.groupByType;
                    }
                    Object.assign(reportCriteriaData.value, jobForm.form.reportCriteriaVPO)
                    initReportAccounts.value =  cloneObj(jobForm.form.reportCriteriaVPO.reportAccounts);
                    initJobScheduleTime.value = cloneObj(jobForm.form.flowJobScheduleTimeVPOS);
                    addCustValid(jobForm.form, ()=>{
                      let result = compListInCustValid(scheduleTimeGridRef.value.showData,
                          initJobScheduleTime.value,
                          null,
                          ['scheduleTime', 'status']);
                        if (!result) {
                          result =compListInCustValid(jobForm.form.reportAccounts || [],
                          initReportAccounts.value,
                          null,
                          ['accountGroup', 'accountGroupDetails', 'accounts']);
                        }
                      return result;
                    });
                    if(jobForm.form?.reportTemplateCode){
                        fetchData(jobForm.form.reportTemplateCode);
                    }
                }
            });
        }
    }
}

const changeChannel = (val) => {
    if('FTG'===val){
        isFTGChannel.value=true;
    }else{
        isFTGChannel.value=false;
        jobForm.form.ftgidCode =null;
    }
};

const changeFrequency = (val) => {
    if('W'===val||'BW'===val||'ETW'===val||'EFW'===val){
        isWeek.value=true;
        if(!jobForm.form.tmpFrequencyDays){
         jobForm.form.tmpFrequencyDays =[]
       }
    }else{
        isWeek.value=false;
        jobForm.form.tmpFrequencyDays =[]
    }
}

const  handleCheckChange=(day) =>{}

const fetchData = async (reportId) => {
    if(reportId && reportId !== ""){
        const msg = await proxy.$axios.post("/rptsched/api/v1/report/criteria/list", {
            param: { reportTemplateCode: reportId, keyScene: 2 },
            current: 1,
            pageSize: 100,
        });
        if (msg.success) {
            reportCriteriaItem.value = [];
            reportCriteriaItem.value = msg.data.data;
            console.log(msg.data.data);
            checkReportCriteriaRules(msg.data.data);
        }
    } else {
        return;
    }
}

const checkReportCriteriaRules = (reportCriData) =>{
    fieldsDtl.fields = {
        'reportTemplateCode': 'Report ID / Name',
        'frequency':'Frequency',
        'status':'Status',
        'ftgidCode':'FTGID Code',
        'description':'Description',
        'scheduleTime':'Schedule Time',
        'effectiveDateFrom':'Effective Date From',
        'effectiveDateTo':'Effective Date To',
    };
    anyOneFieldsAndMsg.anyOneFields = {};
    let arr = (Object.keys(changeDataRules));
    arr?.forEach(e => {
        delete rules[e];
    });
    Object.assign(changeDataRules, {});
    for (let i = 0; i < reportCriData?.length; i++) {
        let data = reportCriData[i];
        if("anyOne" === data.keyType){
            anyOneFieldsAndMsg.anyOneFields[data.keyOptions] = data.keyFunction;
        } else {
            fieldsDtl.fields[data.keyField] = data.keyDesc;
            changeDataRules[data.keyField] = [];
            if(data.keyRules && data.keyRules.toUpperCase().indexOf("required".toUpperCase()) > -1){
                checkFieldKey[data.keyField] = data.keyField;
            }
            // CAP1-431 lisy
            if(data.keyFunction &&  "changeReport" != data.keyField){
            // CAP1-431 lisy
                const funStr = JSON.parse(data.keyFunction);
                if(funStr.default){
                    changeDisabled.value[funStr.disabled] = true;
                }
            }
        }
    }
    for (let i = 0; i < reportCriData?.length; i++) {
        let data = reportCriData[i];
        if (data.keyType == 'D2D') {
            if(reportCriData[i + 1].keyType == 'D2D') {
                data.to = reportCriData[i + 1];
                reportCriData.splice(i + 1, 1);
            }
        }
        //T2T要单独分开处理，不能和D2D同一个判断，不然会相互感染错误
        if (data.keyType == 'T2T') {
            if(reportCriData[i + 1].keyType == 'T2T') {
                data.to = reportCriData[i + 1];
                reportCriData.splice(i + 1, 1);
            }
        }
        data = Object.assign({}, data);
        do {
            data.isExists = false;
            if (data.keyRules) {
                let rs = data.keyRules.split("\|");
                for (let j = 0; j < rs.length; j++) {
                    let rl = rs[j].split(",")
                    if (commonRules[rl[0]]) {
                        for(let z = 1; z < rl.length; z++) {
                            rl['orig'+z] = rl[z];
                            if (rl[z].startsWith("getVal:")) { 
                                rl[z] = ()=>{ return reportCriteriaData[ rl['orig'+z].split(":")[1] ] }
							} else if (rl[z].startsWith("conf:")) {
								let arr = (rl['orig'+z].split(":")[1]).split("^");
								let key = "VITE_YEAR_DAYS";
								let defVal = arr[0];
								if (arr.length > 1) {
									key = arr[0];
									defVal = arr[1];
								}
								rl[z] = getEnvConfigVal(key, defVal);
                            } else if (rl[z].startsWith("label:")) {
                                rl[z] = proxy.$t( rl['orig'+z].split(":")[1] )
                            }
                        }
                        if (typeof commonRules[rl[0]] == 'object') {
                            changeDataRules[data.keyField].push(commonRules[rl[0]]);
                        } else {
                            changeDataRules[data.keyField].push(commonRules[rl[0]](rl[1],rl[2],rl[3],rl[4],rl[5],rl[6]));
                        }
                    }
                }
            }
            if ((data.keyType == 'D2D' || data.keyType == 'T2T') && data.to) {
                data = Object.assign({}, data.to);
                data.isExists = true;
            }
        } while (data.isExists);
    }
	// 因为使用了 Const 和 reactive 特性，给 jobForm 赋值有一定的延迟，需要先完成赋值后再设置 rules
	nextTick(()=>{
    	Object.assign(rules, changeDataRules);
	});
}

const tableRowClassName=({ row, rowIndex })  =>{
      row.index = rowIndex;
}

const handleClick = async(row: any, column: any, event: Event) => {
    if (currentAccountRow.value==null||currentAccountRow.value.reportCustodyAccountOid != row.reportCustodyAccountOid) {
        hiddenAddAccount();
    }
}

const viewOriginalForm = (pendingOid, isDisabled) => {
	formDisabled.value = isDisabled;
	proxy.$axios.get("/rptsched/api/v1/report/scheduler?flowJobControlOid="+pendingOid).then((body) => {
		if(body.success) {
			jobForm.form = body.data;
      // start BAU-142 herg ********
      if(jobForm.form.reportCriteriaVPO.reportAccounts){
        jobForm.form.reportAccounts=[];
        jobForm.form.reportAccounts= cloneObj(jobForm.form.reportCriteriaVPO.reportAccounts);
      }
      // end BAU-142 herg ********
		}
	});
}

const handleDbAccountClick = async (val, event, column) => {
    await accountGrid.value[0].emptySelection();
    isShowAddAccount.value = true;
    isdoubleCheck.value = true;
    isdoubleCheckRow.value = val;
    doubleCheckIndex.value = val.index;
    newAccountsRow.value = {};
    newAccountsRow.value.accountGroup = val.accountGroup;
    newAccountsRow.value.clientGroup = val.accountGroupDetails;
    newAccountsRow.value.accounts = 'S';
    newAccountsRow.value.clientMasterOid = val.clientMasterOid;
    newAccountsRow.value.tradingAccountCode = val.clientMasterOid;
    //详情查看时数据问题解决 xp20250301
    // accountParams.value = {};
    // accountParams.value = val.selectParams;
    if (val.accounts === 'A') {
        newAccountsRow.value.accounts = 'A';
    }
    // 确保 initSelectedData 包含所选的数据
    if (!val.initSelectedData) {
        val.initSelectedData = [];
    }
    // 合并 initSelectedData 和 selectedData
    if (val.selectedData) {
        const combinedData = [...val.initSelectedData, ...val.selectedData];
        const uniqueData = Array.from(new Map(combinedData.map(item => [item.tradingAccountCode, item])).values());
        val.initSelectedData = uniqueData;
    }
    await changeClientGroup().then(() => {
        accountGrid.value[0].initSelection(val.initSelectedData);
    });
};

const handleCurrentAccountChange = (val) => {
    currentAccountRow.value = val;
};

const showAddAccount = async () => {
    await accountGrid.value[0].emptySelection();
    isdoubleCheck.value=false;
    newAccountsRow.value={};
    newAccountsRow.value.accountGroup='';
    accountParams.value= {noResult: 'A'};
    isShowAddAccount.value = true;
};

const deleteAccount = () => {
    const index = jobForm.form.reportAccounts.findIndex(item => item === currentAccountRow.value);
    if (index !== -1) {
        jobForm.form.reportAccounts.splice(index, 1);
        currentAccountRow.value = {};
        hiddenAddAccount();
    }
};

const cell = (row) => {
  return highlight(row);
}

const changeClientGroup= async() => {
    await accountGrid.value[0].emptySelection();
    let noResult='A';
    disabledSelecte.value=false;
    if(newAccountsRow.value.accountGroup&&newAccountsRow.value.accountGroup!=""&&newAccountsRow.value.clientGroup&&newAccountsRow.value.clientGroup!=""){
        noResult='Y';
    }
    if(newAccountsRow.value.accountGroup==='CG'){
        accountParams.value={clientGroupCode: newAccountsRow.value.clientGroup,recordStatus:'A',noResult: noResult};
    }else if(newAccountsRow.value.accountGroup==='SP'){
        accountParams.value={servicePlan: newAccountsRow.value.clientGroup,recordStatus:'A',noResult: noResult};
    }else if(newAccountsRow.value.accountGroup==='FM'){
        accountParams.value={fundMgrCode: newAccountsRow.value.clientGroup,recordStatus:'A',noResult: noResult};
    }else if(newAccountsRow.value.accountGroup==='CNC'){
        accountParams.value={clientCode: newAccountsRow.value.clientGroup,clientMasterOid: newAccountsRow.value.clientMasterOid,recordStatus: 'A',noResult: noResult};
    }else if(newAccountsRow.value.accountGroup==='CA'){
        newAccountsRow.value.accounts = 'S';
        disabledSelecte.value=true;
        accountParams.value={tradingAccountCode: newAccountsRow.value.clientGroup,recordStatus: 'A',noResult: noResult};
    }else{
        accountParams.value={noResult: noResult};
    }
  
};

const changeAccountGroup= () => {
    newAccountsRow.value.clientGroup= "";
    newAccountsRow.value.clientMasterOid= "";
    accountParams.value={noResult: 'A'};
    disabledSelecte.value=false;
    newAccountsRow.value.accounts = '';
    if(newAccountsRow.value.accountGroup==='CA'){
        disabledSelecte.value=true;
        newAccountsRow.value.accounts = 'S';
    }
};

const beforeSearchValidate = ()=>{
    return true;
}

const  checkSelectable=(row)=>{
    if(!props.formDisabled){
        //Start CAP1-415, Tom.Li, 2025-02-19
        if ( newAccountsRow.value.accounts=='A' ) {
            accountGrid.value[0].emptySelection();
        }
        //End CAP1-415, Tom.Li, 2025-02-19
        return newAccountsRow.value.accounts=='S';
    }else{
        return false;
    }
}

const hiddenAddAccount = () => {
    // 当输入错误的信息后，点击 Cancel 按钮时，把数据清除
    newAccountsRow.value = {};
    //Start CAP1-415, Tom.Li, 2025-02-19
    accountParams.value= {noResult: 'A'};
    //End CAP1-415, Tom.Li, 2025-02-19
    isShowAddAccount.value = false;
};

const addAccount = () => {
    const selectedData = toRaw(accountGrid.value[0].getSelectedData());
    const allData = toRaw(accountGrid.value[0].getAllData());
    let tmpCount = 'A';
    if (newAccountsRow.value.accounts !== 'A') {
        tmpCount=  selectedData.length
    }
    if (tmpCount === 0 || allData.length === 0) {
        ElMessage({
            message: 'At least one active custody account number',
            type: 'error',
            duration: 10000,
            offset: 100,
            showClose: true,
        });
        return true;
    }

    if (!Array.isArray(jobForm.form.reportAccounts)) {
        jobForm.form.reportAccounts = [];
    }
    
    if(isdoubleCheck.value){
        const tmpRow = reactive({
            reportCustodyAccountOid:isdoubleCheckRow.value.reportCustodyAccountOid,
            accountGroup: newAccountsRow.value.accountGroup,
            accountGroupDetails: newAccountsRow.value.clientGroup,
            clientMasterOid: newAccountsRow.value.clientMasterOid,
            tradingAccountCode: newAccountsRow.value.tradingAccountCode, 
            accounts: tmpCount,
            recordStatus: 'PD',
            mkckAction: isdoubleCheckRow.value.recordStatus==='A'?'U':isdoubleCheckRow.value.mkckAction,
            selectedData: cloneDeep(selectedData),
            selectParams: cloneDeep(accountParams.value)
        });
        jobForm.form.reportAccounts[doubleCheckIndex.value] = tmpRow;
    }else{
        const tmpRow = reactive({
            accountGroup: newAccountsRow.value.accountGroup,
            accountGroupDetails: newAccountsRow.value.clientGroup,
            clientMasterOid: newAccountsRow.value.clientMasterOid,
            tradingAccountCode: newAccountsRow.value.tradingAccountCode, 
            accounts: tmpCount,
            recordStatus: 'PD',
            mkckAction: 'C',
            selectedData: cloneDeep(selectedData),
            selectParams: cloneDeep(accountParams.value)
        });
        jobForm.form.reportAccounts.push(tmpRow);
    }
    
    hiddenAddAccount();
   
};


const initItem = (item) => {
    if (item) {
        initPage(item);
    }
    return false;
}

const initPage = async (item) => {
    if (item.keyType == 'select') {
        if (item.keyOptions?.startsWith("source:")) { // 可以通过数据库定义 KEY_OPTIONS source:Xxx 来表示下拉列表需要自定义
            let sou = item.keyOptions.replace("source:", "");
            if (sou.toUpperCase() == 'commType'.toUpperCase()) { // KEY_OPTIONS = source:commType
                proxy.$axios.get("/datamgmt/api/v1/comcode/typelist").then((body) => {
                    if (body.success) {
                        source.value = body.data;
                    }
                });
            } else if (true) { // 把 true 改成 KEY_OPTIONS = source:xxx 即可
                
            }
        } 
    } 
    if (item.keyType == 'readOnly' || item.keyType == 'hide') {
        if (item.keyOptions?.startsWith("source:")) { 
            let sou = item.keyOptions.replace("source:", "");
            if (sou.toUpperCase() == 'overdueGracePeriod'.toUpperCase()) { 
                let msg = await proxy.$axios.get("/datamgmt/api/v1/sysctrl/query");
                if(msg.success) {
                    reportCriteriaData[item.keyField] = msg.data.overdueGracePeriod;
                }
            }
        } 
    }

    return false;
}

function keychange(item) {
    reportCriteriaData[item.keyField] = item.keyValue;
}

const validWeek =()=>{
    if(isWeek.value){
        if(jobForm.form.tmpFrequencyDays.length < 1){
            ElMessage({
                message: 'Please select a day of the week',
                type: 'error',
                duration: 10000,
                offset: 100,
                showClose: true,
            });
            return true;
        }
    }    
    return false;
}

const validCustodyAccount = () => {
    if (!jobForm.form.reportAccounts||jobForm.form.reportAccounts.length < 1) {
        ElMessage({
            message: 'At least one active custody account number',
            type: 'error',
            duration: 10000,
            offset: 100,
            showClose: true,
        });
        return true;
    }
    return false;
};

const validScheduleTime = () => {
    let rows = scheduleTimeGridRef.value.showData.filter(function(item){
        return item?.status === 'A' && item?.mkckAction != 'D';
    });
    if (!rows||rows.length == 0) {
        showErrorMsg('At least one active schedule time');
        return true;
    }
    return false;
};

let curPrcsDate=null;
const handleSave = async (searchValid, isOnlyValidate, unPopping) => {
    
    curPrcsDate= await getSysCtrlDate();

    let reParamDatas = {...jobForm.form};

    Object.keys(checkFieldKey || {}).forEach(e => {
        if(e !== "reportAccounts"){
            jobForm.form[e] = reportCriteriaData.value[e];
        }
    });

    if(jobForm.form['fileFormatList'] && jobForm.form['fileLanguage']){
        let fileFormatList = jobForm.form['fileFormatList'];
        let fileLanguage = jobForm.form['fileLanguage'];
        if("TW" === fileLanguage && (fileFormatList.includes('CSV') || fileFormatList.includes('TXT'))) {
            showErrorMsg("When 'CSV' or 'TXT' is selected in File Format, 'ZH_TW' cannot be selected in Language.");
            return false;
        }
    }

    if (scheduleTimeGridRef.value.isEditing()) {
        showErrorMsg("The sub table data has not been saved. Please confirm if you want to leave.");
        return false;
    }

    let valid= await validWeek();
    if(valid){
        return false;
    }

    if(!anyOneValid(reportCriteriaData.value, anyOneFieldsAndMsg)){
        return false;
    }

    let resultF = await formRef.value.validate((valid, fields) => {
        if (valid) {
        } else {
            showValidateMsg(fieldsDtl, fields);
        }
    });
    if (!resultF) {
        return false;
    }

    // let validCa= await validCustodyAccount();
    // if(validCa){
    //     return false;
    // }

    let validSc= await validScheduleTime();
    if(validSc){
        return false;
    }

    if (isOnlyValidate) {
        return resultF;
    }

    if (resultF && searchValid && await saveMsgBox(unPopping)) {
        
        reportCriteriaData.value.reportAccounts = jobForm.form.reportAccounts;
        reParamDatas.reportCriteriaVPO = toRaw(reportCriteriaData.value);
        reParamDatas.flowJobScheduleTimeVPOS = scheduleTimeGridRef.value.mergeShowData();
        if (reParamDatas?.flowJobControlOid) {
            const msg = await proxy.$axios.patch("/rptsched/api/v1/report/scheduler", reParamDatas);
            details.value.writebackId(msg.data);
            editRow(null,null,msg.data)
            return msg.success;
        } else {
            const msg = await proxy.$axios.post("/rptsched/api/v1/report/scheduler", reParamDatas);
            details.value.writebackId(msg.data);
            editRow(null,null,msg.data)
            return msg.success;
        }
        
    }
    return false;

}

const chkBox = (item, rw) => {
  if (rw?.checked && !reportCriteriaData.value[item.keyField]) {
    let list = [];
    list.push(rw.value);
    reportCriteriaData.value[item.keyField] = list;
  }
  return rw.value;
}

defineExpose({
  details,
  editRow,
  showDetails,
});

</script>

<style scoped>
.generator-form-item .ep-form-item__content {
    display: inline-flex;
}

.check-box .ep-form-item__label {
    min-height: 0px ;
}
.select_input div {
  padding: 0;
  margin: 0;
  border:none;
  outline: none;
  box-shadow: none !important;
  background: none !important;
  width: 185px
}

.select_input div:hover {
  box-shadow: none !important;
  background: none !important;
}

</style>