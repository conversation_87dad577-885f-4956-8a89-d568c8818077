<template> 
  <BasePanel :searchParams="searchParams" :paramListData='paramListData' url="/datamgmt/api/v1/market/holiday/list/enquiry" :params="{ modeEdit: 'Y' }" :showDetails="() => {}"
             :dbClickRow="() => {}"
    :deleteRow="deleteRow" ref="tableRef" :sortProp="{}" :onExportCSV="handleExportCSV" :isHideExport="true" :rules="rules" :hideOperation="true">
    <template v-slot:searchPanel="slotProps">
      <FormRow>
        <ElFormItemProxy :label="$t('csscl.si.common.marketCode')" label-width="180" prop="marketCode">
          <SearchInput v-model="slotProps.form.marketCode"
                       maxlength="3"
                       searchField
                       show-desc="false"
                       style="width: 300px"
                       input-style="width:110px"
                       url="/datamgmt/api/v1/market/list"
                       :title="$t('csscl.si.common.marketCode')"
                       :params="{}"
                       :columns="[
              {
                title: $t('csscl.si.common.marketCode'),
                colName: 'marketCode',
              },
              {
                title: $t('csscl.si.common.marketDesc'),
                colName: 'marketDesc',
              }
            ]"
          >
          </SearchInput>
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.si.common.marketDesc')" label-width="180" prop="marketDesc">
          <InputText v-model="slotProps.form.marketDesc" style="width: 300px" maxlength="50"/>
        </ElFormItemProxy>

        <ElFormItemProxy :label="$t('csscl.useradmin.usr.status')" label-width="120" prop="status">
          <Select v-model="slotProps.form.status" style="width: 150px" type='STATUS' :change="statusType(slotProps.form.status)"/>
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy :label="$t('csscl.si.common.market.location')" label-width="180" prop="depoCode">
          <SearchInput v-model="slotProps.form.depoCode"
                       searchField
                       style="width: 300px"
                       input-style="width:110px"
                       url="/datamgmt/api/v1/market/location-depository/list/enquiry"
                       :title="$t('csscl.si.common.depoCode')"
                       :params="{}"
                       :columns="[
              {
                title: $t('csscl.si.common.market.location'),
                colName: 'depoCode',
              },
              {
                title: $t('csscl.si.common.market.locationDescription'),
                colName: 'depoDesc',
              }
            ]"
          >
          </SearchInput>
        </ElFormItemProxy>
        <ElFormItemProxy>
          <ElFormItemProxy label-width="180" :label="$t('csscl.si.common.holidayDate')" prop="holidayDateFrom">
            <DateItem v-model="slotProps.form.holidayDateFrom" type="date" style="width: 110px;"/>
          </ElFormItemProxy>
          <ElFormItemProxy :label="$t('common.title.date.to')" :hideLabel="$t('csscl.common.dateTo')" prop="holidayDateTo" label-width="20px">
            <DateItem v-model="slotProps.form.holidayDateTo" type="date" style="width: 125px;"/>
          </ElFormItemProxy>
        </ElFormItemProxy>
        <ElFormItemProxy></ElFormItemProxy>
      </FormRow>


    </template>
    <template v-slot:tableColumn>
      <el-table-column align="left" header-align="center" sortable="custom" prop="marketCode" :label="$t('csscl.si.common.marketCode')" width="300" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="marketDesc" :label="$t('csscl.si.common.marketDesc')" width="300" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="depoCode" :label="$t('csscl.si.common.market.location')" width="300" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="depoDesc" :label="$t('csscl.si.common.market.locationDescription')"  width="300">
      </el-table-column>
      <el-table-column align="left" header-align="center" sortable="custom" prop="currencyCode" :label="$t('csscl.si.common.currency')" width="300" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="holidayDate" :label="$t('csscl.si.common.holidayDate')" width="300" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="status" :label="$t('csscl.useradmin.usr.status')" >
        <template #default="scope">
          {{ getCommonDesc('STATUS', scope.row.status) }}
        </template>
      </el-table-column>
    </template>

  </BasePanel>
<!--  <Details ref="detailsRef" :reload="reload" />-->
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance, watch } from 'vue';
import {
Search
} from '@element-plus/icons-vue'
import BasePanel from '~/pages/base/index.vue'
// import Details from './Details.vue'
import {dateFormat, downloadFilePost, getCommonDesc, getRecordStatusDesc} from '~/util/Function.js';
import { getOid ,getSysCtrlDate } from '~/util/Function.js';
import {ElLoading, ElMessage} from "element-plus";
import { commonRules } from '~/util/Validators.js';

const { proxy } = getCurrentInstance()
const paramListData = ref({});
const searchParams = ref({
  marketCode:"",
  marketDesc:"",
  status:"",
  depoCode:"",
  holidayDateFrom:"",
  holidayDateTo:"",
  });

const tableRef = ref();
const detailsRef = ref();
const showDetails = (row, disabled) => {
  detailsRef.value.showDetails(row, disabled);
}
const deleteRow = (row) => {
  proxy.$axios.delete("/datamgmt/api/v1/market/upload?objectId="+ getOid(row, false,null,true)).then((body) => {
    if (body.success) {
      tableRef.value.load();
    }
  });
}
const ctrlDate = ref();
const yearFrom = ref();
const yearTo = ref();
onMounted(async () => {
  let sysCtrlDate = await getSysCtrlDate();
  ctrlDate.value = sysCtrlDate;
  let year = new Date(new Date(sysCtrlDate).getTime() - 365*60*60*24*1000);
  let dateTo = new Date(new Date(sysCtrlDate).getTime() + 365*60*60*24*1000);
  yearFrom.value = dateFormat(year);
  yearTo.value = dateFormat(dateTo);
  // searchParams.value.holidayDateFrom = dateFormat(year)
  // searchParams.value.holidayDateTo = dateFormat(sysCtrlDate);
})

const handleExportCSV = async () => {
  // 显示加载中状态
  const loading = ElLoading.service({
    lock: true,
    text: '',
    background: 'rgba(255, 255, 255, 0.3)',
    fullscreen: false,
    target: document.querySelector('.el-main') as HTMLElement || undefined,
    body: false,
    customClass: 'loading-position'
  });
  try {
    await downloadFilePost("/datamgmt/api/v1/market/holiday/export-csv", searchParams.value);
  } catch (error) {
    ElMessage.error('Failed to export CSV');
  } finally {
    loading.close();
  }
};
const rules = reactive({
  holidayDateFrom: [
    commonRules.earlierEquDt(() => { return searchParams.value.holidayDateTo }, proxy.$t('csscl.common.dateTo')),
    // commonRules.requiredEndDate(() => { return searchParams.value.holidayDateTo }, 'Please include the date for Upload Date To'),
    {
      validator: (rule, value, callback) => {
        if (value && value  < yearFrom.value) {
          callback(new Error('Holiday Date From must be within 365 days of system date'));
        } else {
          callback();
        }
      },
      trigger: 'change'
    }
  ],
  holidayDateTo: [
    // commonRules.laterEquDt(() => { return searchParams.value.holidayDateFrom }, proxy.$t('csscl.si.common.holidayDateFrom')),
    {
      validator: (rule, value, callback) => {
        if (value && value  > yearTo.value) {
          callback(new Error('Holiday Date To must be within 365 days of system date'));
        } else {
          callback();
        }
      },
      trigger: 'change'
    }

  ],
  marketDesc: [
    commonRules.name
  ],

});


//paramList 参数显示用的
function recordType(value){
  console.log(searchParams)
  paramListData._value.recordStatus =  getCommonDesc('RECORD_STATUS', value);
}
function statusType(value){
  paramListData._value.status =  getCommonDesc('STATUS', value);
}
const editRow = (row) => {
  detailsRef.value.editRow(row);
}
const reload = () => {
  tableRef.value.load();
}
//-------------------------------

</script>

<style>

</style>
