<template>
    <BaseDetails ref="details" :handleSave="handleSave" :reload="reload" :viewOriginalForm="viewOriginalForm">
        <el-form :validateOnRuleChange="false" :disabled="formDisabled" ref="ruleFormRef" style="width: 100%" :model="projectForm.form"  status-icon>
            <FormRow>
                <ElFormItemProxy :detailsRef="details" :label="$t('csscl.project.clientNumberCin')" prop="clientCode">
                   <el-input disabled v-model="projectForm.form.clientCode" />
                </ElFormItemProxy>
                <ElFormItemProxy :detailsRef="details" :label="$t('csscl.project.clientShortName')" prop="clientShortName">
                   <el-input disabled v-model="projectForm.form.clientShortName" />
                </ElFormItemProxy>
                <ElFormItemProxy></ElFormItemProxy>
            </FormRow>
            <FormRow>
                <ElFormItemProxy :detailsRef="details" :label="$t('csscl.project.custodyAccNum')" prop="custAccNo">
                   <el-input disabled v-model="projectForm.form.custAccNo" />
                </ElFormItemProxy>
                <ElFormItemProxy :detailsRef="details" :label="$t('csscl.project.custodyAccShortName')" prop="custAccShortName">
                   <el-input disabled v-model="projectForm.form.custAccShortName" />
                </ElFormItemProxy>
                <ElFormItemProxy :detailsRef="details" :label="$t('csscl.project.cashAccNum')" prop="cashAccNo">
                   <el-input disabled v-model="projectForm.form.cashAccNo" />
                </ElFormItemProxy>
            </FormRow>
           

            <FormRow>
                <ElFormItemProxy :detailsRef="details" :label="$t('csscl.project.createDate')" prop="createDt">
                   <DateItem v-model="projectForm.form.sysCreateDate" disabled type="datetime"  />
                </ElFormItemProxy>
                <ElFormItemProxy :detailsRef="details" :label="$t('csscl.project.functionType')" prop="funcType">
                   <Select v-model="projectForm.form.funcType" type='FUNCTION_TYPE' disabled/>
                </ElFormItemProxy>
                <ElFormItemProxy :detailsRef="details" :label="$t('csscl.project.classCode')" prop="classCode">
                   <el-input disabled v-model="projectForm.form.classCode" />
                </ElFormItemProxy>
            </FormRow>
            <FormRow>
                <ElFormItemProxy :detailsRef="details" :label="$t('csscl.project.drcr')" prop="drCrInd">
                   <Select  v-model="projectForm.form.drCrInd" 
                    style="width:84px"
                    disabled
                    type="DR_CR"
                  />
                </ElFormItemProxy>
                <ElFormItemProxy :detailsRef="details" :label="$t('csscl.project.currency')" prop="currencyCode">
                   <el-input disabled v-model="projectForm.form.currencyCode" />
                </ElFormItemProxy>
                <ElFormItemProxy :detailsRef="details" :label="$t('csscl.project.amount')" prop="amount">
                   <InputNumber disabled v-model="projectForm.form.amount"  />
                </ElFormItemProxy>
            </FormRow>
            <FormRow>
                <ElFormItemProxy :detailsRef="details" :label="$t('csscl.project.instructionDate')" prop="instrDt">
                   <DateItem disabled v-model="projectForm.form.instrDt" />
                </ElFormItemProxy>
                <ElFormItemProxy :detailsRef="details" :label="$t('csscl.project.valueDate')" prop="valueDate">
                   <DateItem disabled v-model="projectForm.form.valueDate" />
                </ElFormItemProxy>
                <FormItemSign :detailsRef="details" :label="$t('csscl.project.projectValueDate')" prop="adjProjDate">
                   <DateItem v-model="projectForm.form.adjProjDate" placeholder=""/>
                </FormItemSign>
            </FormRow>
            <FormRow>
                <ElFormItemProxy :detailsRef="details" :label="$t('csscl.project.transactionRef')" prop="txnRef" style="flex: 2;">
                    <el-input type="textarea" disabled v-model="projectForm.form.txnRef" style="width: 800px" />
                </ElFormItemProxy>
                <ElFormItemProxy :detailsRef="details" :label="$t('csscl.project.sourceSys')" prop="sourceSys">
                   <el-input disabled v-model="projectForm.form.sourceSys" />
                </ElFormItemProxy>
            </FormRow>
            <el-row>
                <h3>Settlement</h3>
                <div style="paddingBlock:5px;width: 100%;display: flex;flex-flow: wrap;border-top: 1px solid;" ></div>
            </el-row>
            <FormRow>
                <ElFormItemProxy :detailsRef="details" :label="$t('csscl.project.fxType')" prop="fxType">
                   <el-input disabled v-model="projectForm.form.fxType" />
                </ElFormItemProxy>
                <ElFormItemProxy :detailsRef="details" :label="$t('csscl.project.fxPair')" prop="fxPair">
                   <el-input disabled v-model="projectForm.form.fxPair" />
                </ElFormItemProxy>
                <ElFormItemProxy :detailsRef="details" :label="$t('csscl.project.fxRate')" prop="fxRate">
                   <el-input disabled v-model="projectForm.form.fxRate" />
                </ElFormItemProxy>
            </FormRow>
            
            <span style="font-size: 16px; font-weight: bold;padding-top:10px">Time Deposit</span>
            <div style="paddingBlock:5px;width: 100%;display: flex;flex-flow: wrap;border-top: 1px solid;"></div>  

            <FormRow>
                <ElFormItemProxy :detailsRef="details" :label="$t('csscl.project.depositDate')" prop="depositDate">
                   <DateItem disabled v-model="projectForm.form.depositDate" />
                </ElFormItemProxy>
                <ElFormItemProxy :detailsRef="details" :label="$t('csscl.project.maturityDate')" prop="maturityDate">
                   <DateItem disabled v-model="projectForm.form.maturityDate" />
                </ElFormItemProxy>
                <ElFormItemProxy></ElFormItemProxy>
            </FormRow>
            <FormRow>
                <ElFormItemProxy :detailsRef="details" :label="$t('csscl.project.timeDepositIntRate')" prop="timeDepositIntRate">
                   <InputNumber disabled v-model="projectForm.form.timeDepositIntRate" />
                </ElFormItemProxy>
                <ElFormItemProxy :detailsRef="details" :label="$t('csscl.project.timeDepositIntAmount')" prop="timeDepositIntAmt">
                   <InputNumber disabled v-model="projectForm.form.timeDepositIntAmt" />
                </ElFormItemProxy>
                <ElFormItemProxy></ElFormItemProxy>
            </FormRow>
            <FormRow>
                <ElFormItemProxy :detailsRef="details" :label="$t('csscl.project.remarks')" prop="remark">
                    <el-input disabled v-model="projectForm.form.remark" type="textarea" style="width: 800px"/>
                </ElFormItemProxy>
              
            </FormRow>
            <FormRow>
                <ElFormItemProxy :detailsRef="details" :label="$t('csscl.project.actualPosingDate')" prop="postingDt">
                   <DateItem disabled v-model="projectForm.form.postingDt"  />
                </ElFormItemProxy>
                <ElFormItemProxy></ElFormItemProxy>
                <ElFormItemProxy></ElFormItemProxy>
            </FormRow>
            <FormRow>
                <ElFormItemProxy :detailsRef="details" :label="$t('csscl.project.createDate')" prop="creationDate">
                   <DateItem disabled v-model="projectForm.form.creationDate" type="datetime" style="width:190px" />
                </ElFormItemProxy>
                <ElFormItemProxy :detailsRef="details" :label="$t('csscl.project.updateDate')" prop="lastModifyDate">
                   <DateItem disabled v-model="projectForm.form.lastModifyDate" type="datetime" style="width:190px" />
                </ElFormItemProxy>
                <ElFormItemProxy></ElFormItemProxy>
            </FormRow>
        </el-form>
    </BaseDetails>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue';
import type { FormInstance, FormRules } from 'element-plus'
import BaseDetails from '~/pages/base/Details.vue';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import { getOid, dateFormat,checkDateFromTo  ,checkAfterCurDt,checkDateBetween, saveMsgBox } from '~/util/Function.js';
import { ElMessageBox } from 'element-plus';
import moment from "moment"
// import { getCommonDesc, checkBeforeCurDt, checkDateFromTo, checkDateBetween, showErrorMsg } from '~/util/Function.js';
const { proxy } = getCurrentInstance();
const props = defineProps(['reload']);
const details = ref();
const formDisabled = ref(false);
const eventName = ref('');
const caOid = ref('');
const exCaOid = ref('');
const editRow = (row,disabled,newId) => {
    const oid = newId || (row?.currentOid && getOid(row, disabled, proxy));
    // const oid = getOid(row, disabled, proxy);
    if(row?.eventName){
        eventName.value = row.eventName;
    }
    if(oid){
        caOid.value = row?.caOid != null?row.caOid:null;
        // Start SK-COMMON-0125, Alean , 2024-09-02
        //exCaOid.value = row?.exCaOid!=null?row.caOid:null;
        exCaOid.value = row?.exCaOid!=null?row.exCaOid:null;
        // End SK-COMMON-0125, Alean , 2024-09-02
        proxy.$axios.get("/cashmgmt/api/v1/projection/adjustment?objectId=" +oid + "&eventName="+eventName.value+"&caOid="+ caOid.value+"&exCaOid="+exCaOid.value).then((body) => {
            if(body.success) {
                details.value.currentRow = body.data;
                Object.assign(projectForm.form,body.data);
                projectForm.form = body.data;
                if("txn_cash_instr" == eventName.value){
                    //单独取值
                    projectForm.form.creationDate = body.data.sysCreateDate;
                    projectForm.form.lastModifyDate = body.data.sysUpdateDate;
                }
                if(row?.isApproveDetail && disabled){
                    if(row.afterImage?.adjProjDate){
                        projectForm.form.adjProjDate = moment(row.afterImage?.adjProjDate).format('YYYY/MM/DD');
                        details.value.currentRow.mkckOid = row.afterImage.mkckOid;
                        details.value.currentRow.sysUpdateDate = row.afterImage.sysUpdateDate;
                        details.value.currentRow.sysUpdater = row.afterImage.sysUpdater;
                    }
                }
                
            }
            details.value.initWatch(projectForm);
        });
    }else{
        details.value.initWatch(projectForm);
    }
}
const viewOriginalForm = (pendingOid, isDisabled) => {
    formDisabled.value = isDisabled;
  // Start SK-COMMON-0132 LiShaoyi
   // proxy.$axios.get("/cashmgmt/api/v1/projection/adjustment?objectId="+pendingOid + "&eventName="+eventName.value+ "&eventName="+eventName.value+"&caOid="+ caOid.value+"&exCaOid="+exCaOid.value).then((body) => {
  proxy.$axios.get("/cashmgmt/api/v1/projection/adjustment?objectId="+pendingOid +"&eventName="+eventName.value+"&caOid="+ caOid.value+"&exCaOid="+exCaOid.value).then((body) => {
  // End SK-COMMON-0132 LiShaoyi
        if(body.success) {
            projectForm.form = body.data;     
        }
    });
}
const handleSave = async (searchValid, isOnlyValidate, unPopping) => {
    if( projectForm.form.adjProjDate=='' || projectForm.form.adjProjDate==null){
        ElMessageBox.alert( proxy.$t('message.project.value.not.null'), 'Warning', {
                confirmButtonText: 'OK',
                type: 'warning',
            });
        return false;
    }
    let msg1 = await checkAfterCurDt(proxy, proxy.$t('csscl.project.projectValueDate'), projectForm.form.adjProjDate);
    if(msg1){
        ElMessageBox.alert(proxy.$t('message.project.later.curdate'), 'Warning', {
                confirmButtonText: 'OK',
                type: 'warning',
            });
        return false;
    }
    let  msg = checkDateBetween(proxy, projectForm.form.valueDate, projectForm.form.adjProjDate, 30);
    if(msg){
        ElMessageBox.alert(proxy.$t('message.project.date.range'), 'Warning', {
                confirmButtonText: 'OK',
                type: 'warning',
            });
        return false;
    }

    let result = await ruleFormRef.value.validate((valid, fields) => {
        if (valid) {

        } else {
            console.log('error submit!', fields)
        }
    });
    if (isOnlyValidate) {
        return result;
    }
    if (result && searchValid && await saveMsgBox(unPopping)) {
        let msg = {};
        if (projectForm.form.currentOid) {
             msg = await proxy.$axios.patch("/cashmgmt/api/v1/projection/adjustment", {
                ...projectForm.form,
            });
        } else {
             msg = await proxy.$axios.post("/cashmgmt/api/v1/projection/adjustment", {
                ...projectForm.form,
            });
           
        }
        details.value.writebackId(msg.data);
        editRow(null,null,msg.data);
        return msg.success;
        
    }
    return false;
}
const showDetails = (row, disabled) => {
    // Start R2411A-21510 LiShaoyi 2024/08/13
    //formDisabled.value = disabled;
    if(disabled||row.recordStatus?.startsWith('PA')){
        formDisabled.value = true;
    }else{
        formDisabled.value = false;
    }
    // End R2411A-21510 LiShaoyi 2024/08/13

    details.value.showDetails(row, disabled)
    editRow(row,disabled);
    let mkEventName="";
    // if(row.eventName=="txn_billing"){
    //     mkEventName = "CSSCL_CASHM006";
    // }else if(row.eventName=="txn_cash_instr"){
    //     mkEventName = "CSSCL_CASHM007";
    // }else if(row.eventName=="txn_csdr"){
    //     mkEventName = "CSSCL_CASHM010";
    // }else if(row.eventName=="txn_ca_tax"){
    //     mkEventName = "CSSCL_CASHM011";
    // }
    // details.value.initMkck(row,row.txnSysOid,mkEventName)
}
defineExpose({
    details,
    editRow,
    showDetails,
    viewOriginalForm
});
// --------------------------------------------

interface ProjectForm {
    eventName: string
    txnSysOid: string
    clientCode: string
    accountShortName: string
    custAccNo: string
    clientShortName: string
    accountNo: string
    createDt: string
    funcType: number
    classCode: string
    drCrInd: string
    currencyCode:string
    amount: string
    instrDt: number
    valueDate: string
    adjProjDate: string
    txnRef:string
    fxType: string
    fxPair: number
    fxRate: string
    depositDate: string
    maturityDate:string
    timeDepositIntRate: string
    timeDepositIntAmt: number
    remark: string
    actualPosingDate: string
    sourceSys:string
    postingDt:string
}

const ruleFormRef = ref<FormInstance>()
const projectForm = reactive({
    form: {
    eventName: "",
    custAccShortName:"",
    txnSysOid: "",
    clientCode: "",
    accountShortName: "",
    custAccNo: "",
    clientShortName: "",
    accountNo: "",
    createDt: "",
    funcType: "",
    classCode: "",
    drCrInd: "",
    currencyCode:"",
    amount: "",
    instrDt: "",
    valueDate: "",
    adjProjDate: "",
    txnRef:"",
    fxType: "",
    fxPair: "",
    fxRate: "",
    depositDate: "",
    maturityDate:"",
    timeDepositIntRate: "",
    timeDepositIntAmt: "",
    remark: "",
    actualPosingDate: "",
    sourceSys:"",
    postingDt:"",
    creationDate:"",
    lastModifyDate:""
    }
})
const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate((valid, fields) => {
        if (valid) {
            console.log('submit!')
        } else {
            console.log('error submit!', fields)
        }
    })
}

const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.resetFields()
}

const options = Array.from({ length: 10000 }).map((_, idx) => ({
    value: `${idx + 1}`,
    label: `${idx + 1}`,
}))

</script>

<style></style>