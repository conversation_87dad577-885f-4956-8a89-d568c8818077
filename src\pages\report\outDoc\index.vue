<template>
  <BasePanel :searchParams="searchParams" :paramListData='paramListData' url="/rptsched/api/v1/outgoing/document/list"
    :params="{type:'R'}" ref="tableRef" hideOperation="true" :showDetails="showDetails" :rules="dataRules" :selectFirstRecord="true" :showSummary="true" :hideFooterPaging="true"
    :clickRow="clickRow" :beforeSearch="beforeSearch" :afterSearch="afterSearch" :changePageSize="(num:number) => subtableRef.setPageSize(num)">
    <template v-slot:searchPanel="slotProps">
      <FormRow>
        <ElFormItemProxy label-width="180" :label="$t('csscl.outDoc.fileType')" prop="fileType">
          <Select v-model="slotProps.form.fileType" style="width: 220px;" type="OUT_DOC_FILE_TYPE"
            :change="fileType(slotProps.form.fileType)" />
        </ElFormItemProxy>
        <ElFormItemProxy label-width="180" :label="$t('csscl.outDoc.fileName')" prop="fileName">
          <el-input v-model="slotProps.form.fileName" maxlength="80" style="width:280px" class="text-none"  specialTxt />
        </ElFormItemProxy>
        <ElFormItemProxy></ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy>
        <ElFormItemProxy label-width="180" :label="fieldsDtl.fields.outgoingDateFrom" prop="outgoingDateFrom" >
          <DateItem  v-model="slotProps.form.outgoingDateFrom"
                     :title="$t('message.earlier.equal.curdate', [fieldsDtl.fields.outgoingDateFrom] ) + '\r' +
                        $t('message.earlier.equal.dateto', [fieldsDtl.fields.outgoingDateFrom, fieldsDtl.fields.outgoingDateTo] ) + '\r' +
                        $t('message.date.range.error', [7] ) "/>
        </ElFormItemProxy>
        <ElFormItemProxy label-width="45px" :label="$t('common.title.date.to')" :hideLabel="fieldsDtl.fields.outgoingDateTo" prop="outgoingDateTo" >
          <DateItem v-model="slotProps.form.outgoingDateTo"
                    :title="$t('message.earlier.equal.curdate', [fieldsDtl.fields.outgoingDateTo] ) + '\r' +
                      $t('message.date.range.error', [7] ) "/>
        </ElFormItemProxy>
        </ElFormItemProxy>
        <ElFormItemProxy label-width="180" :label="$t('csscl.outDoc.ftgidCode')" prop="ftgidCode">

          <GeneralSearchInput v-model="slotProps.form.ftgidCode" searchType="ftgidCode" style="width: 400px;" />

        </ElFormItemProxy>
        <ElFormItemProxy label-width="180" :label="$t('csscl.outDoc.outgoingChannel')" prop="channel">
          <Select v-model="slotProps.form.channel" style="width: 220px;" type="DELIVERY_CHANNEL"
            :change="channelType(slotProps.form.channel)" />
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy label-width="180" :label="$t('csscl.reportScheduler.schedulerId')" prop="schedulerId">
          <GeneralSearchInput v-model="slotProps.form.schedulerId" maxlength="10" searchType="schedulerId"   showDesc="false"/>
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.acctCode.custodyAcctNumber')" prop="tradingAccountCode" label-width="180" >
          <GeneralSearchInput v-model="slotProps.form.tradingAccountCode"
                searchType="custodyAcct"
                showDesc="false"
                :params="{var1:slotProps.form.clientMasterOid}"
                :change="(val)=>{
                  slotProps.form.clientAccountOid='';
                }"
                :dbClick="(row)=>{
                  slotProps.form.clientAccountOid=row.var2;
                }"
                />
        </ElFormItemProxy>
        <ElFormItemProxy label-width="180" :label="$t('csscl.outDoc.dataResource')" prop="dataResource">
          <Select v-model="slotProps.form.dataResource" style="width: 220px;" type="OUT_DOC_SOURCE"
            :change="channelType(slotProps.form.dataResource)" />
        </ElFormItemProxy>
        <!-- <ElFormItemProxy></ElFormItemProxy> -->
      </FormRow>
    </template>
    <template v-slot:tableColumn>
      <el-table-column sortable="custom" prop="outgoingDate" :label="$t('csscl.outDoc.outgoingDate')" />
      <el-table-column sortable="custom" prop="fileType" :label="$t('csscl.outDoc.fileType')" width="400" />
      <el-table-column sortable="custom" prop="processingProgress" :label="$t('csscl.outDoc.proccesingProgress')" />
      <!-- <el-table-column sortable="custom" prop="dataResource" :label="$t('csscl.outDoc.dataResource')" > -->
        <!-- <template #default="scope">
          {{ getCommonDesc('OUT_DOC_SOURCE',scope.row.dataResource) }}
        </template>
      </el-table-column> -->
      <el-table-column   prop="jobExecLogOid" v-if="false"/>
    </template>
    

    <template #contentBottom>
  <br/>
  <div style="margin: 0px; padding: 3px; border-bottom: 2px solid lightgrey;">
    <el-text style="color: #b31a25;font: 16px bold;" class="mx-1" size="large">Detail</el-text>
  </div>
  <DetailPanel  url="/rptsched/api/v1/outgoing/document/detail/list" :params="reqParams" :hideOperation="true" :isMultiple="true" ref="subtableRef">
    <template v-slot:tableColumn>

      <el-table-column sortable="custom" prop="outgoingDtFmt" :label="$t('csscl.outDoc.outgoingDateAndTime')" width="200" />
      <el-table-column sortable="custom" prop="clientReportSchedulerId" :label="$t('csscl.reportScheduler.schedulerId')" width="220" />
      <el-table-column sortable="custom" prop="tradingAccountCode" :label="$t('csscl.acctCode.custodyAcctNumber')" width="220" />
      <el-table-column sortable="custom" prop="channel" :label="$t('csscl.outDoc.outgoingChannel')" width="170" >
        <template #default="scope">
          {{ getCommonDesc('DELIVERY_CHANNEL',scope.row.channel) }}
        </template>
      </el-table-column>
      <el-table-column sortable="custom" prop="ftgidCode" :label="$t('csscl.outDoc.ftgidCode')" width="130" />
      <el-table-column sortable="custom" prop="processingStatus" :label="$t('csscl.outDoc.proccesingStatus')" width="170" />
      <el-table-column sortable="custom" prop="deliveryDate" :label="$t('csscl.outDoc.deliveryDateAndTime')" width="200" />
      <el-table-column sortable="custom" prop="rptFileName" :label="$t('csscl.outDoc.fileName')" />
      <el-table-column sortable="custom" prop="dataResource" :label="$t('csscl.outDoc.dataResource')" >
        <template #default="scope">
          {{ getCommonDesc('OUT_DOC_SOURCE',scope.row.dataResource) }}
        </template>
        
      </el-table-column>
      <el-table-column :label="$t('common.button.open')" width="85">
        <template #default="scope">
          <el-icon-folder-opened v-if="scope.row.processingStatus == 'Completed' && scope.row.dataResource == 'CSSCL'" style="width:20px;height:20px;color:orange"
            @click="openClick(scope.row)" />
        </template>
      </el-table-column>

      <el-table-column :label="$t('common.button.download')" width="80">
        <template #default="scope">
          <el-icon-download v-if="scope.row.processingStatus == 'Completed' && scope.row.dataResource == 'CSSCL' " style="width:20px;height:20px;color:darkorange"
            @click="downloadClick(scope.row)" />
        </template>
      </el-table-column>
    </template>
  </DetailPanel>

    <br/>
    <el-space style="float:right;">
    <el-button v-if="$currentInfoStore.currentPermission && $currentInfoStore.currentPermission['Regenerate']"
      type="primary" @click="regenerateSelection">
      Regenerate
    </el-button>
    <el-button type="primary" @click="downloadSelection">
      Download Selected
    </el-button>
  </el-space>
  </template>
  </BasePanel>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue';
import {
  Search
} from '@element-plus/icons-vue'
import BasePanel from '~/pages/base/index.vue'
import DetailPanel from '~/pages/base/indexNoCondition.vue'
import { getCommonDesc, downloadFile, downloadOpenFile, downloadBatchFile, checkBeforeCurDt,checkBeforeCalendarDt, checkInputDate } from '~/util/Function.js';
import {ElMessage, ElMessageBox} from 'element-plus';
import type { FormRules } from 'element-plus';
import { commonRules, showValidateMsg } from '~/util/Validators.js';
import {formatDate, timestampToDate} from "~/util/DateUtils";
onMounted(()=>{
  // getSysCtrlDate()
})
let sysCtrlDate = {}
const getSysCtrlDate = ()=>{
  proxy.$axios.post("/datamgmt/api/v1/sysctrl/list",{param: {},
    current: 0,
    pageSize: 15 }).then((body) => {
    if(body.success) {
      if(body.data||body.data.data){
        sysCtrlDate = body.data.data[0]
      }
    }else {
      sysCtrlDate = {}
    }
  }).catch((err) => {
    sysCtrlDate = {}
  });
}
const showDetails = (row, disabled) => {
  // detailsRef.value.showDetails(row, disabled);
}
const { proxy } = getCurrentInstance()
const paramListData = ref({});
const searchParams = {
  //顺序和上面绑定参数一致
  fileType: "",
  fileName:"",
  outgoingDateFrom: "",
  outgoingDateTo: "",
  ftgidCode: "",
  channel: "",
  schedulerId:"",
  tradingAccountCode:"",
  dataResource:"",
  type: "R",
};

const tableRef = ref();
const detailsRef = ref();
const subtableRef = ref();

interface RuleForm {
  outgoingDateFrom: Date
  outgoingDateTo: Date
}
const fieldsDtl = {
  fields:{
    outgoingDateFrom: proxy.$t('csscl.outDoc.outgoingDateFrom'),
    outgoingDateTo: proxy.$t('csscl.outDoc.outgoingDateTo'),
  }
}
const dataRules = reactive<FormRules<RuleForm>>({
  outgoingDateFrom: [
    commonRules.required,
    commonRules.earlierEquCurDate,
    commonRules.earlierEquDt(()=>{ return searchParams.outgoingDateTo }, fieldsDtl.fields.outgoingDateTo),
    commonRules.diffDate(7, ()=>{ return searchParams.outgoingDateTo }, fieldsDtl.fields.outgoingDateTo),
  ],
  outgoingDateTo: [
    commonRules.required,
    commonRules.earlierEquCurDate,
  ],
})
const beforeSearch = async() => {
  // let result = await tableRef.value.formRef.validate((valid, fields) => {
  //   if (!valid) {
  //     showValidateMsg(fieldsDtl, fields);
  //   }
  // });
  // if (!result) {
  //   return false;
  // }
  // let msgs = [];
  // let msg1 = checkBeforeCalendarDt(proxy, proxy.$t('csscl.outDoc.outgoingDateFrom'), tableRef.value.formInline.outgoingDateFrom);
  // msgs.push(msg1);
  // let msg2 = checkBeforeCalendarDt(proxy, proxy.$t('csscl.outDoc.outgoingDateTo'), tableRef.value.formInline.outgoingDateTo);
  // msgs.push(msg2);
  // if (msg1 || msg2) {
  //   return msgs;
  // }
  //
  // let chkMsg = checkInputDate(proxy, tableRef.value.formInline.outgoingDateFrom, tableRef.value.formInline.outgoingDateTo);
  // return chkMsg;
}


const reload = () => {
  tableRef.value.load();
}
function downloadClick(row) {
  if(row?.processingStatus=="Completed"){
    let funcId = proxy.$currentInfoStore.getCurrentFuncId(); 
    let params = {
      funcId: funcId,
      filePath: row.rptFilePath,
      fileName: row.rptFileName
    };
    downloadFile("/datamgmt/api/v1/rptdocenq/download", params);
  } else {
    ElMessageBox.alert("File not exists, please contact Administrator.", 'Warning');
  }
} 
function openClick(row) {
  if(row?.processingStatus=="Completed"){
    let funcId = proxy.$currentInfoStore.getCurrentFuncId();
    let params = {
      funcId: funcId,
      filePath: row.rptFilePath,
      fileName: row.rptFileName
    };
    downloadOpenFile("/datamgmt/api/v1/rptdocenq/download", params);
  } else {
    ElMessageBox.alert("File not exists, please contact Administrator.", 'Warning');
  }
}

const downloadSelection = async () => {
  let funcId = proxy.$currentInfoStore.getCurrentFuncId(); 
  var tableSelectRows = new Array();
  const rowsData = ref([]);
  rowsData.value = subtableRef.value.getRowsData().value;
  if (rowsData.value) {
    for (var index in rowsData.value) {
      tableSelectRows[index] = rowsData.value[index].rptFilePath + rowsData.value[index].rptFileName;
    }
  }
  let params = {
    funcId: funcId,
    rows: tableSelectRows,
  }
  if (tableSelectRows && tableSelectRows.length !== 0) {
    downloadBatchFile("/datamgmt/api/v1/rptdocenq/downloadBatch", params);
  } else {
    ElMessageBox.alert("Please select at least one row of records!", 'Warning');
  }
};

// const regenerateSelection = async () => {
//   let objectIds = ref([]);
//   const isFailed = ref(false);
//   let rowsData = subtableRef.value.getRowsData().value;
//   if (rowsData) {
//     for (var index in rowsData) {
//       let processingStatus = rowsData[index]?.processingStatus;
//       if(processingStatus && "Failed" !== processingStatus){
//         isFailed.value = true;
//         break;
//       }
//       if(rowsData[index]?.jobExecLogOid){
//         objectIds.value.push(rowsData[index]?.jobExecLogOid);
//       }
//     }
//   }
//   if(isFailed.value){
//       ElMessageBox.alert("Please select Failed records.", 'Warning');
//       return;
//     }
//   if (objectIds.value.length === 0) {
//       ElMessageBox.alert("Please select at least one row of records!", 'Warning');
//       return;
//   }
//   for(let i=0; i<objectIds.value.length; i++){
//     let jobExecLogOid = objectIds.value[i];
//     if(jobExecLogOid){
//       proxy.$axios.get("/rptsched/api/v1/inter/scheduler/job/rerun?jobExecLogOid=" + jobExecLogOid)
//         .then((data) => {
//       });
//     }
//   }
//   tableRef.value.load();
// };

const regenerateSelection = async () => {
  let objectIds = [];
  const rowsData = subtableRef.value.getRowsData().value;
  // const curPrcsDate = sysCtrlDate?.curPrcsDate?timestampToDate(sysCtrlDate.curPrcsDate):null
  if (rowsData) {
    for (const rowsDataItem of rowsData) {
      let processingStatus = rowsDataItem?.processingStatus;
      // 只有失败或者完成的记录才能重跑
      if(processingStatus && "Failed" !== processingStatus && "Completed" !== processingStatus){
        ElMessageBox.alert("Please select Failed or Completed records.", 'Warning');
        return;
      }
      // HK同步的记录不支持重跑
      let dataResource = rowsDataItem?.dataResource;
      if(!dataResource) {
        ElMessageBox.alert("Exist records are from CSS-HK sync, Please select source is CSS-CL", 'Warning');
        return;
      }
      // 一次性生成的报表没有定时任务id，暂不支持重跑
      let clientReportSchedulerId = rowsDataItem?.clientReportSchedulerId;
      if(!clientReportSchedulerId) {
        ElMessageBox.alert("Exist records include once generator report, Please select schedule generator report.", 'Warning');
        return;
      }
      // const reportTemplateCode = reqParams.reportTemplateCode
      // if(reportTemplateCode&&reportTemplateCode=='R000201C'){
      //   if(!curPrcsDate){
      //     ElMessageBox.alert("Unable to obtain Last Processing Date from System Control", 'Warning');
      //     return
      //   }
      //   const outgoingDtFmt =formatDate(rowsDataItem.outgoingDtFmt)
      //   if(curPrcsDate!=outgoingDtFmt){
      //     ElMessageBox.alert("Cannot regenerate previous reports, except for output reports today.", 'Warning');
      //     return
      //   }
      // }
      //Start xiangpan 20250320 这里用id严谨一点，如果同一时间多个任务跑了文件名称一样的记录，使用文件名会在后台查出多条记录
      if(rowsDataItem?.rptGenLogDtlOid){
        objectIds.push(rowsDataItem?.rptGenLogDtlOid);
      }
      //End xiangpan 20250320
    }
  }
  if (objectIds.length === 0) {
    ElMessageBox.alert("Please select at least one row of records!", 'Warning');
    return;
  }
  let showMsg = false;
  for(let i=0; i<objectIds.length; i++){
    const rptGenLogDtlOid = objectIds[i];
    if(rptGenLogDtlOid){
      const data = await proxy.$axios.post('/rptsched/api/v1/outgoing/regenerate',{
        rptGenLogDtlOid: rptGenLogDtlOid
      });
      if(!data.success){
        break
      }else {
        showMsg = true
      }
    }
  }
  if(showMsg){
    ElMessageBox.alert("Regenerating, please search again later.", 'Info');
    tableRef.value.load();
  }

};

//paramList 参数显示用的
function fileType(value) {
  // paramListData._value.fileType = getCommonDesc('OUT_DOC_FILE_TYPE', value);
}
function channelType(value) {
  // paramListData._value.channel = getCommonDesc('OUT_DOC_CHANNEL', value);
}



////////////////////////////////////////////////////////////////
let reqParams = reactive({ modeEdit: 'Y' }); 
const clickRow = (row) => {
  if (row) {
    reqParams["outgoingDate"] = row.outgoingDate;
    reqParams["reportTemplateCode"] = row.reportTemplateCode;
    //Start  SK-COMMON-0107 HYC ********
    reqParams["channel"] = searchParams.channel;
    reqParams["rptFileName"] = searchParams.fileName;
    reqParams["ftgidCode"] = searchParams.ftgidCode;
    reqParams["tradingAccountCode"] = searchParams.tradingAccountCode;
    reqParams["clientReportSchedulerId"] = searchParams.schedulerId;
    reqParams["dataResource"] = searchParams.dataResource;
    //End  SK-COMMON-0107 HYC ********
    subtableRef.value.load();
  } else {
    subtableRef.value.resetTable();
  }
}

const afterSearch = (params, data) => {
  // console.log("1212313132331231313113",data);
  // clickRow(data[0])
  //Start SIR-CSG-R60, ZhuangYifan, 2024/08/13
  subtableRef.value.setTableData(null);
  //End SIR-CSG-R60, ZhuangYifan, 2024/08/13
}

</script>

<style></style>