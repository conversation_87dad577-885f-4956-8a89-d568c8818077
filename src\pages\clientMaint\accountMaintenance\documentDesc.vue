<template> 
    <el-space>
        <Grid url="/datamgmt/api/v1/account/document/list"
            isShowSearch="true"
            ref="docGrid"
            :params="{ 
                clientAccountOid: ruleForm.form.opearteOid, 
                pendingOid: ruleForm.form.pendingOid,
                tempKey: ruleForm.form.tempKey,
                isApproveDetail:ruleForm.form.isApproveDetail,
                approveNumber:ruleForm.form.approveNumber 
            }"
            :beforeSearch="beforeSearch"
            :fieldsDtl="fieldsDtl"
            :afterSearch="afterSearch"
            :onClick="gridClick"
            :onDbClick="gridDbClick"
            :searchParams="searchParams"
            :rules="reqRules"
            :columns="[
                { title: 'csscl.acctCode.fileTypeCode', name: 'fileTypeCode', },
                { title: 'csscl.acctCode.doc.docSubType', name: 'fileSubTypeCode', },
                { title: 'csscl.acctCode.fileName', name: 'filePathId', width:'400', fn:commDesc('DOC_TYPE') },
                { title: 'csscl.acctCode.uploadedDt', name: 'uploadedDt', width:'350' },
                { title: 'csscl.acctCode.filePathId', name: 'sysCreator', },
                { title: 'csscl.acctCode.expiryDate', name: 'expiryDate', },
            ]"
            :beforeClick="beforeClick"
        >
            <template v-slot:searchPanel="slotProps">
                <FormRow>
                    <ElFormItemProxy>
                    <ElFormItemProxy label-width="150" :label="$t('csscl.acctCode.uploadedDtFrom')" prop="uploadedDtFrom">
                        <DateItem v-model="slotProps.form.uploadedDtFrom"
                                  :title="$t('message.earlier.equal.curdate', [fieldsDtl.fields.uploadedDtTo] ) + '\r' +
                                  $t('message.earlier.equal.dateto', [fieldsDtl.fields.uploadedDtFrom, fieldsDtl.fields.uploadedDtTo] )" />
                    </ElFormItemProxy>
                    <ElFormItemProxy label-width="45" :label="$t('common.title.date.to')" prop="uploadedDtTo" :hideLabel="fieldsDtl.fields.uploadedDtTo">
                        <DateItem v-model="slotProps.form.uploadedDtTo"
                                  :title="$t('message.earlier.equal.curdate', [fieldsDtl.fields.uploadedDtTo] )" />
                    </ElFormItemProxy>
                    </ElFormItemProxy>
                    
                    <ElFormItemProxy label-width="170" :label="$t('csscl.acctCode.fileTypeCode')" prop="fileTypeCode">
                        <CommonSearchInput v-model="slotProps.form.fileTypeCode" 
                            style="width: 350px;"
                            showDesc="false"
                            maxlength="50"
                            searchField
                            codeTitle="csscl.acctCode.fileTypeCode"
                            commType="DOC_TYPE"  />
                    </ElFormItemProxy>
                    <ElFormItemProxy label-width="170" :label="fieldsDtl.fields.fileSubTypeCode" prop="fileSubTypeCode">
                      <GeneralSearchInput v-model="slotProps.form.fileSubTypeCode"
                                          :params="{ var1:'DOC_TYPE' }"
                                          style="width: 350px;"
                                          setTitle="true"
                                          searchField
                                          codeTitle="csscl.acctCode.doc.docSubType"
                                          showDesc="false"
                                          searchType="parentComm" />
                    </ElFormItemProxy>
                </FormRow>
                <FormRow>
                    <ElFormItemProxy>
                        <ElFormItemProxy label-width="150" :label="$t('csscl.acctCode.expiryDateFrom')" prop="expiryDateFrom">
                            <DateItem v-model="slotProps.form.expiryDateFrom"
                                      :title="$t('message.earlier.equal.dateto', [fieldsDtl.fields.uploadedDtFrom, fieldsDtl.fields.expiryDateTo] )"  />
                        </ElFormItemProxy>
                        <ElFormItemProxy label-width="45" :label="$t('common.title.date.to')" :hideLabel="fieldsDtl.fields.expiryDateTo" prop="expiryDateTo">
                            <DateItem v-model="slotProps.form.expiryDateTo"/>
                        </ElFormItemProxy>
                    </ElFormItemProxy>
                    
                    <ElFormItemProxy label-width="170" :label="$t('csscl.acctCode.fileName')" prop="filePathId">
                        <el-input v-model="slotProps.form.filePathId" maxlength="100" searchField style="width:320px" />
                    </ElFormItemProxy>
                    <ElFormItemProxy></ElFormItemProxy>
                </FormRow>

            </template>
            <template v-slot:tableColumnAfter >
                <el-table-column :label="$t('csscl.acctCode.doc.file')">
                    <template  #default="scope">
                        <el-icon-folder-opened  style="width:20px;height:20px;color:red;margin-right:5px;" @click="handleFile('open', scope.row)" />
                        <el-icon-download style="width:20px;height:20px;color:red;margin-left:5px;" @click="handleFile('down', scope.row)" />
                    </template>
                </el-table-column>
                <el-table-column :label="$t('common.title.recordStatus')" prop="recordStatus">
                    <template #default="scope">
                        {{ getRecordStatusDesc(scope.row) }}
                      </template>
                </el-table-column>
                
            </template>
        </Grid>
        <el-form :validateOnRuleChange="false" :disabled="disabled">
        <el-space style="margin-top: 47px;" direction="vertical">
            <el-button :icon="Plus" @click="addRecord" />
            <el-button :icon="Minus" @click="deleteRecord" />
        </el-space>
        </el-form>
    </el-space>
    <el-card style="width:1300px" class="grid-continer docDtlCard" >
        <el-form :validateOnRuleChange="false" :disabled="!editDis" ref="dataFormRef" style="width: 60%" class="docDtlFrom"
        :model="pageObj.documentVPO" :rules="rules" status-icon>
            <FormRow>
                <FormItemSign label=" " v-if="isAddInd">
                    <el-upload :show-file-list="false" class="upload-demo" drag :file-list="pageObj.documentVPO.fileList" :auto-upload="false" :on-change="handleUpload"
                        style="width: 500px;">
                        <el-icon><Upload /></el-icon>
                        <div class="el-upload__text"><em>Click</em> or drag file to this area to upload</div>
                    </el-upload>
                </FormItemSign>
            </FormRow>
            <FormRow>
                <FormItemSign :label="$t('csscl.acctCode.doc.fileName')" prop="fileName">
                    <el-space>
                            <el-input v-model="pageObj.documentVPO.fileName" disabled style="width: 470px;" class="text-none" />
                            <el-icon-folder-opened type="primary" style="width:20px;color:red"  @click="handleFile('open')" />
                            <el-icon-download type="primary" style="width:20px;color:red" @click="handleFile('down')" />
                    </el-space>
                </FormItemSign>
            </FormRow>
            <FormRow>
                <FormItemSign :detailsRef="docDetails" :label="$t('csscl.acctCode.doc.docType')" prop="fileTypeCode">
                    <CommonSearchInput v-model="pageObj.documentVPO.fileTypeCode" 
                        style="width: 500px;" 
                        maxlength="50"
                        setTitle="true"
                        codeTitle="csscl.acctCode.doc.docType"
                        :change="()=>{
                          pageObj.documentVPO.fileSubTypeCode = null;
                        }"
                        :dbClick="()=>{
                          pageObj.documentVPO.fileSubTypeCode = null;
                        }"
                        showDesc="false"
                        ref="fileTypeCodeInput"
                        commType="DOC_TYPE" />
                </FormItemSign>
            </FormRow>
            <FormRow>
                <FormItemSign :detailsRef="docDetails" :label="$t('csscl.acctCode.doc.docSubType')" prop="fileSubTypeCode">
                    <GeneralSearchInput v-model="pageObj.documentVPO.fileSubTypeCode"
                        :params="{ var1:'DOC_TYPE', var2: pageObj.documentVPO.fileTypeCode || '~' }"
                        style="width: 500px;"
                        setTitle="true"
                        codeTitle="csscl.acctCode.doc.docSubType"
                        showDesc="false"
                        ref="fileSubTypeCodeInput"
                        searchType="parentComm" />
                </FormItemSign>
            </FormRow>
            <FormRow>
                <FormItemSign :detailsRef="docDetails" :label="$t('csscl.acctCode.doc.docDesc')" prop="filePathId">
                    <el-input v-model="pageObj.documentVPO.filePathId" maxlength="100" style="width: 800px;" setTitle="true" />
                </FormItemSign>
            </FormRow>
            <FormRow>
                <ElFormItemProxy :label="$t('csscl.acctCode.doc.expDt')" prop="expiryDate">
                    <DateItem v-model="pageObj.documentVPO.expiryDate" :disabled="!editDis" style="width: 220px;" />
                </ElFormItemProxy>
            </FormRow>
            <FormRow v-if="editDis">
                <el-button type="primary" @click="handleDtlSave" style="float:right;margin-left:5px" >Save</el-button>
                <el-button :action="'Edit'" type="primary" @click="handleDtlCancel" style="float:right;margin-right:5px" >Cancel</el-button>
            </FormRow>
           
        </el-form>
    </el-card>
</template>

<script lang="ts" setup>
import { ref, reactive, getCurrentInstance, nextTick, watch } from 'vue';
import { Upload, Download, FolderOpened, Plus, Minus,} from '@element-plus/icons-vue'
import { FormRules } from 'element-plus';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import Grid from '~/pages/base/Grid.vue';
import { getCommonDesc, getRecordStatusDesc, commDesc, updateListValue, downloadFile, downloadOpenFile,  showErrorMsg, validSearchInputValue, validDateItemValue, isGridModified } from '~/util/Function.js';
import { addEnterObj, addEnterObj4List, addModifiedFlag, } from '~/util/ModifiedValidate.js';
import { rowCompareWithBeforeImage } from '~/util/Function.js';
import { commonRules, showValidateMsg } from '~/util/Validators.js';
import CommonSearchInput from '~/pages/base/CommonSearchInput.vue';

const { proxy } = getCurrentInstance();
const props = defineProps([ "ruleForm", "details", "disabled", 'lightyellowTabs']);
const ruleForm = props.ruleForm;
const details = props.details;
const docGrid = ref();
const fileTypeCodeInput = ref();
const fileSubTypeCodeInput = ref();
// const isRefresh = ref(false);
const dataFormRef = ref({});
const searchParams = {deleteRecord:""};
const editDis = ref(false);
const isAddInd = ref(false);
const pageObj = reactive ({
    documentVPO:{
        clientAccDocOid:null,
        fileTypeCode:null,
        fileSubTypeCode:null,
        filePathId:null,
        expiryDate:null,
    },
    _documentVPO:{}
});
const lastRow = ref(null);

const fieldsDtl = {
    fields:{
        uploadedDtFrom: proxy.$t('csscl.acctCode.uploadedDtFrom'),
        uploadedDtTo: proxy.$t('csscl.acctCode.uploadedDtTo'),
        expiryDateFrom: proxy.$t('csscl.acctCode.expiryDateFrom'),
        expiryDateTo: proxy.$t('csscl.acctCode.expiryDateTo'),
        fileTypeCode: proxy.$t('csscl.acctCode.doc.docType'),
        fileSubTypeCode: proxy.$t('csscl.acctCode.doc.docSubType'),
        filePathId: proxy.$t('csscl.acctCode.fileName'),
    }
}

const docDetails = ref({ /* addField:(c,d)=>{}, */ fields:fieldsDtl.fields });

const reqRules = reactive({
    uploadedDtFrom: [
        commonRules.earlierEquCurDate,
        commonRules.earlierEquDt(()=>{ return searchParams.uploadedDtTo }, fieldsDtl.fields.uploadedDtTo )
    ],
    uploadedDtTo: [ commonRules.earlierEquCurDate ],
    expiryDateFrom:[
        commonRules.earlierEquDt(()=>{ return searchParams.expiryDateTo }, fieldsDtl.fields.expiryDateTo )
    ],
    filePathId:[
        commonRules.nameForSearch
    ],
});

const params = { clientAccountOid: ruleForm.form.opearteOid, pendingOid: ruleForm.form.pendingOid };
const currentDatas = ref();
const rules = reactive({
    fileTypeCode: [
        commonRules.required,
    ],
    fileSubTypeCode: [
        commonRules.required,
    ],
    filePathId:[
        commonRules.name,
    ]
})

const addRecord = () => {
    if(ruleForm.form.recordStatus?.startsWith("PA")){
        return;
    }
    if (isGridModified(pageObj.documentVPO,lastRow.value)) {
        return false;
    }
    editDis.value = true;
    isAddInd.value = true;
    pageObj.documentVPO = {};
    pageObj._documentVPO = {};
    docGrid.value.setCurrentRow(-1);
    lastRow.value = {};
}

const deleteRecord = () => {
    if(pageObj.documentVPO.mkckAction == 'D'){
        return;
    }
    if (!pageObj.documentVPO.oid) {
        return;
    }
    if(ruleForm.form.recordStatus?.startsWith("PA")){
        return;
    }
    ElMessageBox.confirm(
        'Delete the record?',
        'Warning',
        {
            confirmButtonText: 'OK',
            cancelButtonText: 'Cancel',
            type: 'warning',
        }
    ).then(async () => {
        pageObj.documentVPO.isDelete = true;
        ruleForm.form.deleteDocumentVPOs = updateListValue(ruleForm.form.deleteDocumentVPOs, pageObj.documentVPO, "clientAccDocOid");
        searchParams.deleteRecord += ((searchParams.deleteRecord&&",") + pageObj.documentVPO.clientAccDocOid )
        docGrid.value.load();
    }).catch(() => {
    })
}
/*
const fields = ref({});
const addField = (code, desc) => {
    fields.value[code] = desc;
}
*/
const handleDtlCancel = () => {
    editDis.value = false;
    isAddInd.value = false;
    Object.assign(pageObj.documentVPO ,pageObj._documentVPO);
    if (pageObj.documentVPO.isNew) {
      pageObj._documentVPO = {}
      // when click the cancel button, fresh the list.
      docGrid.value.load();
    }
    lastRow.value = null;
}

const handleDtlSave = async () => {
    if (!pageObj.documentVPO.fileName) {
        handleDtlCancel();
    } else {
        let result = await dataFormRef.value.validate((valid, fields) => {
            if (!valid) {
                showValidateMsg(docDetails, fields);
            }
        });
    
        if (result) {
            result = await validSearchInputValue(".docDtlCard input[searchtype]");
            result = validDateItemValue(".docDtlCard .search-date-error input[alt]") == false ? false : result;
        }
    
        if (result) {
            addEnterObj(pageObj._documentVPO);
            delete pageObj.documentVPO.isNew;
            pageObj.documentVPO.tempKey = ruleForm.form.tempKey;
            let msg = await proxy.$axios.post("/datamgmt/api/v1/account/document/save", pageObj.documentVPO);
            if (msg.success) {
              pageObj.documentVPO.version = msg.data.version;
              pageObj.documentVPO.deleteFlag = msg.data.deleteFlag;
              Object.assign(pageObj._documentVPO, pageObj.documentVPO);
            }
            ruleForm.form.documentVPOs = updateListValue(ruleForm.form.documentVPOs, pageObj.documentVPO, "clientAccDocOid");
            editDis.value = false;
            lastRow.value = null;
            docDetails.value.currentRow = null;
        }
    }
}

const handleSave  = async () => {
    let result = true;
    // 检查列表是否存在未赋值的必填字段
    const msg = await proxy.$axios.get("/datamgmt/api/v1/account/document?objectId="+ruleForm.form.opearteOid+"&tempKey="+ruleForm.form.tempKey+"&deletedOid="+searchParams.deleteRecord);
    if (msg.success) {
      if (msg.data > 0) {
        showErrorMsg(proxy.$t('message.account.not.define.filetype'))
        result = false;
      }
    }
    return result;
}

function gridClick(row){
  if (lastRow.value) {
    return;
  }
  pageObj.documentVPO = {};
  if (row) {
    Object.assign(pageObj.documentVPO, row);
    pageObj._documentVPO = {};
    editDis.value = false;
    isAddInd.value = false;
    docDetails.value.currentRow = null;
  }
}

function gridDbClick(row){
    if (props.disabled) {
        return false;
    }
    if(ruleForm.form.recordStatus?.startsWith('PA')){
        return;
    }
    if(row.mkckAction == 'D'){
      return;
    }
    if (row) {
      docDetails.value.currentRow = row;
      pageObj._documentVPO = row;
      editDis.value = true;
      lastRow.value = { ...row };
    }
}

const afterSearch = (params, data) => {
    // if ( afterSearch.isDeleteRecord ) {
    //     afterSearch.isDeleteRecord = false;
    // }
    // if (currentDatas.value?.length > 0 && data.length > 0) {
    //     for (let j = 0; j < currentDatas.value.length; j ++) {
    //         for(let i = 0; i < data.length; i++) {
    //             let d = data[i];
    //             let e = currentDatas.value[j];
    //             if (d.clientAccDocOid == e.clientAccDocOid) {
    //                 Object.assign(d, e);
    //             }
    //         }
    //     }
    // }
    currentDatas.value = data;
    // if (!isRefresh.value) {
    //     gridClick(data[0]);
    // }

    if (data?.length == 0) {
        delete pageObj['documentVPO'];
        delete pageObj['_documentVPO'];
        pageObj.documentVPO={};
        pageObj._documentVPO={};
        docDetails.value.currentRow = null;
    }
    addEnterObj4List(data, 'clientAccDocOid');
    if (ruleForm.form.recordStatus != 'A') {
        for (let i = 0; i < data.length; i++) {
            let row = data[i];
            row.master = ruleForm.form.clientAccountOid;
            if(rowCompareWithBeforeImage(row, "tab-tab6", props.lightyellowTabs, ['recordStatus'])){
              addModifiedFlag(ruleForm.form, 'documentVPOs');
              break;
            }
        }
        ruleForm.form.deleteDocumentVPOs = data.filter(e => { return e.mkckAction == 'D'; });
        ruleForm.form.documentVPOs = data.filter(e => { return (e.recordStatus != 'A' && e.mkckAction != 'D'); });
    }
}

const beforeSearch = async (search, params) => {
}

const handleFile = (op, row) => {
    let fileName = row&&row.fileName?row.fileName:pageObj.documentVPO.fileName;
    if (fileName) {
        let fn = (url:String, obj:Object) => {};
        if (op == 'down') {
            fn = downloadFile;
        } else if (op == 'open') {
            fn = downloadOpenFile;
        }
        fn("/datamgmt/api/v1/account/document/download", {
            clientCode: ruleForm.form.clientCode ,
            clientNo: ruleForm.form.tradingAccountCode ,
            fileName: fileName,  
        });
    }
}

const handleUpload = async (file) => {
    pageObj.documentVPO.fileList = [file];
    //pageObj.documentVPO.fileList.push(file);
    let formData = new FormData();
    formData.append("file", file.raw);
    formData.append("clientAccountOid", ruleForm.form.tempKey); // 未保存前，这个值是主键值
    formData.append("clientCode", ruleForm.form.clientCode);
    formData.append("accountNo", ruleForm.form.tradingAccountCode);
    const msg = await proxy.$axios.post("/datamgmt/api/v1/account/document/upload", formData);
    if(msg.success){
        if (currentDatas.value.length == docDetails.value.pageSize) {
            delete currentDatas.value[docGrid.value.pageSize-1];
        }
        msg.data.isNew = true;
        currentDatas.value.unshift(msg.data);
        isAddInd.value = false;
        lastRow.value = null;
        nextTick(()=>{
            docGrid.value.setCurrentRow(0, false);
            setTimeout(()=>{
                gridDbClick(currentDatas.value[0]);
            },200);
        });
    }
}

const afterSave = async () => {
}

const showDetails = () => {
}

const beforeClick = (nextRow,column,event) => {
    if (isGridModified(pageObj._documentVPO,lastRow.value)) {
        return false;
    }
    return true;
}

watch(editDis,(newVal)=>{
    docGrid.value.setEditing(newVal);
});

defineExpose({
    handleSave,
    afterSave,
    showDetails,
    editDis,
});
</script>

<style>

</style>