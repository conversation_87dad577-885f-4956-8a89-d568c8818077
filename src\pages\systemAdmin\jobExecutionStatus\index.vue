<template>
  <BasePanel 
    :searchParams="searchParams" 
    :paramListData='paramListData'
    url="/datamgmt/api/v1/jobexeclog/list" 
    :params="{modeEdit: 'Y'}" 
    :showDetails="showDetails"
    :deleteRow="deleteRow"
    :editRow="editRow"
    :beforeSearch="beforeSearch"
    :rules="rules"
    :hideOperation="true"
    ref="tableRef">
    <template v-slot:searchPanel="slotProps"  >
      <FormRow>
        <ElFormItemProxy label-width="220px" :label="$t('common.title.opCtryRegionCode')" prop="opCtryRegionCode">
          <CtryRegionSearchInput v-model="slotProps.form.opCtryRegionCode" 
            showDesc="false" 
            style="width: 100px" 
            opCtryRegion />
        </ElFormItemProxy>
      </FormRow> 

      <FormRow> 
        <ElFormItemProxy label-width="220px" :label="$t('csscl.jobExec.type')" prop="type">
          <Select v-model="slotProps.form.type" v-model:desc="paramListData.type" style="width: 240px" type='JOB_TYPE' />
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.jobExec.jobId')" prop="jobId" label-width="230px">
          <GeneralSearchInput v-model="slotProps.form.jobId" 
            style="width: 240px" 
            showDesc="false"
            codeTitle="csscl.jobExec.jobId"
            searchType="jobStatusId"/>
        </ElFormItemProxy>

        <ElFormItemProxy label-width="55px" :label="$t('common.title.status')" prop="status">
          <Select v-model="slotProps.form.status" v-model:desc="paramListData.status" style="width: 240px" type='JOB_STATUS' />
        </ElFormItemProxy>
      </FormRow>

      <FormRow>  
        <ElFormItemProxy label-width="220px" :label="$t('csscl.jobExec.jobName')" prop="jobName">
          <el-input v-model="slotProps.form.jobName" style="width: 260px;" maxlength="50"  class="text-none"  />
        </ElFormItemProxy>
        <ElFormItemProxy label-width="230px" :label="$t('csscl.jobExec.reportId')"  prop="reportId" style="margin-left: -33.33%">
          <GeneralSearchInput v-model="slotProps.form.reportId" 
            inputStyle="width:130px"
            style="width:400px"
            searchType="allReportTemplateCode"  />
        </ElFormItemProxy>
      </FormRow>

      <FormRow style="margin-block:0">  
        <ElFormItemProxy>
          <FormRow>
            <ElFormItemProxy label-width="220px" :label="fieldsDtl.fields.submitDtFrom" prop="submitDtFrom" style="flex:2;">
              <DateItem style="width: 130px" v-model="slotProps.form.submitDtFrom"
                        :title="$t('message.earlier.equal.curdate', [fieldsDtl.fields.submitDtFrom] ) + '\r' +
                        $t('message.earlier.equal.dateto', [fieldsDtl.fields.submitDtFrom, fieldsDtl.fields.submitDtTo] ) + '\r' +
                        $t('message.date.range.error', [7] )" />
              </ElFormItemProxy>
            <ElFormItemProxy label-width="40" :label="$t('common.title.date.to')" :hideLabel="fieldsDtl.fields.submitDtTo" prop="submitDtTo" style="width:200px;">
              <DateItem style="width: 130px" v-model="slotProps.form.submitDtTo"
                        :title="$t('message.earlier.equal.curdate', [fieldsDtl.fields.submitDtTo] ) + '\r' +
                        $t('message.date.range.error', [7] )" />
            </ElFormItemProxy>

          </FormRow>
        </ElFormItemProxy>
        <ElFormItemProxy>
          <FormRow>
            <ElFormItemProxy label-width="230px" :label="$t('csscl.jobExec.submitTimeFrom')" prop="submitTimeFrom" style="flex:2;">
              <el-time-picker style="width: 120px" v-model="slotProps.form.submitTimeFrom" format="HH:mm" value-format="HH:mm"/>
            </ElFormItemProxy>
            <ElFormItemProxy label-width="30px" :label="$t('common.title.date.to')" prop="submitTimeTo" style="width:200px">
              <el-time-picker style="width: 120px" v-model="slotProps.form.submitTimeTo" format="HH:mm" value-format="HH:mm"/>
            </ElFormItemProxy>
          </FormRow>

        </ElFormItemProxy>
        <ElFormItemProxy></ElFormItemProxy>  
      </FormRow>
    </template>
    <template v-slot:tableColumn>
        <el-table-column align="left" header-align="center" sortable="custom" 
        prop="opCtryRegionCode" :label="$t('common.title.opCtryRegionCode')" width="250" />
        <el-table-column align="left" header-align="center" sortable="custom" prop="type"
         :label="$t('csscl.jobExec.type')" width="80" >
        <template #default="scope">
            {{ getCommonDesc('JOB_TYPE', scope.row.type) }}
          </template>
        </el-table-column>
        <el-table-column align="left" header-align="center" sortable="custom" prop="jobId" :label="$t('csscl.jobExec.jobIds')" width="350" />
        <el-table-column align="left" header-align="center" sortable="custom" prop="seqNum" :label="$t('csscl.jobExec.seqNo')" width="170" />
        <el-table-column align="left" header-align="center" sortable="custom" prop="rerunInd" :label="$t('csscl.jobExec.rerunIndicator')" width="160" />
        <el-table-column align="left" header-align="center" sortable="custom" prop="preSeqNum" :label="$t('csscl.jobExec.preSeqNo')" width="230" />
        <el-table-column align="left" header-align="center" sortable="custom" prop="jobName" :label="$t('csscl.jobExec.jobNames')" width="240" />
        <el-table-column align="left" header-align="center" sortable="custom" prop="systemChannel" :label="$t('csscl.jobExec.systemChannel')" width="160" />
        <el-table-column align="left" header-align="center" sortable="custom" prop="fileName" :label="$t('csscl.jobExec.fileName')" width="170" />
        <el-table-column align="left" header-align="center" sortable="custom" prop="userId" :label="$t('csscl.jobExec.userId')" width="115" />  
        <el-table-column align="left" header-align="center" sortable="custom" prop="submitDt" :label="$t('csscl.jobExec.submitTime')" width="180" />  
        <el-table-column align="left" header-align="center" sortable="custom" prop="startTime" :label="$t('csscl.jobExec.startTime')" width="170" />  
        <el-table-column align="left" header-align="center" sortable="custom" prop="endTime" :label="$t('csscl.jobExec.endTime')" width="170" />  
        <el-table-column align="left" header-align="center" sortable="custom" prop="status" :label="$t('common.title.status')" width="90" >
          <template #default="scope">
            {{ getCommonDesc('JOB_STATUS', scope.row.status) }}
          </template>
        </el-table-column>
        <el-table-column align="left" header-align="center" prop="detailReport"
         :label="$t('csscl.jobExec.detailReport')" width="140" >    
         <template #default="scope">
            <el-button type="primary" @click="handleDownload(scope.row, 'L002401EN')" :icon="Download" link v-if="scope.row?.status==='C'" />
          </template>
        </el-table-column>
        <el-table-column align="left" header-align="center" prop="exceptionReport" :label="$t('csscl.jobExec.exceptionReport')" width="160" >    
        <template #default="scope">
            <el-button type="primary" @click="handleDownload(scope.row, 'L002501EN')" :icon="Download" link v-if="scope.row?.status==='F' || ['N', 'F'].includes(scope.row?.processStatus)"/>
          </template>
        </el-table-column>
        <el-table-column align="left" header-align="center" prop="action" :label="$t('csscl.jobExec.action')" width="130">                  
          <template #default="scope">
            <el-button v-if="$currentInfoStore.currentPermission && $currentInfoStore.currentPermission['Rerun']&&scope.row?.status==='F' " 
            type="info"  @click="rerun(scope.row)">
                Rerun
            </el-button>
          </template>
        </el-table-column>
      </template>
  </BasePanel>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance, watch } from 'vue';
import {
Search
} from '@element-plus/icons-vue'
import BasePanel from '~/pages/base/index.vue'
import CtryRegionSearchInput from '~/pages/base/CtryRegionSearchInput.vue'
import {Download} from '@element-plus/icons-vue'
import { getOid, getCommonDesc, downloadFile, checkDateFromTo, checkDateBetween, checkBeforeCurDt } from '~/util/Function.js';
import  { commonRules } from '~/util/Validators.js';

const { proxy } = getCurrentInstance()
const paramListData = {};
const searchParams = {
  //顺序和上面绑定参数一致
  opCtryRegionCode:"",
  type:"",
  jobId:"",
  status:"",
  jobName:"",
  reportId:"",
  submitDtFrom:"",
  submitDtTo:"",
  submitTimeFrom:"",
  submitTimeTo:"",
};
const tableRef = ref();

const reload = () => {
  tableRef.value.load();
}
const detailsRef = ref();
const showDetails = (row) => {
  // detailsRef.value.details.showDetails(row);
}
const deleteRow = (row) => {
  console.log("Delete ...");
}
const editRow = (row) => {
  detailsRef.value.editRow(row);
}
//-------------------------------
const beforeSearch = async() =>{
  // let chkMsg = await checkSubmitDt();
  // return chkMsg;
}

// onMounted(()=>{
//   let curDt = dateFormat(new Date());
//   tableRef.value.formInline.submitDtFrom = curDt;
//   tableRef.value.formInline.submitDtTo = curDt;
// });

function handleDownload(row, reportId){
    // const row = dataForm.form;
    downloadFile("/rptsched/api/v1/inter/scheduler/job/download?report=" + reportId + "&jobExecLogOid=" + row.jobExecLogOid);
}

function rerun(row){
    // const row = dataForm.form;
    proxy.$axios.get("/rptsched/api/v1/inter/scheduler/job/rerun?jobExecLogOid=" + row.jobExecLogOid)
    .then((data) => {
      tableRef.value.load();
    });
}


const fieldsDtl = {
  fields:{
    submitDtFrom: proxy.$t('csscl.jobExec.submitDateFrom'),
    submitDtTo: proxy.$t('csscl.jobExec.submitDateTo'),
  }
}

const rules = reactive({
  submitDtFrom:[
    commonRules.earlierEquCurDate,
    commonRules.earlierEquDt(()=>{ return searchParams.submitDtTo }, fieldsDtl.fields.submitDtTo),
    commonRules.diffDate(7,()=>{ return searchParams.submitDtTo }, fieldsDtl.fields.submitDtTo),
  ],
  submitDtTo:[
    commonRules.earlierEquCurDate,
  ],

});

// const checkSubmitDt = async() => {
//   let msgs = [];
//   let msg1 = await checkBeforeCurDt(proxy, proxy.$t('csscl.jobExec.submitDateFrom'), tableRef.value.formInline.submitDtFrom);
//   msgs.push(msg1);
//   let msg2 = await checkBeforeCurDt(proxy, proxy.$t('csscl.jobExec.submitDateTo'), tableRef.value.formInline.submitDtTo);
//   msgs.push(msg2);
//   if (msg1 || msg2) {
//     return msgs;
//   }
//
//   let msg = checkDateFromTo(proxy, tableRef.value.formInline.submitDtFrom, tableRef.value.formInline.submitDtTo, proxy.$t('csscl.jobExec.submitDateFrom'))
//   if(msg){
//     return msg;
//   }
//
//   msg = checkDateBetween(proxy, tableRef.value.formInline.submitDtFrom, tableRef.value.formInline.submitDtTo, 7)
//   return msg;
// }
 
</script>

<style>

</style>