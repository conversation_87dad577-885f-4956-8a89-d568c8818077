<template>
  <BaseDetails ref="details" :handleSave="handleSave" :reload="reload" :viewOriginalForm="viewOriginalForm" :form="ruleForm" >
    <el-form :validateOnRuleChange="false" :disabled="formDisabled" ref="ruleFormRef" style="width: 100%" :model="ruleForm.form" :rules="rules" status-icon>
      <FormRow>
        <FormItemSign :detailsRef="details" :label="$t('common.title.opCtryRegionCode')" prop="opCtryRegionCode">
          <CtryRegionSearchInput v-model="ruleForm.form.opCtryRegionCode" :dbClick="opCtryChange" :disabled="editDis" showDesc="false" opCtryRegion/>
        </FormItemSign>
      </FormRow>
      <FormRow>  
        <FormItemSign :detailsRef="details" :label="$t('csscl.currencyManagement.currencyCode')" prop="currencyCode"> 
            <InputText v-model="ruleForm.form.currencyCode" onlyLetters="A-Z" uppercase maxlength="3" :disabled="editDis" style="width: 90px" />
        </FormItemSign>
        <FormItemSign :detailsRef="details" :label="$t('csscl.currencyManagement.descpt')" label-width="180" prop="descpt"> 
            <InputText v-model="ruleForm.form.descpt" maxlength="50" style="width: 360px;" />
        </FormItemSign>
        <FormItemSign :detailsRef="details"  prop="restrictedCurrency">
            <el-checkbox :label="$t('csscl.currencyManagement.restrictedCurrency')" v-model="ruleForm.form.restrictedCurrency" true-value="Y" false-value="N" />
        </FormItemSign>
      </FormRow>
      <FormRow>  
        <FormItemSign :detailsRef="details" :label="$t('csscl.currencyManagement.decimalPoint')" prop="decimalPoint" >
          <InputNumber v-model="ruleForm.form.decimalPoint" precision="2" scale="0" style="width: 70px;" />
        </FormItemSign>
        <FormItemSign :detailsRef="details" :label="$t('csscl.currencyManagement.calMethodCode')" label-width="180" prop="calMethodCode" >
            <Select v-model="ruleForm.form.calMethodCode" type='CCY_CAL_METHOD' />
        </FormItemSign>
        <ElFormItemProxy></ElFormItemProxy>
 
      </FormRow>
      <FormRow>  
        <FormItemSign :detailsRef="details" :label="$t('common.title.status')" prop="status">
            <Select v-model="ruleForm.form.status" type="STATUS" />
        </FormItemSign>
      </FormRow>
    </el-form>
  </BaseDetails>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { getOid, saveMsgBox } from '~/util/Function.js';
import BaseDetails from '~/pages/base/Details.vue';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import CtryRegionSearchInput from '~/pages/base/CtryRegionSearchInput.vue';
import { commonRules, showValidateMsg } from '~/util/Validators.js';
import  * as CurrencyApi from "~/api/staticData/currency";

const { proxy } = getCurrentInstance();
const props = defineProps(['reload']);
const details = ref();
const formDisabled = ref(false);
const editDis = ref(false);
const editRow = (row, disabled,newId) => {
    if(row?.isApproveDetail && disabled){
        ruleForm.form = row?.afterImage;
        details.value.currentRow = ruleForm.form;
        opCtryChange(null, ruleForm.form.opCtryRegionCode);
    } else {
        const oid = newId || (row?.currentOid && getOid(row, disabled, proxy));
        if (oid) {
            CurrencyApi.getCurrency(oid).then(body => {
                if (body.success) {
                    ruleForm.form = body.data;
                    details.value.currentRow = body.data;
                    opCtryChange(null, ruleForm.form.opCtryRegionCode);
                }
                details.value.initWatch(ruleForm);
            });
            editDis.value = true;
        }else{
            details.value.initWatch(ruleForm);
        }
    }
}
const viewOriginalForm = (pendingOid, isDisabled) => {
    formDisabled.value = isDisabled;
    CurrencyApi.getCurrency(pendingOid).then(body => {
        if(body.success) {
            ruleForm.form = body.data;
        }
    });
}

const handleSave = async (searchValid, isOnlyValidate, unPopping) => {
    let result = await ruleFormRef.value.validate((valid, fields) => {
        if (!valid) {
            showValidateMsg(details, fields);
        } 
    });
    if (isOnlyValidate) {
        return result;
    }
    if (result && searchValid && await saveMsgBox(unPopping)) {
        let msg = {};
        if (ruleForm.form.currencyOid) {
            msg = await CurrencyApi.updateCurrency(ruleForm.form);
        }else {
            msg = await CurrencyApi.addCurrency(ruleForm.form);
        }
        details.value.writebackId(msg.data);
        editRow(null,null,msg.data);
        return msg.success;
    }
    return false;
}

const showDetails = (row, isdoubleCheck) => {
    if(isdoubleCheck||row.recordStatus==='PA'){
        formDisabled.value = true;
    }else{
        formDisabled.value = false;
    }

    details.value.showDetails(row, formDisabled.value)
    rules['calMethodCode'] = []
    ruleForm.form = {};
    details.value.currentRow={};
    editDis.value = false;
    editRow(row, isdoubleCheck);
}
defineExpose({
    details,
    editRow,
    showDetails,
    viewOriginalForm
});
// --------------------------------------------

const ruleFormRef = ref<FormInstance>();
const ruleForm = reactive({
    rules:()=>{ return [{ rules:rules }] },
    form: {
      opCtryRegionCode: "",
      currencyCode: "",
      descpt:"",
      restrictedCurrency: "",
      decimalPoint: "",
      calMethodCode: "",
      status: "",
    }
});

const rules = reactive<FormRules<CurrencyApi.RuleForm>>({
    opCtryRegionCode: [
        commonRules.required,
    ],
    currencyCode: [
        commonRules.required,
        commonRules.onlyLetters,
    ],
    descpt: [
        commonRules.required,
        commonRules.name,
    ],
    decimalPoint: [
        commonRules.required,
        commonRules.numberSize(5),
    ],
    calMethodCode: [
        commonRules.notRequired,
    ],
    status: [
        commonRules.selectRequired,
    ],
    
})

// const opCtryChange = (row, code) => {
//   if (code == 'ALL') {
//     rules['calMethodCode'] = [commonRules.notRequired];
//   } else {
//     rules['calMethodCode'] = [commonRules.selectRequired];
//   }
// }

</script>
<style>
</style>