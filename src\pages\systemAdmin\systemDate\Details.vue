<template>
  <BaseDetails ref="details" :handleSave="handleSave" :reload="reload" :viewOriginalForm="viewOriginalForm" :form="ruleForm">
    <el-form :validateOnRuleChange="false" :disabled="formDisabled" ref="ruleFormRef" style="width: 100%" :model="ruleForm.form" :rules="rules" status-icon>
      <FormRow>
        <FormItemSign :detailsRef="details" label-width="220" :label="$t('common.title.opCtryRegionCode')" prop="opCtryRegionCode">
          <CtryRegionSearchInput clearable v-model="ruleForm.form.opCtryRegionCode" :disabled="editDis" showDesc="false" style="width: 120px" opCtryRegion />
        </FormItemSign>
        <FormItemSign :detailsRef="details" label-width="100px" :label="$t('csscl.admin.sysDate.holidayDate')" prop="holidayDate">
          <div style="position:relative">
            <DateItem v-model="ruleForm.form.holidayDate" style="width: 150px"/>
          </div>
        </FormItemSign>
        <FormItemSign :detailsRef="details" label-width="60px" :label="$t('common.title.status')" prop="status">
          <Select v-model="ruleForm.form.status" style="width: 150px" type='STATUS' />
        </FormItemSign>
      </FormRow>
    </el-form>
  </BaseDetails>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue';
import type { FormInstance, FormRules } from 'element-plus'
import BaseDetails from '~/pages/base/Details.vue';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import CtryRegionSearchInput from '~/pages/base/CtryRegionSearchInput.vue'
import { commonRules, showValidateMsg } from '~/util/Validators.js';
import { getOid, saveMsgBox } from '~/util/Function.js';
import { timestampToDate } from '~/util/DateUtils.js';

const { proxy } = getCurrentInstance();
const props = defineProps(['reload']);
const details = ref();
const formDisabled = ref(false);
const editDis = ref(false);
const editRow = (row, disabled,newId) => {
  if(row?.isApproveDetail && disabled){
    ruleForm.form = row.afterImage;
    if(row.afterImage.holidayDate){
      ruleForm.form.holidayDate=timestampToDate(row.afterImage.holidayDate);
    }
    details.value.currentRow = ruleForm.form;
  } else {
    const oid = newId || (row?.currentOid && getOid(row, disabled, proxy));
    if (oid) {
      proxy.$axios.get("/datamgmt/api/v1/bank/holiday?bankHolidayId="+oid).then((body) => {
          if(body.success) {
              ruleForm.form = body.data;
              ruleForm.form.orgiHolidayDate = body.data['holidayDate'];
              details.value.currentRow = body.data;
          }
          details.value.initWatch(ruleForm);
      });
      editDis.value = true;
    } else {
      ruleForm.form = {};
      editDis.value = false;
      details.value.initWatch(ruleForm);
    }
  }
 
}

const viewOriginalForm = (pendingOid, isDisabled) => {
  formDisabled.value = isDisabled;
  proxy.$axios.get("/datamgmt/api/v1/bank/holiday?bankHolidayId="+pendingOid).then((body) => {
        if(body.success) {
            ruleForm.form = body.data;
            details.value.currentRow.value = body.data;
        }
    });
    editDis.value = false;
}

const handleSave = async (searchValid, isOnlyValidate, unPopping) => {
    let result = await ruleFormRef.value.validate((valid, fields) => {
        if (valid) {

        } else {
          showValidateMsg(details, fields);
        }
    });
    if (isOnlyValidate) {
        return result;
    }
    if (result && searchValid && await saveMsgBox(unPopping)) {
        if (ruleForm.form.bankHolidayOid) {
            const msg = await proxy.$axios.patch("/datamgmt/api/v1/bank/holiday", {
                ...ruleForm.form,
            });
            if (msg.success) {
              details.value.writebackId(msg.data);
              editRow(null,null,msg.data);
              return msg.success;
            }
        } else {
            const msg = await proxy.$axios.post("/datamgmt/api/v1/bank/holiday", {
                ...ruleForm.form,
            });
            if (msg.success) {
              details.value.writebackId(msg.data);
              editRow(null,null,msg.data);
              return msg.success;
            }
        }
    }
    return false;
}

const showDetails = (row, isdoubleCheck) => {
  console.log(row)
  if(isdoubleCheck||row.recordStatus==='PA'){
        formDisabled.value = true;
        details.value.showDetails(row, true)
  }else{
      formDisabled.value = false;
      details.value.showDetails(row, false)
  }
  ruleForm.form = {};
  details.value.currentRow={};
  editRow(row, isdoubleCheck);   
}
defineExpose({
  details,
  editRow,
  showDetails,
});
// --------------------------------------------
interface RuleForm {
  opCtryRegionCode: String
  holidayDate: Date
  status: String
}

const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive({
    form: {
      opCtryRegionCode: "",
      holidayDate: "",
      status: "",
    }
})
const rules = reactive<FormRules<RuleForm>>({
  opCtryRegionCode: [
      { required: true, message: 'Please select', trigger: 'change' },
  ],
  holidayDate: [
      { required: true, message: 'Please select time', trigger: 'change' },
  ],
  status: [
      { required: true, message: 'Please select', trigger: 'change' },
  ],
})


const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate((valid, fields) => {
        if (valid) {
            console.log('submit!')
        } else {
            console.log('error submit!', fields)
        }
    })
}

const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.resetFields()
}

const options = Array.from({ length: 10000 }).map((_, idx) => ({
    value: `${idx + 1}`,
    label: `${idx + 1}`,
}))

</script>

<style></style>