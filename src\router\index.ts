import { createRouter, createWebHistory } from "vue-router";
import { useCookies } from "vue3-cookies";
import { randomString } from "~/util/Function";
import { currentInfo } from '~/store/modules/currentInfo';
import { ElMessageBox } from 'element-plus';
import axios from '../axios';
const { cookies } = useCookies();

const routes =
    [{
        "name": "TEST_DEPOSITORY",
        "path": "/test/depository",
        component: () => import("../pages/staticData/market/DepositoryManager.test.vue"),
        "children": []
    }, {
        "name": "TEST_DEPOSITORY_EDIT_GRID",
        "path": "/test/depository-edit-grid",
        component: () => import("../pages/staticData/market/DepositoryEditGrid.test.vue"),
        "children": []
    }, {
        "name": "MENU_CASH",
        "path": "/cash",
        "children": [{
            "name": "MENU_CASH_BANKSTAT",
            "path": "/cash/statement",
            "funcId": "CSSCL_CASHM001",
            component: () => import("../pages/cashMgmt/statement/index.vue"),
            "children": []
        }, {
            "name": "MENU_CASH_INSTRUCT",
            "path": "/cash/instruction",
            "funcId": "CSSCL_CASHM002",
            component: () => import("../pages/cashMgmt/instruction/index.vue"),
            "children": []
        }, {
            "name": "MENU_CASH_ADJUST",
            "path": "/cash/project",
            "funcId": "CSSCL_CASHM005",
            component: () => import("../pages/cashMgmt/project/index.vue"),
            "children": []
        }]
    }, {
        "name": "MENU_CASHOPT",
        "path": "/cashopt",
        "children": [{
            "name": "MENU_CASHOPT_ENQ",
            "path": "/cashopt/enquriy",
            component: () => import("../pages/cashOpt/enquiry/index.vue"),
            "children": []
        }]
    }, {
        "name": "MENU_RPT",
        "path": "/rpt",
        "children": [{
            "name": "MENU_RPT_INCDOC",
            "path": "/rpt/incdoc",
            component: () => import("../pages/report/incDoc/index.vue"),
            "children": []
        }, {
            "name": "MENU_RPT_INCSWIFT",
            "path": "/rpt/incswift",
            "funcId": "CSSCL_RPTCH001",
            component: () => import("../pages/report/incSwift/index.vue"),
            "children": []
        }, {
            "name": "MENU_RPT_OUTDOC",
            "path": "/rpt/outdoc",
            component: () => import("../pages/report/outDoc/index.vue"),
            "children": []
        }, {
            "name": "MENU_RPT_CENTER",
            "path": "/rpt/center",
            component: () => import("../pages/report/reportCenter/index.vue"),
            "children": []
        }, {
            "name": "MENU_RPT_GENERAT",
            "path": "/rpt/generat",
            component: () => import("../pages/report/rptGenerator/index.vue"),
            "children": []
        }]
    }, {
        "name": "MENU_CLIENT",
        "path": "/client",
        "children": [{
            "name": "MENU_CLIENT_MAINT",
            "path": "/client/maint",
            "funcId": "CSSCL_CLIENT001",
            component: () => import("../pages/clientMaint/clientMaintenance/index.vue"),
            "children": []
        }, {
            "name": "MENU_CLIENT_ACC",
            "path": "/client/acct",
            "funcId": "CSSCL_CLIENT002",
            component: () => import("../pages/clientMaint/accountMaintenance/index.vue"),
            "children": []
        }]
    }, {
        "name": "MENU_SETTLE",
        "path": "/settle",
        "children": [{
            "name": "MENU_SETTLE_EXCHG",
            "path": "/settle/exchange",
            "funcId": "CSSCL_SETTL002",
            component: () => import("../pages/settle/exchange/index.vue"),
            "children": []
        }, {
            "name": "MENU_SETTLE_AGENT",
            "path": "/settle/agent",
            "funcId": "CSSCL_SETTL001",
            component: () => import("../pages/settle/agent/index.vue"),
            "children": []
        }]
    }, {
        "name": "MENU_DATA",
        "path": "/data",
        "children": [{
            "name": "MENU_DATA_COMMON",
            "path": "/data/common",
            "funcId": "CSSCL_SDATA002",
            component: () => import("../pages/staticData/commonCode/index.vue"),
            "children": []
        }, {
            "name": "MENU_DATA_CTRY",
            "path": "/data/ctry",
            "funcId": "CSSCL_SDATA003",
            component: () => import("../pages/staticData/ctryRegion/index.vue"),
            "children": []
        }, {
            "name": "MENU_DATA_CURRENCY",
            "path": "/data/currency",
            "funcId": "CSSCL_SDATA004",
            component: () => import("../pages/staticData/currency/index.vue"),
            "children": []
        }, {
            "name": "MENU_DATA_FXRATE",
            "path": "/data/fxrate",
            "funcId": "CSSCL_SDATA005",
            component: () => import("../pages/staticData/fxReferenceRate/index.vue"),
            "children": []
        }, {
            "name": "MENU_DATA_INSTR",
            "path": "/data/instrument",
            component: () => import("../pages/staticData/instrumentCode/index.vue"),
            "children": []
        }, {
            "name": "MENU_DATA_NOMINEE",
            "path": "/data/nominee",
            component: () => import("../pages/staticData/nomineeAccount/index.vue"),
            "children": []
        }, {
            "name": "MENU_DATA_MARKET",
            "path": "/data/market",
            "funcId": "CSSCL_SDATA008",
            component: () => import("../pages/staticData/market/index.vue"),
            "children": []
        },{
                "name": "MENU_DATA_UPLOAD",
                "path": "/data/upload",
                "funcId": "CSSCL_SDATA009",
                component: () => import("../pages/staticData/marketHolidayUpload/index.vue"),
                "children": []
        },{
            "name": "MENU_DATA_ENQUIRY",
            "path": "/data/enquire",
            "funcId": "CSSCL_SDATA010",
            component: () => import("../pages/staticData/marketHolidayEnq/index.vue"),
            "children": []
        }]
    }, {
        "name": "MENU_ADMIN",
        "path": "/admin",
        "children": [{
            "name": "MENU_ADMIN_REPORT",
            "path": "/admin/report",
            "funcId": "CSSCL_SYSAD001",
            component: () => import("../pages/systemAdmin/report/index.vue"),
            "children": []
        }, {
            "name": "MENU_ADMIN_UPLOAD",
            "path": "/admin/upload",
            "funcId": "CSSCL_SYSAD008",
            component: () => import("../pages/systemAdmin/dataExtractor/index.vue"),
            "children": []
        },{
            "name": "MENU_ADMIN_FTGID",
            "path": "/admin/ftgid",
            "funcId": "CSSCL_SYSAD007",
            component: () => import("../pages/systemAdmin/ftgid/index.vue"),
            "children": []
        }, {
            "name": "MENU_ADMIN_JOBEXE",
            "path": "/admin/jobexe",
            component: () => import("../pages/systemAdmin/jobExecutionStatus/index.vue"),
            "children": []
        }, {
            "name": "MENU_ADMIN_JOBSCHED",
            "path": "/admin/jobsched",
            "funcId": "CSSCL_SYSAD003",
            component: () => import("../pages/systemAdmin/jobScheduler/index.vue"),
            "children": []
        }, {
            "name": "MENU_ADMIN_SWIFT",
            "path": "/admin/swift",
            component: () => import("../pages/systemAdmin/openCloseSWIFT/index.vue"),
            "children": []
        }, {
            "name": "MENU_ADMIN_CTL",
            "path": "/admin/ctrl",
            "funcId": "CSSCL_SYSAD004",
            component: () => import("../pages/systemAdmin/systemControl/index.vue"),
            "children": []
        }, {
            "name": "MENU_ADMIN_DATE",
            "path": "/admin/date",
            "funcId": "CSSCL_SYSAD006",
            component: () => import("../pages/systemAdmin/systemDate/index.vue"),
            "children": []
        }, {
            "name": "MENU_ADMIN_HOLIDAY",
            "path": "/admin/holiday",
            "funcId": "CSSCL_SYSAD009",
            component: () => import("../pages/systemAdmin/processHoliday/index.vue"),
            "children": []
        }]
    }, {
        "name": "MENU_USERADMIN",
        "path": "/useradmin",
        "children": [{
            "name": "MENU_USERADMIN_FORCE",
            "path": "/useradmin/force",
            "component": () => import("../pages/userAdmin/force/index.vue"),
            "children": []
        }, {
            "name": "MENU_USERADMIN_SEC_FUNC",
            "path": "/useradmin/func",
            "funcId": "CSSCL_USRAD004",
            "component": () => import("../pages/userAdmin/func/index.vue"),
            "children": []
        }, {
            "name": "MENU_USERADMIN_SEC_ROLE",
            "path": "/useradmin/role",
            "funcId": "CSSCL_USRAD003",
            "component": () => import("../pages/userAdmin/role/index.vue"),
            "children": []
        }, {
            "name": "MENU_USERADMIN_SEC_USER",
            "path": "/useradmin/usr",
            "funcId": "CSSCL_USRAD001",
            "component": () => import("../pages/userAdmin/usr/index.vue"),
            "children": []
        }]
    }, {
        "name": "login",
        path: '/login',
        component: () => import("../pages/login/index.vue"),
    }, {
        "name": "MENU_DASHBOARD",
        path: '/',
        component: () => import("../pages/dash/search/index.vue"),
        "children": [{
            "name": "MENU_DASHBOARD_SCH",
            "path": "/dash/search",
            redirect: '/',
            "children": []
        }]
    }, {
        "name": "MENU_CA",
        "path": "/ca",
        "children": [{
            "name": "MENU_CA_ENTITLE_MAINTENANCE",
            "path": "/ca/caEntitlement",
            "funcId": "FUNC_CA_ENTITLE_MAINTENANCE",
            component: () => import("../pages/caManager/caEntitlement/CaEntitlementIndex.vue"),
            "children": []
        }, {
            "name": "MENU_CA_PAYMENT_MAINTENANCE",
            "path": "/ca/caPayment",
            "funcId": "FUNC_CA_PAYMENT_MAINTENANCE",
            component: () => import("../pages/caManager/caPayment/CaPaymentIndex.vue"),
            "children": []
        }],
    },
    {
        "name": "MENU_SI",
        "path": "/si",
        "children": [{
            "name": "MENU_SI_TRANSACTIONS",
            "path": "/si/transactions",
            "funcId": "CSSCL_SETLI001",
            component: () => import("../pages/siManager/transactions/TransactionsIndex.vue"),
            "children": []
        }, {
            "name": "MENU_SI_HOLDPOSITION",
            "path": "/portfolio/holdingPosition",
            "funcId": "CSSCL_SETLI002",
            component: () => import("../pages/portfolioManager/holdingPosition/HoldingPositionIndex.vue"),
            "children": []
        }]
    },
{
        "name": "test",
        "path": "/test",
        "children": [{
            "name": "test",
            "path": "/test/index",
            "funcId": "test",
             component: () => import("../pages/staticData/market/DepositoryManager.test.vue"),
            "children": []
        },
        {
            "name": "test2",
            "path": "/test/index2",
            "funcId": "test2",
             component: () => import("../pages/staticData/market/DepositoryEditGrid.test.vue"),
            "children": []
        }]
    },
    
]

const generateRouter = () => {
    const router = createRouter({
        history: createWebHistory(import.meta.env.VITE_BASEPATH),
        routes
    });
    
    router.beforeEach(async (to, from, next) => {
        const currentInfoStore = currentInfo();
        if (to.fullPath != "/login" && (!cookies.get("x-esession") && !cookies.get("id_token"))) {
            const keyStr = to.fullPath.split("?")[1]
            //code has data , UMS Login
            if(!(keyStr && keyStr.startsWith("code="))){
                ElMessage.closeAll();
                next({ path: "/login" });
            }
        } else {
            // Start SIR-HLH-R30 HYC The title changed too early
          if (currentInfoStore.getIsEditMain) {
              try {
                  let result = await ElMessageBox.confirm(
                      'Confirm to exit maintenance?',
                      'Warning',
                      {
                          confirmButtonText: 'OK',
                          cancelButtonText: 'Cancel',
                          type: 'warning',
                      });
                  if (result == "confirm") {
                    axios.post('/datamgmt/api/v1/handler/unlock',{
                        flag:false
                    });  
                    axios.post('/datamgmt/api/v1/makerchecker/unlock', {
                        logout: true,
                    });
                      currentInfoStore.setIsEditMain(false);
                      ElMessage.closeAll();
                      next();
                      if (to.matched) {
                        let name = "menu";
                        for (let i = 0; i < to.matched.length; i++) {
                          let el = to.matched[i];
                          name += "." + el.name;
                        }
                        currentInfoStore.setHeaderTitle(name);
                        currentInfoStore.setCurrentMenu(name.substring(name.lastIndexOf(".") + 1));
                        currentInfoStore.setCurrentUUID(randomString());
    
                      }
                  } else {
                      next(false);
                  }
              } catch (error) {
                next(false);
              }
          } else {
            if (to.matched) {
                let name = "menu";
                for (let i = 0; i < to.matched.length; i++) {
                  let el = to.matched[i];
                  name += "." + el.name;
                }
                currentInfoStore.setHeaderTitle(name);
                currentInfoStore.setCurrentMenu(name.substring(name.lastIndexOf(".") + 1));
                currentInfoStore.setCurrentUUID(randomString());
                    
              }
            currentInfoStore.setIsEditMain(false);
            ElMessage.closeAll();
            next();
          }
             // End SIR-HLH-R30 HYC The title changed too early
        }
      });
      return router;
}
export default generateRouter