<template>
    <el-upload v-bind="$attrs" :on-change="onChange" >
        <slot></slot>
    </el-upload>
</template>
<script lang="ts" setup>
import { ElMessage } from 'element-plus';
import { getCurrentInstance } from 'vue';
import { showErrorMsg } from '~/util/Function.js';
const props = defineProps(['onChange']);
const { proxy } = getCurrentInstance();

const fileNameRex = /^[a-zA-Z0-9\s\-\(\)\.\,\'\+\!\#\%\=\^\_\{\}\~\;\$\@\&\[\]]*$/;

const onChange = (file) => {
    ElMessage.closeAll();
    let fileName = file.name;
    if (fileNameRex.test(fileName)) {
        props.onChange(file);
    } else {
        showErrorMsg("File Name should be in English and punctation excludes 0x00-0x1F 0x7F \" * / : < > ? \ | .");
    }
    
}

</script>