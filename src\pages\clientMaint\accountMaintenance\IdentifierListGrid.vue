<template>
    <el-form :validateOnRuleChange="false" :disabled="disabled">
        <el-space alignment="flex-start">
            <el-table border ref="singleTableRef" style="width:400px" :data="tableData" highlight-current-row
                :cell-style="cellStyle"
                @row-click="handleCurrentChange">
                <el-table-column :width="400" property="identifierType" :label="$t('csscl.acctCode.identifierType')">
                    <template #default="scope">
                        {{ getCommonDesc('IDENTIFIER_TYPE_CODE', scope.row.identifierType) }}
                    </template>
                </el-table-column>
            </el-table>
            <el-space direction="vertical">
                <el-button :icon="Top" @click="trans(-1)" />
                <el-button :icon="Bottom" @click="trans(1)" />
            </el-space>
        </el-space>
    </el-form>
</template>

<script lang="ts" setup>
import { ref, getCurrentInstance, onMounted, watch } from 'vue'
import { ElTable } from 'element-plus'
import { Top, Bottom } from '@element-plus/icons-vue'
import {getCommonDesc} from '~/util/Function.js';
import { addEnterObj4List } from '~/util/ModifiedValidate.js';

const currentRow = ref()
const singleTableRef = ref<InstanceType<typeof ElTable>>()
const { proxy } = getCurrentInstance()
const props = defineProps(['url', 'ruleForm', "disabled"]);
const ruleForm = props.ruleForm;
const isSave = ref(false);

const handleCurrentChange = (val: any) => {
    currentRow.value = val;
}

const trans = async (act) => {
    if (currentRow.value) {
        let curr = Number(currentRow.value['seqNo']); // 1 2 3 ... 
        let next = Number(curr + act); // 被调换的位置值
        if (!ruleForm.form.identifierPrefVPOs) {
            if (isSave.value) {
                await load();
                currentRow.value = tableData.value[curr-1];
                singleTableRef.value!.setCurrentRow(currentRow.value); // 高亮选中对象
            } else {
                isSave.value = true;
            }
            for (let i = 0; i < tableData.value.length; i++) {
                let e = tableData.value[i];
                e['status'] = 'A';
                e['clientAccountOid'] = ruleForm.form.opearteOid;
            }
        }
        if (next > tableData.value.length) { // 最后一个
            return;
        } else if (next < 1) { // 第一个
            return;
        } else { // 在中间可移动
            let nextRec = tableData.value[next-1]; // 待换位对象
            nextRec['seqNo'] = curr; // 设置被换行对象的新位置
            currentRow.value['seqNo'] = next; // 设置当前选中行的新位置
            tableData.value[curr-1] = nextRec; // 把被换行对象放到换行后的列表位置
            tableData.value[next-1] = currentRow.value; // 把当前选中行放到换行后的列表位置
            ruleForm.form.identifierPrefVPOs = tableData.value;
        }
        
    }
}


const tableData: any[] = ref([]);

onMounted(() => {
    onload();
});

const onload = () => {  
    if (ruleForm.form.opearteOid) {
        load();
    } else {
        setTimeout(onload, 200);
    }
}

const load = async () => {
    const msg = await proxy.$axios.post(props.url, {
        param: {
            clientAccountOid: ruleForm.form.opearteOid || ruleForm.form.clientAccountOid,
            pendingOid: ruleForm.form.pendingOid,
            isApproveDetail:ruleForm.form.isApproveDetail,
            approveNumber:ruleForm.form.approveNumber
        },
        current: 1,
        pageSize: 999,
        orderBy: null,
    });
    if (msg?.success) {
        tableData.value = {};
        tableData.value = msg.data.data;
        let data = msg.data.data;
        ruleForm.form.identifierPrefVPOs = addEnterObj4List(msg.data.data, "clientAccIdPrefOid");
    }
}

const cellStyle = (row, column, rowIndex, columnIndex) => {
  if (row.row.origSeqNo && row.row.seqNo != row.row.origSeqNo) {
    return { backgroundColor: "lightyellow" }
  }
}

watch(()=>(ruleForm.form.clientAccountOid), ()=>{
    load();
});

defineExpose({
    tableData
});

</script>