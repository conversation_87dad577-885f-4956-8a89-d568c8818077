<template>
    <EditGrid ref="editGridRef" :data="tableData" :formData="allocationForm"
      :is-show-search="false" :showAddDeleteButtons="false"
      :formRules="allocationFormRules"
      :onSaveForm="handleFormSave" :beforeSearch="beforeSearch"
      :handleChangePage="handleChangePage" :beforeChangePage="handleBeforeChangePage"
      :enableRecordStatusHighlight="false">

      <template #tableColumnFront>
        <el-table-column prop="tradingAccountCode" :label="$t('csscl.ca.common.custodyAccountNo')" align="center" />
        <el-table-column prop="entitleSelectedQuantity" :label="$t('csscl.ca.common.selectedQuantity')" align="center"/>
        <el-table-column prop="taxRatePercent" :label="$t('csscl.ca.common.withholdingTaxRate')" align="center"/>
        <el-table-column prop="settleAmount" :label="$t('csscl.ca.common.dividend')" align="center"/>
        <el-table-column prop="tranAmount" :label="$t('csscl.ca.common.txnAmount')" align="center"/>
        <el-table-column prop="marketCode" :label="$t('csscl.ca.common.exchangeCode')" align="center"/>
        <el-table-column prop="actualSettleCcyCode" :label="$t('csscl.ca.common.actualDRCRCurrency')" align="center"/>
        <el-table-column prop="actualSettleAmount" :label="$t('csscl.ca.common.actualDRCRAmount')" align="center"/>
        <el-table-column prop="cashPostMethodCa" :label="$t('csscl.ca.common.cashSettlement')" align="center">
          <template #default="scope">
            {{ getCommonDesc('CASH_POST_METHOD_CA', scope.row.cashPostMethodCa) }}
          </template>
        </el-table-column>
        <el-table-column prop="shadowActualPostingDate" :label="$t('csscl.ca.common.actualPostingDate')" align="center"/>
        <el-table-column prop="shadowActualValueDate" :label="$t('csscl.ca.common.actualValueDate')" align="center"/>
        <el-table-column prop="txnReferenceNumber" :label="$t('csscl.ca.common.transactionReferenceNumber')" align="center"/>
        <el-table-column prop="clientCashAccountNumber" :label="$t('csscl.ca.common.clientCashAccount')" align="center"/>
        <el-table-column prop="custodianShadowCashAccountNumber" :label="$t('csscl.ca.common.shadowCashAccount')" align="center"/>
        <el-table-column prop="fxIndicator" :label="$t('csscl.ca.common.fx')" align="center">
          <template #default="scope"> 
            {{ getCommonDesc('YES_NO', scope.row.fxIndicator) }}
          </template>
        </el-table-column>
        <el-table-column prop="fxCcyCode" :label="$t('csscl.ca.common.fxCurrency')" align="center"/>
        <el-table-column prop="fxTxnReferenceNumber" :label="$t('csscl.ca.common.fxReferenceNumber')" align="center"/>
        <el-table-column prop="payStatus" :label="$t('csscl.ca.common.payStatus')" align="center">
          <template #default="scope">
            {{ getCommonDesc('YES_NO', scope.row.payStatus) }}
          </template>
        </el-table-column>
      </template>
      <template #editForm="formSlot">
        <FormRow>
          <FormItemSign :detailsRef="formSlot.detailsRef" :label="$t('csscl.ca.common.custodyAccountNo')"
            prop="tradingAccountCode" label-width="160px">
            <InputText :disabled="true" v-model="formSlot.formData.form.tradingAccountCode" maxlength="100"
              style="width: 200px" />
          </FormItemSign>
          <FormItemSign :detailsRef="formSlot.detailsRef" :label="$t('csscl.ca.common.selectedQuantity')"
            prop="selectedQuantity" label-width="150px" >
            <InputNumber v-model="formSlot.formData.form.selectedQuantity" scale="0" style="width: 200px"/>
          </FormItemSign>
          <FormItemSign />
        </FormRow>
      </template>
    </EditGrid>
    <el-space>
        <el-button @click="listEntitleClick" type="primary">
            {{ $t('csscl.ca.common.listEntitlement') }}
        </el-button>
    </el-space>
    <el-space>
        <el-button @click="calculateClick" style="margin-left: 15px;" type="primary">
            {{ $t('csscl.ca.common.calculate') }}
        </el-button>
    </el-space>
    <br />
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue';
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus';
import { getOid, saveMsgBox, getCommonDesc } from '~/util/Function.js';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import { commonRules, showValidateMsg } from '~/util/Validators.js';
import EditGrid from '~/components/Ca/CaEditGrid.vue';
import { getTimeZone } from "~/util/DateUtils";

const { proxy } = getCurrentInstance();
const props = defineProps(['reload']);
// 定义表格数据
const tableData = ref([]);
const editGridRef = ref();
const allocationForm = reactive({
  form: {
  }
});
const allocationFormRules = reactive<FormRules<allocationForm>>({
  tradingAccountCode: [
    commonRules.required,
  ],
  caEventReferenceNumber:[
    commonRules.required,
  ]
});

const listEntitleClick = () => {
    console.log("listEntitleClick -->")
}

const calculateClick = () => {
    console.log("calculateClick -->")
}

const generateRequestparam = (data) => {
  let params = {
    header: {timezone: getTimeZone(), lang:"en_US"},
    data: data
  };
  return params;
}

const isResponseSuccess = (response) => {
  if (!response || !response.header) {
    return false;
  }
  return response.header.code === '000000';
}

const getErrorMessage = (errorMessage) => {
  return errorMessage || 'Unknown error';
}

const handleFormSave = (formData) => {
  formData.isChanged = true;
  // 查找是否已存在该记录
  const index = tableData.value.findIndex(item => item.oid === formData.oid);
  if (index !== -1) {
    // 如果找到匹配记录，更新现有记录
    Object.assign(tableData.value[index], formData);
    // 标记为更新状态
    // tableData.value[index].mkckAction = 'U';
  } else {
    tableData.value.push(formData);
  }
}

const isDeleteButtonDisabled = (record) => {
  const rtn = record?.systemIndicator === 'Y';
  return rtn;
}

// 处理保存
const handleSave = async () => {
  // TODO: 直接返回
  return;
};

const showDetails = async (row:any, isdoubleCheck:any) => {
  if (editGridRef.value) {
    loadTableData(row);
  }
}

// 获取数据
const loadTableData = async (row:any) => {
  if (!row?.caEventReferenceNumber) return;
  let params = generateRequestparam({
      caEventReferenceNumber: row?.caEventReferenceNumber,
      caEventTaxChargeHdrOid: row?.caEventTaxChargeHdrOid,
    });
  const response = await proxy.$axios.post("/bff/ca/api/v1/ca-event-tax-charge/get-tax-charge-details-page-list", params);
  if (isResponseSuccess(response)) {
    tableData.value = response.data.items;
  } else {
    const errorMsg = getErrorMessage(response?.header?.message);
    ElMessage.error(errorMsg);
  }
};

const handleChangePage = async (newPage:any) => {
    await loadTableData(newPage);
}

const beforeSearch = async (search:any, params:any) => {
    return true;
//   // 检查是否有未保存的修改
//   const hasUnsavedChanges = tableData.value.some(item => item.isChanged);
//   if (hasUnsavedChanges) {
//     try {
//       await ElMessageBox.confirm(
//         'Save the changes before search?',
//         'Warning',
//         {
//           confirmButtonText: 'Save',
//           cancelButtonText: 'Cancel',
//           type: 'warning',
//         }
//       );
//       // 用户选择保存,调用保存方法
//       await handleSave();
//       return true;
//     } catch (e) {
//       // 用户取消保存,阻止切换页面
//       return false;
//     }
//   }
//   return true;
}

const handleBeforeChangePage = async () => {
  return true;
  // 检查是否有未保存的修改
//   const hasUnsavedChanges = tableData.value.some(item => item.isChanged);
  
//   if (hasUnsavedChanges) {
//     try {
//       await ElMessageBox.confirm(
//         'Save the changes before switch page?',
//         'Warning',
//         {
//           confirmButtonText: 'Save',
//           cancelButtonText: 'Cancel',
//           type: 'warning',
//         }
//       );
//       // 用户选择保存,调用保存方法
//       await handleSave();
//       return { isChangePage: true };
//     } catch (e) {
//       // 用户取消保存,阻止切换页面
//       return { isChangePage: false };
//     }
//   }
//   return true;
}

const clearData = async() => {
  tableData.value = [];
  editGridRef.value.total = 0;
  editGridRef.value.cancelForm();
}

defineExpose({
  clearData,
  showDetails,
});

</script>

<style>

</style>