<template>
    <BasePanel :searchParams="searchParams" :paramListData='paramListData' url="/datamgmt/api/v1/makerchecker/getpage"
        :params="{}" contentClass="dash-content" :showDetails="showDetails" :deleteRow="deleteRow" :editRow="editRow"
        ref="tableRef" :beforeSearch="beforeSearch" :afterSearch="afterSearch" :hideOperation="true"
        :handleSelectionChange="handleSelectionChange" :isMultiple="true" :rules="rules"
        :sortProp="{ 'phoneNo': ['phoneExt', 'phoneNo'] }" topLeftWidth="350px" contentLeftWidth="350px"
        :dbClickRow="dbClickRow">
        <template #topLeft>
            <div style="margin: 10px 10px 0px 10px;">
                <div><span style="font-weight: bold;">{{ $t('csscl.home.totalNumOfTask') }}</span><span
                        style="float:right;font-weight: bold;">{{ totalData.total }}</span></div>
                <div style="background-color: var(--ep-color-warning);width: 100%;height: 6px;border-radius: 3px;">
                </div>
            </div>
            <br />
            <el-row style="padding:0px;margin:0px;" :gutter="20">
                <el-col :span="12">
                    <el-button type="warning" link :icon="SuccessFilled" style="font-size: 22px;" /> {{ totalData.numPA
                    }}
                    <br />
                    <el-text class="mx-1">{{ $t('csscl.home.status.pa') }}</el-text>
                </el-col>
                <el-col :span="12">
                    <el-button type="danger" link :icon="SuccessFilled" style="font-size: 22px;" /> {{ totalData.numRC
                    }}
                    <br />
                    <el-text class="mx-1">{{ $t('csscl.home.status.rc') }}</el-text>
                </el-col>
            </el-row>
            <br />
            <el-row style="padding:0px;margin:0px;" :gutter="20">
                <el-col :span="12">
                    <el-button type="danger" link :icon="SuccessFilled" style="font-size: 22px;" /> {{ totalData.numE }}
                    <br />
                    <el-text class="mx-1">{{ $t('csscl.home.status.e') }}</el-text>
                </el-col>
                <el-col :span="12">
                    <el-button type="success" link :icon="SuccessFilled" style="font-size: 22px;" /> {{ totalData.numCA
                    }}
                    <br />
                    <el-text class="mx-1">{{ $t('csscl.home.status.ca') }}</el-text>
                </el-col>
            </el-row>
        </template>
        <template #contentLeft>
            <!-- Start R24114-29552, Tom.Li 2024/08/13 -->
            <!--<span
                style="font-size: 14px; font-weight: bold; display: inline-block; text-align: center; background-color: lightgrey; width: 100%; min-height: 28px; line-height: 28px;">Category</span> -->
            <!-- <el-tree class="tree-demo" style="max-width: 400px;max-height: 600px;" :data="totalData.list" node-key="id"
                default-expand-all :expand-on-click-node="false" :check-strictly="true" ref="treeRef"
                :render-content="renderContent" @node-click="handleNodeClick" /> -->
            <el-row
                style="font-size: 14px; font-weight: bold; background-color: lightgrey; width: 100%; min-height: 28px; line-height: 28px;"
                :gutter="24">
                <el-col :span="8">
                    <el-space style="height: 100%;padding-left: 3px;">
                        <el-button v-if="collopseAll" title="Expand All" type="primary" link :icon="Plus" @click="collopseAll=false;" />
                        <el-button v-if="!collopseAll" title="Collapse All" type="primary" link :icon="Minus" @click="collopseAll=true;" />
                    </el-space>
                </el-col>
                <el-col align="middle" :span="8">
                    Category
                </el-col>
            </el-row>
            <el-tree class="tree-demo" style="max-width: 400px;" :data="totalData.list" node-key="key"
                :highlight-current="true" :expand-on-click-node="false" :check-strictly="true" ref="treeRef"
                :render-content="renderContent" @node-click="handleNodeClick" />
            <!-- End R24114-29552, Tom.Li 2024/08/13 -->
        </template>
        <template v-slot:searchPanel="slotProps">
            <FormRow>
                <ElFormItemProxy>
                    <ElFormItemProxy label-width="120" style="margin-right:8px;" :label="$t('csscl.home.table.dateFrom')"
                        prop="sysUpdateDateFrom">
                        <DateItem :validate-event="false" v-model="slotProps.form.sysUpdateDateFrom"
                                  :title="$t('message.earlier.equal.curdate', [$t('csscl.home.table.dateFrom')] ) + '\r' +
                                  $t('message.earlier.equal.dateto', [$t('csscl.home.table.dateFrom'), $t('csscl.home.table.dateTo')] )" />
                    </ElFormItemProxy>
                    <ElFormItemProxy label-width="30" :validate-event="false" :label="$t('common.title.date.to')" :hideLabel="$t('csscl.home.table.dateTo')" prop="sysUpdateDateTo">
                        <DateItem v-model="slotProps.form.sysUpdateDateTo"
                                  :title="$t('message.earlier.equal.curdate', [$t('csscl.home.table.dateTo')] )" />
                    </ElFormItemProxy>
                </ElFormItemProxy>
                <ElFormItemProxy label-width="130" :label="$t('csscl.home.table.function')" prop="funcName">
                    <GeneralSearchInput v-model="slotProps.form.funcName" 
                        :params="{var1:proxy.$currentInfoStore.getUserInfo.userId}"
                        style="width: 300px"
                        showDesc="false"
                        searchType="funByUserid" />
                </ElFormItemProxy>
                <ElFormItemProxy label-width="160" :label="$t('csscl.useradmin.usr.recordStatus')" prop="multipleRecordStatus">
                    <MultipleSelect v-model="slotProps.form.multipleRecordStatus" v-model:desc="paramListData.multipleRecordStatus" type="DASH_RECORD_STATUS" style="width: 240px" />
                </ElFormItemProxy>
            </FormRow>
            <FormRow>
                <ElFormItemProxy label-width="120" :label="$t('csscl.home.table.custAccount')" prop="custAccount">
                    <GeneralSearchInput v-model="slotProps.form.custAccount" searchType="custodyAcct" showDesc="false"
                        :change="(val) => {
                            slotProps.form.clientAccountOid = '';
                        }" :dbClick="(row) => {
                            slotProps.form.clientAccountOid = row.var2;
                        }" />
                </ElFormItemProxy>
                <ElFormItemProxy label-width="130" :label="$t('csscl.home.table.custShortName')" prop="custShortName">
                    <el-input v-model="slotProps.form.custShortName" class="text-none" maxlength="50"
                        style="width: 300px" />
                </ElFormItemProxy>
                <ElFormItemProxy label-width="160" :label="$t('csscl.home.table.cashAccount')" prop="cashAccount">
                    <el-input v-model="slotProps.form.cashAccount" class="text-none" maxlength="18"
                        style="width: 300px" />
                </ElFormItemProxy>
            </FormRow>
            <FormRow>
                <ElFormItemProxy label-width="120" :label="$t('csscl.home.table.locked')" prop="isLock">
                    <el-select clearable v-model="slotProps.form.isLock" style="width: 120px">
                        <el-option key="Y" label="Y" value="Y" />
                        <el-option key="N" label="N" value="N" />
                    </el-select>
                </ElFormItemProxy>
                <ElFormItemProxy label-width="130" :label="$t('csscl.home.table.lockedBy')" prop="lockId">
                    <GeneralSearchInput v-model="slotProps.form.lockId" maxlength="10" showDesc="false"
                        codeTitle="csscl.home.table.lockedBy" searchType="user" />
                </ElFormItemProxy>
                <ElFormItemProxy>
                </ElFormItemProxy>
            </FormRow>
            <ElFormItemProxy style="display:none;" prop="menuPath">
                <el-input v-model="slotProps.form.menuPath" />
            </ElFormItemProxy>
            <ElFormItemProxy style="display:none;" prop="totalStatus">
                <el-input v-model="slotProps.form.totalStatus" />
            </ElFormItemProxy>
            <ElFormItemProxy style="display:none;" prop="currentNodeKey">
                <el-input v-model="slotProps.form.currentNodeKey" />
            </ElFormItemProxy>
        </template>
        <template v-slot:tableColumn>
            <el-table-column sortable="custom" prop="sysUpdateDate" :label="$t('csscl.home.table.dateTime')"
                width="70" />
            <el-table-column sortable="custom" prop="funcName" :label="$t('csscl.home.table.function')"
                min-width="250" >
                <template #default="scope">
                    <span v-if="scope.row.processStatus&& scope.row.status == 'E'">
                        {{ getCommonDesc('DASH_RECORD_STATUS', scope.row.status) }}
                       ({{ getCommonDesc('DASH_PROCESS_STATUS', scope.row.processStatus) }})
                    </span>
                    <span v-if="scope.row.status !== 'E'">
                        {{ scope.row.funcName }}
                    </span>
                </template>
            </el-table-column>


            <el-table-column sortable="custom" prop="status" :label="$t('csscl.home.table.recStatus')" width="185">
                <template #default="scope">
                    {{ getCommonDesc('DASH_RECORD_STATUS', scope.row.status) }}
                    <span v-if="scope.row.status && scope.row.status !== 'A' && scope.row.status !== 'E'">
                        for {{ getCommonDesc('MKCK_ACTION', scope.row.mkckAction) }}
                    </span>
                </template>
            </el-table-column>
            <el-table-column sortable="custom" prop="custAccount" :label="$t('csscl.home.table.custAccount')"
                width="85" />
            <el-table-column sortable="custom" prop="custShortName" :label="$t('csscl.home.table.custShortName')"
                width="75" />
            <el-table-column sortable="custom" prop="cashAccount" :label="$t('csscl.home.table.cashAccount')"
                width="75" />
            <el-table-column sortable="custom" prop="valueDate" :label="$t('csscl.project.valueDate')" width="70" />
            <el-table-column sortable="custom" prop="drCrInd" :label="$t('csscl.home.table.drCr')" width="60" >
                <template #default="scope">
                    {{ getCommonDesc('DR_CR', scope.row.drCrInd) }}
                </template>
            </el-table-column>
            <el-table-column sortable="custom" prop="ccy" :label="$t('csscl.home.table.ccy')" width="55" />
            <el-table-column sortable="custom" prop="amount" :label="$t('csscl.home.table.amount')" width="80" align="right">
                <template #default="scope">{{ thousFormat(scope.row.amount, 2) }}</template>
            </el-table-column>
            <el-table-column sortable="custom" prop="makerId" :label="$t('csscl.home.table.maker')" width="50" />

            <el-table-column sortable="custom" prop="approverId" :label="$t('csscl.home.table.appr1')" width="70" />
            <el-table-column sortable="custom" prop="checkerId" :label="$t('csscl.home.table.appr2')" width="70" />
            <!-- <el-table-column sortable="custom" prop="lockId" :label="$t('csscl.home.table.locked')" width="60">
                <template #default="scope"> -->
            <!-- scope.row -->
            <!-- {{ scope.row.lockId ? "Y" : "N" }}
                </template>
            </el-table-column> -->
            <el-table-column sortable="custom" prop="lockId" :label="$t('csscl.home.table.lockedBy')" width="100" />
        </template>

        <template #contentBottom>
            <br />
            <el-space style="float:right;">
                <el-button type="primary" @click="approveSelect">Approve</el-button>
            </el-space>
        </template>
    </BasePanel>


    <UserDetails ref="isUserDetails" :reload="reload" />
    <RoleDetails ref="isRoleDetails" :reload="reload" />
    <RoleFuncDetails ref="isRoleFuncDetails" :reload="reload" />

    <InstructionDetails ref="isInstructionDetails" :reload="reload" />
    <ProjectDateDetails ref="isProjectDateDetails" :reload="reload" />
    <StatementDetails ref="isStatementDetails" :reload="reload" />

    <SystemControlDetails ref="isSystemControlDetails" :reload="reload" />
    <SystemDateDetails ref="isSystemDateDetails" :reload="reload" />
    <FTGDateDetails ref="isFTGDateDetails" :reload="reload" />

    <JobSchedulerDetails ref="isJobSchedulerDetails" :reload="reload" />
    <ReportSchedulerDetails ref="isReportSchedulerDetails" :reload="reload" />
    <CurrencyDetails ref="isCurrencyDetails" :reload="reload" />
    <CtryRegionDetails ref="isCtryRegionDetails" :reload="reload" />
    <CommonCodeDetails ref="isCommonCodeDetails" :reload="reload" />
    <FxReferenceRateDetails ref="isFxReferenceRateDetails" :reload="reload" />

    <ProcessHolidayDetails ref="isProcessHolidayDetails" :reload="reload" />
    <AgentDetails ref="isAgentDetails" :reload="reload" />
    <ExchangeDetails ref="isExchangeDetails" :reload="reload" />

    <IncSwiftDetails ref="isIncSwiftDetails" :reload="reload" />


    <AccountMaintenanceDetails ref="isAccountMaintenanceDetails" :reload="reload" />
    <ClientMaintenanceDetails ref="isClientMaintenanceDetails" :reload="reload" />

    <DataExtractorDetails ref="isDataExtractorDetails" :reload="reload" />
    <MarketDetails ref="isMarketDetails" :reload="reload" />
    <MarketUploadDetails ref="isMarketUploadDetails" :reload="reload" />

    <el-dialog v-model="showApproveDialog" title="Approve" width="30%" :show-close="false" class="mkck-dialog"
        append-to-body>
        <template #header>
            <div class="mkckTitle">
                <span class="title-name">Approve</span>
            </div>
        </template>
        <br>
        <el-col style="padding-right: 12px; padding-left: 12px;">
            <el-form-item label="Remark">
                <el-input type="textarea" v-model="remarkApprove.remark" rows="8"></el-input>
            </el-form-item>
        </el-col>
        <br>
        <div class="button-group">
            <el-button @click="handleCancel" class="ep-button-custom">Cancel</el-button>
            <el-button type="primary" @click="handleApprove" class="ep-button-custom" v-pre-dup-click>OK</el-button>
        </div>
        <br>
    </el-dialog>

</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance, watch, onUnmounted } from 'vue';
import {
// Start R24114-29552, Tom.Li 2024/08/13
    Plus,
    Minus,
// End R24114-29552, Tom.Li 2024/08/13
    Search,
    SuccessFilled
} from '@element-plus/icons-vue'
import BasePanel from '~/pages/base/index.vue'
import UserDetails from '~/pages/userAdmin/usr/Details.vue'
import RoleDetails from '~/pages/userAdmin/role/Details.vue'
import RoleFuncDetails from '~/pages/userAdmin/func/Details.vue'

import SystemControlDetails from '~/pages/systemAdmin/systemControl/Details.vue'
import SystemDateDetails from '~/pages/systemAdmin/systemDate/Details.vue'
import FTGDateDetails from '~/pages/systemAdmin/ftgid/Details.vue'
import JobSchedulerDetails from '~/pages/systemAdmin/jobScheduler/Details.vue'
import ReportSchedulerDetails from '~/pages/systemAdmin/report/Details.vue'
import DataExtractorDetails from '~/pages/systemAdmin/dataExtractor/Details.vue'
import MarketDetails from '~/pages/staticData/market/Details.vue'
import MarketUploadDetails from '~/pages/staticData/marketHolidayUpload/Details.vue'

import InstructionDetails from '~/pages/cashMgmt/instruction/Details.vue'
import ProjectDateDetails from '~/pages/cashMgmt/project/Details.vue'
import StatementDetails from '~/pages/cashMgmt/statement/Details.vue'

import AgentDetails from '~/pages/settle/agent/Details.vue'
import ExchangeDetails from '~/pages/settle/exchange/Details.vue'


import CurrencyDetails from '~/pages/staticData/currency/Details.vue'
import CtryRegionDetails from '~/pages/staticData/ctryRegion/Details.vue'
import CommonCodeDetails from '~/pages/staticData/commonCode/Details.vue'
import FxReferenceRateDetails from '~/pages/staticData/fxReferenceRate/Details.vue'

import ProcessHolidayDetails from '~/pages/systemAdmin/processHoliday/Details.vue'
import AccountMaintenanceDetails from '~/pages/clientMaint/accountMaintenance/Details.vue'
import ClientMaintenanceDetails from '~/pages/clientMaint/clientMaintenance/Details.vue'

import IncSwiftDetails from '~/pages/report/incSwift/Details.vue'

import { ElMessageBox, ElMessage } from 'element-plus';
import { getCommonDesc, checkBeforeCurDt, checkDateFromTo, thousFormat } from '~/util/Function.js';
import  { commonRules } from '~/util/Validators.js';
import { useRouter } from 'vue-router';
import { checkPropEqual, checkPropEmpty, checkPropValueEqual, checkPropValueIncludes } from '~/util/CommonArrayUtils.js';
import { formatCashAccountNumber} from '~/util/AccountUtils.js';
import MarkApproveDetails from "~/pages/report/incSwift/MarkApproveDetails.vue";

const { proxy } = getCurrentInstance()
const paramListData = {};
const searchParams = {
    //顺序和上面绑定参数一致
    sysUpdateDateFrom: "",
    sysUpdateDateTo: "",
    funcName: "",
    multipleRecordStatus: [],
    custAccount: "",
    custShortName: "",
    cashAccount: "",
    isLock: "",
    lockId: "",
};
const tableRef = ref();
const detailsRef = ref();


const isUserDetails = ref();
const isRoleDetails = ref();
const isRoleFuncDetails = ref();

const isInstructionDetails = ref();
const isProjectDateDetails = ref();
const isStatementDetails = ref();

const isSystemControlDetails = ref();
const isSystemDateDetails = ref();
const isFTGDateDetails = ref();

const isJobSchedulerDetails = ref();
const isReportSchedulerDetails = ref();

const isAgentDetails = ref();
const isExchangeDetails = ref();

const isCurrencyDetails = ref();
const isCtryRegionDetails = ref();
const isCommonCodeDetails = ref();
const isFxReferenceRateDetails = ref();

const isProcessHolidayDetails = ref();

const isAccountMaintenanceDetails = ref();
const isClientMaintenanceDetails = ref();
const isDataExtractorDetails = ref();
const isMarketDetails = ref();
const isMarketUploadDetails = ref();

const isIncSwiftDetails = ref();

const router = useRouter();
const routeInfo = ref({
    currentRouteName: '',
    parentRouteName: ''
});

const showApproveDialog = ref(false);
const remarkApprove = ref({
  remark: '',
});
let socket = null;

const showDetails = async (row, disabled) => {
    if (row.flowMakerCheckerOid) {
        let result = await proxy.$axios.post('/datamgmt/api/v1/makerchecker/lock', {
            flowMakerCheckerOid: row.flowMakerCheckerOid,
            eventOid: row.eventOid,
            recordStatus:row.status
        });
        if (!result.data) {
            return;
        }
    }
    let eventOid = await proxy.$axios.post('/datamgmt/api/v1/makerchecker/original', {
        flowMakerCheckerOid: row.flowMakerCheckerOid,
    });
    if(row.funcId==='CSSCL_CASHM005_1'||row.funcId==='CSSCL_CASHM005_2'||row.funcId==='CSSCL_CASHM005_3'||
        row.funcId==='CSSCL_CASHM005_4'||row.funcId==='CSSCL_CASHM005_5'||row.funcId==='CSSCL_CASHM005_6'
    ){
        row.funcId='CSSCL_CASHM005'
    }
    //A状态不上锁
    if (proxy.$currentInfoStore.currentPermission['Edit']) {
        if('A'!==row.status){
            let flag = await proxy.$axios.post('/datamgmt/api/v1/handler/lock', {
            eventName: row.funcId,
            //Start SIR-Cristin-R045,heruiguang,20240816
            //lock record when user reject 
            // eventOid: row.eventOid,
            eventOid: row.eventPkey!=null?row.eventPkey:row.eventOid,
            //Start SIR-Cristin-R045,heruiguang,20240816 
            flag: true
            });
            if (!flag.data) {
                return;
            }
        }else{
            //这个的a状态上锁
            if(row.funcId=='CSSCL_CASHM001'){
                let flag = await proxy.$axios.post('/datamgmt/api/v1/handler/lock', {
                eventName: row.funcId,
                eventOid: row.eventOid,
                flag: true
                });
                if (!flag.data) {
                    return;
                }
            }
        }
    }

    let roleId = undefined;
    if (row.funcId == "CSSCL_USRAD003") {
        await proxy.$axios.get("/auth/api/v1/user/role/desc?sysRoleOid=" + row.eventPkey).then((body) => {
            if (body.success) {
                roleId = body.data?.roleId;
            }
        });
    }
    await findRouteByFuncId(router.options.routes, row.funcId);
    proxy.$currentInfoStore.setCurrentMenu(routeInfo.value.currentRouteName)
    proxy.$currentInfoStore.setHeaderTitle('menu.' + routeInfo.value.parentRouteName + '.' + routeInfo.value.currentRouteName)

    if(row.funcId == "CSSCL_CASHM001" && row.status == 'E' && row.processStatus == 'N'){
        router.push({ path: "/cash/statement", query: { reconBankStmtOid: row.eventOid } });
        return;
    }
    if(row.funcId == "CSSCL_CASHM002" && row.status == 'E' && row.processStatus == 'F'){
        router.push({ path: "/cash/instruction", query: { eventOid: row.eventOid } });
        return;
    }
    const componentRef = getDetailsComponent(row);
    if (componentRef) {
        let isDisabled = (row.status === 'A');
        if (row.status === 'A') {
            if (eventOid.data) {
                row.eventOid = eventOid.data
            }
        }
        if (row.status === 'E'||row.status === 'R') {
            if (!proxy.$currentInfoStore.currentPermission['Edit']) {
                isDisabled = true;
            } else {
                let result = await proxy.$axios.get("/datamgmt/api/v1/handler?eventName=" +row.funcId + "&eventOid=" + row?.eventOid + "&userName=" + proxy.$currentInfoStore.getUserInfo.userId);
                if (result.success) {
                    let userData = result.data;
                    if (!userData?.flag) {
                        let alertMsg = result.alertMessage.toString();
                        alertMsg = alertMsg.replace(/"/g, '');
                        ElMessageBox.confirm(
                            alertMsg,
                            'Warning',
                            {
                                customStyle: {
                                    'max-width': '26%',
                                },
                                confirmButtonText: 'OK',
                                cancelButtonText: 'Cancel',
                                type: 'warning',
                                closeOnClickModal: false
                            }).then(() => {
                                proxy.$axios.post('/datamgmt/api/v1/handler/lock', {
                                    eventName: row.funcId,
                                    eventOid:  row?.eventOid,
                                    recordStatus: row.status,
                                    version: row.version,
                                    flag: false,
                                    recordOid: row?.eventOid,
                                }).then((body) => {
                                    if (!body.success) { return; } else {
                                        showDetailsRel(row,roleId,isDisabled,componentRef);
                                        proxy.$axios.post('/datamgmt/api/v1/handler', {
                                            eventName: row.funcId,
                                            eventOid: row?.eventOid,
                                            handlerId: proxy.$currentInfoStore.getUserInfo.userId
                                        });
                                    }
                                });
                            }
                            ).catch(() => { })
                    } else {
                        proxy.$axios.post('/datamgmt/api/v1/handler/lock', {
                            eventName: row.funcId,
                            eventOid: row?.eventOid,
                            recordStatus:  row.status,
                            version: row.version,
                            flag: true,
                            recordOid: row?.eventOid,
                        })
                        showDetailsRel(row,roleId,isDisabled,componentRef);
                    }
                }
            }
        }else{
            showDetailsRel(row,roleId,isDisabled,componentRef);
        }
    }
}


const showDetailsRel=(row,roleId,isDisabled,componentRef)=>{
            let afterImage;
            let jsonData;
            if (row?.afterImage && row?.eventName) {
                let listData = [];
                afterImage = JSON.parse(row?.afterImage);
                for (let key in afterImage) {
                    let keyStr = key.substring(0, key.lastIndexOf("_"));
                    if (keyStr === row?.eventName.toUpperCase()) {
                        jsonData = JSON.parse(afterImage[key]);
                        jsonData.mkckOid = row.flowMakerCheckerOid;
                        jsonData.recordStatus = row.status;
                    }
                    if ("CUSTODIAN_ACC_EXB" === keyStr) {
                        listData.push(JSON.parse(afterImage[key]));
                    }
                    if ("REPORT_CRITERIA" === keyStr) {
                        listData.push(JSON.parse(afterImage[key]));
                    }
                }
                jsonData.listData = listData;
            }
            componentRef.value.showDetails({
                currentOid: row.eventOid,
                mkckOid: row.flowMakerCheckerOid,
                pendingOid: row.eventOid,
                recordStatus: row.status,
                roleId: roleId,
                eventName: row.eventName,
                isApproveDetail: true,
                afterImage: jsonData,
                eventPkey: row?.eventPkey,
                approveNumber: row?.approveNumber
            }, isDisabled);
}
const getDetailsComponent = (row) => {
    if (row.funcId == "CSSCL_USRAD001") {
        return isUserDetails;
    } else if (row.funcId == "CSSCL_USRAD003") {
        return isRoleDetails;
    } else if (row.funcId == "CSSCL_USRAD004") {
        return isRoleFuncDetails;
    } else if (row.funcId == "CSSCL_SDATA005") {
        return isFxReferenceRateDetails;
    } else if (row.funcId == "CSSCL_SDATA004") {
        return isCurrencyDetails;
    } else if (row.funcId == "CSSCL_SDATA003") {
        return isCtryRegionDetails;
    } else if (row.funcId == "CSSCL_SDATA002") {
        return isCommonCodeDetails;
    } else if (row.funcId == "CSSCL_CASHM001") {
        return isStatementDetails;
    } else if (row.funcId == "CSSCL_CASHM002") {
        return isInstructionDetails;
    } else if (row.funcId == "CSSCL_CASHM005") {
        return isProjectDateDetails;
    } else if (row.funcId == "CSSCL_SETTL001") {
        return isAgentDetails;
    } else if (row.funcId == "CSSCL_SETTL002") {
        return isExchangeDetails;;
    } else if (row.funcId == "CSSCL_SYSAD001") {
        return isReportSchedulerDetails;
    } else if (row.funcId == "CSSCL_SYSAD003") {
        return isJobSchedulerDetails;
    } else if (row.funcId == "CSSCL_SYSAD004") {
        return isSystemControlDetails;
    } else if (row.funcId == "CSSCL_SYSAD006") {
        return isSystemDateDetails;
    } else if (row.funcId == "CSSCL_SYSAD007") {
        return isFTGDateDetails;
    } else if (row.funcId == "CSSCL_SYSAD009") {
        return isProcessHolidayDetails;
    } else if (row.funcId == "CSSCL_CLIENT002") {
        return isAccountMaintenanceDetails;
    } else if (row.funcId == "CSSCL_SYSAD008") {
        return isDataExtractorDetails;
    } else if (row.funcId == "CSSCL_RPTCH001") {
        return isIncSwiftDetails;
    } else if (row.funcId == "CSSCL_SDATA008") {
        return isMarketDetails;
    }else if (row.funcId == "CSSCL_SDATA009") {
      return isMarketUploadDetails;
    }else if (row.funcId == "CSSCL_CLIENT001") {
        return isClientMaintenanceDetails;
    }


    
   
    
    else {

        return null;
    }
};

function findRouteByFuncId(routes, funcId, parentRoute = null) {
    for (const route of routes) {
        if (route.funcId === funcId) {
            routeInfo.value.currentRouteName = route.name;
            routeInfo.value.parentRouteName = parentRoute ? parentRoute.name : '';
            return route;
        }
        if (route.children) {
            const nestedRoute = findRouteByFuncId(route.children, funcId, route);
            if (nestedRoute) {
                return nestedRoute;
            }
        }
    }
    return undefined;
}

const deleteRow = (row) => {
    proxy.$axios.delete("/auth/api/v1/user?userOid=" + row.userOid).then((body) => {
        if (body.success) {
            tableRef.value.load();
        }
    });
}
const editRow = (row) => {
    detailsRef.value.editRow(row);
}
function handleSelectionChange(val) {
  console.log(val );
}

const approveSelect = async () => { 
    
    if(tableRef.value.selectedRecord){
        const rows = new Array();
        for(var index in tableRef.value.selectedRecord){
            rows.push(tableRef.value.selectedRecord[index]);
        }
        //check R01
        if(rows.length === 0){
            ElMessageBox.alert("Please select at least one row of records!", 'Warning');
            return;
        }

        //Check R02 ? Not exactly the same 'FS'
        let status = ["PA","PA1","PA2"]; //need check PA1 or PA2
        //let checkStatus = checkPropValueEqual(rows, 'status', status);
        let checkStatus = checkPropValueIncludes(rows, 'status', status);
        if(!checkStatus){
            if(rows.length > 1){
                // ElMessage({
                //     message: "Bypassed the one record(s) has been locked or is not ‘Pending approve’ status.",
                //     type: 'error',
                //     duration: 10000,
                //     offset: 100,
                //     showClose: true,
                // });
            } else {
                ElMessageBox.alert("Bypassed the one record is not ‘Pending approve’ status.", 'Warning');
                return;
            }
        }

        //Check R03: exceeded your approval limit (select checkbox)

        //Check R04: user has approve access (select checkbox)

        //check R05
        let isComFuncId = checkPropEqual(rows, 'funcId');
        if(!isComFuncId){
            ElMessageBox.alert("Only allows to do the batch approval for same function.", 'Warning');
            return;
        }

        //Check R6: record status has been changed,refresh 

        //Check R7
        let checkLockId = checkPropEmpty(rows, 'lockId');
        if(!checkLockId){
            if(rows.length > 1){
                // ElMessage({
                //     message: "Bypassed the one record(s) has been locked or is not ‘Pending approve’ status.",
                //     type: 'error',
                //     duration: 10000,
                //     offset: 100,
                //     showClose: true,
                // });
            } else {
                ElMessageBox.alert("Record has been locked by User. Please try again later.", 'Warning');
                return;
            }
        }

        //Check R8
        let userId = proxy.$currentInfoStore.getUserInfo.userId;
        let checkMakerId = checkPropValueEqual(rows, 'makerId', userId);
        if(checkMakerId){
            ElMessageBox.alert("Maker, Approver 1&2 must not be the same person.", 'Warning');
            return;
        }
        
        // check approve right
        let flag = false;
        await proxy.$axios.get('/auth/api/v1/user/checkApproveRight?funcId='+ rows[0].funcId).then((body) => {
            if (body.data) {
                flag = body.data;
            }
        });
        if(!flag){
            ElMessageBox.alert("You have no approval right of current function.", 'Warning');
            return;
        }

        //Check R9
        if(rows.length > 1){
            let checkAllowBatchApprove = false;
            await proxy.$axios.post('/rptsched/api/v1/flow/getFlowList', {funcId: rows[0].funcId}).then((body) => {
                if (body.data) {
                    if(body.data.data[0].allowBatchApprove === 'Y'){
                        checkAllowBatchApprove = true;
                    }
                }
            });
            if(checkAllowBatchApprove){
                const checkRows = new Array();
                for(var index in rows){
                    let row = rows[index];
                    if(row.status && (row.status == 'PA'||row.status == 'PA1'||row.status == 'PA2')
                        && (row.lockId === undefined || row.lockId === '' || row.lockId === null)){
                        checkRows.push(row);
                    }
                }
                if(checkRows.length === 0){
                    ElMessageBox.alert("The 'Pending approval' status record has not been selected.", 'Warning');
                    return;
                } else {
                    showApproveDialog.value = true;
                }
            } else {
                ElMessageBox.alert('The selected functions that not allows batch approval', 'Warning');
                return;
            }
        } else {
            //only one selected: go to details page
            tableRef.value.handleDbClick(rows[0]);
        }
    }
}

const handleApprove = async() => { 
    const rows = new Array();
    let checkAllowBatchApprove = false;
    for(var index in tableRef.value.selectedRecord){
        let row = tableRef.value.selectedRecord[index];
        row.remark = remarkApprove.value.remark;
        if(row.flowMakerCheckerOid){
            await proxy.$axios.get('/datamgmt/api/v1/makerchecker/getMakerChecker?mkckOid='+row.flowMakerCheckerOid).then((body) => {
                if (body.data) {
                    let status = body.data.status;
                    let lockId = body.data.lockId;
                    if(status && (status == 'PA'|| status == 'PA1'|| status == 'PA2')
                        && (lockId === undefined || lockId === '' || lockId === null)){
                        row.status = "A";
                        rows.push(row);
                    } else {
                        checkAllowBatchApprove = true;
                    }
                }
            });
        }
    }
    if(checkAllowBatchApprove){
        ElMessage({
            message: "Bypassed the one record(s) has been locked or is not ‘Pending approve’ status.",
            type: 'error',
            duration: 10000,
            offset: 100,
            showClose: true,
        });
    }
    if(rows.length === 0){
        ElMessageBox.alert("The 'Pending approval' status record has not been selected.", 'Warning');
        return;
    }
    proxy.$axios.post('/datamgmt/api/v1/makerchecker/approvalBatch', rows).then((body) => {
        if (body?.success) {
            showApproveDialog.value = false;
            ElMessageBox.alert("Approve successfully.", 'Success', {
                confirmButtonText: 'OK',
                type: 'success',
                callback: (action: Action) => {
                    tableRef.value.onSearch();
                }
            });
        } else {
            showApproveDialog.value = false;
            ElMessageBox.alert("Approve Failed.", 'Warning', {
                confirmButtonText: 'OK',
                type: 'warning',
                callback: (action: Action) => {
                    tableRef.value.onSearch();
                }
            });
        }
        remarkApprove.value.remark = "";
    });
}

const handleCancel = () => {
  showApproveDialog.value = false;
  remarkApprove.value.remark = "";
}



const reload = () => {
    // Start SK-COMMON-0075, Tom.Li, 2024/08/16
    // openWebSocket();
    // End SK-COMMON-0075, Tom.Li, 2024/08/16
    mkckSummary();
    tableRef.value.refreshTime();
    tableRef.value.load();
    proxy.$currentInfoStore.setCurrentMenu("MENU_DASHBOARD_SCH");
}
//-------------------------------
const totalData = ref({});
const treeRef = ref();
let elMsg = null;
const beforeSearch = async(param) => {
    mkckSummary();
    elMsg?.close();
    elMsg = null;
    // let chkMsg = await checkInputDateFromTo();
    // return chkMsg;
}
const afterSearch = () => {
    treeRef.value!.setCheckedKeys([tableRef.value?.formInline.currentNodeKey], false);
    // Start R24114-29552, Tom.Li 2024/08/13
    treeRef.value!.setCurrentKey(tableRef.value?.formInline.currentNodeKey);
    // End R24114-29552, Tom.Li 2024/08/13
}
//beforeSearch();
const mkckSummary = () => {
    proxy.$axios.get("/datamgmt/api/v1/makerchecker/summary").then((body) => {
        if (body.success) {
            // Start R24114-29552, Tom.Li 2024/08/13
            // totalData.value = body.data;
            if (totalData.value?.list) {
                totalData.value.numCA = body.data.numCA;
                totalData.value.numE = body.data.numE;
                totalData.value.numPA = body.data.numPA;
                totalData.value.numRC = body.data.numRC;
                totalData.value.total = body.data.total;
                let nodes = treeRef.value?.store.nodesMap;
                if (nodes) {
                    let list = body.data.list;
                    changeItem(list, nodes);
                }
            } else {
                totalData.value = body.data;
            }
            // End R24114-29552, Tom.Li 2024/08/13
        }
    });
}
onMounted(() => {
    // Start SK-COMMON-0106, Tom.Li, 2024/08/26
    //mkckSummary();
    // End SK-COMMON-0106, Tom.Li, 2024/08/26
});

const rules = reactive({
  sysUpdateDateFrom:[
      commonRules.earlierEquCurDate,
      commonRules.earlierEquDt(()=>{ return searchParams.sysUpdateDateTo }, proxy.$t("csscl.home.table.dateTo")),
  ],
  sysUpdateDateTo:[
      commonRules.earlierEquCurDate,
  ],
});

const renderContent = (h,
    {
        node,
        data,
        store,
    }: {
        node: Node
        data: Tree
        store: Node['store']
    }) => {
    let color = "unset";
    let type = data.type;
    switch (type) {
        case 'PA':
            color = "var(--ep-color-warning)";
            type = "csscl.home.status.pa";
            break;
        case 'R':
            color = "var(--ep-color-danger)";
            type = "csscl.home.status.rc";
            break;
        case 'E':
            color = "var(--ep-color-danger)";
            type = "csscl.home.status.e";
            break;
        case 'A':
            color = "var(--ep-color-success)";
            type = "csscl.home.status.ca";
            break;
        default:
            break;
    }
    return h(
        'span',
        {
            class: 'custom-tree-node',
        },
        h('span', {
            style: "font-weight: bold; max-width: 220px; overflow-wrap: break-word; text-wrap: wrap;color:" + color,
        }, proxy.$t(type)),
        h(
            'span',
            null,
            data.num
        )
    )
}
const handleNodeClick = (data) => {
	// Start R24114-29552, Tom.Li 2024/08/13
	// tableRef.value.formInline.currentNodeKey = data.id;
    tableRef.value.formInline.currentNodeKey = data.key;
    // End R24114-29552, Tom.Li 2024/08/13
    tableRef.value.formInline.menuPath = data.type.indexOf(".") > 0 ? data.type : "";
    tableRef.value.formInline.totalStatus = data.recordStatus;
    tableRef.value.onSearch();
    // Start R24114-29552, Tom.Li 2024/08/14
    scrollTo(0, 0);
    // End R24114-29552, Tom.Li 2024/08/14
}
const dbClickRow = (row) => {

    return {
        isOnlyQueryAprvRec: false,
    };
}

// const checkInputDateFromTo = async() => {
//     let msgs = [];
//     let msg1 = await checkBeforeCurDt(proxy, proxy.$t('csscl.home.table.dateFrom'), tableRef.value.formInline.sysUpdateDateFrom);
//     msgs.push(msg1);
//     let msg2 = await checkBeforeCurDt(proxy, proxy.$t('csscl.common.dateTo'), tableRef.value.formInline.sysUpdateDateTo);
//     msgs.push(msg2);
//     if (msg1 || msg2) {
//         return msgs;
//     }
//
//     let msg3 = checkDateFromTo(proxy, tableRef.value.formInline.sysUpdateDateFrom, tableRef.value.formInline.sysUpdateDateTo);
//     return msg3;
// }
//paramList 参数显示用的
function lockType(value) {
    paramListData.isLock = getCommonDesc('COM_YN', value);
}

const openWebSocket = () => {
    let baseUrl = proxy.$axios.defaults.baseURL;
    socket = new WebSocket(import.meta.env.VITE_WS_PROTOCOL + baseUrl.substr(baseUrl.indexOf(":")) + "/datamgmt/push/msg");

    socket.onopen = function(event) {
        console.log('Connection opened');
    };
  
    socket.onmessage = function(event) {
        if (elMsg) {
            return ;
        }
        let funcs = event.data.substr(0,event.data.indexOf(">"));
        let isShowMsg = false;
        let funcAry = funcs.split(",");
        let el;
        for (let j = 0; j < funcAry.length; j++) {
            el = funcAry[j];
            if(proxy.$currentInfoStore.havePermission(el)){
                isShowMsg = true;
                break;
            }
        }
        if (isShowMsg) {
            let msg = event.data.substr(event.data.indexOf(">")+1);
            elMsg = ElMessage({
                message: msg,
                type: 'warning',
                duration: 0,
                offset: 100,
                showClose: true,
            });
        }
    };  
    
    socket.onclose = function(event) {  
        console.log('Connection closed');
    };  
    
    socket.onerror = function(error) {  
        console.error('WebSocket Error: ', error);  
    };
}
// openWebSocket();

onUnmounted(()=>{
    // socket.close();
});

// Start R24114-29552, Tom.Li 2024/08/13
const collopseAll = ref(true);
watch(collopseAll,(newVal)=>{
    let nodes = treeRef.value?.store.nodesMap;
    if(nodes){
        for (let key in nodes) {
            nodes[key].expanded = !newVal;
        }
    }
});
const changeItem = (list: any, nodes: any) => {
    for (let i = 0; i < list.length; i++) {
        nodes[list[i].key].data.num = list[i].num;
        if (list[i].children&&list[i].children.length > 0) {
            changeItem(list[i].children, nodes);
        }
    }
}
// End R24114-29552, Tom.Li 2024/08/13

</script>

<style>
.custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
}

.tree-demo .ep-tree-node__content {
    height: unset;
}
/* Start R24114-29552, Tom.Li 2024/08/13 */
/* .ep-tree-node:focus>.ep-tree-node__content {
    background-color: var(--ep-color-primary-light-9);
}

.is-checked>.ep-tree-node__content,
.ep-tree-node__content:hover {
    background-color: var(--ep-color-primary-light-9);
} */
.is-checked>.ep-tree-node__content {
    background-color: var(--ep-color-primary-light-9);
}
.ep-tree-node__content:hover {
    background-color: var(--ep-fill-color-light) !important;
}
/* End R24114-29552, Tom.Li 2024/08/13 */

.dash-content table th div.cell,
.dash-content table td div {
    font-size: 13px !important;
    padding: 0px;
}

.dash-content table th div.cell {
    text-align: center;
}

.button-group {
  display: flex;
  justify-content: center;
  align-items: center;
}

::v-deep .ep-textarea__inner {
  min-height: 150px !important;
}
</style>