<template> 
 
        <Grid url="/datamgmt/api/v1/clearingagent/channel/list"
            :params="{ clearingAgentOid: agentForm.form.pendingOid!=null?agentForm.form.pendingOid:agentForm.form.clearingAgentOid }" 
            :onClick="channelClick"
            :beforeSearch="()=>{ pageObj.channelVPO = {}; }"
            :columns="[
                {title:'csscl.agent.type',name:'type',fn:commDesc('SWIFT_TYPE_CODE') },
                {title:'csscl.agent.channel',name:'channel',fn:commDesc('SETTLE_INSTR_INCOMING_CHANNEL_CODE') },
                {title:'csscl.agent.addrBicCode',name:'addrBicCode',},
                {title:'csscl.agent.corporateAction',name:'purposeCa'},
                {title:'csscl.agent.tradeSettlement',name:'purposeRradeSettle',},
                {title:'csscl.agent.positionReconcilation',name:'purposePosRecon', width:'230'},
                {title:'csscl.agent.cashReconcilation',name:'purposeCashRecon',},
                {title:'csscl.agent.cashPenalty',name:'purposeCashPenalty',},
                {title:'csscl.agent.status',name:'status',fn:commDesc('STATUS') },
                {title:'csscl.agent.recordStatus',name:'recordStatus',fn:getRecordStatusDesc },
            ]"
            >
        </Grid>

        <FormRow>
            <FormItemSign :detailsRef="details" label-width="80" :label="$t('csscl.agent.type')" prop="channelVPO.type">
                <Select v-model="pageObj.channelVPO.type"  style="width: 180px" disabled type="SWIFT_TYPE_CODE" />
            </FormItemSign>
            <FormItemSign :detailsRef="details" label-width="80" :label="$t('csscl.agent.channel')" prop="channelVPO.channel" >
                <Select v-model="pageObj.channelVPO.channel" style="width: 180px" disabled type="SETTLE_INSTR_INCOMING_CHANNEL_CODE" />
            </FormItemSign>
            <FormItemSign :detailsRef="details" label-width="150" :label="$t('csscl.agent.addrBicCode')" prop="channelVPO.addrBicCode">
                <el-input disabled v-model="pageObj.channelVPO.addrBicCode" />
            </FormItemSign>
        </FormRow>
        <FormRow>
            <FormItemSign  :detailsRef="details" label-width="80" :label="$t('csscl.agent.purpose')" style="flex: 2;">
                <FormRow style="width: 900px;" >
                    <el-checkbox :label="$t('csscl.agent.corporateAction')" disabled v-model="pageObj.channelVPO.purposeCa" true-value="Y" false-value="N" />
                    <el-checkbox :label="$t('csscl.agent.tradeSettlement')" disabled v-model="pageObj.channelVPO.purposeRradeSettle" true-value="Y" false-value="N" />
                    <el-checkbox :label="$t('csscl.agent.positionReconcilation')" disabled v-model="pageObj.channelVPO.purposePosRecon" true-value="Y" false-value="N" />
                    <el-checkbox :label="$t('csscl.agent.cashReconcilation')" disabled v-model="pageObj.channelVPO.purposeCashRecon" true-value="Y" false-value="N" />
                    <el-checkbox :label="$t('csscl.agent.cashPenalty')" disabled v-model="pageObj.channelVPO.purposeCashPenalty" true-value="Y" false-value="N" />
                </FormRow>
            </FormItemSign>
            <FormItemSign :detailsRef="details" label-width="151" :label="$t('common.title.status')" prop="channelVPO.status">
                <Select v-model="pageObj.channelVPO.status" style="width: 180px" disabled type="STATUS" />
            </FormItemSign>
        </FormRow>

</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import Grid from '~/pages/base/Grid.vue';
import  { getCommonDesc, commDesc, getRecordStatusDesc } from '~/util/Function.js';

const props = defineProps([ "agentForm", "details"]);

const details = props.details;
const agentForm = props.agentForm;
const pageObj = reactive({
  channelVPO: {},
});
const channelClick = (row) => {
    pageObj.channelVPO={};
    Object.assign(pageObj.channelVPO, row);
    details.initWatch(agentForm);
}

</script>

<style></style>