<template>
  <BasePanel :searchParams="searchParams" :paramListData='paramListData' url="/cashmgmt/api/v1/bankstmt/reconciliation/list" :params="{ modeEdit: 'Y' }" 
    :beforeSearch="beforeSearch" :afterSearch="afterSearch" :isMultiple="true" :selectable="selectable" :beforeEdit="beforeEdit" :dbClickRow="dbClickRow"
    :showDetails="showDetails" :deleteRow="deleteRow" :editRow="editRow" :rules="rules" :handleSelectionChange="handleSelectionChange" ref="tableRef" contentLeftWidth="360px">
    <template #contentLeft>
      <div ref="bankStateChartRef" :style="{width: '280px', height: '320px', zoom:zoomRef, transformOrigin:'0 0'}"></div>
      <br/>
      <div ref="bankReconChartRef" :style="{width: '280px', height: '350px', zoom:zoomRef, transformOrigin:'0 0'}"></div>
    </template>
    <template v-slot:searchPanel="slotProps">
      <FormRow>
        <ElFormItemProxy>
          <ElFormItemProxy :label="$t('csscl.cash.statement.stmtDateFrom')" prop="stmtDateFrom" label-width="175px">
            <DateItem :validate-event="false" v-model="slotProps.form.stmtDateFrom"
                      :title="$t('message.earlier.equal.curdate', [$t('csscl.cash.statement.stmtDateFrom')] ) + '\r' +
                        $t('message.earlier.equal.dateto', [$t('csscl.cash.statement.stmtDateFrom'), $t('csscl.cash.statement.stmtDateTo')] ) + '\r' +
                        $t('message.date.range.error', [30] ) "
                      type="date" style="width: 130px;"/>
          </ElFormItemProxy>
          <ElFormItemProxy :label="$t('common.title.date.to')" :hideLabel="$t('csscl.cash.statement.stmtDateTo')" prop="stmtDateTo" label-width="45px">
            <DateItem :validate-event="false" v-model="slotProps.form.stmtDateTo"
                      :title="$t('message.earlier.equal.curdate', [$t('csscl.cash.statement.stmtDateTo')] ) + '\r' +
                        $t('message.date.range.error', [30] ) "
                      type="date" style="width: 130px;"/>
          </ElFormItemProxy>
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.cash.statement.channel')" prop="channel" label-width="280px">
          <Select v-model="slotProps.form.channel" style="width: 110px" type='RECON_CHANNEL'  v-model:desc="paramListData.channel" />
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.cash.statement.processStatus')" prop="processStatus" label-width="150px">
          <Select v-model="slotProps.form.processStatus" style="width: 180px" type='RECON_PROCESS_STATUS' v-model:desc="paramListData.processStatus" />
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy :label="$t('csscl.cash.statement.custAccNo')" prop="custAccNo" label-width="175px">
          
          <GeneralSearchInput v-model="slotProps.form.custAccNo"
                searchType="custodyAcct"
                showDesc="false"
                codeTitle="csscl.acctCode.clientAccountOid"
                codeDescTitle="csscl.acctCode.accountShortName"
                />

        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.cash.statement.custAccShortName')" prop="custAccShortName" label-width="280px">
          <el-input v-model="slotProps.form.custAccShortName" maxlength="50" style="width:280px" input-style="text-transform:none"/>
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.cash.statement.reconStatus')" prop="reconStatus" label-width="150px">
          <Select v-model="slotProps.form.reconStatus" style="width: 180px" type='RECON_STATUS' v-model:desc="paramListData.reconStatus" />
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy :label="$t('csscl.cash.statement.currency')" prop="currencyCode" label-width="175px">
          <CurrencySearchInput v-model="slotProps.form.currencyCode" showDesc="false" style="width: 100px"/>
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.cash.statement.cashAccNo')" prop="cashAccNo" label-width="280px">
          <el-input v-model="slotProps.form.cashAccNo" maxlength="18" style="width:180px"/>
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('common.title.recordStatus')" prop="multipleRecordStatus" label-width="150px">
            <MultipleSelect v-model="slotProps.form.multipleRecordStatus" v-model:desc="paramListData.multipleRecordStatus" />
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy :label="$t('csscl.cash.statement.clientSubCustCashAccNo')" prop="custCashAccNo" label-width="175px">
          <el-input v-model="slotProps.form.custCashAccNo" maxlength="16" style="width: 180px;"/>
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.cash.statement.clearingAgentCode')" prop="clearingAgentCode" label-width="280px">
          <GeneralSearchInput v-model="slotProps.form.clearingAgentCode"
                style="width: 150px"
                showDesc="false"
                searchType="clearingAgentCode" />
        </ElFormItemProxy>
        <ElFormItemProxy :label="$t('csscl.cash.statement.addrBIC')" prop="addrBicCode" label-width="150px">
          <el-input v-model="slotProps.form.addrBicCode" maxlength="20" style="width:180px"/>
        </ElFormItemProxy>
      </FormRow>
    </template>
    <template v-slot:tableColumn>
      <el-table-column sortable="custom" prop="stmtDate" :label="$t('csscl.cash.statement.stmtDate')" header-align="center" width="150"/>
      <el-table-column sortable="custom" prop="custAccNo" :label="$t('csscl.cash.statement.custAccNo')" header-align="center" width="210" />
      <el-table-column sortable="custom" prop="custAccShortName" :label="$t('csscl.cash.statement.custAccShortName')" header-align="center" min-width="250" />
      <el-table-column sortable="custom" prop="clearingAgentCode" :label="$t('csscl.cash.statement.clearingAgentCode')" header-align="center" width="180" />
      <el-table-column sortable="custom" prop="cashAccNo" :label="$t('csscl.cash.statement.cashAccNo')" header-align="center" width="200" />
      <el-table-column sortable="custom" prop="currencyCode" :label="$t('csscl.cash.statement.currency')" header-align="center" width="110" />
      <el-table-column sortable="custom" prop="addrBicCode" :label="$t('csscl.cash.statement.addrBIC')" header-align="center" width="200" />
      <el-table-column sortable="custom" prop="swiftMsgId" :label="$t('csscl.cash.statement.swiftMsgId')" header-align="center" width="200" />
      <el-table-column sortable="custom" prop="processStatus" :label="$t('csscl.cash.statement.processStatus')" header-align="center" width="160">
        <template #default="scope">
          <!-- scope.row -->
          {{ getCommonDesc('RECON_PROCESS_STATUS', scope.row.processStatus) }}
        </template>
      </el-table-column>
      <el-table-column sortable="custom" prop="reconStatus" :label="$t('csscl.cash.statement.reconStatus')" header-align="center" width="200">
        <template #default="scope">
          <!-- scope.row -->
          {{ getCommonDesc('RECON_STATUS', scope.row.reconStatus) }}
        </template>
      </el-table-column>
      <el-table-column sortable="custom" prop="recordStsMkckAction" :label="$t('common.title.recordStatus')" header-align="center" width="200">
        <template #default="scope">
          {{ getRecordStatusDesc(scope.row) }}
        </template>
      </el-table-column>
    </template>
    <template v-slot:contentBottom>
      <br/>
      <el-space style="float:right;">
        <el-button v-if="$currentInfoStore.currentPermission && $currentInfoStore.currentPermission['Resume']" type="primary" @click="clickResume">
          {{$t('csscl.cash.statement.button.resume')}}</el-button >
        <el-button v-if="$currentInfoStore.currentPermission && $currentInfoStore.currentPermission['Marked as Complete']"
          type="primary" @click="clickMarkedAsComplete">
          {{$t('csscl.cash.statement.button.markedAsComplete')}}</el-button>
      </el-space>
    </template>
  </BasePanel>  
  <Details ref="detailsRef" :reload="reload" />

  <el-dialog v-model="dialogRef.showMkAsCompDialog" :title="$t('csscl.cash.statement.button.markedAsComplete')"
    :close-on-click-modal="false" width="30%" class="mkck-dialog" append-to-body :show-close="false">
    <template #header>
      <div class="mkckTitle">
        <span class="title-name">{{$t('csscl.cash.statement.button.markedAsComplete')}}</span>
      </div>
    </template>
    <br>
    <el-form-item label="Remark" style="padding-right: 12px; padding-left: 12px;">
      <el-input type="textarea" v-model="dialogRef.remark" :rows="6" style="width: 470px;"></el-input>
    </el-form-item>
    <br>
    <div class="button-group" style="text-align:center;">
      <el-button @click="dialogCancel" class="ep-button-custom">Cancel</el-button>
      <el-button type="primary" @click="markedAsCompOk" class="ep-button-custom">OK</el-button>
    </div>
    <br>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, getCurrentInstance, reactive } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import * as echarts from 'echarts';
import CurrencySearchInput from '~/pages/base/CurrencySearchInput.vue';
import { getCommonDesc, getRecordStatusDesc, checkBeforeCurDt, checkDateFromTo, checkDateBetween, showErrorMsg } from '~/util/Function.js';
import { commonRules } from '~/util/Validators.js';

import BasePanel from '~/pages/base/index.vue'
import Details from './Details.vue'
const { proxy } = getCurrentInstance()
const paramListData = {};
const searchParams = {
  //顺序和上面绑定参数一致
  stmtDateFrom:null,
  stmtDateTo:null,
  channel:"",
  processStatus:"",
  custAccNo:"",
  custAccShortName:"",
  reconStatus:"",
  currencyCode:"",
  cashAccNo:"",
  multipleRecordStatus:[],
  custCashAccNo:"",
  clearingAgentCode:"",
  addrBicCode:"",
};

const tableRef = ref();
const detailsRef = ref();

const showDetails = (row, disabled) => {
  detailsRef.value.showDetails(row, disabled);
}
const deleteRow = (row) => {
  console.log("Delete ...");
}
const editRow = (row) => {
  detailsRef.value.editRow(row);
}
const reload = () => {
  tableRef.value.load();
}

const rules = reactive({
  stmtDateFrom:[
    commonRules.selectRequired,
    commonRules.earlierEquCurProcDate,
    commonRules.earlierEquDt(()=>{ return searchParams.stmtDateTo }, proxy.$t("csscl.cash.statement.stmtDateTo")),
    commonRules.diffDate(30,()=>{ return searchParams.stmtDateTo }, proxy.$t("csscl.cash.statement.stmtDateTo")),
  ],
  stmtDateTo:[
    commonRules.selectRequired,
    commonRules.earlierEquCurProcDate,
  ],
});

//-------------------------------
const beforeSearch = async() =>{
// let dateFrom = tableRef.value.formInline.stmtDateFrom;
// let dateTo = tableRef.value.formInline.stmtDateTo;

// let msgs = [];
// let msg1 = await checkBeforeCurDt(proxy, proxy.$t('csscl.cash.statement.stmtDateFrom'), dateFrom);
// msgs.push(msg1);
// let msg2 = await checkBeforeCurDt(proxy, proxy.$t('csscl.cash.statement.stmtDateTo'), dateTo);
// msgs.push(msg2);
// if(msg1 || msg2){
//   return msgs;
// }

// let msg = checkDateFromTo(proxy, dateFrom, dateTo, proxy.$t('csscl.cash.statement.stmtDateFrom'));
// if(msg){
//   return msg;
// }

// msg = checkDateBetween(proxy, dateFrom, dateTo, 30);
// return msg;
}
const afterSearch = (params, data) =>{
  initPie(params);
}

const beforeEdit = async(row) => {
  if(!checkReconStatus(row)){
    return false;
  }
  
  if(await checkReconLock()){
    return false;
  }

  return true;
}

const dbClickRow = (row) => {
  return checkReconStatus(row);
}

const checkReconStatus = (row) => {
  if (row.recordStatus == 'A' && row?.processStatus !== 'R') {
    ElMessageBox.alert(proxy.$t('message.cash.statement.recon.status'), 'Warning', {
      confirmButtonText: 'OK',
      type: 'warning',
    });
    return false;
  }
  return true;
}
const checkReconLock = async() => {
  let islock = false;
  await proxy.$axios.get("/cashmgmt/api/v1/bankstmt/reconciliation/lock").then((body) => {
    if (!body.success) {
      islock = true;
    }
  });
  return islock;
}

const EColorProcSts = {
  P: 'orange',
  R: 'green',
  N: 'red',
  H: 'yellow',
  M: 'blue',
};
const EColorReconSts = {
  P: 'orange',
  C: 'green',
  F: 'red',
  N: 'blue',
};

const zoomRef = ref(1);
const bankStateChartRef = ref(null);
const bankReconChartRef = ref(null);
const initPie = (params) =>{
  //解决鼠标悬停时错位的问题
  let cw = window.outerWidth;
  if (cw < 1920) {
    zoomRef.value = 1/(cw / 1920);
  }

  proxy.$axios.post("/cashmgmt/api/v1/bankstmt/reconciliation/overview", params).then((body) => {
    if (body.success) {
      let stateDatas = [];
      let countProcessStatus = body.data?.countProcessStatus;
      if(countProcessStatus){
        for(var idx in countProcessStatus){
          let row = countProcessStatus[idx];
          stateDatas.push(
            {
              name: getCommonDesc('RECON_PROCESS_STATUS', row.processStatus),
              value: row.counts,
              color: EColorProcSts[row.processStatus],
            }
          );
        }
      }
      const bankStateChart = echarts.init(bankStateChartRef.value);
      let stateOption = createEchartOption(proxy.$t('csscl.cash.statement.bankStmtOverview'), stateDatas);
      bankStateChart.setOption(stateOption);

      let reconDatas = [];
      let countReconStatus = body.data?.countReconStatus;
      if(countReconStatus){
        for(var idx in countReconStatus){
          let row = countReconStatus[idx];
          reconDatas.push(
            {
              name: getCommonDesc('RECON_STATUS', row.reconStatus),
              value: row.counts,
              color: EColorReconSts[row.reconStatus],
            }
          );
        }
      }
      const bankReconChart = echarts.init(bankReconChartRef.value);
      let reconOption = createEchartOption(proxy.$t('csscl.cash.statement.reconOverview'), reconDatas);
      bankReconChart.setOption(reconOption);
    }
  });
};

const createEchartOption = (title, datas) => {
  const option = {
    title: {
      text: title,
      left: 'center',
      top: '5%',
      textStyle: {
        fontSize: 12
      },
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b}, {c}',
      confine: true,
    },
    legend: {
      bottom: '10%',
      textStyle: {
        fontSize: 9
      },
      itemWidth: 6,
      itemHeight: 6,
      itemGap: 0,
      left:0,
    },
    series: [
      {
        type: 'pie',
        radius: ['45%', '55%'],
        itemStyle: {
          borderRadius: 0,
          borderColor: '#fff',
          borderWidth: 2,
          normal:{
            color: function(params){
              return params.data.color;
            }
          },
        },
        labelLine: {
          length: 10
        },
        label: {
          formatter: (params)=>{
            return params.data.name + ", " + params.data.value;
          },
          //backgroundColor: '#F6F8FC',
          borderColor: '#8C8D8E',
          borderWidth: 1,
          borderRadius: 0,
          overflow: 'break',
          fontSize: 8,
          fontWeight: 'bold',
          padding: [2, 4, 2, 4],
          textStyle: {
          },
        },
        data: datas
      }
    ]
  };
  return option;
}

const tableSelectRowsRef  = ref([]);
const handleSelectionChange = (val) => {
  tableSelectRowsRef.value = val;
}
const selectable = (row) => {
  return row?.recordStatus === "A";
}
const dialogRef=ref({
  showMkAsCompDialog: false,
  remark:'',
});
const clickResume = () => {
  if(tableSelectRowsRef.value.length == 0){
    showErrorMsg(proxy.$t('message.common.not.checked'));
    return;
  }
  for(var index in tableSelectRowsRef.value){
    let row = tableSelectRowsRef.value[index];
    if(row.processStatus != 'N' && row.processStatus != 'H' && row.processStatus != 'P'){
      showErrorMsg(proxy.$t('message.cash.statement.resume.procsts'));
      return;
    }
  }
  ElMessageBox.confirm(proxy.$t('message.cash.statement.resume.warning'), 'Warning',
    {
      customStyle: {
        'max-width': '26%',
      },
      confirmButtonText: 'OK',
      cancelButtonText: 'Cancel',
      type: 'warning',
      closeOnClickModal: false
    })
    .then(() => {
      resumeOk();
    })
    .catch(() => {

    });
}
const clickMarkedAsComplete = () => {
  if(tableSelectRowsRef.value.length == 0){
    showErrorMsg(proxy.$t('message.common.not.checked'));
    return;
  }
  for(var index in tableSelectRowsRef.value){
    let row = tableSelectRowsRef.value[index];
    if(row.processStatus != 'N'){
      showErrorMsg(proxy.$t('message.cash.statement.mkascomp.procsts'));
      return;
    }
  }
  dialogRef.value.showMkAsCompDialog=true;
}
const resumeOk = async () => {
  let params = {
    reconBankStmtVpos: tableSelectRowsRef.value,
  };
  const resp = await proxy.$axios.post("/cashmgmt/api/v1/bankstmt/reconciliation/resume", params);
  if(resp.success){
    ElMessageBox.alert("Resume successfully.", 'Success', {
      confirmButtonText: 'OK',
      type: 'success',
    });
  }
  reload();
};
const markedAsCompOk = async () => {
  for(var index in tableSelectRowsRef.value){
    let row = tableSelectRowsRef.value[index];
    let params = "isQueryVpo=true&objectIds="+row.currentOid;
    const resp = await proxy.$axios.patch("/cashmgmt/api/v1/bankstmt/reconciliation/marked/complete?"+params);
    if(resp.success){
      if(resp?.data?.length > 0){
        let editRow = resp?.data[0];
        await handleSubmit(editRow.mkckOid, editRow.currentOid, dialogRef.value.remark);
      }
    }else{
      console.log("Fialed update record " + row.currentOid);
    }
  }
  reload();
  dialogCancel();  
};
const dialogCancel = () => {
  dialogRef.value.remark = '';
  dialogRef.value.showMkAsCompDialog = false;
}

const handleSubmit = async (mkckOid, eventOid, remark) => {
  let funcId = proxy.$currentInfoStore.getCurrentFuncId();
  
  const response = await proxy.$axios.post('/datamgmt/api/v1/makerchecker/initial', {
    flowMakerCheckerOid: mkckOid,
    remark: remark,
    status: "A",
    eventOid: eventOid,
    eventType: 'SD',
    funcId: funcId
  });
  if(response.success){
    ElMessageBox.alert("Submitted successfully.", 'Success', {
      confirmButtonText: 'OK',
      type: 'success',
    });
  }
};

//paramList 参数显示用的
// function channelType(value) {
//   paramListData._value.channel = getCommonDesc('RECON_CHANNEL', value);
// }
// function processStatusType(value) {
//   paramListData._value.processStatus = getCommonDesc('RECON_PROCESS_STATUS', value);
// }
// function recordStatusType(value) {
//   paramListData._value.recordStatus = getCommonDesc('RECORD_STATUS', value);
// }
// function reconStatusType(value) {
//   paramListData._value.reconStatus = getCommonDesc('RECON_STATUS', value);
// }
</script>

<style></style>