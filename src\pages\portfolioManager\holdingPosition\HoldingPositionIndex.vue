<template>
  <!-- :rules="rules" -->
  <BasePanel :searchParams="searchParams" :paramListData="paramListData" 
    url="/bff/portfolio/api/v1/holding-position/get-holding-position-page-list"
    selectFirstRecord="true" :clickRow="handleClick"
    ref="tableRef" :hideOperation="true" :isHideAdd="true" :params="{ modeEdit: 'Y' }" :beforeSearch="beforeSearch"
    :onExportCSV="handleExportCSV">
    <template v-slot:searchPanel="slotProps">
      <FormRow>
        <ElFormItemProxy label-width="200" :label="$t('csscl.portfolio.common.category')" prop="category" required>
          <Select v-model="slotProps.form.category" type="PORTFOLIO_HOLDING_POSITION_CATEGORY" style="width: 250px;" @change="handleCategoryChange" />
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy label-width="200" :label="$t('csscl.portfolio.common.clientCode')" prop="clientCode">
          <GeneralSearchInput v-model="slotProps.form.clientCode" input-style="width:250px" searchType="clientCode" showDesc="false"/>
        </ElFormItemProxy>
        <ElFormItemProxy label-width="200" :label="$t('csscl.portfolio.common.shortName')" prop="clientShortName">
          <GeneralSearchInput v-model="slotProps.form.clientShortName" input-style="width:250px" searchType="clientShortName" showDesc="false"/>
        </ElFormItemProxy>
        <ElFormItemProxy />
      </FormRow>
      <FormRow>
        <ElFormItemProxy label-width="200" :label="$t('csscl.si.common.custodyAccountNumber')" prop="custodyAccountGroup">
          <MultipleGeneralSearchInput v-model="slotProps.form.custodyAccountGroup" searchType="custodyAcct" style="width:250px"/>
        </ElFormItemProxy>
        <ElFormItemProxy label-width="200" :label="$t('csscl.portfolio.common.shortName')" prop="accountShortName">
          <GeneralSearchInput v-model="slotProps.form.accountShortName" input-style="width:250px" searchType="custodyAcctShortName" showDesc="false"/>
        </ElFormItemProxy>
        <ElFormItemProxy label-width="200" :label="$t('csscl.si.common.numOfCustodyAccount')" prop="numOfCustodyAccount">
          <el-input v-model="slotProps.form.numOfCustodyAccount" style="width: 180px" :disabled="true"></el-input>
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy label-width="200" :label="$t('csscl.portfolio.common.externalInstrument')" prop="externalInstrument">
          <GeneralSearchInput v-model="slotProps.form.externalInstrument" input-style="width:250px" searchType="externalInstrumentCode" showDesc="false"/>
        </ElFormItemProxy>
        <ElFormItemProxy label-width="200" :label="$t('csscl.portfolio.common.exchangeBoard')" prop="exchangeBoard">
          <GeneralSearchInput v-model="slotProps.form.exchangeBoard" input-style="width:190px" style="width:380px" searchType="exboardCode" showDesc="true"/>
        </ElFormItemProxy>
        <ElFormItemProxy />
      </FormRow>
      <FormRow>
        <ElFormItemProxy label-width="200" :label="$t('csscl.si.common.securitiesId')" prop="instrumentCode">
          <GeneralSearchInput v-model="slotProps.form.instrumentCode" input-style="width:250px" searchType="instrumentCode" showDesc="false"/>
        </ElFormItemProxy>
        <ElFormItemProxy label-width="200" :label="$t('csscl.si.common.securityName')" prop="instrumentName">
          <GeneralSearchInput v-model="slotProps.form.instrumentName" input-style="width:250px" searchType="instrumentName" showDesc="false"/>
        </ElFormItemProxy>
        <ElFormItemProxy label-width="200" :label="$t('csscl.si.common.outputInstrumentType')" prop="outputInstrumentType">
          <Select v-model="slotProps.form.outputInstrumentType" type='IDENTIFIER_TYPE_CODE' style="width: 180px;" />
        </ElFormItemProxy>
      </FormRow>
      <FormRow>
        <ElFormItemProxy label-width="200" :label="$t('csscl.portfolio.common.marketCode')" prop="marketCode">
          <GeneralSearchInput v-model="slotProps.form.marketCode" input-style="width:190px" style="width:380px" searchType="exboardCode" showDesc="true"/>
        </ElFormItemProxy>
        <ElFormItemProxy label-width="200" :label="$t('csscl.portfolio.common.location')" prop="location">
          <GeneralSearchInput v-model="slotProps.form.location" input-style="width:250px" searchType="exchangeCode" showDesc="false"/>
        </ElFormItemProxy>
        <ElFormItemProxy />
      </FormRow>
      <FormRow>
        <ElFormItemProxy label-width="200" :label="$t('csscl.portfolio.common.dateOfHolding')" prop="dateOfHolding">
          <DateItem v-model="slotProps.form.holdingDate" type="date" style="width: 180px;"/>
        </ElFormItemProxy>
        <ElFormItemProxy label-width="200" :label="$t('csscl.si.common.custodyLevel')" prop="custodyLevel" required>
          <Select v-model="slotProps.form.custodyLevel" type="CUSTODY_LEVEL" style="width: 160px;" />
        </ElFormItemProxy>
        <ElFormItemProxy label-width="200" :label="$t('csscl.si.common.custodyMarket')" prop="exchangeCode">
          <GeneralSearchInput v-model="slotProps.form.exchangeCode" input-style="width:250px" searchType="custodyMarket" showDesc="false"/>
        </ElFormItemProxy>
      </FormRow>
    </template>  
    <template v-slot:tableColumn>
      <el-table-column sortable="custom" prop="custodyAccountNumber" :label="$t('csscl.si.common.custodyAccountNumber')" align="center" width="150"/>
      <el-table-column sortable="custom" prop="custodyAccountName" :label="$t('csscl.si.common.custodyAccountName')" align="center" width="150"/>
      <el-table-column sortable="custom" prop="location" :label="$t('csscl.portfolio.common.location')" align="center" width="100"/>
      <el-table-column sortable="custom" prop="marketCode" :label="$t('csscl.portfolio.common.marketCode')" align="center" width="100"/>
      <el-table-column sortable="custom" prop="marketName" :label="$t('csscl.si.common.marketName')" align="center" width="120"/>
      <el-table-column sortable="custom" prop="safeAccountNumber" :label="$t('csscl.portfolio.common.safeAccountNumber')" align="center" width="120"/>
      <el-table-column sortable="custom" prop="securitiesIdType" :label="$t('csscl.si.common.securitiesIdType')" align="center" width="120"/>
      <el-table-column sortable="custom" prop="securitiesId" :label="$t('csscl.si.common.securitiesId')" align="center" width="120"/>
      <el-table-column sortable="custom" prop="securitiesName" :label="$t('csscl.si.common.securitiesName')" align="center" width="150"/>
      <el-table-column sortable="custom" prop="subTotalMarketPriceCurrency" :label="$t('csscl.portfolio.common.subTotalMarketPriceCurrency')" align="center" width="120"/>
      <el-table-column sortable="custom" prop="subTotalMarketUnitPrice" :label="$t('csscl.portfolio.common.subTotalMarketUnitPrice')" align="center" width="120"/>
      <el-table-column sortable="custom" prop="subTotalMarketValue" :label="$t('csscl.portfolio.common.subTotalMarketValue')" align="center" width="120"/>
      <el-table-column sortable="custom" prop="exchangeCurrencyPair" :label="$t('csscl.portfolio.common.exchangeCurrencyPair')" align="center" width="120"/>
      <el-table-column sortable="custom" prop="subTotalExchangeRate" :label="$t('csscl.portfolio.common.subTotalExchangeRate')" align="center" width="120"/>
      <el-table-column sortable="custom" prop="subTotalEquivalentCurrency" :label="$t('csscl.portfolio.common.subTotalEquivalentCurrency')" align="center" width="140"/>
      <el-table-column sortable="custom" prop="subTotalEquivalentMarketValue" :label="$t('csscl.portfolio.common.subTotalEquivalentMarketValue')" align="center" width="150"/>
      <el-table-column sortable="custom" prop="subTotalQuantityType" :label="$t('csscl.portfolio.common.subTotalQuantityType')" align="center" width="120"/>
      <el-table-column sortable="custom" prop="subTotalHolding" :label="$t('csscl.portfolio.common.subTotalHolding')" align="center" width="120"/>
      <el-table-column sortable="custom" prop="holdingAvailableBalance" :label="$t('csscl.portfolio.common.holdingAvailableBalance')" align="center" width="150"/>
    </template>

    <template v-slot:contentBottom>
      <br />
      <!-- Holding Details ------------------------------>
      <div style="margin: 0px; padding: 3px; border-bottom: 2px solid #1AA4A4;">
        <el-text style="color: #1AA4A4; font: 14px bold;">
          {{ $t('csscl.portfolio.common.holdingDetails') }}
        </el-text>
      </div>
      <HoldingDetailsTable ref="holdingDetailsTableRef" />
    </template>
  </BasePanel>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance, watch, computed } from 'vue';
import type { FormRules } from 'element-plus';
import BasePanel from '~/components/Si/SIIndex.vue'
import HoldingDetailsTable from "~/pages/portfolioManager/holdingPosition/HoldingDetailsTable.vue";
import { ElMessage, ElLoading, ElMessageBox } from 'element-plus';
import { getTimeZone } from "~/util/DateUtils";
import { getCommonDesc, downloadFilePost } from '~/util/Function.js';
import { commonRules } from '~/util/Validators.js';

const { proxy } = getCurrentInstance() as any;
const tableRef = ref<any>(null);
const holdingDetailsTableRef = ref<InstanceType<typeof HoldingDetailsTable> | null>(null);

const paramListData = reactive({
  tableData: []
});

// 搜索参数
const searchParams = reactive({
  category: 'PORTFOLIO_HOLDING',
  clientCode: '',
  clientShortName: '',
  custodyAccountGroup: '',
  accountShortName: '',
  numOfCustodyAccount: '',
  externalInstrument: '',
  exchangeBoard: '',
  instrumentCode: '',
  instrumentName: '',
  outputInstrumentType: '',
  marketCode: '',
  location: '',
  holdingDate: '',
  custodyLevel: 'GL',
  exchangeCode: '',
  tradingAccountCodes: [] as string[],
});

watch(() => searchParams.custodyAccountGroup, (newValue) => {
  if (newValue) {
    searchParams.tradingAccountCodes = newValue.split(',').filter(item => item !== '');
    searchParams.numOfCustodyAccount = searchParams.tradingAccountCodes.length.toString();
  } else {
    searchParams.tradingAccountCodes = [];
    searchParams.numOfCustodyAccount = '';
  }
});

interface RuleForm {
  outputInstrumentType: String
}

const generateRequestparam = (data: any) => {
  let params = {
    header: {timezone: getTimeZone(), lang:"en_US"},
    data: data
  };
  return params;
};

const isResponseSuccess = (response: any) => {
  if (!response || !response.header) {
    return false;
  }
  return response.header.code === '000000';
};

const getErrorMessage = (errorMessage: any) => {
  return errorMessage || 'Unknown error';
};

// 搜索前的处理函数
const beforeSearch = async (params: any) => {
  // 清空详情组件的数据
  clearComponentsData();
  return true;
};

// 清空所有子组件数据
const clearComponentsData = () => {
  holdingDetailsTableRef.value?.clearData?.();
};

// 处理category变化
const handleCategoryChange = (value: any) => {
  searchParams.category = value;
};

const handleExportCSV = async () => {
  // 显示加载中状态
  const loading = ElLoading.service({
    lock: true,
    text: '',
    background: 'rgba(255, 255, 255, 0.3)',
    fullscreen: false,
    target: document.querySelector('.el-main') as HTMLElement || undefined,
    body: false,
    customClass: 'loading-position'
  });
  try {
    await downloadFilePost("/bff/portfolio/api/v1/holding-position/export-csv", generateRequestparam(searchParams));
  } catch (error) {
    ElMessage.error('Failed to export CSV');
  } finally {
    loading.close();
  }
};

// 处理行点击事件
const handleClick = async (row: any) => {
  if (!row || !row.oid) {
    return;
  }

  // 清空详情组件的数据
  clearComponentsData();
  
  // 加载中
  const loading = ElLoading.service({
    lock: true,
    text: 'Loading',
    background: 'rgba(255, 255, 255, 0.3)',
    fullscreen: false,
    target: document.querySelector('.el-main') as HTMLElement || undefined,
    body: false,
    customClass: 'loading-position'
  });
  
  try {
    // 调用详情表格的showDetails方法加载数据
    if (holdingDetailsTableRef.value) {
      holdingDetailsTableRef.value.showDetails(row.oid);
    }
    
  } catch (error) {
    ElMessage.error('Failed to load detail data');
  } finally {
    loading.close();
  }
};

// 表单校验规则 - TBC
const rules = reactive<FormRules<RuleForm>>({
     outputInstrumentType:[
       commonRules.selectRequired,
     ],
     holdingDate:[
       commonRules.selectRequired,
     ]
});

</script>

<style>
/* 与TransactionsIndex.vue相同的样式 */
.loading-position {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 9999 !important;
  margin: 0 !important;
  padding: 0 !important;
}

.loading-position .el-loading-spinner {
  position: fixed !important;
  top: 50vh !important;
  left: 50vw !important;
  transform: translate(-50%, -50%) scale(1.8) !important;
  margin: 0 !important;
  z-index: 10000 !important;
}

.loading-position .el-loading-spinner svg {
  width: 50px !important;
  height: 50px !important;
  color: #409eff !important;
}

.loading-position .el-loading-spinner svg circle,
.loading-position .el-loading-spinner svg path {
  stroke: #409eff !important;
  stroke-width: 4 !important;
}

.loading-position .el-loading-text {
  color: #409eff !important;
  font-size: 16px !important;
  font-weight: 500 !important;
}

.loading-position .el-loading-text {
  position: fixed !important;
  top: calc(50vh + 40px) !important;
  left: 50vw !important;
  transform: translateX(-50%) !important;
  margin: 0 !important;
  z-index: 10000 !important;
}

.el-main {
  overflow-x: hidden;
}
</style>
