<template>
  <FormRow>
    <ElFormItemProxy label-width="200" :label="$t('csscl.si.common.settlementCurrency')">
      <el-input v-model="formData.settlementCurrency" :disabled="true"></el-input>
    </ElFormItemProxy>
    <ElFormItemProxy label-width="200" :label="$t('csscl.si.common.settlementAmount')">
      <el-input v-model="formData.settlementAmount" :disabled="true"></el-input>
    </ElFormItemProxy>
    <ElFormItemProxy label-width="200" :label="$t('csscl.si.common.siType')">
      <Select v-model="formData.siType" style="width: 190px" type='SI_TYPE' :disabled="true" />
    </ElFormItemProxy>
  </FormRow>
  <FormRow>
    <ElFormItemProxy label-width="200" :label="$t('csscl.si.common.debitCreditCurrency')">
      <el-input v-model="formData.debitCreditCurrency" :disabled="true"></el-input>
    </ElFormItemProxy>
    <ElFormItemProxy label-width="200" :label="$t('csscl.si.common.debitCreditAmount')">
      <el-input v-model="formData.debitCreditAmount" :disabled="true"></el-input>
    </ElFormItemProxy>
    <ElFormItemProxy label-width="200" :label="$t('csscl.si.common.siStatus')">
      <Select v-model="formData.siStatus" style="width: 190px" type='SI_STATUS_NEW' :disabled="true" />
    </ElFormItemProxy>
  </FormRow>
  <FormRow>
    <ElFormItemProxy label-width="200" :label="$t('csscl.si.common.clearingAgent')">
      <el-input v-model="formData.counterpartyClearingAgent" :disabled="true"></el-input>
    </ElFormItemProxy>
    <ElFormItemProxy label-width="200" :label="$t('csscl.si.common.custodianAccount')">
      <el-input v-model="formData.custodianAccountNumber" :disabled="true"></el-input>
    </ElFormItemProxy>
    <ElFormItemProxy />
  </FormRow>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

// 表单数据
const formData = ref({
  tradeOid: null,
  executedPrice: '',
  settlementCurrency: '',
  settlementAmount: '',
  debitCreditCurrency: '',
  debitCreditAmount: '',
  counterpartySwiftBic: '',
  counterpartyShortName: '',
  siType: '',
  siStatus: '',
  counterpartyClearingAgent: '',
  custodianAccountNumber: ''
});

// 设置数据方法
const setData = (data: any) => {
  if (!data) return;
  
  formData.value = {
    tradeOid: data.tradeOid || null,
    executedPrice: data.executedPrice || '',
    settlementCurrency: data.settlementCurrency || '',
    settlementAmount: data.settlementAmount || '',
    debitCreditCurrency: data.debitCreditCurrency || '',
    debitCreditAmount: data.debitCreditAmount || '',
    counterpartySwiftBic: data.counterpartySwiftBic || '',
    counterpartyShortName: data.counterpartyShortName || '',
    siType: data.siType || '',
    siStatus: data.siStatus || '',
    counterpartyClearingAgent: data.counterpartyClearingAgent || '',
    custodianAccountNumber: data.custodianAccountNumber || ''
  };
};

// 清除数据方法
const clearData = () => {
  formData.value = {
    tradeOid: null,
    executedPrice: '',
    settlementCurrency: '',
    settlementAmount: '',
    debitCreditCurrency: '',
    debitCreditAmount: '',
    counterpartySwiftBic: '',
    counterpartyShortName: '',
    siType: '',
    siStatus: ''
  };
};

// 暴露方法供父组件调用
defineExpose({
  clearData,
  setData
});
</script>

<style scoped>
</style> 