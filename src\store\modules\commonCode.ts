import { defineStore } from "pinia" // 定义容器
import axios from '~/axios';

export const commonCode = defineStore('commonCodeStore', {

  state: () => {
    return {
      all: {},
      recordStatusList: {}
    }
  },

  getters: {
    getAll(): object {
      return this.all;
    },
    getRecordStatusList(): object {
      return this.recordStatusList;
    }
  },

  actions: {
    setAll(codes: object) {
      this.all = codes;
    },
    setRecordStatusList(list: object) {
      this.recordStatusList = list;
    },
    async fetchAll(){
      let body = await axios.post('/datamgmt/api/v1/comcode/cache', {
        current: 1,
        pageSize: 1000,
        param: {
          flag: 'true',
        }
      });
      if (body.success) {
        let obj = {};
        for (let i = 0; i < body.data.data.length; i++) {
          let ele = body.data.data[i];
          if (!obj[ele.codeType]) {
            obj[ele.codeType] = [];
          }
          obj[ele.codeType].push(ele);
        }
        this.setAll(obj);
  
        let obj2 = {};
        for (let i = 0; i < obj['RECORD_STATUS'].length; i++) {
          let ele = obj['RECORD_STATUS'][i];
          obj2[ele.code] = ele.codeDesc;
        }
        this.setRecordStatusList(obj2);
      }
    }
  }

})