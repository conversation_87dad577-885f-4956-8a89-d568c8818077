<template>
    <el-form :validateOnRuleChange="false"  ref="formRef" :inline="true" :model="formInline"  :disabled="formDisabled">
        <br>
        <el-row>
            <span style="font-size: 16px; font-weight: bold;">Custody Account Criteria</span>
            <div style="paddingBlock:5px;width: 100%;display: flex;flex-flow: wrap;border-top: 1px solid;"></div>
        </el-row>
        <div style="display: flex; align-items: center;">
            <el-row style="width: 100%;">
                <el-col :span="12">
                    <el-table :data="formInline.reportAccounts" height="250" highlight-current-row
                     :row-class-name="tableRowClassName" ref="tabRef" @row-click="handleClick" :cell-style="cell"
                      @row-dblclick="handleDbAccountClick" @current-change="handleCurrentAccountChange" border
                        style="width: 100%; margin: 0; padding: 0;">
                        <el-table-column prop="accountGroup" :label="$t('csscl.reportScheduler.accountGroup')" >
                            <template #default="scope">
                                {{ getCommonDesc('ACCOUNT_GROUP', scope.row.accountGroup) }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="accountGroupDetails" :label="$t('csscl.reportScheduler.accountGroupDetails')" >
                            <template #default="scope">
                                {{ getCommonDesc('CLIENT_GROUP', scope.row.accountGroupDetails) }}
                            </template>
                        </el-table-column>

                        <el-table-column prop="accounts"
                            :label="$t('csscl.reportScheduler.numOfCustodyAccount')">
                            <template #default="scope">
                                {{ getCommonDesc('ACCOUNTS', scope.row.accounts) }}
                            </template>

                        </el-table-column>

                        <el-table-column  v-if="props.isReportScheduler" prop="recordStatus" :label="$t('common.title.recordStatus')">
                            <template #default="scope">
                                {{ getRecordStatusDesc(scope.row) }}
                            </template>
                        </el-table-column>
                    </el-table>
                </el-col>
                <el-col :span="12">
                    <el-space style="margin-top: 10px;margin-left: 10px;" direction="vertical">
                        <el-button @click="showAddAccount" style="width: 30px" :icon="Plus" size="small"></el-button>
                        <el-button @click="deleteAccount" style="width: 30px" :icon="Minus" size="small"></el-button>
                    </el-space>
                </el-col>
            </el-row>
        </div>
        <el-form :validateOnRuleChange="false"  label-position="left">
            <br>
            <el-row style="margin:5px 0" v-if="isShowAddAccount">
                <FormItemSign :detailsRef="details" label="Account Group" prop="accountGroup">
                    <Select v-model="newAccountsRow.accountGroup" style="width: 150px" type='ACCOUNT_GROUP'
                        :change="changeAccountGroup" :disabled="formDisabled" />
                </FormItemSign>
            </el-row>
            <el-col :span="14" v-show="isShowAddAccount">
                <el-card>
                    <FormItemSign v-if="newAccountsRow.accountGroup === 'CG'" :detailsRef="details" label="Client Group"
                        prop="clientGroup" label-width="130px">

<!--                      <Select v-model="newAccountsRow.clientGroup" style="width: 150px" type='CLIENT_GROUP'-->
<!--                            :change="changeClientGroup" :disabled="formDisabled"/>-->
                        <CommonSearchInput v-model="newAccountsRow.clientGroup"
                                           :change="(val)=>{
                                              newAccountsRow.clientGroup=val;
                                              changeClientGroup(val)
                                           }"
                                           :dbClick="(row)=>{
                                              newAccountsRow.clientGroup=row.code;
                                              changeClientGroup()
                                           }"
                                           :disabled="formDisabled"
                                           maxlength="50"
                                           codeTitle="Client Group"
                                           showDesc="false"
                                           commType='CLIENT_GROUP' />

                    </FormItemSign>
                    <FormItemSign v-if="newAccountsRow.accountGroup === 'CNC'" :detailsRef="details"
                        label="Client Number CIN" prop="clientGroup" label-width="130px">
                            <GeneralSearchInput 
                                    v-model="newAccountsRow.clientGroup"
                                    style="width:200px"
                                    maxlength="11"
                                    searchType="clientCode"
                                    showDesc="false"
                                    :change="(val)=>{
                                        newAccountsRow.clientMasterOid='1';
                                        changeClientGroup()
                                    }"
                                    :dbClick="(row)=>{
                                        newAccountsRow.clientGroup=row.code;
                                        newAccountsRow.clientMasterOid=row.var1;
                                        changeClientGroup()
                                    }"
                                    :disabled="formDisabled"
                                    />
                                

                    </FormItemSign>
                    <FormItemSign v-if="newAccountsRow.accountGroup === 'FM'" :detailsRef="details" label="Fund Manager"
                        prop="clientGroup" label-width="130px">
<!--                        <Select v-model="newAccountsRow.clientGroup" style="width: 150px" type='FUND_MANAGER'-->
<!--                            :change="changeClientGroup"  :disabled="formDisabled"/>-->
                        <CommonSearchInput v-model="newAccountsRow.clientGroup"
                                           :change="(val)=>{
                                              newAccountsRow.clientGroup=val;
                                              changeClientGroup(val)
                                           }"
                                           :dbClick="(row)=>{
                                              newAccountsRow.clientGroup=row.code;
                                              changeClientGroup()
                                           }"
                                         :disabled="formDisabled"
                                         maxlength="50"
                                         codeTitle="Fund Manager"
                                         showDesc="false"
                                         commType='FUND_MANAGER' />
                    </FormItemSign>

                    <FormItemSign v-if="newAccountsRow.accountGroup === 'CA'" :detailsRef="details"
                        label="Custody Account" prop="clientGroup" label-width="130px">
                            <GeneralSearchInput v-model="newAccountsRow.clientGroup"
                                    style="width:200px"
                                    searchType="custodyAcct"
                                    showDesc="false"
                                    :params="{ recordStatus: 'A'  }"
                                    codeTitle="Custody Account"
                                    :change="(val)=>{
                                        newAccountsRow.clientGroup=val;
                                        changeClientGroup(val)
                                    }"
                                    :dbClick="(row)=>{
                                        newAccountsRow.clientGroup=row.code;
                                        changeClientGroup()
                                    }"
                                    :disabled="formDisabled"
                                    />
                    </FormItemSign>
                    <FormItemSign v-if="newAccountsRow.accountGroup === 'SP'" :detailsRef="details" label="Service Plan"
                        prop="clientGroup" label-width="130px">
<!--                        <Select v-model="newAccountsRow.clientGroup" style="width: 150px" type='SERVICE_PLAN_CODE'-->
<!--                            :change="changeClientGroup"  :disabled="formDisabled"/>-->
                      <CommonSearchInput v-model="newAccountsRow.clientGroup"
                                         :change="(val)=>{
                                              newAccountsRow.clientGroup=val;
                                              changeClientGroup(val)
                                         }"
                                         :dbClick="(row)=>{
                                              newAccountsRow.clientGroup=row.code;
                                              changeClientGroup()
                                         }"
                                         :disabled="formDisabled"
                                         codeTitle="Service Plan"
                                         showDesc="false"
                                         commType='SERVICE_PLAN_CODE' />
                    </FormItemSign>

                    <FormItemSign :detailsRef="details" label="Accounts" prop="accounts" label-width="130px">
                        <Select v-if="!disabledSelecte" v-model="newAccountsRow.accounts" style="width: 150px" type='ACCOUNTS'  :disabled="formDisabled"/>
                        <Select v-if="disabledSelecte" v-model="newAccountsRow.accounts" style="width: 150px" type='ACCOUNTS'  :disabled="true"/>
                    </FormItemSign>
                    <SelectionGrid url="/datamgmt/api/v1/account/list" ref="grid" 
                        :params="newParams" 
                        :disabled="formDisabled"
                        :beforeSearch="()=>{ return beforeSearchValidate();}" 
                        :columns="[
                            { title: 'csscl.acctCode.clientAccountOid', name: 'tradingAccountCode' },
                            { title: 'csscl.acctCode.accountShortName', name: 'accountShortName' },
                            { title: 'common.title.recordStatus', name: 'recordStatus', fn: getRecordStatusDesc },
                        ]">
                        <template v-slot:tableColumnAfter>
                            <el-table-column type="selection"  width="55" :selectable="checkSelectable" />
                        </template>
                    </SelectionGrid>
                    <div style="justify-content: right;display: flex;">
                        <el-button @click="hiddenAddAccount" class="ep-button-custom">Cancel</el-button>
                        <el-button type="primary" @click="addAccount" class="ep-button-custom" :disabled="formDisabled">OK</el-button>
                    </div>
                </el-card>
            </el-col>
        </el-form>
        <br v-if="!props.isReportScheduler">
        <el-row v-if="!props.isReportScheduler">
            <span style="font-size: 16px; font-weight: bold;">Other Criteria</span>
            <div style="paddingBlock:5px;width: 100%;display: flex;flex-flow: wrap;border-top: 1px solid;"></div>
        </el-row>
        <FormRow v-if="!props.isReportScheduler">
            <FormItemSign :detailsRef="details" label-width="100px" :label="$t('csscl.reportScheduler.groupBy')"
                prop="groupByType">
                <Select v-model="formInline.groupByType" style="width: 180px" vkEnqual :type="groupByType" />
            </FormItemSign>
            
        </FormRow>
        <FormRow v-if="!props.isReportScheduler">
            <FormItemSign :detailsRef="details" label-width="100px" :label="$t('csscl.reportScheduler.fileFormat')"
                prop="outputFileType">
                <el-checkbox-group v-model="formInline.outputFileType" disabled>
                    <el-checkbox label="PDF" value="PDF"></el-checkbox>
                    <el-checkbox label="EXCEL" value="XLSX"></el-checkbox>
                    <el-checkbox label="CSV" value="CSV"></el-checkbox>
                    <el-checkbox label="TXT" value="TXT"></el-checkbox>
                </el-checkbox-group>
            </FormItemSign>

        </FormRow>
        <FormRow v-if="!props.isReportScheduler">
            <FormItemSign :detailsRef="details" label-width="100px" :label="$t('csscl.reportScheduler.langguage')"
                prop="language">
                <Select v-model="formInline.language" style="width: 150px" vkEnqual type="LANGUAGE" />
            </FormItemSign>
        </FormRow>

    </el-form>

    <FormRow > 
            <ElFormItemProxy  v-if="!props.isReportScheduler">
                <el-button   style="float:right; margin-top: 50px; margin-right: 40px;" type="primary" @click="onGenerator">Generate</el-button>
            </ElFormItemProxy>
    </FormRow>
</template>
<script lang="ts" setup>
import {ref, reactive, getCurrentInstance, watchEffect, onMounted, toRaw, watch} from 'vue';
import type { FormInstance, FormRules } from 'element-plus'
import BaseDetails from '~/pages/base/Details.vue';
import FormItemSign from '~/pages/base/FormItemSign.vue';
import CommonSearchInput from '~/pages/base/CommonSearchInput.vue';
import { getOid, getCommonDesc, getRecordStatusDesc, showErrorMsg, highlight } from '~/util/Function.js';
import { showValidateMsg } from '~/util/Validators.js';
import { publicDecrypt } from 'crypto';
import { fa } from 'element-plus/es/locale';
import SelectionGrid   from '~/pages/base/SelectionGrid.vue';
import { useRouter } from 'vue-router';
import cloneDeep from 'lodash/cloneDeep';
import {
    Edit,
    Delete,
    Plus,
    Minus,
    Back,
} from '@element-plus/icons-vue';
import { ElMessageBox,ElMessage } from 'element-plus';
const formInline = ref({
    outputFileType:['XLSX']
});
const { proxy } = getCurrentInstance()
const props = defineProps(['reportTemplateCode','style', 'data', 'onGenerator','isReportScheduler','initData','formDisabled','details'])

const editing = ref(false);
const lastRow = ref(null);
const tabRef = ref();

////////////////////////////////////////////////////////////////AccountDetails Start
const currentAccountRow=ref({});
const newAccountsRow = ref({});
const isShowAddAccount=ref(false);
const grid = ref();
const newParams= ref({});
 
const disabledSelecte=ref(false);
const isdoubleCheck=ref(false);
const isdoubleCheckRow=ref();
const doubleCheckIndex = ref(-1);
const changeAccountGroup= () => {
    newAccountsRow.value.clientGroup= "";
    newAccountsRow.value.clientMasterOid= "";
    newParams.value={noResult: 'A'};
    disabledSelecte.value=false;
    newAccountsRow.value.accounts = '';
    if(newAccountsRow.value.accountGroup==='CA'){
        disabledSelecte.value=true;
        newAccountsRow.value.accounts = 'S';
    }
};
const cell = (row) => {
  if (props.isReportScheduler) {
    return highlight(row);
  }
}
const changeClientGroup= async() => {
    await grid.value.emptySelection();
    let noResult='A';
    disabledSelecte.value=false;
    if(newAccountsRow.value.accountGroup&&newAccountsRow.value.accountGroup!=""&&newAccountsRow.value.clientGroup&&newAccountsRow.value.clientGroup!=""){
        noResult='Y';
    }
    if(newAccountsRow.value.accountGroup==='CG'){
        newParams.value={clientGroupCode: newAccountsRow.value.clientGroup,recordStatus:'A',noResult: noResult};
    }else if(newAccountsRow.value.accountGroup==='SP'){
        newParams.value={servicePlan: newAccountsRow.value.clientGroup,recordStatus:'A',noResult: noResult};
    }else if(newAccountsRow.value.accountGroup==='FM'){
        newParams.value={fundMgrCode: newAccountsRow.value.clientGroup,recordStatus:'A',noResult: noResult};
    }else if(newAccountsRow.value.accountGroup==='CNC'){
        newParams.value={clientCode: newAccountsRow.value.clientGroup,clientMasterOid: newAccountsRow.value.clientMasterOid,recordStatus: 'A',noResult: noResult};
    }else if(newAccountsRow.value.accountGroup==='CA'){
        newAccountsRow.value.accounts = 'S';
        disabledSelecte.value=true;
        newParams.value={tradingAccountCode: newAccountsRow.value.clientGroup,recordStatus: 'A',noResult: noResult};
    }else{
        newParams.value={noResult: noResult};
    }
  
};
const handleCurrentAccountChange = (val) => {
    if(val&&isModified()){
        tabRef.value.setCurrentRow(currentAccountRow.value);
        return;
    }
    currentAccountRow.value = val;
};

watch(() => props.reportTemplateCode, (newVal) => {
  formInline.value = {};
});

const showAddAccount = async () => {
    if(isModified()) {
        return false;
    }
    await grid.value.emptySelection();
    isdoubleCheck.value=false;
    newAccountsRow.value={};
    newAccountsRow.value.accountGroup='CG';
    disabledSelecte.value=false;
    newAccountsRow.value.accounts = '';
    newParams.value={noResult: 'A'};
    isShowAddAccount.value = true;
    editing.value = true;
    lastRow.value = {};
    tabRef.value.setCurrentRow(null);
};
const hiddenAddAccount = () => {
    isShowAddAccount.value = false;
    editing.value = false;
    lastRow.value = null;
};
const addAccount = () => {
    const selectedData = toRaw(grid.value.getSelectedData());
    const allData = toRaw(grid.value.getAllData());
    let tmpCount = 'A';
    if (newAccountsRow.value.accounts !== 'A') {
        tmpCount=  selectedData.length
    }
    if (tmpCount === 0 || allData.length === 0) {
        ElMessage({
            message: 'At least one active custody account number',
            type: 'error',
            duration: 10000,
            offset: 100,
            showClose: true,
        });
        return true;
    }

    if (!Array.isArray(formInline.value.reportAccounts)) {
        formInline.value.reportAccounts = [];
    }
    if(isdoubleCheck.value){
        const tmpRow = reactive({
        reportCustodyAccountOid:isdoubleCheckRow.value.reportCustodyAccountOid,
        accountGroup: newAccountsRow.value.accountGroup,
        accountGroupDetails: newAccountsRow.value.clientGroup,
        clientMasterOid: newAccountsRow.value.clientMasterOid,
        tradingAccountCode: newAccountsRow.value.tradingAccountCode, 
        accounts: tmpCount,
        recordStatus: 'PD',
        mkckAction: isdoubleCheckRow.value.recordStatus==='A'?'U':isdoubleCheckRow.value.mkckAction,
        selectedData: cloneDeep(selectedData),
        selectParams: cloneDeep(newParams.value)
    });
        formInline.value.reportAccounts[doubleCheckIndex.value ] = tmpRow;
    }else{
        const tmpRow = reactive({
        accountGroup: newAccountsRow.value.accountGroup,
        accountGroupDetails: newAccountsRow.value.clientGroup,
        clientMasterOid: newAccountsRow.value.clientMasterOid,
        tradingAccountCode: newAccountsRow.value.tradingAccountCode, 
        accounts: tmpCount,
        recordStatus: 'PD',
        mkckAction: 'C',
        selectedData: cloneDeep(selectedData),
        selectParams: cloneDeep(newParams.value)
    });
        formInline.value.reportAccounts.push(tmpRow);
    }
    
    hiddenAddAccount();
   
};
const deleteAccount = () => {
    const index = formInline.value.reportAccounts.findIndex(item => item === currentAccountRow.value);
    if (index !== -1) {
        formInline.value.reportAccounts.splice(index, 1);
        currentAccountRow.value = {};
        hiddenAddAccount();
    }
};
const tableRowClassName=({ row, rowIndex })  =>{
      row.index = rowIndex;
}

const isModified = () => {
    ElMessage.closeAll();
    if(lastRow.value==null) {
        return false;
    }
    const selectedData = toRaw(grid.value.getSelectedData());
    const allData = toRaw(grid.value.getAllData());
    let tmpCount = 'A';
    if (newAccountsRow.value.accounts !== 'A') {
        tmpCount=  selectedData.length
    }
    if (tmpCount == '0') {
        tmpCount = '';
    }
    if (
        equalString(lastRow.value.accountGroupDetails , newAccountsRow.value.clientGroup) &&
        equalString(lastRow.value.clientMasterOid , newAccountsRow.value.clientMasterOid) &&
        equalString(lastRow.value.accounts , tmpCount) &&
        equalString(lastRow.value.tradingAccountCode , newAccountsRow.value.tradingAccountCode)) {
        return false;
    } 
    showErrorMsg("The sub table data has not been saved. Please confirm if you want to leave.");
    return true;
}

const equalString = (a,b) => {
    if(a==b) {
        return true;
    } else if (!a&&!b) {
        return true;
    }
    return false;
}

const handleClick = async(row: any, column: any, event: Event) => {
    if (currentAccountRow.value==null||currentAccountRow.value.reportCustodyAccountOid != row.reportCustodyAccountOid) {
        if (isModified()) {
            event.stopPropagation();
            return ;
        }
        hiddenAddAccount();
    }
}

const handleDbAccountClick = async (val, event, column) => {
    if(isModified()) {
        return false;
    }
    await grid.value.emptySelection();
    isShowAddAccount.value = true;
    isdoubleCheck.value = true;
    isdoubleCheckRow.value = val;
    doubleCheckIndex.value = val.index;
    newAccountsRow.value = {};
    newAccountsRow.value.accountGroup = val.accountGroup;
    newAccountsRow.value.clientGroup = val.accountGroupDetails;
    newAccountsRow.value.accounts = 'S';
    newAccountsRow.value.clientMasterOid = val.clientMasterOid;
    newAccountsRow.value.tradingAccountCode = val.clientMasterOid;
    // newParams.value = val.selectParams;
    if (val.accounts === 'A') {
        newAccountsRow.value.accounts = 'A';
    }
    // 确保 initSelectedData 包含所选的数据
    if (!val.initSelectedData) {
        val.initSelectedData = [];
    }
    // 合并 initSelectedData 和 selectedData
    if (val.selectedData) {
        const combinedData = [...val.initSelectedData, ...val.selectedData];
        const uniqueData = Array.from(new Map(combinedData.map(item => [item.tradingAccountCode, item])).values());
        val.initSelectedData = uniqueData;
    }
    await changeClientGroup().then(() => {
        grid.value.initSelection(val.initSelectedData);
    });
    editing.value = true;
    lastRow.value = { ...val };
};

////////////////////////////////////////////////////////////////AccountDetails end


////////////////////////////////////////////////////////////////
const onGenerator = async () => {
        if(!formInline.value.reportAccounts||formInline.value.reportAccounts?.length<=0){
            ElMessage.error("Please Select Custody Account");
            return;
        }
        formInline.value.reportId = props.reportTemplateCode;
        formInline.value.userId = proxy.$currentInfoStore.getUserInfo.userId;

        let result = true;
        if (props.onGenerator){
         result = props.onGenerator(formInline);
        }
        if (result == false) {
            return;
        }
        let alertMsg = "Confirm to onGenerator?";
        let onlineInd = "";
        await proxy.$axios.get("/rptsched/api/v1/report/template/reportCode?reportTemplateCode="+formInline.value.reportId).then((body) => {
            if (body.success) {
                if(body.data){
                    if(body.data?.onlineInd === 'Y') {
                        onlineInd = "Y";
                        alertMsg = "Are you sure to generate report? Report will export directly soon after you confirm.";
                    }
                    if(body.data?.onlineInd === 'N') {
                        onlineInd = "N";
                        alertMsg = "Are you sure to generate report? Please download from report center after you confirm.";
                    }
                }
            }
        });
        ElMessageBox.confirm(alertMsg,'Warning', {
            confirmButtonText: 'OK',
            cancelButtonText: 'Cancel',
            type: 'warning',
        }).then(() => {
            if (onlineInd === "N") {
                ElMessageBox.alert('Submit Success.', 'Success');
            }
            formInline.value.executeType='A';
            proxy.$axios.post("/rptsched/api/v1/report/template/genrpt", formInline.value,{
                responseType: 'blob',
            }).then((data) => {
                if (data?.data && onlineInd === "Y") {
                    let fileName = data?.headers['content-disposition']?.match(/filename=(.*)/)[1]
                    const blob = new Blob([data?.data]);
                    const link = document.createElement('a');
                    link.style.display = "none";
                    link.href = URL.createObjectURL(blob);
                    link.download = fileName;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                } 
            });
        }).catch(() => {
        })
        return result;
    };

    const getR000101CFormData = () => {
        return formInline.value;
    };
    const initR000101CFormData=()=>{
        if(props.initData){
            if(props.initData.reportAccounts){
                formInline.value={...props.initData};
            }
        }

    }
    const  checkSelectable=(row)=>{
        if(!props.formDisabled){
            return newAccountsRow.value.accounts=='S';
        }else{
            return false;
        }
    }
    const getSource= async ()=>{
        await proxy.$axios.get("/rptsched/api/v1/report/template/reportCode?reportTemplateCode="+props.reportTemplateCode).then((body) => {
            if (body.success) {
                if(body.data){
                    groupByType.value = "GROUP_BY_TYPE_" +  body.data?.allowIndivInd;
                }
            }
        });
    }
    const groupByType = ref();
    watchEffect(() => {
        initR000101CFormData();
        getSource();
        const data = proxy.$commonCodeStore.getAll[groupByType.value];
        if(data&&data?.length>0){
            formInline.value.groupByType=data[0].code;
        }
    });
    const beforeSearchValidate = ()=>{
        return true;
    }
    defineExpose({
        getR000101CFormData,
        initR000101CFormData,
        formInline,
        editing,
    });

</script>

<style>

</style>