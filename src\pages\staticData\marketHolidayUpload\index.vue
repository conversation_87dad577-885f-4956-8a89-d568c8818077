<template> 
  <BasePanel :searchParams="searchParams" :paramListData='paramListData' url="/datamgmt/api/v1/market/upload/list" :params="{ modeEdit: 'Y' }"
    :showDetails="showDetails" :deleteRow="deleteRow" :editRow="editRow" ref="tableRef" :sortProp="{}" :onExportCSV="handleExportCSV" :isHideExport="true" :rules="rules">
    <template v-slot:searchPanel="slotProps">
      <FormRow>
        <ElFormItemProxy :label="$t('csscl.si.common.fileName')" label-width="180" prop="fileName">
          <el-input v-model="slotProps.form.fileName" maxlength="100" style="width: 250px" />
        </ElFormItemProxy>

          <ElFormItemProxy>
            <ElFormItemProxy label-width="140" :label="$t('csscl.si.common.uploadDateFrom')" prop="uploadDateFrom">
              <DateItem v-model="slotProps.form.uploadDateFrom" type="date" style="width: 110px;"/>
            </ElFormItemProxy>
            <ElFormItemProxy :label="$t('common.title.date.to')" :hideLabel="$t('csscl.common.dateTo')" prop="uploadDateTo" label-width="20px">
              <DateItem v-model="slotProps.form.uploadDateTo" type="date" style="width: 125px;"/>
            </ElFormItemProxy>
          </ElFormItemProxy>

        <ElFormItemProxy :label="$t('common.title.recordStatus')" label-width="120" prop="multipleRecordStatus">
          <MultipleSelect v-model="slotProps.form.multipleRecordStatus" v-model:desc="paramListData.multipleRecordStatus"  />
        </ElFormItemProxy>
      </FormRow>


    </template>
    <template v-slot:tableColumn>
      <el-table-column align="left" header-align="center" sortable="custom" prop="fileName" :label="$t('csscl.si.common.fileName')" width="600" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="uploadDt" :label="$t('csscl.si.common.uploadDate')" width="600" />
      <el-table-column align="left" header-align="center" sortable="custom" prop="recordStatus" :label="$t('csscl.useradmin.usr.recordStatus')" >
        <template #default="scope">
          {{ getRecordStatusDesc(scope.row) }}
        </template>
      </el-table-column>
    </template>
  </BasePanel>
  <Details ref="detailsRef" :reload="reload" />
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance, watch } from 'vue';
import {
Search
} from '@element-plus/icons-vue'
import BasePanel from '~/pages/base/index.vue'
import Details from './Details.vue'
import CtryRegionSearchInput from '~/pages/base/CtryRegionSearchInput.vue'
import CurrencySearchInput from '~/pages/base/CurrencySearchInput.vue'
import {downloadFilePost, getCommonDesc, getRecordStatusDesc} from '~/util/Function.js';
import { getOid } from '~/util/Function.js';
import {ElLoading, ElMessage} from "element-plus";
import {getTimeZone} from "~/util/DateUtils";
import { commonRules } from '~/util/Validators.js';

const { proxy } = getCurrentInstance()
const paramListData = ref({});
const searchParams = ref({
  fileName:"",
  uploadDateFrom:"",
  uploadDateTo:"",
  multipleRecordStatus:[]
  });

const tableRef = ref();
const detailsRef = ref();
const showDetails = (row, disabled) => {
  detailsRef.value.showDetails(row, disabled);
}
const deleteRow = (row) => {
  proxy.$axios.delete("/datamgmt/api/v1/market/upload?objectId="+ getOid(row, false,null,true)).then((body) => {
    if (body.success) {
      tableRef.value.load();
    }
  });
}

const handleExportCSV = async () => {
  // 显示加载中状态
  const loading = ElLoading.service({
    lock: true,
    text: '',
    background: 'rgba(255, 255, 255, 0.3)',
    fullscreen: false,
    target: document.querySelector('.el-main') as HTMLElement || undefined,
    body: false,
    customClass: 'loading-position'
  });
  try {
    await downloadFilePost("/datamgmt/api/v1/market/upload/export-csv", searchParams.value);
  } catch (error) {
    ElMessage.error('Failed to export CSV');
  } finally {
    loading.close();
  }
};
const rules = reactive({
  uploadDateFrom: [
    commonRules.earlierEquDt(() => { return searchParams.value.uploadDateTo }, proxy.$t('csscl.common.dateTo')),
    commonRules.requiredEndDate(() => { return searchParams.value.uploadDateTo }, 'Please include the date for Upload Date To'),
  ],
  uploadDateTo: [
    // commonRules.laterEquDt(() => { return searchParams.value.uploadDateFrom }, proxy.$t('csscl.si.common.uploadDateFrom')),
  ],

});


//paramList 参数显示用的
function recordType(value){
  console.log(searchParams)
  paramListData._value.recordStatus =  getCommonDesc('RECORD_STATUS', value);
}
function statusType(value){
  paramListData._value.status =  getCommonDesc('STATUS', value);
}
const editRow = (row) => {
  detailsRef.value.editRow(row);
}
const reload = () => {
  tableRef.value.load();
}
//-------------------------------

</script>

<style>

</style>
